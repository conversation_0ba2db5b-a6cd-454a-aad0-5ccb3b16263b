#!/usr/bin/env python3
"""
测试完整的数据结构，包括新增的pct_chg字段
"""

import sys
import os

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

from data_update.dailydata_func_tdx import read_all_stocks_to_dataframe_parallel

def test_complete_data_structure():
    """测试完整的数据结构"""
    print("=== 测试完整数据结构 ===")
    
    try:
        # 获取少量数据进行测试
        stock_df, index_df = read_all_stocks_to_dataframe_parallel(
            max_workers=2, 
            fetch_stock_info=True  # 包含股票基本信息
        )
        
        print(f"数据获取结果:")
        print(f"  股票数据: {len(stock_df)} 条记录")
        print(f"  指数数据: {len(index_df)} 条记录")
        
        if not stock_df.empty:
            print(f"\n股票数据结构:")
            print(f"  总列数: {len(stock_df.columns)}")
            print(f"  列名: {list(stock_df.columns)}")
            
            # 预期的完整字段列表
            expected_columns = [
                'ts_code',      # 股票代码
                'trade_date',   # 交易日期
                'open',         # 开盘价
                'high',         # 最高价
                'low',          # 最低价
                'close',        # 收盘价
                'pre_close',    # 前收盘价
                'pct_chg',      # 涨跌幅 (新增)
                'vol',          # 成交量
                'amount',       # 成交额
                'total_share',  # 总股本
                'float_share',  # 流通股本
                'total_mv',     # 总市值
                'circ_mv',      # 流通市值
                'turnover_rate' # 换手率
            ]
            
            print(f"\n字段完整性检查:")
            missing_fields = []
            for field in expected_columns:
                if field in stock_df.columns:
                    print(f"  ✓ {field}")
                else:
                    print(f"  ❌ {field} (缺失)")
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"\n缺失字段: {missing_fields}")
            else:
                print(f"\n✓ 所有预期字段都存在")
            
            # 检查各字段的数据质量
            print(f"\n数据质量检查:")
            for field in expected_columns:
                if field in stock_df.columns:
                    valid_count = stock_df[field].notna().sum()
                    total_count = len(stock_df)
                    percentage = (valid_count / total_count * 100) if total_count > 0 else 0
                    print(f"  {field}: {valid_count}/{total_count} ({percentage:.1f}%) 有效")
            
            # 显示完整记录示例
            print(f"\n完整记录示例:")
            if len(stock_df) > 0:
                sample_record = stock_df.iloc[0].to_dict()
                for key, value in sample_record.items():
                    if isinstance(value, float):
                        print(f"  {key}: {value:.4f}")
                    else:
                        print(f"  {key}: {value}")
        
        if not index_df.empty:
            print(f"\n指数数据结构:")
            print(f"  总列数: {len(index_df.columns)}")
            print(f"  列名: {list(index_df.columns)}")
            
            # 检查指数数据中的pct_chg
            if 'pct_chg' in index_df.columns:
                valid_pct_chg = index_df['pct_chg'].notna().sum()
                print(f"  指数pct_chg有效记录: {valid_pct_chg}/{len(index_df)}")
                
                if valid_pct_chg > 0:
                    print(f"  指数pct_chg示例:")
                    sample_cols = ['ts_code', 'trade_date', 'close', 'pre_close', 'pct_chg']
                    available_cols = [col for col in sample_cols if col in index_df.columns]
                    print(index_df[available_cols].head().to_string(index=False))
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("开始测试完整数据结构...\n")
    
    test_complete_data_structure()
    
    print("\n" + "=" * 50)
    print("数据结构总结:")
    print("✓ 基础价格数据: open, high, low, close, pre_close")
    print("✓ 涨跌幅指标: pct_chg (新增)")
    print("✓ 成交数据: vol, amount")
    print("✓ 股票基本信息: total_share, float_share, total_mv, circ_mv")
    print("✓ 计算指标: turnover_rate")
    print("✓ 标识信息: ts_code, trade_date")
    print("=" * 50)
    
    print("\npct_chg字段说明:")
    print("- 计算公式: (close - pre_close) / pre_close * 100")
    print("- 单位: 百分比 (%)")
    print("- 精度: 保留4位小数")
    print("- 适用: 股票和指数数据")
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
