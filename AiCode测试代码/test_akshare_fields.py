#!/usr/bin/env python3
"""
测试akshare返回的字段名
"""

import sys
import os

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

def test_akshare_fields():
    """测试akshare返回的实际字段名"""
    try:
        import akshare as ak
        print("akshare导入成功")
    except ImportError:
        print("akshare未安装，请先安装: pip install akshare")
        return
    
    # 测试几只股票
    test_symbols = ['000001', '000002', '600000']
    
    for symbol in test_symbols:
        print(f"\n=== 测试股票: {symbol} ===")
        try:
            # 获取股票基本信息
            stock_info = ak.stock_individual_info_em(symbol=symbol)
            
            print(f"返回数据类型: {type(stock_info)}")
            print(f"数据内容:")
            
            if isinstance(stock_info, dict):
                # 如果是字典，显示所有键值对
                for key, value in stock_info.items():
                    print(f"  '{key}': {value}")
                
                # 检查我们需要的字段是否存在
                required_fields = ['总股本', '流通股', '总市值', '流通市值']
                print(f"\n检查必需字段:")
                for field in required_fields:
                    if field in stock_info:
                        print(f"  ✓ '{field}': {stock_info[field]}")
                    else:
                        print(f"  ❌ '{field}': 字段不存在")
                        
                # 查找可能的相似字段
                print(f"\n所有包含'股本'的字段:")
                for key in stock_info.keys():
                    if '股本' in key:
                        print(f"  '{key}': {stock_info[key]}")
                        
                print(f"\n所有包含'市值'的字段:")
                for key in stock_info.keys():
                    if '市值' in key:
                        print(f"  '{key}': {stock_info[key]}")
                        
            else:
                print(f"  数据: {stock_info}")
                
        except Exception as e:
            print(f"获取股票 {symbol} 信息失败: {str(e)}")
        
        # 只测试第一只股票，避免请求过多
        break

def test_field_mapping():
    """测试字段映射的正确性"""
    print("\n=== 测试字段映射 ===")

    # 导入我们的处理函数
    from data_update.dailydata_func_tdx import _get_field_value, _convert_chinese_number

    # 模拟akshare返回的不同格式数据
    test_cases = [
        {
            'name': '标准格式',
            'data': {
                '总股本': '194.06亿',
                '流通股': '194.06亿',
                '总市值': '2876.23亿',
                '流通市值': '2876.23亿',
                '股票代码': '000001',
                '股票简称': '平安银行'
            }
        },
        {
            'name': '带括号格式',
            'data': {
                '总股本(股)': '19405918198',
                '流通股(股)': '19405918198',
                '总市值(元)': '287623000000',
                '流通市值(元)': '287623000000',
                '股票代码': '000001'
            }
        },
        {
            'name': '万股格式',
            'data': {
                '总股本(万股)': '1940591.82',
                '流通A股': '1940591.82万股',
                '总市值(万元)': '28762300',
                '流通A股市值': '28762300万元',
                '股票代码': '000001'
            }
        },
        {
            'name': '混合格式',
            'data': {
                '总股本': '1940591.82万',
                '流通股': '194.06亿',
                '总市值': '2876.23亿',
                '流通市值(万元)': '28762300',
                '股票代码': '000001'
            }
        }
    ]

    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        stock_info = test_case['data']

        print("原始数据:")
        for key, value in stock_info.items():
            print(f"  '{key}': {value}")

        # 测试字段映射
        print("\n字段映射结果:")
        result = {
            'total_share': _get_field_value(stock_info, ['总股本', '总股本(股)', '总股本(万股)']),
            'float_share': _get_field_value(stock_info, ['流通股', '流通股(股)', '流通股(万股)', '流通A股']),
            'total_mv': _get_field_value(stock_info, ['总市值', '总市值(元)', '总市值(万元)']),
            'circ_mv': _get_field_value(stock_info, ['流通市值', '流通市值(元)', '流通市值(万元)', '流通A股市值']),
        }

        for key, value in result.items():
            print(f"  {key}: {value}")

        # 测试数据转换
        print("\n数据转换结果:")
        for key, value in result.items():
            converted = _convert_chinese_number(value)
            if converted is not None:
                print(f"  {key}: {value} → {converted:,.0f}")
            else:
                print(f"  {key}: {value} → None")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")

    from data_update.dailydata_func_tdx import _get_field_value, _convert_chinese_number

    # 测试字段不存在的情况
    empty_data = {}
    result = _get_field_value(empty_data, ['总股本', '总股本(股)'])
    print(f"空数据字段查找: {result}")

    # 测试数值转换的边界情况
    test_values = [
        None,
        '',
        '0',
        '123.45',
        '123.45万',
        '123.45亿',
        '123.45千',
        '1,234.56万',
        '1,234,567.89',
        'N/A',
        '无数据',
        123.45,
        0
    ]

    print("\n数值转换测试:")
    for value in test_values:
        converted = _convert_chinese_number(value)
        print(f"  {repr(value)} → {converted}")

def test_real_akshare_integration():
    """测试真实的akshare集成"""
    print("\n=== 测试真实akshare集成 ===")

    from data_update.dailydata_func_tdx import get_stock_basic_info, AKSHARE_AVAILABLE

    if not AKSHARE_AVAILABLE:
        print("akshare不可用，跳过真实集成测试")
        return

    # 测试几只股票
    test_codes = ['000001.SZ', '600000.SH']

    for ts_code in test_codes:
        print(f"\n测试股票: {ts_code}")
        try:
            info = get_stock_basic_info(ts_code)
            if info:
                print("获取成功:")
                for key, value in info.items():
                    if value is not None:
                        print(f"  {key}: {value:,.0f}")
                    else:
                        print(f"  {key}: None")
            else:
                print("获取失败或返回空数据")
        except Exception as e:
            print(f"异常: {str(e)}")

        # 只测试一只股票，避免请求过多
        break

def main():
    """主函数"""
    print("开始测试akshare字段名处理...")

    # 测试实际的akshare返回数据
    test_akshare_fields()

    # 测试字段映射逻辑
    test_field_mapping()

    # 测试边界情况
    test_edge_cases()

    # 测试真实集成
    test_real_akshare_integration()

    print("\n" + "=" * 50)
    print("测试总结:")
    print("✓ akshare字段名映射测试完成")
    print("✓ 中文数值转换测试完成")
    print("✓ 边界情况处理测试完成")
    print("✓ 真实集成测试完成")
    print("=" * 50)

    print("\n测试完成！")

if __name__ == '__main__':
    main()
