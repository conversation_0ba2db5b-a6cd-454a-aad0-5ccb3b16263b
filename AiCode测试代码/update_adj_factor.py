#!/usr/bin/env python3
"""
更新stock_data数据库中的adj_factor字段
从CSV文件读取adj_factor数据并更新到数据库中
"""

import os
import sys
import pandas as pd
from sqlalchemy import create_engine, text
import logging
from datetime import datetime

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

import config.config_Ali as config_Ali

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_adj_factor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdjFactorUpdater:
    """adj_factor数据更新器"""
    
    def __init__(self):
        """初始化数据库连接"""
        self.engine = None
        self.csv_files = {
            '2025-08-21': 'adj_factor_20250821.csv'
        }
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        
    def connect_database(self):
        """连接数据库"""
        try:
            conf = config_Ali.configModel()
            self.engine = create_engine(
                f'mysql+pymysql://{conf.DC_DB_USER}:{conf.DC_DB_PASS}@{conf.DC_DB_URL}:{conf.DC_DB_PORT}/stocksfit',
                pool_pre_ping=True,
                pool_recycle=3600
            )
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def read_csv_file(self, file_path: str, expected_trade_date: str) -> pd.DataFrame:
        """
        读取CSV文件
        Args:
            file_path: CSV文件路径
            expected_trade_date: 期望的交易日期（用于验证）
        Returns:
            pd.DataFrame: 包含ts_code和adj_factor的DataFrame
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return pd.DataFrame()

            # 读取CSV文件
            df = pd.read_csv(file_path)
            logger.info(f"读取文件 {file_path}，共 {len(df)} 条记录")

            # 检查必要的列是否存在
            required_columns = ['ts_code', 'trade_date', 'adj_factor']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"文件 {file_path} 缺少必要列: {missing_columns}")
                return pd.DataFrame()

            # 转换trade_date格式：20250815 -> 2025-08-15
            def convert_date_format(date_str):
                """使用pandas datetime将20250815格式转换为2025-08-15格式"""
                try:
                    # 转换为字符串并清理
                    date_str = str(date_str).strip()

                    # 检查格式是否正确（8位数字）
                    if len(date_str) == 8 and date_str.isdigit():
                        # 使用pandas to_datetime解析日期
                        date_obj = pd.to_datetime(date_str, format='%Y%m%d')
                        # 使用strftime格式化为目标格式
                        return date_obj.strftime('%Y-%m-%d')
                    else:
                        return None
                except Exception as e:
                    logger.warning(f"日期转换失败: {date_str}, 错误: {str(e)}")
                    return None

            # 应用日期格式转换
            df['trade_date_converted'] = df['trade_date'].apply(convert_date_format)

            # 过滤掉转换失败的记录
            df = df.dropna(subset=['trade_date_converted'])

            # 验证日期是否符合预期
            unique_dates = df['trade_date_converted'].unique()
            logger.info(f"文件中包含的交易日期: {unique_dates}")

            if expected_trade_date not in unique_dates:
                logger.warning(f"文件中没有找到期望的日期 {expected_trade_date}")
                # 如果只有一个日期且不是期望日期，可能是文件命名问题，继续处理
                if len(unique_dates) == 1:
                    logger.warning(f"使用文件中的日期 {unique_dates[0]} 继续处理")

            # 使用转换后的日期
            df['trade_date'] = df['trade_date_converted']

            # 只保留需要的列
            df = df[['ts_code', 'trade_date', 'adj_factor']].copy()

            # 数据清理
            df = df.dropna(subset=['ts_code', 'adj_factor'])

            # 确保adj_factor为数值类型
            df['adj_factor'] = pd.to_numeric(df['adj_factor'], errors='coerce')
            df = df.dropna(subset=['adj_factor'])

            # 确保ts_code和trade_date为字符串类型
            df['ts_code'] = df['ts_code'].astype(str)
            df['trade_date'] = df['trade_date'].astype(str)

            # 添加数据类型验证
            logger.info(f"数据类型检查:")
            logger.info(f"  ts_code: {df['ts_code'].dtype}")
            logger.info(f"  trade_date: {df['trade_date'].dtype}")
            logger.info(f"  adj_factor: {df['adj_factor'].dtype}")

            # 检查是否有异常值
            if len(df) > 0:
                logger.info(f"样本数据:")
                sample = df.head(3)
                for idx, row in sample.iterrows():
                    logger.info(f"  {row['ts_code']} | {row['trade_date']} | {row['adj_factor']} | {type(row['adj_factor'])}")

            logger.info(f"清理后有效记录: {len(df)} 条")
            return df

        except Exception as e:
            logger.error(f"读取文件 {file_path} 失败: {str(e)}")
            return pd.DataFrame()
    
    def check_existing_data(self, trade_date: str) -> int:
        """
        检查数据库中指定日期的记录数和缺失adj_factor的记录数
        Args:
            trade_date: 交易日期
        Returns:
            int: 总记录数量
        """
        try:
            # 检查总记录数
            total_query = """
            SELECT COUNT(*) as count
            FROM stock_data
            WHERE trade_date = :trade_date
            """

            # 检查缺失adj_factor的记录数
            missing_query = """
            SELECT COUNT(*) as count
            FROM stock_data
            WHERE trade_date = :trade_date AND (adj_factor IS NULL OR adj_factor = 0)
            """

            with self.engine.connect() as conn:
                # 总记录数
                result = conn.execute(text(total_query), {'trade_date': trade_date})
                total_count = result.fetchone()[0]

                # 缺失记录数
                result = conn.execute(text(missing_query), {'trade_date': trade_date})
                missing_count = result.fetchone()[0]

                logger.info(f"数据库中 {trade_date}: 总记录 {total_count} 条，缺失adj_factor {missing_count} 条")
                return total_count

        except Exception as e:
            logger.error(f"检查数据库记录失败: {str(e)}")
            return 0
    
    def get_missing_adj_factor_records(self, trade_date: str) -> pd.DataFrame:
        """
        获取指定日期中adj_factor为空的记录
        Args:
            trade_date: 交易日期
        Returns:
            pd.DataFrame: 需要更新的记录
        """
        try:
            query = """
            SELECT ts_code, trade_date
            FROM stock_data
            WHERE trade_date = :trade_date AND (adj_factor IS NULL OR adj_factor = 0)
            """

            with self.engine.connect() as conn:
                result = conn.execute(text(query), {'trade_date': trade_date})
                missing_records = pd.DataFrame(result.fetchall(), columns=['ts_code', 'trade_date'])

            logger.info(f"{trade_date} 需要更新adj_factor的记录: {len(missing_records)} 条")
            return missing_records

        except Exception as e:
            logger.error(f"获取缺失记录失败: {str(e)}")
            return pd.DataFrame()

    def update_adj_factor_fast(self, csv_df: pd.DataFrame, trade_date: str) -> tuple:
        """
        高速批量更新adj_factor字段
        Args:
            csv_df: CSV数据DataFrame
            trade_date: 交易日期
        Returns:
            tuple: (更新是否成功, 实际更新的记录数)
        """
        try:
            # 1. 获取数据库中需要更新的记录
            missing_records = self.get_missing_adj_factor_records(trade_date)
            if missing_records.empty:
                logger.info(f"{trade_date} 没有需要更新的记录")
                return True, 0

            # 2. 与CSV数据进行内连接，只保留需要更新的记录
            update_data = missing_records.merge(
                csv_df[['ts_code', 'trade_date', 'adj_factor']],
                on=['ts_code', 'trade_date'],
                how='inner'
            )

            if update_data.empty:
                logger.warning(f"{trade_date} CSV数据与数据库缺失记录无匹配")
                return True, 0

            logger.info(f"{trade_date} CSV文件记录: {len(csv_df)} 条")
            logger.info(f"{trade_date} 数据库缺失记录: {len(missing_records)} 条")
            logger.info(f"{trade_date} 实际需要更新的记录: {len(update_data)} 条")

            # 3. 使用临时表进行高速批量更新
            success, actual_updated = self._fast_bulk_update(update_data, trade_date)
            return success, actual_updated

        except Exception as e:
            logger.error(f"高速更新失败: {str(e)}")
            return False, 0

    def _fast_bulk_update(self, update_data: pd.DataFrame, trade_date: str) -> tuple:
        """
        使用临时表进行高速批量更新
        Args:
            update_data: 更新数据
            trade_date: 交易日期
        Returns:
            tuple: (更新是否成功, 实际更新的记录数)
        """
        try:
            with self.engine.connect() as conn:
                trans = conn.begin()
                try:
                    # 创建临时表
                    temp_table_name = f"temp_adj_factor_{trade_date.replace('-', '')}"

                    create_temp_sql = f"""
                    CREATE TEMPORARY TABLE {temp_table_name} (
                        ts_code VARCHAR(20),
                        trade_date VARCHAR(20),
                        adj_factor DOUBLE,
                        INDEX idx_temp (ts_code, trade_date)
                    )
                    """

                    conn.execute(text(create_temp_sql))
                    logger.info(f"创建临时表 {temp_table_name}")

                    # 批量插入数据到临时表
                    insert_sql = f"""
                    INSERT INTO {temp_table_name} (ts_code, trade_date, adj_factor)
                    VALUES (:ts_code, :trade_date, :adj_factor)
                    """

                    # 准备插入数据
                    insert_data = [
                        {
                            'ts_code': str(row['ts_code']),
                            'trade_date': str(row['trade_date']),
                            'adj_factor': float(row['adj_factor'])
                        }
                        for _, row in update_data.iterrows()
                    ]

                    # 批量插入到临时表
                    for data in insert_data:
                        conn.execute(text(insert_sql), data)

                    logger.info(f"向临时表插入 {len(insert_data)} 条记录")

                    # 使用JOIN进行批量更新
                    update_sql = f"""
                    UPDATE stock_data s
                    INNER JOIN {temp_table_name} t
                    ON s.ts_code = t.ts_code AND s.trade_date = t.trade_date
                    SET s.adj_factor = t.adj_factor
                    """

                    result = conn.execute(text(update_sql))
                    updated_count = result.rowcount

                    trans.commit()
                    logger.info(f"✅ 高速批量更新完成，影响 {updated_count} 行")

                    return updated_count > 0, updated_count

                except Exception as e:
                    trans.rollback()
                    logger.error(f"批量更新事务失败: {str(e)}")
                    return False, 0

        except Exception as e:
            logger.error(f"高速批量更新失败: {str(e)}")
            return False, 0
    
    def verify_update(self, trade_date: str, expected_updated_count: int) -> bool:
        """
        验证更新结果
        Args:
            trade_date: 交易日期
            expected_updated_count: 期望更新的记录数（实际更新的记录数）
        Returns:
            bool: 验证是否通过
        """
        try:
            # 查询更新后有adj_factor的记录数
            query = """
            SELECT COUNT(*) as count
            FROM stock_data
            WHERE trade_date = :trade_date AND adj_factor IS NOT NULL AND adj_factor != 0
            """

            with self.engine.connect() as conn:
                result = conn.execute(text(query), {'trade_date': trade_date})
                actual_count = result.fetchone()[0]

            logger.info(f"{trade_date} 验证结果:")
            logger.info(f"  实际更新记录数: {expected_updated_count} 条")
            logger.info(f"  数据库中有效adj_factor记录数: {actual_count} 条")

            if expected_updated_count == 0:
                logger.info(f"{trade_date} 没有记录需要更新，验证通过")
                return True
            elif actual_count >= expected_updated_count:
                logger.info(f"{trade_date} 验证通过")
                return True
            else:
                logger.warning(f"{trade_date} 验证失败，可能有部分数据未更新")
                return False

        except Exception as e:
            logger.error(f"验证更新结果失败: {str(e)}")
            return False
    
    def process_all_files(self):
        """处理所有CSV文件"""
        logger.info("=" * 60)
        logger.info("开始更新adj_factor数据")
        logger.info("=" * 60)
        
        if not self.connect_database():
            return False
        
        total_success = 0
        total_failed = 0
        
        for trade_date, filename in self.csv_files.items():
            logger.info(f"\n处理 {trade_date} 的数据...")
            
            # 构建文件路径
            file_path = os.path.join(self.script_dir, filename)
            
            # 检查数据库中的现有记录
            existing_count = self.check_existing_data(trade_date)
            if existing_count == 0:
                logger.warning(f"数据库中没有 {trade_date} 的记录，跳过")
                continue
            
            # 读取CSV文件
            df = self.read_csv_file(file_path, trade_date)
            if df.empty:
                logger.error(f"跳过 {trade_date}，无有效数据")
                total_failed += 1
                continue
            
            # 使用高速批量更新
            success, actual_updated_count = self.update_adj_factor_fast(df, trade_date)
            if success:
                # 验证更新结果，使用实际更新的记录数
                if self.verify_update(trade_date, actual_updated_count):
                    total_success += 1
                    logger.info(f"✅ {trade_date} 处理成功，更新了 {actual_updated_count} 条记录")
                else:
                    total_failed += 1
                    logger.error(f"❌ {trade_date} 验证失败")
            else:
                total_failed += 1
                logger.error(f"❌ {trade_date} 更新失败")
        
        # 关闭数据库连接
        if self.engine:
            self.engine.dispose()
        
        logger.info("\n" + "=" * 60)
        logger.info("adj_factor数据更新完成")
        logger.info(f"成功: {total_success} 个文件，失败: {total_failed} 个文件")
        logger.info("=" * 60)
        
        return total_failed == 0

def main():
    """主函数"""
    updater = AdjFactorUpdater()
    
    # 检查CSV文件是否存在
    missing_files = []
    for trade_date, filename in updater.csv_files.items():
        file_path = os.path.join(updater.script_dir, filename)
        if not os.path.exists(file_path):
            missing_files.append(filename)
    
    if missing_files:
        logger.error(f"以下CSV文件不存在: {missing_files}")
        logger.error("请确保以下文件存在于脚本目录中:")
        for filename in updater.csv_files.values():
            logger.error(f"  - {filename}")
        return False
    
    # 执行更新
    success = updater.process_all_files()
    
    if success:
        logger.info("所有adj_factor数据更新成功！")
    else:
        logger.error("部分adj_factor数据更新失败，请检查日志")
    
    return success

if __name__ == '__main__':
    main()
