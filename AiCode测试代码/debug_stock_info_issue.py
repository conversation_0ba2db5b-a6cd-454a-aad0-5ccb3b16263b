#!/usr/bin/env python3
"""
诊断股票基本信息未能存储和换手率未能计算的问题
"""

import sys
import os
import pandas as pd

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

from data_update.dailydata_func_tdx import (
    get_stock_basic_info,
    get_stocks_basic_info_batch,
    process_single_stock_file,
    DayStockFileProcessor,
    AKSHARE_AVAILABLE
)

def debug_akshare_availability():
    """检查akshare可用性"""
    print("=== 检查akshare可用性 ===")
    print(f"AKSHARE_AVAILABLE: {AKSHARE_AVAILABLE}")
    
    if AKSHARE_AVAILABLE:
        try:
            import akshare as ak
            print("✓ akshare导入成功")
            
            # 测试API调用
            result = ak.stock_individual_info_em(symbol='000001')
            print("✓ akshare API调用成功")
            print(f"  返回数据类型: {type(result)}")
            if isinstance(result, dict):
                print(f"  包含字段: {list(result.keys())}")
            
        except Exception as e:
            print(f"❌ akshare测试失败: {str(e)}")
    else:
        print("❌ akshare不可用")

def debug_single_stock_info():
    """调试单只股票信息获取"""
    print("\n=== 调试单只股票信息获取 ===")
    
    if not AKSHARE_AVAILABLE:
        print("跳过：akshare不可用")
        return
    
    test_code = '000001.SZ'
    print(f"测试股票: {test_code}")
    
    try:
        result = get_stock_basic_info(test_code)
        print(f"获取结果: {result}")
        
        if result:
            for key, value in result.items():
                print(f"  {key}: {value} (类型: {type(value)})")
        else:
            print("❌ 返回空结果")
            
    except Exception as e:
        print(f"❌ 获取失败: {str(e)}")
        import traceback
        traceback.print_exc()

def debug_batch_stock_info():
    """调试批量股票信息获取"""
    print("\n=== 调试批量股票信息获取 ===")
    
    if not AKSHARE_AVAILABLE:
        print("跳过：akshare不可用")
        return {}
    
    test_codes = ['000001.SZ', '000002.SZ']
    print(f"测试股票: {test_codes}")
    
    try:
        result_map = get_stocks_basic_info_batch(test_codes, max_workers=2)
        print(f"批量获取结果: {len(result_map)} 只股票")
        
        for ts_code, info in result_map.items():
            print(f"\n{ts_code}:")
            if info:
                for key, value in info.items():
                    print(f"  {key}: {value} (类型: {type(value)})")
            else:
                print("  空信息")
        
        return result_map
        
    except Exception as e:
        print(f"❌ 批量获取失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}

def debug_file_processing():
    """调试文件处理过程"""
    print("\n=== 调试文件处理过程 ===")
    
    # 获取一个真实的股票文件
    processor = DayStockFileProcessor("")
    stock_file_map, _ = processor.prepare_stock_files()
    
    if not stock_file_map:
        print("❌ 没有找到股票文件")
        return
    
    # 取第一个股票进行测试
    ts_code, file_path = list(stock_file_map.items())[0]
    print(f"测试文件: {ts_code} -> {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    print(f"✓ 文件存在")
    
    # 模拟股票信息
    mock_stock_info_map = {}
    if AKSHARE_AVAILABLE:
        print("获取真实股票信息...")
        try:
            info = get_stock_basic_info(ts_code)
            if info:
                mock_stock_info_map[ts_code] = info
                print(f"✓ 获取到股票信息: {info}")
            else:
                print("❌ 未获取到股票信息")
        except Exception as e:
            print(f"❌ 获取股票信息失败: {str(e)}")
    
    # 测试文件处理
    print(f"\n处理文件...")
    try:
        if mock_stock_info_map:
            result = process_single_stock_file((ts_code, file_path, mock_stock_info_map))
        else:
            result = process_single_stock_file((ts_code, file_path, {}))
        
        data_type, df = result
        print(f"处理结果: data_type={data_type}")
        
        if df is not None and not df.empty:
            print(f"✓ 数据处理成功")
            print(f"  记录数: {len(df)}")
            print(f"  列数: {len(df.columns)}")
            print(f"  列名: {list(df.columns)}")
            
            # 检查关键字段
            key_fields = ['total_share', 'float_share', 'total_mv', 'circ_mv', 'turnover_rate']
            print(f"\n关键字段检查:")
            for field in key_fields:
                if field in df.columns:
                    valid_count = df[field].notna().sum()
                    print(f"  {field}: {valid_count}/{len(df)} 有效值")
                    if valid_count > 0:
                        print(f"    示例值: {df[field].dropna().iloc[0]}")
                else:
                    print(f"  {field}: 字段缺失")
            
            # 显示第一条记录
            print(f"\n第一条记录:")
            print(df.iloc[0].to_dict())
            
        else:
            print("❌ 数据处理失败或返回空数据")
            
    except Exception as e:
        print(f"❌ 文件处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

def debug_data_conversion():
    """调试数据转换问题"""
    print("\n=== 调试数据转换问题 ===")
    
    from data_update.dailydata_func_tdx import _convert_chinese_number
    
    # 测试各种格式的数据转换
    test_values = [
        '194.06亿',
        '1940591.82万',
        '19405918198',
        '1,234,567.89',
        None,
        '',
        'N/A'
    ]
    
    print("数据转换测试:")
    for value in test_values:
        converted = _convert_chinese_number(value)
        print(f"  {repr(value)} → {converted}")

def debug_turnover_calculation():
    """调试换手率计算"""
    print("\n=== 调试换手率计算 ===")
    
    # 模拟数据
    test_data = {
        'vol': 1000000,  # 成交量
        'total_share': 1000000000,  # 总股本
    }
    
    print(f"测试数据: {test_data}")
    
    # 计算换手率
    if test_data['total_share'] and test_data['total_share'] > 0:
        turnover_rate = (test_data['vol'] / test_data['total_share']) * 100
        print(f"计算结果: {turnover_rate}%")
    else:
        print("无法计算：总股本为空或为0")

def main():
    """主诊断函数"""
    print("开始诊断股票基本信息和换手率问题...\n")
    
    # 1. 检查akshare可用性
    debug_akshare_availability()
    
    # 2. 调试单只股票信息获取
    debug_single_stock_info()
    
    # 3. 调试批量股票信息获取
    debug_batch_stock_info()
    
    # 4. 调试文件处理过程
    debug_file_processing()
    
    # 5. 调试数据转换
    debug_data_conversion()
    
    # 6. 调试换手率计算
    debug_turnover_calculation()
    
    print("\n" + "=" * 50)
    print("诊断完成！请检查上述输出中的错误信息")
    print("=" * 50)

if __name__ == '__main__':
    main()
