#!/usr/bin/env python3
"""
测试pct_chg涨跌幅指标的计算
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

from data_update.dailydata_func_tdx import (
    process_single_stock_file,
    DayStockFileProcessor,
    read_all_stocks_to_dataframe_parallel
)

def test_pct_chg_calculation_logic():
    """测试pct_chg计算逻辑"""
    print("=== 测试pct_chg计算逻辑 ===")
    
    # 创建测试数据
    test_data = {
        'close': [10.0, 10.5, 9.8, 11.2, 10.9],
        'pre_close': [10.0, 10.0, 10.5, 9.8, 11.2]  # 手动设置前收盘价
    }
    
    df = pd.DataFrame(test_data)
    
    # 计算pct_chg
    df['pct_chg'] = ((df['close'] - df['pre_close']) / df['pre_close'] * 100).round(4)
    
    print("测试数据:")
    print(df[['close', 'pre_close', 'pct_chg']])
    
    # 验证计算结果
    expected_pct_chg = [
        0.0,      # (10.0 - 10.0) / 10.0 * 100 = 0%
        5.0,      # (10.5 - 10.0) / 10.0 * 100 = 5%
        -6.6667,  # (9.8 - 10.5) / 10.5 * 100 = -6.6667%
        14.2857,  # (11.2 - 9.8) / 9.8 * 100 = 14.2857%
        -2.6786   # (10.9 - 11.2) / 11.2 * 100 = -2.6786%
    ]
    
    print(f"\n预期结果: {expected_pct_chg}")
    print(f"实际结果: {df['pct_chg'].tolist()}")
    
    # 检查计算精度
    for i, (actual, expected) in enumerate(zip(df['pct_chg'], expected_pct_chg)):
        diff = abs(actual - expected)
        if diff < 0.01:  # 允许0.01%的误差
            print(f"  第{i+1}行: ✓ {actual}% (预期: {expected}%)")
        else:
            print(f"  第{i+1}行: ❌ {actual}% (预期: {expected}%, 误差: {diff}%)")

def test_real_file_processing():
    """测试真实文件处理中的pct_chg计算"""
    print("\n=== 测试真实文件处理中的pct_chg计算 ===")
    
    # 获取一个真实的股票文件
    processor = DayStockFileProcessor("")
    stock_file_map, _ = processor.prepare_stock_files()
    
    if not stock_file_map:
        print("❌ 没有找到股票文件")
        return
    
    # 取第一个股票进行测试
    ts_code, file_path = list(stock_file_map.items())[0]
    print(f"测试文件: {ts_code} -> {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    print(f"✓ 文件存在")
    
    # 处理文件
    try:
        result = process_single_stock_file((ts_code, file_path, {}))
        data_type, df = result
        
        if df is not None and not df.empty:
            print(f"✓ 文件处理成功")
            print(f"  记录数: {len(df)}")
            print(f"  列名: {list(df.columns)}")
            
            # 检查pct_chg字段
            if 'pct_chg' in df.columns:
                print(f"✓ pct_chg字段存在")
                
                valid_pct_chg = df['pct_chg'].notna().sum()
                print(f"  有效pct_chg记录: {valid_pct_chg}/{len(df)}")
                
                if valid_pct_chg > 0:
                    # 显示统计信息
                    pct_chg_stats = df['pct_chg'].describe()
                    print(f"  pct_chg统计:")
                    print(f"    最小值: {pct_chg_stats['min']:.4f}%")
                    print(f"    最大值: {pct_chg_stats['max']:.4f}%")
                    print(f"    平均值: {pct_chg_stats['mean']:.4f}%")
                    print(f"    标准差: {pct_chg_stats['std']:.4f}%")
                    
                    # 显示前几条记录
                    print(f"\n  前5条记录:")
                    sample_cols = ['trade_date', 'close', 'pre_close', 'pct_chg']
                    available_cols = [col for col in sample_cols if col in df.columns]
                    print(df[available_cols].head().to_string(index=False))
                    
                    # 验证计算正确性
                    print(f"\n  计算验证（前3条记录）:")
                    for i in range(min(3, len(df))):
                        close = df.iloc[i]['close']
                        pre_close = df.iloc[i]['pre_close']
                        pct_chg = df.iloc[i]['pct_chg']
                        
                        if pd.notna(close) and pd.notna(pre_close) and pre_close != 0:
                            expected = ((close - pre_close) / pre_close * 100)
                            diff = abs(pct_chg - expected)
                            status = "✓" if diff < 0.01 else "❌"
                            print(f"    第{i+1}行: {status} 实际={pct_chg:.4f}%, 预期={expected:.4f}%, 误差={diff:.6f}%")
                        else:
                            print(f"    第{i+1}行: 跳过验证（数据异常）")
                
            else:
                print(f"❌ pct_chg字段缺失")
        else:
            print(f"❌ 文件处理失败")
            
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def test_batch_processing():
    """测试批量处理中的pct_chg"""
    print("\n=== 测试批量处理中的pct_chg ===")
    
    try:
        # 使用小批量测试
        stock_df, index_df = read_all_stocks_to_dataframe_parallel(
            max_workers=2, 
            fetch_stock_info=False  # 跳过股票信息获取，专注测试pct_chg
        )
        
        print(f"批量处理结果:")
        print(f"  股票数据: {len(stock_df)} 条记录")
        print(f"  指数数据: {len(index_df)} 条记录")
        
        if not stock_df.empty:
            # 检查pct_chg字段
            if 'pct_chg' in stock_df.columns:
                print(f"✓ 股票数据包含pct_chg字段")
                
                valid_pct_chg = stock_df['pct_chg'].notna().sum()
                total_records = len(stock_df)
                percentage = (valid_pct_chg / total_records * 100) if total_records > 0 else 0
                
                print(f"  有效pct_chg记录: {valid_pct_chg}/{total_records} ({percentage:.1f}%)")
                
                if valid_pct_chg > 0:
                    # 统计信息
                    pct_chg_stats = stock_df['pct_chg'].describe()
                    print(f"  整体pct_chg统计:")
                    print(f"    记录数: {pct_chg_stats['count']:.0f}")
                    print(f"    平均值: {pct_chg_stats['mean']:.4f}%")
                    print(f"    标准差: {pct_chg_stats['std']:.4f}%")
                    print(f"    最小值: {pct_chg_stats['min']:.4f}%")
                    print(f"    最大值: {pct_chg_stats['max']:.4f}%")
                    
                    # 检查异常值
                    extreme_high = stock_df[stock_df['pct_chg'] > 20]
                    extreme_low = stock_df[stock_df['pct_chg'] < -20]
                    
                    if not extreme_high.empty:
                        print(f"  ⚠️  发现{len(extreme_high)}条涨幅超过20%的记录")
                    if not extreme_low.empty:
                        print(f"  ⚠️  发现{len(extreme_low)}条跌幅超过20%的记录")
            else:
                print(f"❌ 股票数据缺少pct_chg字段")
        
        if not index_df.empty:
            # 检查指数数据的pct_chg
            if 'pct_chg' in index_df.columns:
                print(f"✓ 指数数据包含pct_chg字段")
                
                valid_pct_chg = index_df['pct_chg'].notna().sum()
                print(f"  指数有效pct_chg记录: {valid_pct_chg}/{len(index_df)}")
            else:
                print(f"❌ 指数数据缺少pct_chg字段")
        
    except Exception as e:
        print(f"❌ 批量处理测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试pct_chg涨跌幅指标计算...\n")
    
    # 测试1: 计算逻辑验证
    test_pct_chg_calculation_logic()
    
    # 测试2: 真实文件处理
    test_real_file_processing()
    
    # 测试3: 批量处理
    test_batch_processing()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print("✓ pct_chg计算公式: (close - pre_close) / pre_close * 100")
    print("✓ 结果保留4位小数")
    print("✓ 第一条记录的pct_chg设为0.0")
    print("✓ 支持股票和指数数据")
    print("=" * 50)
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
