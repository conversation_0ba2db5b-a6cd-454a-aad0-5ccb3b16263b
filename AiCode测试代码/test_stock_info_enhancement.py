#!/usr/bin/env python3
"""
测试股票基本信息获取和换手率计算功能
"""

import sys
import os

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

from data_update.dailydata_func_tdx import (
    get_stock_basic_info,
    get_stocks_basic_info_batch,
    process_single_stock_file,
    read_all_stocks_to_dataframe_parallel,
    AKSHARE_AVAILABLE
)

def test_akshare_availability():
    """测试akshare可用性"""
    print("=== 测试akshare可用性 ===")
    print(f"akshare可用: {AKSHARE_AVAILABLE}")
    
    if AKSHARE_AVAILABLE:
        print("✓ akshare已安装并可用")
    else:
        print("❌ akshare不可用，请安装: pip install akshare")
    
    return AKSHARE_AVAILABLE

def test_single_stock_info():
    """测试单只股票信息获取"""
    print("\n=== 测试单只股票信息获取 ===")
    
    if not AKSHARE_AVAILABLE:
        print("跳过测试：akshare不可用")
        return
    
    # 测试几只知名股票
    test_codes = ['000001.SZ', '000002.SZ', '600000.SH']
    
    for ts_code in test_codes:
        print(f"\n测试股票: {ts_code}")
        try:
            info = get_stock_basic_info(ts_code)
            if info:
                print(f"  总股本: {info.get('total_share', 'N/A')}")
                print(f"  流通股本: {info.get('float_share', 'N/A')}")
                print(f"  总市值: {info.get('total_mv', 'N/A')}")
                print(f"  流通市值: {info.get('circ_mv', 'N/A')}")
                print("  ✓ 获取成功")
            else:
                print("  ❌ 获取失败")
        except Exception as e:
            print(f"  ❌ 异常: {str(e)}")

def test_batch_stock_info():
    """测试批量股票信息获取"""
    print("\n=== 测试批量股票信息获取 ===")
    
    if not AKSHARE_AVAILABLE:
        print("跳过测试：akshare不可用")
        return {}
    
    # 测试少量股票
    test_codes = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
    
    print(f"批量获取 {len(test_codes)} 只股票信息...")
    info_map = get_stocks_basic_info_batch(test_codes, max_workers=2)
    
    print(f"成功获取 {len(info_map)} 只股票信息")
    
    for ts_code, info in info_map.items():
        print(f"\n{ts_code}:")
        print(f"  总股本: {info.get('total_share', 'N/A'):,}")
        print(f"  流通股本: {info.get('float_share', 'N/A'):,}")
        print(f"  总市值: {info.get('total_mv', 'N/A'):,}")
        print(f"  流通市值: {info.get('circ_mv', 'N/A'):,}")
    
    return info_map

def test_turnover_calculation():
    """测试换手率计算"""
    print("\n=== 测试换手率计算 ===")
    
    # 模拟股票基本信息
    mock_info_map = {
        '000001.SZ': {
            'total_share': 19405918198,  # 平安银行总股本
            'float_share': 19405918198,
            'total_mv': 300000000000,
            'circ_mv': 300000000000
        }
    }
    
    # 模拟文件路径（实际测试时需要真实文件）
    print("注意：此测试需要真实的.day文件")
    
    # 如果有真实文件，可以测试
    # result = process_single_stock_file(('000001.SZ', 'path/to/sz000001.day', mock_info_map))
    # if result[1] is not None:
    #     df = result[1]
    #     print(f"换手率计算结果:")
    #     print(df[['trade_date', 'vol', 'total_share', 'turnover_rate']].head())

def test_data_structure():
    """测试数据结构"""
    print("\n=== 测试数据结构 ===")
    
    try:
        # 测试小规模数据读取
        print("尝试读取少量股票数据...")
        stock_df, index_df = read_all_stocks_to_dataframe_parallel(
            max_workers=2, 
            fetch_stock_info=AKSHARE_AVAILABLE
        )
        
        if not stock_df.empty:
            print(f"\n股票数据结构:")
            print(f"  记录数: {len(stock_df)}")
            print(f"  列数: {len(stock_df.columns)}")
            print(f"  列名: {list(stock_df.columns)}")
            
            # 检查新增字段
            new_fields = ['total_share', 'float_share', 'total_mv', 'circ_mv', 'turnover_rate']
            for field in new_fields:
                if field in stock_df.columns:
                    valid_count = stock_df[field].notna().sum()
                    print(f"  {field}: {valid_count}/{len(stock_df)} 有效记录")
                else:
                    print(f"  {field}: 字段缺失")
            
            # 显示示例数据
            print(f"\n股票数据示例:")
            display_columns = ['ts_code', 'trade_date', 'close', 'vol', 'total_share', 'turnover_rate']
            available_columns = [col for col in display_columns if col in stock_df.columns]
            print(stock_df[available_columns].head(3))
        
        if not index_df.empty:
            print(f"\n指数数据结构:")
            print(f"  记录数: {len(index_df)}")
            print(f"  列数: {len(index_df.columns)}")
            print(f"  列名: {list(index_df.columns)}")
            
            print(f"\n指数数据示例:")
            display_columns = ['ts_code', 'trade_date', 'close', 'vol']
            available_columns = [col for col in display_columns if col in index_df.columns]
            print(index_df[available_columns].head(3))
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")

def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 测试性能对比 ===")
    
    import time
    
    print("对比获取股票信息 vs 不获取股票信息的性能差异")
    
    try:
        # 测试不获取股票信息
        print("\n1. 不获取股票信息模式:")
        start_time = time.time()
        stock_df1, index_df1 = read_all_stocks_to_dataframe_parallel(
            max_workers=4, 
            fetch_stock_info=False
        )
        time1 = time.time() - start_time
        print(f"   耗时: {time1:.2f}秒")
        
        if AKSHARE_AVAILABLE:
            # 测试获取股票信息
            print("\n2. 获取股票信息模式:")
            start_time = time.time()
            stock_df2, index_df2 = read_all_stocks_to_dataframe_parallel(
                max_workers=4, 
                fetch_stock_info=True
            )
            time2 = time.time() - start_time
            print(f"   耗时: {time2:.2f}秒")
            
            print(f"\n性能对比:")
            print(f"   额外耗时: {time2 - time1:.2f}秒")
            print(f"   性能影响: {((time2 - time1) / time1 * 100):.1f}%")
        else:
            print("\n2. 跳过获取股票信息模式测试（akshare不可用）")
        
    except Exception as e:
        print(f"性能测试过程中发生错误: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试股票基本信息获取和换手率计算功能...\n")
    
    # 测试1: akshare可用性
    if not test_akshare_availability():
        print("\n由于akshare不可用，部分测试将被跳过")
    
    # 测试2: 单只股票信息获取
    test_single_stock_info()
    
    # 测试3: 批量股票信息获取
    test_batch_stock_info()
    
    # 测试4: 换手率计算
    test_turnover_calculation()
    
    # 测试5: 数据结构
    test_data_structure()
    
    # 测试6: 性能对比
    test_performance_comparison()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print("✓ akshare集成功能测试完成")
    print("✓ 股票基本信息获取功能测试完成")
    print("✓ 换手率计算功能测试完成")
    print("✓ 数据结构扩展测试完成")
    print("=" * 50)
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
