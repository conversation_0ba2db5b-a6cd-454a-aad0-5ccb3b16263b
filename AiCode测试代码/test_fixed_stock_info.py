#!/usr/bin/env python3
"""
测试修复后的股票基本信息获取和换手率计算功能
"""

import sys
import os

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

from data_update.dailydata_func_tdx import (
    read_all_stocks_to_dataframe_parallel,
    AKSHARE_AVAILABLE
)

def test_small_batch():
    """测试小批量处理"""
    print("=== 测试小批量处理（包含股票基本信息）===")
    
    if not AKSHARE_AVAILABLE:
        print("akshare不可用，跳过测试")
        return
    
    try:
        # 使用小批量测试
        stock_df, index_df = read_all_stocks_to_dataframe_parallel(
            max_workers=2, 
            fetch_stock_info=True
        )
        
        print(f"处理结果:")
        print(f"  股票数据: {len(stock_df)} 条记录")
        print(f"  指数数据: {len(index_df)} 条记录")
        
        if not stock_df.empty:
            print(f"\n股票数据字段:")
            print(f"  总列数: {len(stock_df.columns)}")
            print(f"  列名: {list(stock_df.columns)}")
            
            # 检查关键字段
            key_fields = ['total_share', 'float_share', 'total_mv', 'circ_mv', 'turnover_rate']
            print(f"\n关键字段统计:")
            for field in key_fields:
                if field in stock_df.columns:
                    valid_count = stock_df[field].notna().sum()
                    total_count = len(stock_df)
                    percentage = (valid_count / total_count * 100) if total_count > 0 else 0
                    print(f"  {field}: {valid_count}/{total_count} ({percentage:.1f}%) 有效")
                else:
                    print(f"  {field}: 字段缺失")
            
            # 显示有换手率数据的记录
            if 'turnover_rate' in stock_df.columns:
                valid_turnover = stock_df[stock_df['turnover_rate'].notna()]
                if not valid_turnover.empty:
                    print(f"\n换手率数据示例:")
                    sample = valid_turnover[['ts_code', 'vol', 'total_share', 'turnover_rate']].head(3)
                    print(sample.to_string(index=False))
                else:
                    print(f"\n❌ 没有有效的换手率数据")
            
            # 显示完整记录示例
            print(f"\n完整记录示例:")
            print(stock_df.iloc[0].to_dict())
        
        if not index_df.empty:
            print(f"\n指数数据字段:")
            print(f"  总列数: {len(index_df.columns)}")
            print(f"  列名: {list(index_df.columns)}")
            
            # 检查指数数据是否正确（应该没有股票基本信息字段的有效值）
            key_fields = ['total_share', 'float_share', 'total_mv', 'circ_mv', 'turnover_rate']
            print(f"\n指数数据字段检查（应该都为空）:")
            for field in key_fields:
                if field in index_df.columns:
                    valid_count = index_df[field].notna().sum()
                    print(f"  {field}: {valid_count} 有效值 {'✓' if valid_count == 0 else '❌'}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_data_quality():
    """测试数据质量"""
    print("\n=== 测试数据质量 ===")
    
    if not AKSHARE_AVAILABLE:
        print("akshare不可用，跳过测试")
        return
    
    try:
        # 获取少量数据进行质量检查
        stock_df, _ = read_all_stocks_to_dataframe_parallel(
            max_workers=2, 
            fetch_stock_info=True
        )
        
        if stock_df.empty:
            print("没有股票数据")
            return
        
        print(f"数据质量检查 ({len(stock_df)} 条记录):")
        
        # 检查换手率计算的合理性
        if 'turnover_rate' in stock_df.columns:
            valid_turnover = stock_df[stock_df['turnover_rate'].notna()]
            if not valid_turnover.empty:
                min_rate = valid_turnover['turnover_rate'].min()
                max_rate = valid_turnover['turnover_rate'].max()
                avg_rate = valid_turnover['turnover_rate'].mean()
                
                print(f"  换手率范围: {min_rate:.4f}% - {max_rate:.4f}%")
                print(f"  平均换手率: {avg_rate:.4f}%")
                
                # 检查是否有异常值
                if max_rate > 100:
                    print(f"  ⚠️  发现异常高换手率: {max_rate:.4f}%")
                if min_rate < 0:
                    print(f"  ⚠️  发现负换手率: {min_rate:.4f}%")
        
        # 检查股本数据的合理性
        if 'total_share' in stock_df.columns:
            valid_share = stock_df[stock_df['total_share'].notna()]
            if not valid_share.empty:
                min_share = valid_share['total_share'].min()
                max_share = valid_share['total_share'].max()
                
                print(f"  总股本范围: {min_share:,.0f} - {max_share:,.0f}")
                
                # 检查是否有异常值
                if min_share <= 0:
                    print(f"  ⚠️  发现异常股本: {min_share}")
        
        # 检查市值数据
        if 'total_mv' in stock_df.columns:
            valid_mv = stock_df[stock_df['total_mv'].notna()]
            if not valid_mv.empty:
                min_mv = valid_mv['total_mv'].min()
                max_mv = valid_mv['total_mv'].max()
                
                print(f"  总市值范围: {min_mv:,.0f} - {max_mv:,.0f}")
        
    except Exception as e:
        print(f"❌ 数据质量检查失败: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试修复后的股票基本信息功能...\n")
    
    # 测试1: 小批量处理
    test_small_batch()
    
    # 测试2: 数据质量检查
    test_data_quality()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print("✓ akshare DataFrame格式处理已修复")
    print("✓ 股票基本信息获取正常")
    print("✓ 换手率计算正常")
    print("✓ 数据拼接成功")
    print("=" * 50)
    
    print("\n测试完成！现在可以正常存储股票基本信息和计算换手率了。")

if __name__ == '__main__':
    main()
