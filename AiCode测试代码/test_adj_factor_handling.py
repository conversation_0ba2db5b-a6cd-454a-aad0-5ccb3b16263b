#!/usr/bin/env python3
"""
测试adj_factor为空时的处理逻辑
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

from function_ai.Func_Base import get_stock_data

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range('2023-01-01', periods=10, freq='D')
    
    # 创建基础数据
    base_data = {
        'ts_code': ['000001.SZ'] * 10,
        'trade_date': dates.strftime('%Y-%m-%d'),
        'open': [10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9],
        'high': [10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1],
        'low': [9.8, 9.9, 10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7],
        'close': [10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0],
        'pre_close': [10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9],
        'vol': [1000000] * 10,
        'amount': [10000000] * 10
    }
    
    return pd.DataFrame(base_data)

def test_with_valid_adj_factor():
    """测试有效adj_factor的情况"""
    print("=== 测试有效adj_factor情况 ===")
    
    df = create_test_data()
    # 添加有效的adj_factor
    df['adj_factor'] = [1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9]
    
    print("原始数据:")
    print(df[['trade_date', 'close', 'adj_factor']].head())
    
    # 调用get_stock_data函数
    result = get_stock_data(df)
    
    if isinstance(result, list) and len(result) > 0:
        result_df = pd.DataFrame(result)
        print("\n处理后数据:")
        print(result_df[['trade_date', 'close', 'adj_factor']].head())
        
        # 检查是否进行了复权调整
        original_close = df['close'].iloc[0]
        adjusted_close = result_df['close'].iloc[0]
        print(f"\n复权调整检查:")
        print(f"原始收盘价: {original_close}")
        print(f"调整后收盘价: {adjusted_close}")
        print(f"是否进行了调整: {'是' if abs(original_close - adjusted_close) > 0.001 else '否'}")
    else:
        print("函数返回空结果")

def test_with_null_adj_factor():
    """测试adj_factor为空的情况"""
    print("\n=== 测试adj_factor为空情况 ===")
    
    df = create_test_data()
    # adj_factor全为空
    df['adj_factor'] = None
    
    print("原始数据:")
    print(df[['trade_date', 'close', 'adj_factor']].head())
    
    # 调用get_stock_data函数
    result = get_stock_data(df)
    
    if isinstance(result, list) and len(result) > 0:
        result_df = pd.DataFrame(result)
        print("\n处理后数据:")
        print(result_df[['trade_date', 'close', 'adj_factor']].head())
        
        # 检查是否跳过了复权调整
        original_close = df['close'].iloc[0]
        adjusted_close = result_df['close'].iloc[0]
        print(f"\n复权调整检查:")
        print(f"原始收盘价: {original_close}")
        print(f"调整后收盘价: {adjusted_close}")
        print(f"是否跳过调整: {'是' if abs(original_close - adjusted_close) < 0.001 else '否'}")
    else:
        print("函数返回空结果")

def test_without_adj_factor_column():
    """测试没有adj_factor列的情况"""
    print("\n=== 测试没有adj_factor列情况 ===")
    
    df = create_test_data()
    # 不添加adj_factor列
    
    print("原始数据:")
    print(df[['trade_date', 'close']].head())
    print(f"列名: {list(df.columns)}")
    
    # 调用get_stock_data函数
    result = get_stock_data(df)
    
    if isinstance(result, list) and len(result) > 0:
        result_df = pd.DataFrame(result)
        print("\n处理后数据:")
        print(result_df[['trade_date', 'close']].head())
        
        # 检查是否跳过了复权调整
        original_close = df['close'].iloc[0]
        adjusted_close = result_df['close'].iloc[0]
        print(f"\n复权调整检查:")
        print(f"原始收盘价: {original_close}")
        print(f"调整后收盘价: {adjusted_close}")
        print(f"是否跳过调整: {'是' if abs(original_close - adjusted_close) < 0.001 else '否'}")
    else:
        print("函数返回空结果")

def test_mixed_adj_factor():
    """测试部分adj_factor为空的情况"""
    print("\n=== 测试部分adj_factor为空情况 ===")
    
    df = create_test_data()
    # 部分adj_factor为空
    df['adj_factor'] = [1.0, None, 1.2, None, 1.4, 1.5, None, 1.7, 1.8, None]
    
    print("原始数据:")
    print(df[['trade_date', 'close', 'adj_factor']].head())
    
    # 调用get_stock_data函数
    result = get_stock_data(df)
    
    if isinstance(result, list) and len(result) > 0:
        result_df = pd.DataFrame(result)
        print("\n处理后数据:")
        print(result_df[['trade_date', 'close', 'adj_factor']].head())
        
        # 检查哪些记录进行了调整
        print(f"\n复权调整检查:")
        for i in range(min(5, len(df))):
            original_close = df['close'].iloc[i]
            adjusted_close = result_df['close'].iloc[i]
            adj_factor = df['adj_factor'].iloc[i]
            is_adjusted = abs(original_close - adjusted_close) > 0.001
            print(f"第{i+1}行: adj_factor={adj_factor}, 原始={original_close}, 调整后={adjusted_close:.3f}, 是否调整={'是' if is_adjusted else '否'}")
    else:
        print("函数返回空结果")

def test_zero_adj_factor():
    """测试adj_factor为0的情况"""
    print("\n=== 测试adj_factor为0情况 ===")
    
    df = create_test_data()
    # adj_factor为0
    df['adj_factor'] = [0.0, 1.1, 0.0, 1.3, 1.4, 0.0, 1.6, 1.7, 0.0, 1.9]
    
    print("原始数据:")
    print(df[['trade_date', 'close', 'adj_factor']].head())
    
    # 调用get_stock_data函数
    result = get_stock_data(df)
    
    if isinstance(result, list) and len(result) > 0:
        result_df = pd.DataFrame(result)
        print("\n处理后数据:")
        print(result_df[['trade_date', 'close', 'adj_factor']].head())
        
        # 检查哪些记录进行了调整
        print(f"\n复权调整检查:")
        for i in range(min(5, len(df))):
            original_close = df['close'].iloc[i]
            adjusted_close = result_df['close'].iloc[i]
            adj_factor = df['adj_factor'].iloc[i]
            is_adjusted = abs(original_close - adjusted_close) > 0.001
            print(f"第{i+1}行: adj_factor={adj_factor}, 原始={original_close}, 调整后={adjusted_close:.3f}, 是否调整={'是' if is_adjusted else '否'}")
    else:
        print("函数返回空结果")

def main():
    """主测试函数"""
    print("开始测试adj_factor处理逻辑...\n")
    
    # 测试1: 有效adj_factor
    test_with_valid_adj_factor()
    
    # 测试2: adj_factor为空
    test_with_null_adj_factor()
    
    # 测试3: 没有adj_factor列
    test_without_adj_factor_column()
    
    # 测试4: 部分adj_factor为空
    test_mixed_adj_factor()
    
    # 测试5: adj_factor为0
    test_zero_adj_factor()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print("✓ 有效adj_factor时进行复权调整")
    print("✓ adj_factor为空时跳过复权调整")
    print("✓ 没有adj_factor列时跳过复权调整")
    print("✓ 部分adj_factor为空时只调整有效记录")
    print("✓ adj_factor为0时跳过复权调整")
    print("=" * 50)
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
