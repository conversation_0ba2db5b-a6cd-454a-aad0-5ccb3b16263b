#!/usr/bin/env python3
"""
测试新的pre_close计算逻辑：获取2天数据，只存储最新一天
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

from data_update.dailydata_func_tdx import (
    read_day_data,
    process_single_stock_file,
    DayStockFileProcessor
)

def test_read_day_data_with_days():
    """测试read_day_data函数的days参数"""
    print("=== 测试read_day_data函数的days参数 ===")
    
    # 获取一个真实的股票文件
    processor = DayStockFileProcessor("")
    stock_file_map, _ = processor.prepare_stock_files()
    
    if not stock_file_map:
        print("❌ 没有找到股票文件")
        return
    
    # 取第一个股票进行测试
    ts_code, file_path = list(stock_file_map.items())[0]
    print(f"测试文件: {ts_code} -> {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        # 测试读取所有数据
        df_all = read_day_data(file_path)
        print(f"读取所有数据: {len(df_all)} 条记录")
        if not df_all.empty:
            print(f"  日期范围: {df_all['date'].min()} 到 {df_all['date'].max()}")
        
        # 测试读取最近2天数据
        df_2days = read_day_data(file_path, days=2)
        print(f"读取最近2天数据: {len(df_2days)} 条记录")
        if not df_2days.empty:
            print(f"  日期范围: {df_2days['date'].min()} 到 {df_2days['date'].max()}")
            print(f"  数据详情:")
            for i, row in df_2days.iterrows():
                print(f"    {row['date'].strftime('%Y-%m-%d')}: close={row['close']}")
        
        # 测试读取最近1天数据
        df_1day = read_day_data(file_path, days=1)
        print(f"读取最近1天数据: {len(df_1day)} 条记录")
        if not df_1day.empty:
            print(f"  日期: {df_1day['date'].iloc[0].strftime('%Y-%m-%d')}")
            print(f"  收盘价: {df_1day['close'].iloc[0]}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_preclose_calculation():
    """测试新的pre_close计算逻辑"""
    print("\n=== 测试新的pre_close计算逻辑 ===")
    
    # 获取一个真实的股票文件
    processor = DayStockFileProcessor("")
    stock_file_map, _ = processor.prepare_stock_files()
    
    if not stock_file_map:
        print("❌ 没有找到股票文件")
        return
    
    # 取第一个股票进行测试
    ts_code, file_path = list(stock_file_map.items())[0]
    print(f"测试文件: {ts_code}")
    
    try:
        # 先读取最近2天的原始数据
        df_raw = read_day_data(file_path, days=2)
        if df_raw.empty:
            print("❌ 没有读取到数据")
            return
        
        print(f"原始数据 ({len(df_raw)} 天):")
        for i, row in df_raw.iterrows():
            print(f"  {row['date'].strftime('%Y-%m-%d')}: close={row['close']}")
        
        # 使用新的处理逻辑
        result = process_single_stock_file((ts_code, file_path, {}))
        data_type, df_processed = result
        
        if df_processed is not None and not df_processed.empty:
            print(f"\n处理后数据 ({len(df_processed)} 天):")
            for i, row in df_processed.iterrows():
                print(f"  {row['trade_date']}: close={row['close']}, pre_close={row['pre_close']}, pct_chg={row['pct_chg']}%")
            
            # 验证pre_close的正确性
            if len(df_raw) >= 2:
                expected_pre_close = df_raw.iloc[-2]['close']  # 倒数第二天的收盘价
                actual_pre_close = df_processed.iloc[0]['pre_close']
                
                print(f"\npre_close验证:")
                print(f"  预期pre_close (前一天收盘价): {expected_pre_close}")
                print(f"  实际pre_close: {actual_pre_close}")
                
                if abs(expected_pre_close - actual_pre_close) < 0.01:
                    print(f"  ✓ pre_close计算正确")
                else:
                    print(f"  ❌ pre_close计算错误，差异: {abs(expected_pre_close - actual_pre_close)}")
                
                # 验证pct_chg计算
                expected_pct_chg = ((df_processed.iloc[0]['close'] - expected_pre_close) / expected_pre_close * 100)
                actual_pct_chg = df_processed.iloc[0]['pct_chg']
                
                print(f"\npct_chg验证:")
                print(f"  预期pct_chg: {expected_pct_chg:.4f}%")
                print(f"  实际pct_chg: {actual_pct_chg}%")
                
                if abs(expected_pct_chg - actual_pct_chg) < 0.01:
                    print(f"  ✓ pct_chg计算正确")
                else:
                    print(f"  ❌ pct_chg计算错误，差异: {abs(expected_pct_chg - actual_pct_chg)}")
            else:
                print(f"\n只有1天数据，pre_close应该使用开盘价")
                expected_pre_close = df_processed.iloc[0]['open'] if 'open' in df_processed.columns else None
                actual_pre_close = df_processed.iloc[0]['pre_close']
                print(f"  开盘价: {expected_pre_close}")
                print(f"  pre_close: {actual_pre_close}")
        else:
            print("❌ 处理失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_multiple_stocks():
    """测试多只股票的处理"""
    print("\n=== 测试多只股票的处理 ===")
    
    processor = DayStockFileProcessor("")
    stock_file_map, _ = processor.prepare_stock_files()
    
    if not stock_file_map:
        print("❌ 没有找到股票文件")
        return
    
    # 测试前5只股票
    test_stocks = list(stock_file_map.items())[:5]
    
    for ts_code, file_path in test_stocks:
        print(f"\n测试股票: {ts_code}")
        
        try:
            # 检查原始数据
            df_raw = read_day_data(file_path, days=2)
            print(f"  原始数据: {len(df_raw)} 天")
            
            # 处理数据
            result = process_single_stock_file((ts_code, file_path, {}))
            data_type, df_processed = result
            
            if df_processed is not None and not df_processed.empty:
                print(f"  处理后: {len(df_processed)} 天")
                print(f"  最新日期: {df_processed.iloc[0]['trade_date']}")
                print(f"  pre_close: {df_processed.iloc[0]['pre_close']}")
                print(f"  pct_chg: {df_processed.iloc[0]['pct_chg']}%")
                print(f"  ✓ 处理成功")
            else:
                print(f"  ❌ 处理失败")
                
        except Exception as e:
            print(f"  ❌ 处理异常: {str(e)}")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 创建模拟数据来测试边界情况
    print("测试只有1天数据的情况:")
    
    # 模拟只有1天数据的DataFrame
    test_data = pd.DataFrame([{
        'date': datetime(2025, 8, 15),
        'open': 10.0,
        'high': 10.5,
        'low': 9.8,
        'close': 10.2,
        'vol': 1000000,
        'amount': 10200000
    }])
    
    # 模拟处理逻辑
    test_data['ts_code'] = 'TEST.SZ'
    test_data['trade_date'] = test_data['date'].dt.strftime('%Y-%m-%d')
    test_data = test_data.sort_values('date')
    test_data['pre_close'] = test_data['close'].shift(1)
    
    # 如果只有一天数据，pre_close使用开盘价
    if len(test_data) == 1:
        test_data.iloc[0, test_data.columns.get_loc('pre_close')] = test_data.iloc[0]['open']
    
    test_data['pct_chg'] = ((test_data['close'] - test_data['pre_close']) / test_data['pre_close'] * 100).round(4)
    
    print(f"  日期: {test_data.iloc[0]['trade_date']}")
    print(f"  开盘价: {test_data.iloc[0]['open']}")
    print(f"  收盘价: {test_data.iloc[0]['close']}")
    print(f"  pre_close: {test_data.iloc[0]['pre_close']}")
    print(f"  pct_chg: {test_data.iloc[0]['pct_chg']}%")
    
    expected_pct_chg = ((10.2 - 10.0) / 10.0 * 100)
    print(f"  预期pct_chg: {expected_pct_chg:.4f}%")
    
    if abs(test_data.iloc[0]['pct_chg'] - expected_pct_chg) < 0.01:
        print(f"  ✓ 单天数据处理正确")
    else:
        print(f"  ❌ 单天数据处理错误")

def main():
    """主测试函数"""
    print("开始测试新的pre_close计算逻辑...\n")
    
    # 测试1: read_day_data函数的days参数
    test_read_day_data_with_days()
    
    # 测试2: pre_close计算逻辑
    test_preclose_calculation()
    
    # 测试3: 多只股票处理
    test_multiple_stocks()
    
    # 测试4: 边界情况
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print("✓ 读取最近2天数据计算准确的pre_close")
    print("✓ 只存储最新1天数据到数据库")
    print("✓ pre_close使用前一天的真实收盘价")
    print("✓ 单天数据时pre_close使用开盘价")
    print("✓ pct_chg基于准确的pre_close计算")
    print("=" * 50)
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
