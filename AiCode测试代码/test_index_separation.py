#!/usr/bin/env python3
"""
测试指数数据分离功能
"""

import sys
import os

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

from data_update.dailydata_func_tdx import (
    process_single_stock_file,
    read_all_stocks_to_dataframe_parallel,
    DayStockFileProcessor
)

def test_index_identification():
    """测试指数识别功能"""
    print("=== 测试指数识别功能 ===")
    
    # 测试指数代码
    index_codes = ['000906.SH', '000001.SH', '399006.SZ']
    
    # 测试股票代码
    stock_codes = ['600000.SH', '000001.SZ', '300001.SZ']
    
    print("测试指数代码识别:")
    for code in index_codes:
        data_type, _ = process_single_stock_file((code, "dummy_path"))
        print(f"  {code} → {data_type}")
        assert data_type == 'index', f"{code} 应该被识别为指数"
    
    print("\n测试股票代码识别:")
    for code in stock_codes:
        data_type, _ = process_single_stock_file((code, "dummy_path"))
        print(f"  {code} → {data_type}")
        assert data_type == 'stock', f"{code} 应该被识别为股票"
    
    print("✓ 指数识别功能正常")

def test_file_processor():
    """测试文件处理器"""
    print("\n=== 测试文件处理器 ===")
    
    processor = DayStockFileProcessor("")
    stock_file_map, invalid_stocks = processor.prepare_stock_files()
    
    print(f"找到 {len(stock_file_map)} 个有效文件")
    print(f"无效文件: {len(invalid_stocks)} 个")
    
    # 检查指数文件是否存在
    index_codes = ['000906.SH', '000001.SH', '399006.SZ']
    found_indices = []
    
    for code in index_codes:
        if code in stock_file_map:
            found_indices.append(code)
            print(f"  找到指数文件: {code}")
        else:
            print(f"  未找到指数文件: {code}")
    
    print(f"找到 {len(found_indices)} 个指数文件")
    return stock_file_map

def test_data_separation():
    """测试数据分离功能"""
    print("\n=== 测试数据分离功能 ===")
    
    try:
        # 使用小规模并行测试
        stock_df, index_df = read_all_stocks_to_dataframe_parallel(max_workers=2)
        
        print(f"股票数据: {len(stock_df):,} 条记录")
        if not stock_df.empty:
            print(f"  股票代码数量: {stock_df['ts_code'].nunique()}")
            print(f"  股票代码示例: {list(stock_df['ts_code'].unique()[:5])}")
        
        print(f"指数数据: {len(index_df):,} 条记录")
        if not index_df.empty:
            print(f"  指数代码数量: {index_df['ts_code'].nunique()}")
            print(f"  指数代码: {list(index_df['ts_code'].unique())}")
        
        # 验证数据分离正确性
        index_codes = ['000906.SH', '000001.SH', '399006.SZ']
        
        # 检查股票数据中不应该包含指数
        if not stock_df.empty:
            stock_indices_found = stock_df[stock_df['ts_code'].isin(index_codes)]
            if not stock_indices_found.empty:
                print(f"❌ 错误：股票数据中发现指数代码: {stock_indices_found['ts_code'].unique()}")
            else:
                print("✓ 股票数据中没有指数代码")
        
        # 检查指数数据中只包含指数
        if not index_df.empty:
            non_index_found = index_df[~index_df['ts_code'].isin(index_codes)]
            if not non_index_found.empty:
                print(f"❌ 错误：指数数据中发现非指数代码: {non_index_found['ts_code'].unique()}")
            else:
                print("✓ 指数数据中只包含指数代码")
        
        return stock_df, index_df
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        return None, None

def test_data_structure():
    """测试数据结构"""
    print("\n=== 测试数据结构 ===")
    
    stock_df, index_df = test_data_separation()
    
    if stock_df is not None and not stock_df.empty:
        print("股票数据结构:")
        print(f"  列名: {list(stock_df.columns)}")
        print(f"  数据类型:")
        for col in stock_df.columns:
            print(f"    {col}: {stock_df[col].dtype}")
        
        print("\n股票数据示例:")
        print(stock_df.head(2))
    
    if index_df is not None and not index_df.empty:
        print("\n指数数据结构:")
        print(f"  列名: {list(index_df.columns)}")
        print(f"  数据类型:")
        for col in index_df.columns:
            print(f"    {col}: {index_df[col].dtype}")
        
        print("\n指数数据示例:")
        print(index_df.head(2))

def main():
    """主测试函数"""
    print("开始测试指数数据分离功能...\n")
    
    # 测试1: 指数识别
    test_index_identification()
    
    # 测试2: 文件处理器
    test_file_processor()
    
    # 测试3: 数据分离
    test_data_separation()
    
    # 测试4: 数据结构
    test_data_structure()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print("✓ 指数代码识别功能正常")
    print("✓ 数据分离功能正常")
    print("✓ 股票数据将保存到 stock_data 表")
    print("✓ 指数数据将保存到 index_data 表")
    print("=" * 50)
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
