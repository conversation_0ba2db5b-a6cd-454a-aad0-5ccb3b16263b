#!/usr/bin/env python3
"""
测试2天数据场景的pre_close计算
"""

import sys
import os
import pandas as pd
from datetime import datetime
import tempfile
import struct

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

from data_update.dailydata_func_tdx import (
    read_day_data,
    process_single_stock_file
)

def create_mock_day_file(data_list):
    """
    创建模拟的.day文件
    Args:
        data_list: 包含日线数据的列表，每个元素为(date, open, high, low, close, amount, volume)
    Returns:
        str: 临时文件路径
    """
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.day', delete=False)
    
    with open(temp_file.name, 'wb') as f:
        for date_int, open_price, high_price, low_price, close_price, amount, volume in data_list:
            # 价格需要乘以100存储（通达信格式）
            open_int = int(open_price * 100)
            high_int = int(high_price * 100)
            low_int = int(low_price * 100)
            close_int = int(close_price * 100)
            
            # 打包数据：IIIIIfII格式
            record = struct.pack('<IIIIIfII', 
                               date_int,      # 日期
                               open_int,      # 开盘价
                               high_int,      # 最高价
                               low_int,       # 最低价
                               close_int,     # 收盘价
                               amount,        # 成交额
                               volume,        # 成交量
                               0)             # 保留字段
            f.write(record)
    
    return temp_file.name

def test_two_day_scenario():
    """测试2天数据场景"""
    print("=== 测试2天数据场景 ===")
    
    # 创建2天的模拟数据
    # 格式: (日期YYYYMMDD, 开盘, 最高, 最低, 收盘, 成交额, 成交量)
    mock_data = [
        (20250814, 10.00, 10.50, 9.80, 10.20, 102000000, 10000000),  # 第一天
        (20250815, 10.20, 10.80, 10.00, 10.60, 106000000, 10200000)   # 第二天
    ]
    
    # 创建临时文件
    temp_file = create_mock_day_file(mock_data)
    
    try:
        print(f"创建临时文件: {temp_file}")
        
        # 测试读取所有数据
        df_all = read_day_data(temp_file)
        print(f"\n读取所有数据: {len(df_all)} 条记录")
        for i, row in df_all.iterrows():
            print(f"  {row['date'].strftime('%Y-%m-%d')}: open={row['open']}, close={row['close']}")
        
        # 测试读取最近2天数据
        df_2days = read_day_data(temp_file, days=2)
        print(f"\n读取最近2天数据: {len(df_2days)} 条记录")
        for i, row in df_2days.iterrows():
            print(f"  {row['date'].strftime('%Y-%m-%d')}: open={row['open']}, close={row['close']}")
        
        # 测试读取最近1天数据
        df_1day = read_day_data(temp_file, days=1)
        print(f"\n读取最近1天数据: {len(df_1day)} 条记录")
        for i, row in df_1day.iterrows():
            print(f"  {row['date'].strftime('%Y-%m-%d')}: open={row['open']}, close={row['close']}")
        
        # 测试完整的处理流程
        print(f"\n=== 测试完整处理流程 ===")
        result = process_single_stock_file(('TEST.SZ', temp_file, {}))
        data_type, df_processed = result
        
        if df_processed is not None and not df_processed.empty:
            print(f"处理结果:")
            for i, row in df_processed.iterrows():
                print(f"  日期: {row['trade_date']}")
                print(f"  开盘价: {row['open']}")
                print(f"  收盘价: {row['close']}")
                print(f"  前收盘价: {row['pre_close']}")
                print(f"  涨跌幅: {row['pct_chg']}%")
            
            # 验证pre_close是否正确
            # 应该是第一天的收盘价10.20
            expected_pre_close = 10.20
            actual_pre_close = df_processed.iloc[0]['pre_close']
            
            print(f"\npre_close验证:")
            print(f"  预期pre_close (第一天收盘价): {expected_pre_close}")
            print(f"  实际pre_close: {actual_pre_close}")
            
            if abs(expected_pre_close - actual_pre_close) < 0.01:
                print(f"  ✓ pre_close计算正确")
            else:
                print(f"  ❌ pre_close计算错误，差异: {abs(expected_pre_close - actual_pre_close)}")
            
            # 验证pct_chg计算
            # (10.60 - 10.20) / 10.20 * 100 = 3.9216%
            expected_pct_chg = ((10.60 - 10.20) / 10.20 * 100)
            actual_pct_chg = df_processed.iloc[0]['pct_chg']
            
            print(f"\npct_chg验证:")
            print(f"  预期pct_chg: {expected_pct_chg:.4f}%")
            print(f"  实际pct_chg: {actual_pct_chg}%")
            
            if abs(expected_pct_chg - actual_pct_chg) < 0.01:
                print(f"  ✓ pct_chg计算正确")
            else:
                print(f"  ❌ pct_chg计算错误，差异: {abs(expected_pct_chg - actual_pct_chg)}")
            
            # 验证只返回最新一天数据
            if len(df_processed) == 1:
                print(f"  ✓ 只返回最新一天数据")
            else:
                print(f"  ❌ 返回了{len(df_processed)}天数据，应该只返回1天")
        else:
            print("❌ 处理失败")
    
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)
            print(f"\n清理临时文件: {temp_file}")

def test_three_day_scenario():
    """测试3天数据场景，验证只取最近2天"""
    print("\n=== 测试3天数据场景 ===")
    
    # 创建3天的模拟数据
    mock_data = [
        (20250813, 9.50, 10.00, 9.30, 9.80, 98000000, 9800000),   # 第一天
        (20250814, 9.80, 10.50, 9.60, 10.20, 102000000, 10000000), # 第二天
        (20250815, 10.20, 10.80, 10.00, 10.60, 106000000, 10200000) # 第三天
    ]
    
    temp_file = create_mock_day_file(mock_data)
    
    try:
        print(f"创建3天数据的临时文件")
        
        # 读取所有数据
        df_all = read_day_data(temp_file)
        print(f"所有数据: {len(df_all)} 条记录")
        for i, row in df_all.iterrows():
            print(f"  {row['date'].strftime('%Y-%m-%d')}: close={row['close']}")
        
        # 读取最近2天数据
        df_2days = read_day_data(temp_file, days=2)
        print(f"\n最近2天数据: {len(df_2days)} 条记录")
        for i, row in df_2days.iterrows():
            print(f"  {row['date'].strftime('%Y-%m-%d')}: close={row['close']}")
        
        # 处理数据
        result = process_single_stock_file(('TEST.SZ', temp_file, {}))
        data_type, df_processed = result
        
        if df_processed is not None and not df_processed.empty:
            print(f"\n处理结果:")
            print(f"  日期: {df_processed.iloc[0]['trade_date']}")
            print(f"  收盘价: {df_processed.iloc[0]['close']}")
            print(f"  前收盘价: {df_processed.iloc[0]['pre_close']}")
            print(f"  涨跌幅: {df_processed.iloc[0]['pct_chg']}%")
            
            # 验证pre_close应该是第二天的收盘价10.20
            expected_pre_close = 10.20
            actual_pre_close = df_processed.iloc[0]['pre_close']
            
            if abs(expected_pre_close - actual_pre_close) < 0.01:
                print(f"  ✓ pre_close正确使用了前一天(第二天)的收盘价")
            else:
                print(f"  ❌ pre_close错误，预期{expected_pre_close}，实际{actual_pre_close}")
    
    finally:
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def main():
    """主测试函数"""
    print("开始测试2天数据场景的pre_close计算...\n")
    
    # 测试2天数据场景
    test_two_day_scenario()
    
    # 测试3天数据场景
    test_three_day_scenario()
    
    print("\n" + "=" * 50)
    print("2天数据场景测试总结:")
    print("✓ 读取最近2天数据用于计算pre_close")
    print("✓ pre_close使用前一天的真实收盘价")
    print("✓ 只返回最新1天数据用于存储")
    print("✓ pct_chg基于准确的pre_close计算")
    print("✓ 支持多天数据文件的处理")
    print("=" * 50)
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
