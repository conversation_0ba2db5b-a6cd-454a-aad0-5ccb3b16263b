#!/usr/bin/env python3
"""
测试超高速日线数据处理功能
"""

import sys
import os
import time

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_dir)

from data_update.dailydata_func_tdx import (
    read_all_stocks_to_dataframe,
    read_all_stocks_to_dataframe_parallel,
    save_dataframe_to_database,
    process_tdx_daydata_parallel,
    process_tdx_daydata_serial
)

def test_serial_reading():
    """测试串行读取"""
    print("=== 测试串行读取 ===")
    start_time = time.time()

    stock_df, index_df = read_all_stocks_to_dataframe()

    elapsed = time.time() - start_time
    print(f"串行读取完成，耗时: {elapsed:.2f}秒")

    if not stock_df.empty:
        print(f"股票数据: {len(stock_df):,} 条记录，{stock_df['ts_code'].nunique()} 只股票")
        print(f"数据列: {list(stock_df.columns)}")
        print("\n股票数据前3条记录:")
        print(stock_df.head(3))

    if not index_df.empty:
        print(f"指数数据: {len(index_df):,} 条记录，{index_df['ts_code'].nunique()} 只指数")
        print("\n指数数据前3条记录:")
        print(index_df.head(3))

    return (stock_df, index_df), elapsed

def test_parallel_reading():
    """测试并行读取"""
    print("\n=== 测试并行读取 ===")
    start_time = time.time()

    stock_df, index_df = read_all_stocks_to_dataframe_parallel(max_workers=8)

    elapsed = time.time() - start_time
    print(f"并行读取完成，耗时: {elapsed:.2f}秒")

    if not stock_df.empty:
        print(f"股票数据: {len(stock_df):,} 条记录，{stock_df['ts_code'].nunique()} 只股票")
        print(f"数据列: {list(stock_df.columns)}")
        print("\n股票数据前3条记录:")
        print(stock_df.head(3))

    if not index_df.empty:
        print(f"指数数据: {len(index_df):,} 条记录，{index_df['ts_code'].nunique()} 只指数")
        print("\n指数数据前3条记录:")
        print(index_df.head(3))

    return (stock_df, index_df), elapsed

def test_database_saving(data_tuple):
    """测试数据库保存（模拟）"""
    print("\n=== 测试数据库保存（模拟）===")

    stock_df, index_df = data_tuple

    if stock_df.empty and index_df.empty:
        print("没有数据可保存")
        return

    total_records = len(stock_df) + len(index_df)
    print(f"模拟保存数据到数据库...")
    print(f"股票数据: {len(stock_df):,} 条 → stock_data 表")
    print(f"指数数据: {len(index_df):,} 条 → index_data 表")
    print(f"总计: {total_records:,} 条记录")

    start_time = time.time()

    # 模拟保存股票数据
    if not stock_df.empty:
        print("模拟保存股票数据...")
        time.sleep(0.02)  # 模拟数据库写入时间

    # 模拟保存指数数据
    if not index_df.empty:
        print("模拟保存指数数据...")
        time.sleep(0.01)  # 模拟数据库写入时间

    elapsed = time.time() - start_time
    print(f"模拟保存完成，耗时: {elapsed:.2f}秒")
    print(f"模拟速度: {total_records/elapsed:,.0f}条记录/秒")

def compare_performance():
    """性能对比测试"""
    print("=" * 60)
    print("开始性能对比测试")
    print("=" * 60)
    
    # 测试串行读取
    serial_df, serial_time = test_serial_reading()
    
    # 测试并行读取
    parallel_df, parallel_time = test_parallel_reading()
    
    # 性能对比
    if serial_time > 0 and parallel_time > 0:
        speedup = serial_time / parallel_time
        print(f"\n=== 性能对比结果 ===")
        print(f"串行读取耗时: {serial_time:.2f}秒")
        print(f"并行读取耗时: {parallel_time:.2f}秒")
        print(f"性能提升: {speedup:.2f}倍")
        
        if speedup > 1:
            print("✓ 并行处理更快")
        else:
            print("✗ 串行处理更快（可能是数据量太小或线程开销）")
    
    # 测试数据库保存
    if not parallel_df.empty:
        test_database_saving(parallel_df)
    
    return parallel_df

def test_full_pipeline():
    """测试完整流水线"""
    print("\n" + "=" * 60)
    print("测试完整处理流水线")
    print("=" * 60)
    
    print("注意：这将执行完整的数据处理流程，包括实际的数据库操作")
    print("如果不想实际写入数据库，请按 Ctrl+C 取消")
    
    try:
        # 等待5秒，给用户取消的机会
        for i in range(5, 0, -1):
            print(f"将在 {i} 秒后开始... (按 Ctrl+C 取消)")
            time.sleep(1)
        
        print("\n开始完整流水线测试...")
        
        # 执行并行处理
        process_tdx_daydata_parallel()
        
    except KeyboardInterrupt:
        print("\n用户取消了完整流水线测试")

def main():
    """主测试函数"""
    print("开始测试超高速日线数据处理功能...\n")
    
    # 性能对比测试
    df = compare_performance()
    
    # 询问是否执行完整流水线测试
    if df is not None and not df.empty:
        response = input("\n是否执行完整流水线测试（包括实际数据库写入）？(y/N): ")
        if response.lower() in ['y', 'yes']:
            test_full_pipeline()
        else:
            print("跳过完整流水线测试")
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
