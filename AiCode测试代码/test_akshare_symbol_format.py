#!/usr/bin/env python3
"""
测试akshare API对股票代码格式的要求
"""

def test_akshare_symbol_format():
    """测试akshare对股票代码格式的要求"""
    try:
        import akshare as ak
        print("akshare导入成功")
    except ImportError:
        print("akshare未安装，请先安装: pip install akshare")
        return
    
    # 测试不同格式的股票代码
    test_cases = [
        {
            'name': '平安银行',
            'symbol_with_suffix': '000001.SZ',
            'symbol_without_suffix': '000001'
        },
        {
            'name': '浦发银行', 
            'symbol_with_suffix': '600000.SH',
            'symbol_without_suffix': '600000'
        }
    ]
    
    for case in test_cases:
        print(f"\n=== 测试 {case['name']} ===")
        
        # 测试带后缀的格式
        print(f"1. 测试带后缀格式: {case['symbol_with_suffix']}")
        try:
            result1 = ak.stock_individual_info_em(symbol=case['symbol_with_suffix'])
            print(f"   结果: 成功获取数据")
            if isinstance(result1, dict) and '股票简称' in result1:
                print(f"   股票简称: {result1.get('股票简称', 'N/A')}")
        except Exception as e:
            print(f"   结果: 失败 - {str(e)}")
        
        # 测试不带后缀的格式
        print(f"2. 测试不带后缀格式: {case['symbol_without_suffix']}")
        try:
            result2 = ak.stock_individual_info_em(symbol=case['symbol_without_suffix'])
            print(f"   结果: 成功获取数据")
            if isinstance(result2, dict) and '股票简称' in result2:
                print(f"   股票简称: {result2.get('股票简称', 'N/A')}")
        except Exception as e:
            print(f"   结果: 失败 - {str(e)}")

def test_our_conversion_logic():
    """测试我们的转换逻辑"""
    print("\n=== 测试我们的转换逻辑 ===")
    
    test_codes = [
        '000001.SZ',
        '600000.SH', 
        '399006.SZ',
        '000906.SH'
    ]
    
    for ts_code in test_codes:
        # 模拟我们的转换逻辑
        symbol = ts_code.split('.')[0]
        print(f"{ts_code} → {symbol}")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    edge_cases = [
        '000001',      # 没有后缀
        '000001.SZ.XX', # 多个点
        '.SZ',         # 空代码
        '000001.',     # 空后缀
    ]
    
    for case in edge_cases:
        try:
            symbol = case.split('.')[0]
            print(f"'{case}' → '{symbol}'")
        except Exception as e:
            print(f"'{case}' → 错误: {str(e)}")

def main():
    """主函数"""
    print("开始测试akshare股票代码格式要求...")
    
    # 测试akshare API格式要求
    test_akshare_symbol_format()
    
    # 测试我们的转换逻辑
    test_our_conversion_logic()
    
    # 测试边界情况
    test_edge_cases()
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
