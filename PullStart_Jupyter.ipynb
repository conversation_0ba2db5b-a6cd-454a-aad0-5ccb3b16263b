{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8ba8ae66", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测算日期： 2025-08-21\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:381: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:565: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:636: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["行业: 石油石化, 回撤转折日期: 2025-08-19, 转折日期: 2025-08-21, 转折回升幅度: 2.14, 下行天数: 325, 下行幅度: -33.06\n", "行业: 通信, 回撤转折日期: 2025-08-15, 转折日期: 2025-08-13, 转折回升幅度: 6.75, 下行天数: 32, 下行幅度: -22.63\n", "行业: 美容护理, 回撤转折日期: 2025-08-15, 转折日期: 2025-06-04, 转折回升幅度: 1.97, 下行天数: 236, 下行幅度: -33.24\n", "行业: 电子, 回撤转折日期: 2025-08-14, 转折日期: 2025-08-13, 转折回升幅度: 3.84, 下行天数: 29, 下行幅度: -20.91\n", "行业: 综合, 回撤转折日期: 2025-08-14, 转折日期: 2025-08-11, 转折回升幅度: 8.32, 下行天数: 124, 下行幅度: -41.78\n", "行业: 建筑材料, 回撤转折日期: 2025-08-06, 转折日期: 2025-07-22, 转折回升幅度: 0.60, 下行天数: 352, 下行幅度: -29.77\n", "行业: 房地产, 回撤转折日期: 2025-08-04, 转折日期: 2024-11-07, 转折回升幅度: 0.24, 下行天数: 157, 下行幅度: -35.94\n", "行业: 汽车, 回撤转折日期: 2025-07-30, 转折日期: 2025-03-20, 转折回升幅度: 4.85, 下行天数: 50, 下行幅度: -21.20\n", "行业: 轻工制造, 回撤转折日期: 2025-07-30, 转折日期: 2024-12-13, 转折回升幅度: 1.72, 下行天数: 146, 下行幅度: -28.33\n", "行业: 家用电器, 回撤转折日期: 2025-07-29, 转折日期: 2025-08-19, 转折回升幅度: 1.39, 下行天数: 133, 下行幅度: -15.26\n", "行业: 电力设备, 回撤转折日期: 2025-04-09, 转折日期: 2024-11-25, 转折回升幅度: 7.64, 下行天数: 215, 下行幅度: -23.78\n", "行业: 机械设备, 回撤转折日期: 2025-04-08, 转折日期: 2025-03-10, 转折回升幅度: 12.80, 下行天数: 27, 下行幅度: -19.96\n", "行业: 计算机, 回撤转折日期: 2025-04-08, 转折日期: 2025-03-06, 转折回升幅度: 11.32, 下行天数: 174, 下行幅度: -33.49\n", "处理股票 603161.SH 时出错: attempt to get argmin of an empty sequence\n", "处理股票 605255.SH 时出错: attempt to get argmin of an empty sequence\n", "处理股票 688347.SH 时出错: attempt to get argmin of an empty sequence\n", "处理股票 688228.SH 时出错: attempt to get argmin of an empty sequence\n", "处理股票 603058.SH 时出错: attempt to get argmin of an empty sequence\n", "行业排序结果:\n", "1. 机械设备: 107.84, 涨停数/总数:2/520\n", "2. 计算机: 90.10, 涨停数/总数:5/332\n", "3. 电力设备: 81.10, 涨停数/总数:1/349\n", "4. 汽车: 35.13, 涨停数/总数:1/264\n", "5. 家用电器: 33.47, 涨停数/总数:1/95\n", "6. 轻工制造: 33.10, 涨停数/总数:2/152\n", "7. 电子: 22.97, 涨停数/总数:4/451\n", "8. 房地产: 17.95, 涨停数/总数:1/103\n", "9. 建筑材料: 16.13, 涨停数/总数:2/71\n", "10. 通信: 7.14, 涨停数/总数:0/125\n", "11. 美容护理: 2.43, 涨停数/总数:0/31\n", "12. 石油石化: 2.11, 涨停数/总数:1/47\n", "13. 综合: -3.05, 涨停数/总数:1/17\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2195: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_start_list['pullstart_sort_position'] = pull_start_list.groupby('industry').cumcount() + 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-08-21 筛选数据存储 \n", "存储PullStart股票条目： 11\n", "存储成功\n", "PullStart股票行业分布：\n", "           count\n", "industry       \n", "计算机           3\n", "电子            2\n", "机械设备          1\n", "家用电器          1\n", "轻工制造          1\n", "建筑材料          1\n", "石油石化          1\n", "综合            1\n", "电力设备          0\n", "汽车            0\n", "房地产           0\n", "通信            0\n", "美容护理          0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2443: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  \n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2469: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  result_df_recentindus = result_df_recentindus.sort_values(\n"]}], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "# trade_date = get_trade_date(start_date='2025-06-20', end_date='2025-07-25')\n", "# trade_date = trade_date[::-1]\n", "trade_date = ['2025-08-21']\n", "for date in trade_date:\n", "    # industry_list = ['房地产', '纺织服饰', '建筑装饰', '建筑材料', '轻工制造', '家用电器', '交通运输', '公用事业', \n", "                    #  '煤炭', '农林牧渔']\n", "    # trend_industry_list = ['农林牧渔', '食品饮料', '商贸零售', '美容护理', '基础化工', '国防军工', \n", "    # '非银金融', '通信', '房地产', '有色金属', '汽车', '家用电器', '综合', '电力设备', '计算机', '机械设备']\n", "    print('测算日期：', date)\n", "    trend_industry_list = None\n", "    # recent_industry_list = ['通信', '环保', '基础化工']\n", "    recent_industry_list = None\n", "    # industry_list = None\n", "    # '2025-03-24', '2025-03-10', '2025-02-28', '2025-02-18', \n", "    end_date, trend_startdate = date, '2025-04-07'\n", "    recent_turndate = ['2025-05-23', '2025-06-10', '2025-06-19', '2025-06-27', '2025-07-07', \n", "                       '2025-07-16', '2025-07-23', '2025-07-31', '2025-08-04', '2025-08-14', \n", "                       '2025-08-27']\n", "    # recent_turndate = ['2025-07-15', '2025-08-01']\n", "    result_df_head, result_df_turn, pull_start_list, pull_start_list_core, result_df_recentindus = track_pullstart_stocks(\n", "        end_date=end_date, trend_startdate=trend_startdate,\n", "        recent_turndate=recent_turndate,\n", "        rise_stop_signal=True,\n", "        industry_list=trend_industry_list,\n", "        limit_num=80, store_mode=True,\n", "        recent_indus=recent_industry_list,\n", "        recentindus_calstartdate='2025-03-18')"]}, {"cell_type": "code", "execution_count": 3, "id": "6ae2b7d4", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ts_code", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "industry", "rawType": "category", "type": "unknown"}, {"name": "recent_turndate", "rawType": "object", "type": "string"}, {"name": "Period_TurnDate", "rawType": "object", "type": "string"}, {"name": "Section_StartDate", "rawType": "object", "type": "string"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "Now_PRA_Rate", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MinPRADate2Now_RiseAvg", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PRA_MaxRate_BreachCount", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_BandDiff", "rawType": "float64", "type": "float"}, {"name": "PRA2Cls_Percentile_Diff", "rawType": "float64", "type": "float"}, {"name": "PostNowSecMaxPRA_PostTurnRank5PRA_Diff", "rawType": "float64", "type": "float"}, {"name": "PostNowSecMaxPGV_PostTurnRank5PGV_Diff", "rawType": "float64", "type": "float"}, {"name": "NowSec_PRA2Close_CoverDays_Diff", "rawType": "float64", "type": "float"}, {"name": "PRA2Cls_Percentile_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_MaxPGV_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxPRA_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxCls_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "Recent_MinPRA_Date", "rawType": "object", "type": "string"}, {"name": "Recent_MinPRA_DownCoverDays", "rawType": "float64", "type": "float"}, {"name": "Recent_MinPRA2Now_Days", "rawType": "float64", "type": "float"}, {"name": "Recent_MinPRA_DownCover2PostSec_DaysProp", "rawType": "float64", "type": "float"}, {"name": "PostTurn_VGVRank5_NearNowDate", "rawType": "object", "type": "string"}, {"name": "PostTurn_VGVRank5_NearNowDate_Days", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MinDaily2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Recent_Neg42Now_Days", "rawType": "int64", "type": "integer"}, {"name": "Recent_MaxPRA_Date", "rawType": "object", "type": "string"}, {"name": "Recent_MaxPRA_PRA_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Recent_MaxPRA_Cls_CoverDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MaxRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MinRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_PGV_MaxRollAvg_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxClose_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "CoverDays_BreakDate_RatioBand", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_PGV_MinRollAvg_Date", "rawType": "object", "type": "string"}, {"name": "BullStk_RecentPRA_Signal", "rawType": "float64", "type": "float"}, {"name": "prepost_recentbottom_daysdiff", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_PRA_MaxRate_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_VRA_MaxRate_CoverDays", "rawType": "float64", "type": "float"}, {"name": "peak2recentbottom_lastdays", "rawType": "float64", "type": "float"}, {"name": "recentbottom2now_lastdays", "rawType": "int64", "type": "integer"}, {"name": "PostNowSec_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_LowBand", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_UpBand", "rawType": "float64", "type": "float"}, {"name": "PostNowSec2PreNowPeak_PRV2Price_RatioDiff", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PullStart_Score", "rawType": "float64", "type": "float"}], "ref": "8eb96f1c-7aba-4cc2-bbe5-52876d324a00", "rows": [["183", "002155.SZ", "湖南黄金", "有色金属", "2025-08-14", "2024-12-31", "2024-12-31", "2025-08-12", "113.157", "0.706", "2.0", "27.767", "-0.646", "0.776", "0.983", "-1120.0", "0.515", "0.609", "0.687", "1.333", "2025-08-29", "13.0", "1.0", "0.08", "2025-08-13", "13.0", "14.0", "89.0", "4", "2025-08-25", "84.0", "52.0", "119.0", "31.0", "89.0", "1209.0", "319.258", "2025-07-18", "1.0", "80.0", "13.0", "1.0", "6.0", "86", "0.595", "0.03", "0.833", "-23.526", "0.662", "43.5"], ["1773", "301031.SZ", "中熔电气", "电子", "2025-08-27", "2025-01-03", "2025-06-20", "2025-08-27", "63.239", "0.54", "2.0", "12.105", "-1.355", "0.734", "0.96", "-512.0", "0.189", "0.339", "0.316", "1.671", "2025-08-26", "13.0", "4.0", "0.271", "2025-08-15", "11.0", "3.0", "12.0", "13", "2025-09-01", "91.0", "66.0", "0.0", "40.0", "91.0", "603.0", "153.6", "2025-08-26", "0.0", "1.0", "39.0", "1.0", "3.0", "4", "2.66", "0.56", "6.779", "70.214", "1.397", "73.0"], ["1099", "605196.SH", "华通线缆", "电力设备", "2025-08-27", "2025-03-03", "2025-03-03", "2025-08-27", "83.781", "1.046", "1.0", "21.457", "-0.395", "1.014", "1.208", "-1029.0", "0.646", "1.033", "0.721", "1.116", "2025-08-22", "16.0", "6.0", "0.133", "2025-07-01", "44.0", "3.0", "101.0", "4", "2025-09-01", "16.0", "492.0", "17.0", "94.0", "16.0", "1045.0", "274.566", "2025-08-25", "0.0", "2.0", "57.0", "1.0", "2.0", "4", "0.581", "0.035", "0.751", "28.619", "0.432", "63.5"], ["307", "603289.SH", "泰瑞机器", "机械设备", "2025-08-27", "2025-04-08", "2025-04-08", "2025-08-29", "117.636", "1.668", "1.0", "19.818", "-0.94", "0.572", "0.978", "-515.0", "0.121", "0.255", "0.129", "1.069", "2025-08-15", "89.0", "11.0", "0.989", "2025-08-26", "4.0", "1.0", "3.0", "4", "2025-08-20", "50.0", "100.0", "58.0", "11.0", "7.0", "522.0", "78.772", "2025-08-25", "0.0", "-5.0", "10.0", "92.0", "7.0", "2", "0.12", "0.033", "0.654", "-42.964", "0.203", "79.5"], ["39", "601116.SH", "三江购物", "商贸零售", "2025-08-14", "2025-04-07", "2025-06-19", "2025-08-14", "96.443", "0.66", "1.0", "8.523", "-0.359", "0.56", "0.92", "-63.0", "0.59", "0.592", "0.516", "0.875", "2025-08-14", "40.0", "12.0", "0.976", "2025-08-26", "4.0", "12.0", "18.0", "19", "2025-09-01", "28.0", "91.0", "37.0", "12.0", "28.0", "91.0", "15.891", "2025-08-14", "0.0", "-4.0", "31.0", "1.0", "17.0", "13", "0.243", "0.044", "0.375", "-16.981", "0.288", "79.5"], ["1866", "603660.SH", "苏州科达", "计算机", "2025-08-14", "2025-04-08", "2025-04-08", "2025-08-14", "61.722", "0.687", "2.0", "7.324", "-0.807", "0.968", "1.351", "-110.0", "0.455", "0.661", "0.675", "1.482", "2025-08-14", "15.0", "12.0", "0.169", "2025-08-18", "10.0", "12.0", "3.0", "4", "2025-09-01", "16.0", "126.0", "16.0", "29.0", "16.0", "126.0", "49.747", "2025-08-14", "0.0", "7.0", "10.0", "1.0", "6.0", "13", "0.19", "0.034", "0.249", "-31.613", "0.236", "48.5"], ["294", "603159.SH", "上海亚虹", "机械设备", "2025-08-27", "2025-04-08", "2025-04-08", "2025-08-28", "70.101", "0.782", "1.0", "1.584", "0.015", "1.235", "1.552", "-2.0", "1.016", "3.004", "0.977", "0.962", "2025-08-13", "18.0", "13.0", "0.205", "2025-09-01", "0.0", "2.0", "7.0", "4", "2025-08-22", "374.0", "2.0", "6.0", "78.0", "6.0", "8.0", "152.49", "2025-08-27", "1.0", "-3.0", "35.0", "1.0", "6.0", "3", "0.654", "0.356", "0.564", "5.558", "0.63", "75.0"], ["114", "600531.SH", "豫光金铅", "有色金属", "2025-08-14", "2025-04-08", "2025-04-08", "2025-08-19", "43.474", "0.421", "0.0", "8.148", "-0.108", "1.433", "2.116", "-717.0", "0.92", "1.251", "1.245", "1.353", "2025-08-12", "17.0", "14.0", "0.195", "2025-06-04", "63.0", "9.0", "89.0", "13", "2025-09-01", "492.0", "492.0", "0.0", "74.0", "492.0", "1209.0", "143.372", "2025-08-12", "0.0", "-1.0", "13.0", "1.0", "11.0", "10", "0.269", "0.027", "0.22", "15.511", "0.211", "58.0"], ["557", "002975.SZ", "博杰股份", "机械设备", "2025-08-27", "2025-01-03", "2025-04-09", "2025-08-27", "-22.063", "2.248", "0.0", "22.08", "-0.63", "0.519", "0.553", "-840.0", "0.428", "0.364", "0.471", "1.101", "2025-08-12", "13.0", "14.0", "0.151", "2025-09-01", "0.0", "3.0", "16.0", "9", "2025-08-07", "105.0", "475.0", "17.0", "29.0", "8.0", "848.0", "156.702", "2025-08-22", "0.0", "-2.0", "4.0", "2.0", "6.0", "4", "1.063", "0.1", "2.208", "-36.431", "1.556", "84.0"], ["1473", "688195.SH", "腾景科技", "电子", "2025-08-27", "2025-04-07", "2025-04-07", "2025-08-26", "68.95", "1.116", "1.0", "17.559", "-0.058", "1.7", "2.736", "-572.0", "0.961", "1.719", "1.431", "1.489", "2025-08-08", "17.0", "16.0", "0.198", "2025-07-29", "24.0", "4.0", "7.0", "5", "2025-09-01", "491.0", "491.0", "0.0", "94.0", "491.0", "1063.0", "470.724", "2025-08-26", "0.0", "1.0", "11.0", "8.0", "4.0", "5", "5.276", "0.195", "3.424", "27.459", "3.424", "42.0"], ["135", "601958.SH", "金钼股份", "有色金属", "2025-08-04", "2025-04-07", "2025-04-07", "2025-08-01", "29.64", "0.735", "2.0", "9.647", "-0.685", "1.285", "1.934", "-717.0", "0.646", "1.024", "1.248", "1.933", "2025-08-07", "13.0", "17.0", "0.153", "2025-05-28", "67.0", "21.0", "3.0", "102", "2025-08-26", "488.0", "488.0", "4.0", "73.0", "492.0", "1209.0", "258.385", "2025-08-07", "0.0", "20.0", "4.0", "1.0", "2.0", "22", "0.418", "0.034", "0.328", "-0.275", "0.328", "41.5"], ["128", "601212.SH", "白银有色", "有色金属", "2025-08-04", "2025-04-08", "2025-08-01", "2025-08-01", "62.57", "1.227", "4.0", "12.455", "-0.81", "1.003", "1.377", "-1175.0", "0.413", "0.827", "0.57", "1.38", "2025-08-01", "0.0", "21.0", "0.0", "2025-06-24", "49.0", "21.0", "3.0", "4", "2025-09-01", "34.0", "492.0", "0.0", "14.0", "34.0", "1209.0", "71.239", "2025-08-01", "0.0", "-13.0", "19.0", "1.0", "35.0", "22", "0.087", "0.011", "0.137", "-19.318", "0.096", "109.0"], ["1992", "002642.SZ", "荣联科技", "计算机", "2025-08-14", "2025-01-10", "2025-06-20", "2025-08-14", "40.821", "0.408", "2.0", "6.921", "-0.05", "0.913", "1.226", "-1.0", "0.947", "0.729", "0.899", "0.949", "2025-07-30", "18.0", "23.0", "0.621", "2025-08-15", "11.0", "12.0", "1.0", "57", "2025-08-28", "109.0", "109.0", "2.0", "43.0", "111.0", "112.0", "42.027", "2025-08-11", "0.0", "8.0", "4.0", "1.0", "5.0", "13", "0.281", "0.038", "0.263", "153.831", "0.104", "57.0"], ["2052", "300324.SZ", "旋极信息", "计算机", "2025-08-27", "2025-04-07", "2025-04-07", "2025-08-26", "-12.125", "1.022", "1.0", "26.333", "-0.773", "1.124", "1.293", "-1189.0", "0.462", "0.59", "0.663", "1.436", "2025-07-30", "20.0", "23.0", "0.253", "2025-08-04", "20.0", "4.0", "43.0", "2", "2025-08-19", "113.0", "1.0", "9.0", "43.0", "9.0", "1198.0", "317.297", "2025-08-27", "1.0", "3.0", "18.0", "12.0", "2.0", "5", "0.342", "0.018", "0.474", "-28.64", "0.368", "81.5"], ["1599", "002138.SZ", "顺络电子", "电子", "2025-08-04", "2024-09-20", "2025-06-13", "2025-08-04", "79.86", "0.576", "2.0", "7.938", "-0.551", "1.374", "1.826", "-508.0", "0.597", "1.051", "0.817", "1.368", "2025-07-25", "30.0", "26.0", "0.968", "2025-07-17", "32.0", "20.0", "40.0", "422", "2025-09-01", "377.0", "492.0", "0.0", "26.0", "377.0", "885.0", "98.269", "2025-07-25", "0.0", "9.0", "13.0", "1.0", "12.0", "21", "0.974", "0.128", "1.016", "267.279", "0.25", "50.5"], ["1632", "002654.SZ", "万润科技", "电子", "2025-08-27", "2025-04-08", "2025-06-13", "2025-08-29", "56.738", "0.52", "1.0", "9.18", "-0.754", "0.953", "1.117", "-72.0", "0.411", "0.65", "0.526", "1.28", "2025-07-22", "27.0", "29.0", "0.964", "2025-08-18", "10.0", "1.0", "3.0", "4", "2025-09-01", "41.0", "113.0", "41.0", "29.0", "41.0", "113.0", "29.657", "2025-08-29", "0.0", "-1.0", "6.0", "1.0", "3.0", "2", "0.287", "0.05", "0.459", "-4.565", "0.285", "81.0"], ["459", "000988.SZ", "华工科技", "机械设备", "2025-08-04", "2025-04-08", "2025-04-08", "2025-08-08", "-7.457", "0.786", "2.0", "8.606", "-0.607", "1.837", "2.555", "-717.0", "0.741", "1.762", "1.733", "2.34", "2025-07-14", "43.0", "35.0", "0.652", "2025-07-17", "32.0", "16.0", "17.0", "101", "2025-08-29", "491.0", "491.0", "1.0", "78.0", "492.0", "1209.0", "409.572", "2025-08-08", "0.0", "9.0", "7.0", "2.0", "8.0", "17", "2.682", "0.17", "1.463", "181.996", "0.817", "67.0"], ["1171", "002028.SZ", "思源电气", "电力设备", "2025-08-14", "2025-04-08", "2025-07-11", "2025-08-19", "67.951", "0.496", "1.0", "8.694", "-0.7", "1.258", "1.699", "-988.0", "0.537", "1.012", "0.811", "1.511", "2025-07-08", "-1.0", "39.0", "0.0", "2025-08-07", "17.0", "9.0", "22.0", "11", "2025-09-01", "221.0", "492.0", "0.0", "31.0", "221.0", "1209.0", "427.465", "2025-08-22", "0.0", "-1.0", "101.0", "1.0", "11.0", "10", "1.804", "0.242", "2.104", "9.891", "1.496", "63.0"]], "shape": {"columns": 49, "rows": 18}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>name</th>\n", "      <th>industry</th>\n", "      <th>recent_turndate</th>\n", "      <th>Period_TurnDate</th>\n", "      <th>Section_StartDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>Now_PRA_Rate</th>\n", "      <th>PostSecStart_MinPRADate2Now_RiseAvg</th>\n", "      <th>PostNowSec_PRA_MaxRate_BreachCount</th>\n", "      <th>...</th>\n", "      <th>Recent3Day_PRA_MaxRate_CoverDays</th>\n", "      <th>Recent3Day_VRA_MaxRate_CoverDays</th>\n", "      <th>peak2recentbottom_lastdays</th>\n", "      <th>recentbottom2now_lastdays</th>\n", "      <th>PostNowSec_PGV_MaxRollAvg</th>\n", "      <th>Turn2NowSec_PRA_LowBand</th>\n", "      <th>Turn2NowSec_PRA_UpBand</th>\n", "      <th>PostNowSec2PreNowPeak_PRV2Price_RatioDiff</th>\n", "      <th>PreNowPeak_PRV_Top3Mean</th>\n", "      <th>PullStart_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>183</th>\n", "      <td>002155.SZ</td>\n", "      <td>湖南黄金</td>\n", "      <td>有色金属</td>\n", "      <td>2025-08-14</td>\n", "      <td>2024-12-31</td>\n", "      <td>2024-12-31</td>\n", "      <td>2025-08-12</td>\n", "      <td>113.157</td>\n", "      <td>0.706</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>13.0</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>86</td>\n", "      <td>0.595</td>\n", "      <td>0.030</td>\n", "      <td>0.833</td>\n", "      <td>-23.526</td>\n", "      <td>0.662</td>\n", "      <td>43.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1773</th>\n", "      <td>301031.SZ</td>\n", "      <td>中熔电气</td>\n", "      <td>电子</td>\n", "      <td>2025-08-27</td>\n", "      <td>2025-01-03</td>\n", "      <td>2025-06-20</td>\n", "      <td>2025-08-27</td>\n", "      <td>63.239</td>\n", "      <td>0.540</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>39.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>4</td>\n", "      <td>2.660</td>\n", "      <td>0.560</td>\n", "      <td>6.779</td>\n", "      <td>70.214</td>\n", "      <td>1.397</td>\n", "      <td>73.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1099</th>\n", "      <td>605196.SH</td>\n", "      <td>华通线缆</td>\n", "      <td>电力设备</td>\n", "      <td>2025-08-27</td>\n", "      <td>2025-03-03</td>\n", "      <td>2025-03-03</td>\n", "      <td>2025-08-27</td>\n", "      <td>83.781</td>\n", "      <td>1.046</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>57.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>4</td>\n", "      <td>0.581</td>\n", "      <td>0.035</td>\n", "      <td>0.751</td>\n", "      <td>28.619</td>\n", "      <td>0.432</td>\n", "      <td>63.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>307</th>\n", "      <td>603289.SH</td>\n", "      <td>泰瑞机器</td>\n", "      <td>机械设备</td>\n", "      <td>2025-08-27</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-29</td>\n", "      <td>117.636</td>\n", "      <td>1.668</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>10.0</td>\n", "      <td>92.0</td>\n", "      <td>7.0</td>\n", "      <td>2</td>\n", "      <td>0.120</td>\n", "      <td>0.033</td>\n", "      <td>0.654</td>\n", "      <td>-42.964</td>\n", "      <td>0.203</td>\n", "      <td>79.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>601116.SH</td>\n", "      <td>三江购物</td>\n", "      <td>商贸零售</td>\n", "      <td>2025-08-14</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025-08-14</td>\n", "      <td>96.443</td>\n", "      <td>0.660</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>31.0</td>\n", "      <td>1.0</td>\n", "      <td>17.0</td>\n", "      <td>13</td>\n", "      <td>0.243</td>\n", "      <td>0.044</td>\n", "      <td>0.375</td>\n", "      <td>-16.981</td>\n", "      <td>0.288</td>\n", "      <td>79.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1866</th>\n", "      <td>603660.SH</td>\n", "      <td>苏州科达</td>\n", "      <td>计算机</td>\n", "      <td>2025-08-14</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-14</td>\n", "      <td>61.722</td>\n", "      <td>0.687</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>13</td>\n", "      <td>0.190</td>\n", "      <td>0.034</td>\n", "      <td>0.249</td>\n", "      <td>-31.613</td>\n", "      <td>0.236</td>\n", "      <td>48.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>294</th>\n", "      <td>603159.SH</td>\n", "      <td>上海亚虹</td>\n", "      <td>机械设备</td>\n", "      <td>2025-08-27</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-28</td>\n", "      <td>70.101</td>\n", "      <td>0.782</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>35.0</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>3</td>\n", "      <td>0.654</td>\n", "      <td>0.356</td>\n", "      <td>0.564</td>\n", "      <td>5.558</td>\n", "      <td>0.630</td>\n", "      <td>75.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>600531.SH</td>\n", "      <td>豫光金铅</td>\n", "      <td>有色金属</td>\n", "      <td>2025-08-14</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-19</td>\n", "      <td>43.474</td>\n", "      <td>0.421</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>13.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>10</td>\n", "      <td>0.269</td>\n", "      <td>0.027</td>\n", "      <td>0.220</td>\n", "      <td>15.511</td>\n", "      <td>0.211</td>\n", "      <td>58.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>557</th>\n", "      <td>002975.SZ</td>\n", "      <td>博杰股份</td>\n", "      <td>机械设备</td>\n", "      <td>2025-08-27</td>\n", "      <td>2025-01-03</td>\n", "      <td>2025-04-09</td>\n", "      <td>2025-08-27</td>\n", "      <td>-22.063</td>\n", "      <td>2.248</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>4.0</td>\n", "      <td>2.0</td>\n", "      <td>6.0</td>\n", "      <td>4</td>\n", "      <td>1.063</td>\n", "      <td>0.100</td>\n", "      <td>2.208</td>\n", "      <td>-36.431</td>\n", "      <td>1.556</td>\n", "      <td>84.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1473</th>\n", "      <td>688195.SH</td>\n", "      <td>腾景科技</td>\n", "      <td>电子</td>\n", "      <td>2025-08-27</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-08-26</td>\n", "      <td>68.950</td>\n", "      <td>1.116</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>11.0</td>\n", "      <td>8.0</td>\n", "      <td>4.0</td>\n", "      <td>5</td>\n", "      <td>5.276</td>\n", "      <td>0.195</td>\n", "      <td>3.424</td>\n", "      <td>27.459</td>\n", "      <td>3.424</td>\n", "      <td>42.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>601958.SH</td>\n", "      <td>金钼股份</td>\n", "      <td>有色金属</td>\n", "      <td>2025-08-04</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-08-01</td>\n", "      <td>29.640</td>\n", "      <td>0.735</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>22</td>\n", "      <td>0.418</td>\n", "      <td>0.034</td>\n", "      <td>0.328</td>\n", "      <td>-0.275</td>\n", "      <td>0.328</td>\n", "      <td>41.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128</th>\n", "      <td>601212.SH</td>\n", "      <td>白银有色</td>\n", "      <td>有色金属</td>\n", "      <td>2025-08-04</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-01</td>\n", "      <td>2025-08-01</td>\n", "      <td>62.570</td>\n", "      <td>1.227</td>\n", "      <td>4.0</td>\n", "      <td>...</td>\n", "      <td>19.0</td>\n", "      <td>1.0</td>\n", "      <td>35.0</td>\n", "      <td>22</td>\n", "      <td>0.087</td>\n", "      <td>0.011</td>\n", "      <td>0.137</td>\n", "      <td>-19.318</td>\n", "      <td>0.096</td>\n", "      <td>109.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1992</th>\n", "      <td>002642.SZ</td>\n", "      <td>荣联科技</td>\n", "      <td>计算机</td>\n", "      <td>2025-08-14</td>\n", "      <td>2025-01-10</td>\n", "      <td>2025-06-20</td>\n", "      <td>2025-08-14</td>\n", "      <td>40.821</td>\n", "      <td>0.408</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>5.0</td>\n", "      <td>13</td>\n", "      <td>0.281</td>\n", "      <td>0.038</td>\n", "      <td>0.263</td>\n", "      <td>153.831</td>\n", "      <td>0.104</td>\n", "      <td>57.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2052</th>\n", "      <td>300324.SZ</td>\n", "      <td>旋极信息</td>\n", "      <td>计算机</td>\n", "      <td>2025-08-27</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-08-26</td>\n", "      <td>-12.125</td>\n", "      <td>1.022</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>18.0</td>\n", "      <td>12.0</td>\n", "      <td>2.0</td>\n", "      <td>5</td>\n", "      <td>0.342</td>\n", "      <td>0.018</td>\n", "      <td>0.474</td>\n", "      <td>-28.640</td>\n", "      <td>0.368</td>\n", "      <td>81.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1599</th>\n", "      <td>002138.SZ</td>\n", "      <td>顺络电子</td>\n", "      <td>电子</td>\n", "      <td>2025-08-04</td>\n", "      <td>2024-09-20</td>\n", "      <td>2025-06-13</td>\n", "      <td>2025-08-04</td>\n", "      <td>79.860</td>\n", "      <td>0.576</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>13.0</td>\n", "      <td>1.0</td>\n", "      <td>12.0</td>\n", "      <td>21</td>\n", "      <td>0.974</td>\n", "      <td>0.128</td>\n", "      <td>1.016</td>\n", "      <td>267.279</td>\n", "      <td>0.250</td>\n", "      <td>50.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1632</th>\n", "      <td>002654.SZ</td>\n", "      <td>万润科技</td>\n", "      <td>电子</td>\n", "      <td>2025-08-27</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-06-13</td>\n", "      <td>2025-08-29</td>\n", "      <td>56.738</td>\n", "      <td>0.520</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>6.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>2</td>\n", "      <td>0.287</td>\n", "      <td>0.050</td>\n", "      <td>0.459</td>\n", "      <td>-4.565</td>\n", "      <td>0.285</td>\n", "      <td>81.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>459</th>\n", "      <td>000988.SZ</td>\n", "      <td>华工科技</td>\n", "      <td>机械设备</td>\n", "      <td>2025-08-04</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-08</td>\n", "      <td>-7.457</td>\n", "      <td>0.786</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>7.0</td>\n", "      <td>2.0</td>\n", "      <td>8.0</td>\n", "      <td>17</td>\n", "      <td>2.682</td>\n", "      <td>0.170</td>\n", "      <td>1.463</td>\n", "      <td>181.996</td>\n", "      <td>0.817</td>\n", "      <td>67.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1171</th>\n", "      <td>002028.SZ</td>\n", "      <td>思源电气</td>\n", "      <td>电力设备</td>\n", "      <td>2025-08-14</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-07-11</td>\n", "      <td>2025-08-19</td>\n", "      <td>67.951</td>\n", "      <td>0.496</td>\n", "      <td>1.0</td>\n", "      <td>...</td>\n", "      <td>101.0</td>\n", "      <td>1.0</td>\n", "      <td>11.0</td>\n", "      <td>10</td>\n", "      <td>1.804</td>\n", "      <td>0.242</td>\n", "      <td>2.104</td>\n", "      <td>9.891</td>\n", "      <td>1.496</td>\n", "      <td>63.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18 rows × 49 columns</p>\n", "</div>"], "text/plain": ["        ts_code  name industry recent_turndate Period_TurnDate  \\\n", "183   002155.SZ  湖南黄金     有色金属      2025-08-14      2024-12-31   \n", "1773  301031.SZ  中熔电气       电子      2025-08-27      2025-01-03   \n", "1099  605196.SH  华通线缆     电力设备      2025-08-27      2025-03-03   \n", "307   603289.SH  泰瑞机器     机械设备      2025-08-27      2025-04-08   \n", "39    601116.<PERSON>  三江购物     商贸零售      2025-08-14      2025-04-07   \n", "1866  603660.SH  苏州科达      计算机      2025-08-14      2025-04-08   \n", "294   603159.<PERSON>  上海亚虹     机械设备      2025-08-27      2025-04-08   \n", "114   600531.SH  豫光金铅     有色金属      2025-08-14      2025-04-08   \n", "557   002975.SZ  博杰股份     机械设备      2025-08-27      2025-01-03   \n", "1473  688195.SH  腾景科技       电子      2025-08-27      2025-04-07   \n", "135   601958.SH  金钼股份     有色金属      2025-08-04      2025-04-07   \n", "128   601212.SH  白银有色     有色金属      2025-08-04      2025-04-08   \n", "1992  002642.SZ  荣联科技      计算机      2025-08-14      2025-01-10   \n", "2052  300324.SZ  旋极信息      计算机      2025-08-27      2025-04-07   \n", "1599  002138.SZ  顺络电子       电子      2025-08-04      2024-09-20   \n", "1632  002654.SZ  万润科技       电子      2025-08-27      2025-04-08   \n", "459   000988.SZ  华工科技     机械设备      2025-08-04      2025-04-08   \n", "1171  002028.SZ  思源电气     电力设备      2025-08-14      2025-04-08   \n", "\n", "     Section_StartDate Now_SecDate  Now_PRA_Rate  \\\n", "183         2024-12-31  2025-08-12       113.157   \n", "1773        2025-06-20  2025-08-27        63.239   \n", "1099        2025-03-03  2025-08-27        83.781   \n", "307         2025-04-08  2025-08-29       117.636   \n", "39          2025-06-19  2025-08-14        96.443   \n", "1866        2025-04-08  2025-08-14        61.722   \n", "294         2025-04-08  2025-08-28        70.101   \n", "114         2025-04-08  2025-08-19        43.474   \n", "557         2025-04-09  2025-08-27       -22.063   \n", "1473        2025-04-07  2025-08-26        68.950   \n", "135         2025-04-07  2025-08-01        29.640   \n", "128         2025-08-01  2025-08-01        62.570   \n", "1992        2025-06-20  2025-08-14        40.821   \n", "2052        2025-04-07  2025-08-26       -12.125   \n", "1599        2025-06-13  2025-08-04        79.860   \n", "1632        2025-06-13  2025-08-29        56.738   \n", "459         2025-04-08  2025-08-08        -7.457   \n", "1171        2025-07-11  2025-08-19        67.951   \n", "\n", "      PostSecStart_MinPRADate2Now_RiseAvg  PostNowSec_PRA_MaxRate_BreachCount  \\\n", "183                                 0.706                                 2.0   \n", "1773                                0.540                                 2.0   \n", "1099                                1.046                                 1.0   \n", "307                                 1.668                                 1.0   \n", "39                                  0.660                                 1.0   \n", "1866                                0.687                                 2.0   \n", "294                                 0.782                                 1.0   \n", "114                                 0.421                                 0.0   \n", "557                                 2.248                                 0.0   \n", "1473                                1.116                                 1.0   \n", "135                                 0.735                                 2.0   \n", "128                                 1.227                                 4.0   \n", "1992                                0.408                                 2.0   \n", "2052                                1.022                                 1.0   \n", "1599                                0.576                                 2.0   \n", "1632                                0.520                                 1.0   \n", "459                                 0.786                                 2.0   \n", "1171                                0.496                                 1.0   \n", "\n", "      ...  Recent3Day_PRA_MaxRate_CoverDays  Recent3Day_VRA_MaxRate_CoverDays  \\\n", "183   ...                              13.0                               1.0   \n", "1773  ...                              39.0                               1.0   \n", "1099  ...                              57.0                               1.0   \n", "307   ...                              10.0                              92.0   \n", "39    ...                              31.0                               1.0   \n", "1866  ...                              10.0                               1.0   \n", "294   ...                              35.0                               1.0   \n", "114   ...                              13.0                               1.0   \n", "557   ...                               4.0                               2.0   \n", "1473  ...                              11.0                               8.0   \n", "135   ...                               4.0                               1.0   \n", "128   ...                              19.0                               1.0   \n", "1992  ...                               4.0                               1.0   \n", "2052  ...                              18.0                              12.0   \n", "1599  ...                              13.0                               1.0   \n", "1632  ...                               6.0                               1.0   \n", "459   ...                               7.0                               2.0   \n", "1171  ...                             101.0                               1.0   \n", "\n", "      peak2recentbottom_lastdays  recentbottom2now_lastdays  \\\n", "183                          6.0                         86   \n", "1773                         3.0                          4   \n", "1099                         2.0                          4   \n", "307                          7.0                          2   \n", "39                          17.0                         13   \n", "1866                         6.0                         13   \n", "294                          6.0                          3   \n", "114                         11.0                         10   \n", "557                          6.0                          4   \n", "1473                         4.0                          5   \n", "135                          2.0                         22   \n", "128                         35.0                         22   \n", "1992                         5.0                         13   \n", "2052                         2.0                          5   \n", "1599                        12.0                         21   \n", "1632                         3.0                          2   \n", "459                          8.0                         17   \n", "1171                        11.0                         10   \n", "\n", "      PostNowSec_PGV_MaxRollAvg  Turn2NowSec_PRA_LowBand  \\\n", "183                       0.595                    0.030   \n", "1773                      2.660                    0.560   \n", "1099                      0.581                    0.035   \n", "307                       0.120                    0.033   \n", "39                        0.243                    0.044   \n", "1866                      0.190                    0.034   \n", "294                       0.654                    0.356   \n", "114                       0.269                    0.027   \n", "557                       1.063                    0.100   \n", "1473                      5.276                    0.195   \n", "135                       0.418                    0.034   \n", "128                       0.087                    0.011   \n", "1992                      0.281                    0.038   \n", "2052                      0.342                    0.018   \n", "1599                      0.974                    0.128   \n", "1632                      0.287                    0.050   \n", "459                       2.682                    0.170   \n", "1171                      1.804                    0.242   \n", "\n", "      Turn2NowSec_PRA_UpBand  PostNowSec2PreNowPeak_PRV2Price_RatioDiff  \\\n", "183                    0.833                                    -23.526   \n", "1773                   6.779                                     70.214   \n", "1099                   0.751                                     28.619   \n", "307                    0.654                                    -42.964   \n", "39                     0.375                                    -16.981   \n", "1866                   0.249                                    -31.613   \n", "294                    0.564                                      5.558   \n", "114                    0.220                                     15.511   \n", "557                    2.208                                    -36.431   \n", "1473                   3.424                                     27.459   \n", "135                    0.328                                     -0.275   \n", "128                    0.137                                    -19.318   \n", "1992                   0.263                                    153.831   \n", "2052                   0.474                                    -28.640   \n", "1599                   1.016                                    267.279   \n", "1632                   0.459                                     -4.565   \n", "459                    1.463                                    181.996   \n", "1171                   2.104                                      9.891   \n", "\n", "      PreNowPeak_PRV_Top3Mean PullStart_Score  \n", "183                     0.662            43.5  \n", "1773                    1.397            73.0  \n", "1099                    0.432            63.5  \n", "307                     0.203            79.5  \n", "39                      0.288            79.5  \n", "1866                    0.236            48.5  \n", "294                     0.630            75.0  \n", "114                     0.211            58.0  \n", "557                     1.556            84.0  \n", "1473                    3.424            42.0  \n", "135                     0.328            41.5  \n", "128                     0.096           109.0  \n", "1992                    0.104            57.0  \n", "2052                    0.368            81.5  \n", "1599                    0.250            50.5  \n", "1632                    0.285            81.0  \n", "459                     0.817            67.0  \n", "1171                    1.496            63.0  \n", "\n", "[18 rows x 49 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_sorted = pull_start_list[['ts_code', 'name', 'industry', 'recent_turndate', 'Period_TurnDate', 'Section_StartDate', 'Now_SecDate', \n", "                                          'Now_PRA_Rate', 'PostSecStart_MinPRADate2Now_RiseAvg',\n", "                                          'PostNowSec_PRA_MaxRate_BreachCount', 'Turn2NowSec_PRA_BandDiff', \n", "                                          'PRA2Cls_Percentile_Diff', 'PostNowSecMaxPRA_PostTurnRank5PRA_Diff', 'PostNowSecMaxPGV_PostTurnRank5PGV_Diff',\n", "                                          'NowSec_PRA2Close_CoverDays_Diff', \n", "                                          'PRA2Cls_Percentile_Ratio', \n", "                                          'Recent3Day_MaxPGV_Percentile_PostTurn', \n", "                                          'NowSec_MaxPRA_Percentile_PostTurn', \n", "                                          'PostNowSec_MaxCls_Percentile_PostTurn', \n", "                                          'Recent_MinPRA_Date',\n", "                                          'Recent_MinPRA_DownCoverDays',\n", "                                          'Recent_MinPRA2Now_Days',\n", "                                          'Recent_MinPRA_DownCover2PostSec_DaysProp',\n", "                                          'PostTurn_VGVRank5_NearNowDate',\n", "                                          'PostTurn_VGVRank5_NearNowDate_Days',\n", "                                          'PostNowSec_LastDays',\n", "                                          'PostSecStart_MinDaily2Now_LastDays',\n", "                                          'Recent_Neg42Now_Days',\n", "                                          'Recent_MaxPRA_Date',\n", "                                          'Recent_MaxPRA_PRA_CoverDays',\n", "                                          'Recent_MaxPRA_Cls_CoverDays',\n", "                                          'PostSecStart_PGV_MaxRollAvg2Now_LastDays',\n", "                                          'PostSecStart_PGV_MinRollAvg2Now_LastDays',\n", "                                          'NowSec_PGV_MaxRollAvg_UpCoverDays',\n", "                                          'NowSec_MaxClose_UpCoverDays',\n", "                                          'CoverDays_BreakDate_RatioBand',\n", "                                          'PostPreNowPeak_PGV_MinRollAvg_Date',\n", "                                          'BullStk_RecentPRA_Signal',\n", "                                          'prepost_recentbottom_daysdiff',\n", "                                          'Recent3Day_PRA_MaxRate_CoverDays', 'Recent3Day_VRA_MaxRate_CoverDays',\n", "                                          'peak2recentbottom_lastdays',\n", "                                          'recentbottom2now_lastdays','PostNowSec_PGV_MaxRollAvg', 'Turn2NowSec_PRA_LowBand', 'Turn2NowSec_PRA_UpBand', \n", "                                          'PostNowSec2PreNowPeak_PRV2Price_RatioDiff', 'PreNowPeak_PRV_Top3Mean', 'PullStart_Score']\n", "                                         ].sort_values(by=['Recent_MinPRA2Now_Days', 'Recent_MinPRA_DownCover2PostSec_DaysProp'], ascending=[True, False])\n", "pull_start_list_sorted"]}, {"cell_type": "code", "execution_count": 4, "id": "c407940d", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ts_code", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "industry", "rawType": "category", "type": "unknown"}, {"name": "Period_TurnDate", "rawType": "object", "type": "string"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "recent_bottom_date", "rawType": "object", "type": "string"}, {"name": "Now_PRA_Rate", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PRA_MaxRate_BreachCount", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_BandDiff", "rawType": "float64", "type": "float"}, {"name": "PRA2Cls_Percentile_Diff", "rawType": "float64", "type": "float"}, {"name": "PostNowSecMaxPRA_PostTurnRank5PRA_Diff", "rawType": "float64", "type": "float"}, {"name": "PostNowSecMaxPGV_PostTurnRank5PGV_Diff", "rawType": "float64", "type": "float"}, {"name": "NowSec_PRA2Close_CoverDays_Diff", "rawType": "float64", "type": "float"}, {"name": "PRA2Cls_Percentile_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_MaxPGV_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxPRA_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxCls_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "PostTurn_VGVRank5_NearNowDate", "rawType": "object", "type": "string"}, {"name": "PostTurn_VGVRank5_NearNowDate_Days", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MinDaily2Now_LastDays", "rawType": "int64", "type": "integer"}, {"name": "Recent_Neg42Now_Days", "rawType": "int64", "type": "integer"}, {"name": "Recent_MaxPRA_Date", "rawType": "object", "type": "unknown"}, {"name": "Recent_MaxPRA_PRA_CoverDays", "rawType": "int64", "type": "integer"}, {"name": "Recent_MaxPRA_Cls_CoverDays", "rawType": "int64", "type": "integer"}, {"name": "PostSecStart_PGV_MaxRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_PGV_MaxRollAvg_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxClose_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "CoverDays_BreakDate_RatioBand", "rawType": "float64", "type": "float"}, {"name": "prepost_recentbottom_daysdiff", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_PRA_MaxRate_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_VRA_MaxRate_CoverDays", "rawType": "float64", "type": "float"}, {"name": "peak2recentbottom_lastdays", "rawType": "float64", "type": "float"}, {"name": "recentbottom2now_lastdays", "rawType": "int64", "type": "integer"}, {"name": "PostNowSec_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_LowBand", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_UpBand", "rawType": "float64", "type": "float"}, {"name": "PostNowSec2PreNowPeak_PRV2Price_RatioDiff", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PullStart_Score", "rawType": "float64", "type": "float"}], "ref": "ecead6cc-a52c-44b5-aefa-4dd53e400f05", "rows": [["1316", "600841.SH", "动力新科", "汽车", "2025-01-27", "2025-08-15", "2025-07-11", "116.693", "1.0", "10.139", "-0.82", "0.399", "0.744", "-704.0", "0.247", "0.592", "0.269", "1.089", "2025-06-09", "51.0", "2.0", "99", "57", null, "99", "99", "5.0", "5.0", "709.0", "141.401", "20.0", "25.0", "14.0", "8.0", "28", "0.135", "0.036", "0.365", "-25.605", "0.171", "118.0"], ["162", "600557.SH", "康缘药业", "医药生物", "2025-04-07", "2025-08-07", "2025-06-26", "-4.973", "0.0", "8.529", "-0.793", "0.897", "1.065", "-298.0", "0.406", "0.411", "0.541", "1.334", "2025-05-29", "57.0", "8.0", "99", "9", null, "99", "99", "13.0", "11.0", "309.0", "65.941", "29.0", "1.0", "2.0", "10.0", "39", "0.375", "0.068", "0.58", "-44.541", "0.58", "74.5"], ["2490", "600805.SH", "悦达投资", "综合", "2025-04-08", "2025-08-18", "2025-08-18", "181.389", "1.0", "7.385", "-0.652", "0.715", "1.112", "-371.0", "0.378", "0.755", "0.397", "1.049", "2025-07-21", "21.0", "1.0", "99", "2", null, "99", "99", "0.0", "18.0", "389.0", "79.201", "-18.0", "58.0", "1.0", "20.0", "2", "0.101", "0.026", "0.192", "-26.173", "0.134", "140.5"], ["196", "603139.SH", "康惠制药", "医药生物", "2025-01-03", "2025-08-14", "2025-06-20", "314.062", "1.0", "37.475", "-0.519", "0.733", "1.254", "-32.0", "0.379", "0.346", "0.317", "0.836", "2025-06-12", "48.0", "3.0", "99", "46", null, "99", "99", "45.0", "15.0", "47.0", "182.117", "37.0", "100.0", "2.0", "6.0", "43", "0.53", "0.04", "1.499", "-56.373", "1.499", "66.5"], ["2486", "600682.SH", "南京新百", "综合", "2025-04-07", "2025-08-12", "2025-07-15", "86.282", "1.0", "8.467", "-0.376", "1.03", "1.252", "-478.0", "0.689", "1.026", "0.834", "1.21", "2025-07-08", "30.0", "5.0", "99", "18", null, "99", "99", "18.0", "17.0", "495.0", "86.626", "19.0", "20.0", "1.0", "7.0", "26", "0.239", "0.03", "0.254", "-17.289", "0.269", "83.5"], ["3015", "000617.SZ", "中油资本", "非银金融", "2025-04-07", "2025-08-08", "2025-08-08", "59.241", "2.0", "12.975", "-0.371", "0.985", "1.34", "11.0", "0.624", "0.883", "0.616", "0.987", "2025-07-02", "34.0", "7.0", "99", "14", null, "99", "99", "0.0", "26.0", "15.0", "16.547", "-7.0", "30.0", "1.0", "15.0", "8", "0.357", "0.04", "0.519", "24.902", "0.287", "136.0"]], "shape": {"columns": 40, "rows": 6}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>name</th>\n", "      <th>industry</th>\n", "      <th>Period_TurnDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>recent_bottom_date</th>\n", "      <th>Now_PRA_Rate</th>\n", "      <th>PostNowSec_PRA_MaxRate_BreachCount</th>\n", "      <th>Turn2NowSec_PRA_BandDiff</th>\n", "      <th>PRA2Cls_Percentile_Diff</th>\n", "      <th>...</th>\n", "      <th>Recent3Day_PRA_MaxRate_CoverDays</th>\n", "      <th>Recent3Day_VRA_MaxRate_CoverDays</th>\n", "      <th>peak2recentbottom_lastdays</th>\n", "      <th>recentbottom2now_lastdays</th>\n", "      <th>PostNowSec_PGV_MaxRollAvg</th>\n", "      <th>Turn2NowSec_PRA_LowBand</th>\n", "      <th>Turn2NowSec_PRA_UpBand</th>\n", "      <th>PostNowSec2PreNowPeak_PRV2Price_RatioDiff</th>\n", "      <th>PreNowPeak_PRV_Top3Mean</th>\n", "      <th>PullStart_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1316</th>\n", "      <td>600841.SH</td>\n", "      <td>动力新科</td>\n", "      <td>汽车</td>\n", "      <td>2025-01-27</td>\n", "      <td>2025-08-15</td>\n", "      <td>2025-07-11</td>\n", "      <td>116.693</td>\n", "      <td>1.0</td>\n", "      <td>10.139</td>\n", "      <td>-0.820</td>\n", "      <td>...</td>\n", "      <td>25.0</td>\n", "      <td>14.0</td>\n", "      <td>8.0</td>\n", "      <td>28</td>\n", "      <td>0.135</td>\n", "      <td>0.036</td>\n", "      <td>0.365</td>\n", "      <td>-25.605</td>\n", "      <td>0.171</td>\n", "      <td>118.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>600557.SH</td>\n", "      <td>康缘药业</td>\n", "      <td>医药生物</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-08-07</td>\n", "      <td>2025-06-26</td>\n", "      <td>-4.973</td>\n", "      <td>0.0</td>\n", "      <td>8.529</td>\n", "      <td>-0.793</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>10.0</td>\n", "      <td>39</td>\n", "      <td>0.375</td>\n", "      <td>0.068</td>\n", "      <td>0.580</td>\n", "      <td>-44.541</td>\n", "      <td>0.580</td>\n", "      <td>74.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2490</th>\n", "      <td>600805.SH</td>\n", "      <td>悦达投资</td>\n", "      <td>综合</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-18</td>\n", "      <td>2025-08-18</td>\n", "      <td>181.389</td>\n", "      <td>1.0</td>\n", "      <td>7.385</td>\n", "      <td>-0.652</td>\n", "      <td>...</td>\n", "      <td>58.0</td>\n", "      <td>1.0</td>\n", "      <td>20.0</td>\n", "      <td>2</td>\n", "      <td>0.101</td>\n", "      <td>0.026</td>\n", "      <td>0.192</td>\n", "      <td>-26.173</td>\n", "      <td>0.134</td>\n", "      <td>140.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>196</th>\n", "      <td>603139.SH</td>\n", "      <td>康惠制药</td>\n", "      <td>医药生物</td>\n", "      <td>2025-01-03</td>\n", "      <td>2025-08-14</td>\n", "      <td>2025-06-20</td>\n", "      <td>314.062</td>\n", "      <td>1.0</td>\n", "      <td>37.475</td>\n", "      <td>-0.519</td>\n", "      <td>...</td>\n", "      <td>100.0</td>\n", "      <td>2.0</td>\n", "      <td>6.0</td>\n", "      <td>43</td>\n", "      <td>0.530</td>\n", "      <td>0.040</td>\n", "      <td>1.499</td>\n", "      <td>-56.373</td>\n", "      <td>1.499</td>\n", "      <td>66.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2486</th>\n", "      <td>600682.SH</td>\n", "      <td>南京新百</td>\n", "      <td>综合</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-08-12</td>\n", "      <td>2025-07-15</td>\n", "      <td>86.282</td>\n", "      <td>1.0</td>\n", "      <td>8.467</td>\n", "      <td>-0.376</td>\n", "      <td>...</td>\n", "      <td>20.0</td>\n", "      <td>1.0</td>\n", "      <td>7.0</td>\n", "      <td>26</td>\n", "      <td>0.239</td>\n", "      <td>0.030</td>\n", "      <td>0.254</td>\n", "      <td>-17.289</td>\n", "      <td>0.269</td>\n", "      <td>83.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3015</th>\n", "      <td>000617.SZ</td>\n", "      <td>中油资本</td>\n", "      <td>非银金融</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-08-08</td>\n", "      <td>2025-08-08</td>\n", "      <td>59.241</td>\n", "      <td>2.0</td>\n", "      <td>12.975</td>\n", "      <td>-0.371</td>\n", "      <td>...</td>\n", "      <td>30.0</td>\n", "      <td>1.0</td>\n", "      <td>15.0</td>\n", "      <td>8</td>\n", "      <td>0.357</td>\n", "      <td>0.040</td>\n", "      <td>0.519</td>\n", "      <td>24.902</td>\n", "      <td>0.287</td>\n", "      <td>136.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6 rows × 40 columns</p>\n", "</div>"], "text/plain": ["        ts_code  name industry Period_TurnDate Now_SecDate recent_bottom_date  \\\n", "1316  600841.SH  动力新科       汽车      2025-01-27  2025-08-15         2025-07-11   \n", "162   600557.SH  康缘药业     医药生物      2025-04-07  2025-08-07         2025-06-26   \n", "2490  600805.SH  悦达投资       综合      2025-04-08  2025-08-18         2025-08-18   \n", "196   603139.SH  康惠制药     医药生物      2025-01-03  2025-08-14         2025-06-20   \n", "2486  600682.<PERSON>  <PERSON>京新百       综合      2025-04-07  2025-08-12         2025-07-15   \n", "3015  000617.SZ  中油资本     非银金融      2025-04-07  2025-08-08         2025-08-08   \n", "\n", "      Now_PRA_Rate  PostNowSec_PRA_MaxRate_BreachCount  \\\n", "1316       116.693                                 1.0   \n", "162         -4.973                                 0.0   \n", "2490       181.389                                 1.0   \n", "196        314.062                                 1.0   \n", "2486        86.282                                 1.0   \n", "3015        59.241                                 2.0   \n", "\n", "      Turn2NowSec_PRA_BandDiff  PRA2Cls_Percentile_Diff  ...  \\\n", "1316                    10.139                   -0.820  ...   \n", "162                      8.529                   -0.793  ...   \n", "2490                     7.385                   -0.652  ...   \n", "196                     37.475                   -0.519  ...   \n", "2486                     8.467                   -0.376  ...   \n", "3015                    12.975                   -0.371  ...   \n", "\n", "      Recent3Day_PRA_MaxRate_CoverDays  Recent3Day_VRA_MaxRate_CoverDays  \\\n", "1316                              25.0                              14.0   \n", "162                                1.0                               2.0   \n", "2490                              58.0                               1.0   \n", "196                              100.0                               2.0   \n", "2486                              20.0                               1.0   \n", "3015                              30.0                               1.0   \n", "\n", "      peak2recentbottom_lastdays  recentbottom2now_lastdays  \\\n", "1316                         8.0                         28   \n", "162                         10.0                         39   \n", "2490                        20.0                          2   \n", "196                          6.0                         43   \n", "2486                         7.0                         26   \n", "3015                        15.0                          8   \n", "\n", "      PostNowSec_PGV_MaxRollAvg  Turn2NowSec_PRA_LowBand  \\\n", "1316                      0.135                    0.036   \n", "162                       0.375                    0.068   \n", "2490                      0.101                    0.026   \n", "196                       0.530                    0.040   \n", "2486                      0.239                    0.030   \n", "3015                      0.357                    0.040   \n", "\n", "      Turn2NowSec_PRA_UpBand PostNowSec2PreNowPeak_PRV2Price_RatioDiff  \\\n", "1316                   0.365                                   -25.605   \n", "162                    0.580                                   -44.541   \n", "2490                   0.192                                   -26.173   \n", "196                    1.499                                   -56.373   \n", "2486                   0.254                                   -17.289   \n", "3015                   0.519                                    24.902   \n", "\n", "      PreNowPeak_PRV_Top3Mean  PullStart_Score  \n", "1316                    0.171            118.0  \n", "162                     0.580             74.5  \n", "2490                    0.134            140.5  \n", "196                     1.499             66.5  \n", "2486                    0.269             83.5  \n", "3015                    0.287            136.0  \n", "\n", "[6 rows x 40 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# industry_pick = ['有色金属', '汽车']\n", "# pull_start_list_sorted.query('industry in @industry_pick')\n", "stk_list = ['600841.SH', '600557.SH', '600805.SH', '603139.SH', '600682.SH', '000617.SZ']\n", "pull_start_list_sorted.query('ts_code in @stk_list')"]}, {"cell_type": "code", "execution_count": 5, "id": "136fec49", "metadata": {}, "outputs": [], "source": ["neg_threshold = -20\n", "pull_start_list_risestop = pull_start_list.query('industry==\"房地产\"').query('rise_stop_flag == 1')"]}, {"cell_type": "code", "execution_count": 4, "id": "7e56ef79", "metadata": {}, "outputs": [{"data": {"text/plain": ["['002155.SZ',\n", " '301031.SZ',\n", " '605196.SH',\n", " '603289.SH',\n", " '601116.SH',\n", " '603660.SH',\n", " '603159.SH',\n", " '600531.SH',\n", " '002975.SZ',\n", " '688195.SH',\n", " '601958.SH',\n", " '601212.SH',\n", " '002642.SZ',\n", " '300324.SZ',\n", " '002138.SZ',\n", " '002654.SZ',\n", " '000988.SZ',\n", " '002028.SZ']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_sorted['ts_code'].values.tolist()"]}, {"cell_type": "code", "execution_count": 4, "id": "0d522835", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "PostSecStart_MaxValleyGapValue", "rawType": "float64", "type": "float"}], "ref": "b5dd6801-599d-4ccd-8625-59da9b954e73", "rows": [["416", "维康药业", "0.443"], ["148", "成都先导", "0.825"], ["1461", "长飞光纤", "1.016"], ["644", "应流股份", "0.929"], ["51", "汉商集团", "0.424"], ["1245", "建设工业", "1.426"], ["55", "人民同泰", "1.012"]], "shape": {"columns": 2, "rows": 7}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>PostSecStart_MaxValleyGapValue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>416</th>\n", "      <td>维康药业</td>\n", "      <td>0.443</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>成都先导</td>\n", "      <td>0.825</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1461</th>\n", "      <td>长飞光纤</td>\n", "      <td>1.016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>644</th>\n", "      <td>应流股份</td>\n", "      <td>0.929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>汉商集团</td>\n", "      <td>0.424</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1245</th>\n", "      <td>建设工业</td>\n", "      <td>1.426</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>人民同泰</td>\n", "      <td>1.012</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      name  PostSecStart_MaxValleyGapValue\n", "416   维康药业                           0.443\n", "148   成都先导                           0.825\n", "1461  长飞光纤                           1.016\n", "644   应流股份                           0.929\n", "51    汉商集团                           0.424\n", "1245  建设工业                           1.426\n", "55    人民同泰                           1.012"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list[['name', 'PostSecStart_MaxValleyGapValue']]"]}, {"cell_type": "code", "execution_count": null, "id": "132653dc", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "PostSecStart_MaxTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxTurnover_Date", "rawType": "object", "type": "string"}], "ref": "ca8c0c6e-95d7-457d-9f7f-790110123adf", "rows": [["1060", "大智慧", "11.62", "1.73", "2025-06-25"], ["1064", "恒银科技", "18.936", "4.801", "2025-06-25"], ["1069", "顶点软件", "7.727", "1.435", "2025-06-25"], ["809", "好上好", "36.101", "13.776", "2025-06-06"], ["326", "野马电池", "6.695", "2.139", "2025-06-25"], ["319", "龙蟠科技", "16.073", "11.292", "2025-06-25"], ["6", "航发科技", "16.166", "6.793", "2025-05-30"], ["160", "市北高新", "3.732", "1.259", "2025-06-25"]], "shape": {"columns": 4, "rows": 8}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>PostSecStart_MaxTurnover</th>\n", "      <th>PostSecStart_AvgTurnover</th>\n", "      <th>PostSecStart_MaxTurnover_Date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1060</th>\n", "      <td>大智慧</td>\n", "      <td>11.620</td>\n", "      <td>1.730</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1064</th>\n", "      <td>恒银科技</td>\n", "      <td>18.936</td>\n", "      <td>4.801</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1069</th>\n", "      <td>顶点软件</td>\n", "      <td>7.727</td>\n", "      <td>1.435</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>809</th>\n", "      <td>好上好</td>\n", "      <td>36.101</td>\n", "      <td>13.776</td>\n", "      <td>2025-06-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>326</th>\n", "      <td>野马电池</td>\n", "      <td>6.695</td>\n", "      <td>2.139</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>319</th>\n", "      <td>龙蟠科技</td>\n", "      <td>16.073</td>\n", "      <td>11.292</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>航发科技</td>\n", "      <td>16.166</td>\n", "      <td>6.793</td>\n", "      <td>2025-05-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160</th>\n", "      <td>市北高新</td>\n", "      <td>3.732</td>\n", "      <td>1.259</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      name  PostSecStart_MaxTurnover  PostSecStart_AvgTurnover  \\\n", "1060   大智慧                    11.620                     1.730   \n", "1064  恒银科技                    18.936                     4.801   \n", "1069  顶点软件                     7.727                     1.435   \n", "809    好上好                    36.101                    13.776   \n", "326   野马电池                     6.695                     2.139   \n", "319   龙蟠科技                    16.073                    11.292   \n", "6     航发科技                    16.166                     6.793   \n", "160   市北高新                     3.732                     1.259   \n", "\n", "     PostSecStart_MaxTurnover_Date  \n", "1060                    2025-06-25  \n", "1064                    2025-06-25  \n", "1069                    2025-06-25  \n", "809                     2025-06-06  \n", "326                     2025-06-25  \n", "319                     2025-06-25  \n", "6                       2025-05-30  \n", "160                     2025-06-25  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_risestop[['name', 'PostSecStart_MaxTurnover', 'PostSecStart_AvgTurnover', 'PostSecStart_MaxTurnover_Date']]"]}, {"cell_type": "code", "execution_count": null, "id": "3e7d3417", "metadata": {}, "outputs": [], "source": ["turn_col = ['PostSec_PGV_TurnP_Date',\n", "            'PostSec_PGV_TurnP_AbsChange',\n", "            'PostSec_PGV_TurnP_RelaChange',\n", "            'PostSec_PGV_PostTurnP_LastDays',    \n", "            'PostSec_TO_TurnP_Date',\n", "            'PostSec_TO_TurnP_AbsChange',\n", "            'PostSec_TO_TurnP_RelaChange',\n", "            'PostSec_TO_PostTurnP_LastDays']\n", "col_list = pull_start_list.columns.tolist()\n", "col_list = [col for col in col_list if col not in turn_col]\n", "pull_start_list_adj = pull_start_list[turn_col + col_list].copy()\n", "pull_start_list_adj['PostSec_PGV_TurnP_RelaChange_abs'] = pull_start_list_adj['PostSec_PGV_TurnP_RelaChange'].abs()\n", "pull_start_list_adj['PostSec_TO_TurnP_RelaChange_abs'] = pull_start_list_adj['PostSec_TO_TurnP_RelaChange'].abs()\n", "pull_start_list_adj = pull_start_list_adj.sort_values(\n", "    by='PostSec_PGV_TurnP_RelaChange_abs', ascending=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "cb047038", "metadata": {}, "outputs": [], "source": ["pull_start_list_adj2 = pull_start_list_adj.query('PostSec_PGV_TurnP_RelaChange<0')\n", "# pull_start_list_adj2['ts_code'].iloc[:20].values.tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "c8677c2d", "metadata": {}, "outputs": [], "source": ["industry_list = list(set(recent_industry_list + trend_industry_list))\n", "pull_start_list_core_temp = pull_start_list.query('industry in @industry_list & Now_PRA_Rate>=50 & PostNowSec_PRA_MaxRate_BreachCount==1 & Peak_Pres_Num>=2'\n", "                                             ).sort_values(by=['PostSecStart_PGV_Max2Mean_Ratio'], ascending=[False]).copy()\n", "if 'pullstart_sort_position' in pull_start_list_core_temp.columns:\n", "            cols = pull_start_list_core_temp.columns.tolist()\n", "            cols.remove('pullstart_sort_position')\n", "            pull_start_list_core_temp = pull_start_list_core_temp[['pullstart_sort_position'] + cols]\n"]}, {"cell_type": "code", "execution_count": null, "id": "9515f847", "metadata": {}, "outputs": [], "source": ["pull_start_list['Max2SecPeak_PRV_Diff_Rank'] = pull_start_list['PostSecStart_Max2SecPeak_PRV_Diff'].rank(ascending=False)\n", "pull_start_list['PostSecStart_PGV_Max2Mean_Ratio_Rank'] = pull_start_list['PostSecStart_PGV_Max2Mean_Ratio'].rank(ascending=False)\n", "pull_start_list['BreakPreNowPeak_Ratio_Rank'] = pull_start_list['BreakPreNowPeak_Ratio'].rank(ascending=False)\n", "pull_start_list['SecStart_PRA_Breach_Count_Rank'] = pull_start_list['SecStart_PRA_Breach_Count'].rank(ascending=False)\n", "pull_start_list['SecValley_GapRatio_Rank'] = pull_start_list['SecValley_GapRatio'].rank(ascending=False)\n", "pull_start_list['pullstart_sort_position_Rank'] = pull_start_list['pullstart_sort_position'].rank(ascending=True)\n", "pull_start_list['PostSecStart_AvgRatio_Rank'] = pull_start_list['PostSecStart_AvgRatio'].rank(ascending=True)\n", "pull_start_list['PullStart_Score'] = pull_start_list['Max2SecPeak_PRV_Diff_Rank'] + pull_start_list['PostSecStart_PGV_Max2Mean_Ratio_Rank'] + \\\n", "                                          pull_start_list['BreakPreNowPeak_Ratio_Rank'] + pull_start_list['SecStart_PRA_Breach_Count_Rank'] + \\\n", "                                          pull_start_list['SecValley_GapRatio_Rank'] + pull_start_list['pullstart_sort_position_Rank'] + \\\n", "                                          pull_start_list['PostSecStart_AvgRatio_Rank']\n", "pull_start_list2 = pull_start_list.sort_values(by=['industry', 'PullStart_Score'], ascending=[True, True])\n", "pull_start_list = pull_start_list.drop(columns=['Max2SecPeak_PRV_Diff_Rank', 'PostSecStart_PGV_Max2Mean_Ratio_Rank', 'BreakPreNowPeak_Ratio_Rank', 'SecStart_PRA_Breach_Count_Rank', 'SecValley_GapRatio_Rank', 'pullstart_sort_position_Rank', 'PostSecStart_AvgRatio_Rank'])"]}, {"cell_type": "code", "execution_count": null, "id": "fdd35b45", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"['华海诚科', '隆扬电子', '佰维存储', '美迪凯', '芯瑞达', '方邦股份', '智动力', '盛美上海', '*ST华微', '骏亚科技']\""]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["indus = \"电子\"\n", "str(pull_start_list.query('industry==@indus & BreakPreNowPeak_Ratio<3')['name'].iloc[:10].tolist())"]}, {"cell_type": "code", "execution_count": null, "id": "bc30ef99", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"['中芯国际', '工业富联', '海光信息', '寒武纪-U', '立讯精密', '北方华创', '韦尔股份', '京东方A', '中微公司', '蓝思科技']\""]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["str(result_df_head.query('industry==@indus')['name'].iloc[:10].tolist())"]}, {"cell_type": "code", "execution_count": 8, "id": "903f2930", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "now_overpre1day", "rawType": "int64", "type": "integer"}, {"name": "recentbottom_downcoverdays", "rawType": "int64", "type": "integer"}, {"name": "postcumret_consecutive_over7num", "rawType": "int64", "type": "integer"}, {"name": "nowsec_recent_diff", "rawType": "int64", "type": "integer"}, {"name": "recent2bottom_days", "rawType": "int64", "type": "integer"}, {"name": "postnowsec_risestop_num", "rawType": "int64", "type": "integer"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "recent_bottom_date", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PGV_Max2Mean_Ratio", "rawType": "float64", "type": "float"}, {"name": "Peak_Pres_Num", "rawType": "float64", "type": "float"}, {"name": "postnowsec_risestop_num", "rawType": "int64", "type": "integer"}, {"name": "recentbottom2now_lastdays", "rawType": "int64", "type": "integer"}, {"name": "PostNowSecMaxPRA_PostTurnRank5PRA_Diff", "rawType": "float64", "type": "float"}], "ref": "542f7f1c-0343-450f-b9c8-a579843b1706", "rows": [["1864", "1", "12", "1", "6", "6", "1", "2025-07-07", "2025-07-07", "3.788", "2.0", "1", "9", "0.607"]], "shape": {"columns": 13, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>now_overpre1day</th>\n", "      <th>recentbottom_downcoverdays</th>\n", "      <th>postcumret_consecutive_over7num</th>\n", "      <th>nowsec_recent_diff</th>\n", "      <th>recent2bottom_days</th>\n", "      <th>postnowsec_risestop_num</th>\n", "      <th>Now_SecDate</th>\n", "      <th>recent_bottom_date</th>\n", "      <th>PostSecStart_PGV_Max2Mean_Ratio</th>\n", "      <th>Peak_Pres_Num</th>\n", "      <th>postnowsec_risestop_num</th>\n", "      <th>recentbottom2now_lastdays</th>\n", "      <th>PostNowSecMaxPRA_PostTurnRank5PRA_Diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1864</th>\n", "      <td>1</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>2025-07-07</td>\n", "      <td>2025-07-07</td>\n", "      <td>3.788</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>0.607</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      now_overpre1day  recentbottom_downcoverdays  \\\n", "1864                1                          12   \n", "\n", "      postcumret_consecutive_over7num  nowsec_recent_diff  recent2bottom_days  \\\n", "1864                                1                   6                   6   \n", "\n", "      postnowsec_risestop_num Now_SecDate recent_bottom_date  \\\n", "1864                        1  2025-07-07         2025-07-07   \n", "\n", "      PostSecStart_PGV_Max2Mean_Ratio  Peak_Pres_Num  postnowsec_risestop_num  \\\n", "1864                            3.788            2.0                        1   \n", "\n", "      recentbottom2now_lastdays  PostNowSecMaxPRA_PostTurnRank5PRA_Diff  \n", "1864                          9                                   0.607  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp = result_df_head.query('ts_code==\"601869.SH\"')\n", "stk_temp[['now_overpre1day',\n", "            'recentbottom_downcoverdays',\n", "            'postcumret_consecutive_over7num',\n", "            'nowsec_recent_diff', 'recent2bottom_days',\n", "            'postnowsec_risestop_num', 'Now_SecDate', 'recent_bottom_date','PostSecStart_PGV_Max2Mean_Ratio', 'Peak_Pres_Num', 'postnowsec_risestop_num', 'recentbottom2now_lastdays', 'PostNowSecMaxPRA_PostTurnRank5PRA_Diff']]"]}, {"cell_type": "code", "execution_count": 11, "id": "11ea2de4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:381: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:565: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:636: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["行业: 传媒, 回撤转折日期: 2023-09-21, 转折日期: 2023-09-22, 转折回升幅度: 2.55, 下行天数: 66, 下行幅度: -40.45\n", "行业: 商贸零售, 回撤转折日期: 2023-09-14, 转折日期: 2023-08-02, 转折回升幅度: 0.40, 下行天数: 124, 下行幅度: -28.96\n", "行业: 钢铁, 回撤转折日期: 2023-09-12, 转折日期: 2023-09-04, 转折回升幅度: 0.83, 下行天数: 424, 下行幅度: -32.41\n", "行业: 医药生物, 回撤转折日期: 2023-09-07, 转折日期: 2023-08-16, 转折回升幅度: 4.01, 下行天数: 174, 下行幅度: -21.63\n", "行业: 交通运输, 回撤转折日期: 2023-09-06, 转折日期: 2023-09-21, 转折回升幅度: 1.22, 下行天数: 180, 下行幅度: -14.23\n", "行业: 非银金融, 回撤转折日期: 2023-09-05, 转折日期: 2023-08-28, 转折回升幅度: 1.15, 下行天数: 122, 下行幅度: -15.15\n", "行业: 纺织服饰, 回撤转折日期: 2023-08-28, 转折日期: 2023-08-21, 转折回升幅度: 2.59, 下行天数: 37, 下行幅度: -9.42\n", "行业: 汽车, 回撤转折日期: 2023-08-28, 转折日期: 2023-07-12, 转折回升幅度: 4.12, 下行天数: 179, 下行幅度: -29.63\n", "行业: 通信, 回撤转折日期: 2023-08-25, 转折日期: 2023-09-11, 转折回升幅度: 5.62, 下行天数: 47, 下行幅度: -25.67\n", "行业: 家用电器, 回撤转折日期: 2023-08-10, 转折日期: 2023-06-29, 转折回升幅度: 5.68, 下行天数: 53, 下行幅度: -16.02\n", "行业: 煤炭, 回撤转折日期: 2023-08-04, 转折日期: 2023-09-13, 转折回升幅度: 16.96, 下行天数: 203, 下行幅度: -42.25\n", "行业: 银行, 回撤转折日期: 2023-07-18, 转折日期: 2023-05-08, 转折回升幅度: 7.44, 下行天数: 82, 下行幅度: -26.20\n", "行业排序结果:\n", "1. 医药生物: 37.30, 涨停数/总数:1/461\n", "2. 通信: 24.10, 涨停数/总数:11/123\n", "3. 汽车: 22.49, 涨停数/总数:1/257\n", "4. 交通运输: 13.53, 涨停数/总数:0/122\n", "5. 家用电器: 11.16, 涨停数/总数:1/90\n", "6. 纺织服饰: 8.46, 涨停数/总数:0/104\n", "7. 传媒: 7.17, 涨停数/总数:2/129\n", "8. 商贸零售: 7.00, 涨停数/总数:1/97\n", "9. 非银金融: 6.55, 涨停数/总数:1/83\n", "10. 煤炭: 6.45, 涨停数/总数:0/37\n", "11. 银行: 5.41, 涨停数/总数:0/42\n", "12. 钢铁: 3.39, 涨停数/总数:0/45\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2186: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_start_list['pullstart_sort_position'] = pull_start_list.groupby('industry').cumcount() + 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-09-22 筛选数据存储 \n", "存储PullStart股票条目： 2\n", "存储成功\n", "PullStart股票行业分布：\n", "           count\n", "industry       \n", "通信            1\n", "家用电器          1\n", "医药生物          0\n", "汽车            0\n", "交通运输          0\n", "纺织服饰          0\n", "传媒            0\n", "商贸零售          0\n", "非银金融          0\n", "煤炭            0\n", "银行            0\n", "钢铁            0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2426: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_indus_count = pull_start_list[['ts_code', 'industry']].groupby('industry').count()\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2452: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  by=['industry', 'stk_checkperiod_sumratio'], ascending=[True, False]).groupby('industry').head(20)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:381: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:565: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:636: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["行业: 美容护理, 回撤转折日期: 2023-12-27, 转折日期: 2023-12-28, 转折回升幅度: 3.47, 下行天数: 212, 下行幅度: -31.66\n", "行业: 非银金融, 回撤转折日期: 2023-12-27, 转折日期: 2023-08-28, 转折回升幅度: 1.56, 下行天数: 122, 下行幅度: -15.47\n", "行业: 国防军工, 回撤转折日期: 2023-12-21, 转折日期: 2023-06-30, 转折回升幅度: 1.47, 下行天数: 54, 下行幅度: -25.63\n", "行业: 家用电器, 回撤转折日期: 2023-12-20, 转折日期: 2023-12-15, 转折回升幅度: 0.48, 下行天数: 53, 下行幅度: -15.96\n", "行业: 电力设备, 回撤转折日期: 2023-12-18, 转折日期: 2023-12-20, 转折回升幅度: 4.80, 下行天数: 324, 下行幅度: -43.04\n", "行业: 食品饮料, 回撤转折日期: 2023-12-14, 转折日期: 2023-11-03, 转折回升幅度: 3.23, 下行天数: 116, 下行幅度: -12.33\n", "行业: 有色金属, 回撤转折日期: 2023-12-13, 转折日期: 2023-12-06, 转折回升幅度: 4.23, 下行天数: 151, 下行幅度: -18.33\n", "行业: 银行, 回撤转折日期: 2023-11-15, 转折日期: 2023-10-23, 转折回升幅度: 1.99, 下行天数: 82, 下行幅度: -26.71\n", "行业: 石油石化, 回撤转折日期: 2023-11-08, 转折日期: 2023-09-14, 转折回升幅度: 5.19, 下行天数: 18, 下行幅度: -20.51\n", "行业: 环保, 回撤转折日期: 2023-04-20, 转折日期: 2022-11-10, 转折回升幅度: 7.72, 下行天数: 34, 下行幅度: -14.50\n", "行业排序结果:\n", "1. 环保: 49.60, 涨停数/总数:0/127\n", "2. 电力设备: 29.19, 涨停数/总数:24/341\n", "3. 石油石化: 7.66, 涨停数/总数:1/47\n", "4. 国防军工: 5.97, 涨停数/总数:0/134\n", "5. 食品饮料: 5.82, 涨停数/总数:1/123\n", "6. 有色金属: 5.16, 涨停数/总数:1/131\n", "7. 银行: 4.48, 涨停数/总数:0/42\n", "8. 家用电器: 2.63, 涨停数/总数:0/92\n", "9. 非银金融: 0.73, 涨停数/总数:1/83\n", "10. 美容护理: -2.11, 涨停数/总数:0/31\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2186: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_start_list['pullstart_sort_position'] = pull_start_list.groupby('industry').cumcount() + 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-12-28 筛选数据存储 \n", "存储PullStart股票条目： 7\n", "存储成功\n", "PullStart股票行业分布：\n", "           count\n", "industry       \n", "电力设备          7\n", "环保            0\n", "石油石化          0\n", "国防军工          0\n", "食品饮料          0\n", "有色金属          0\n", "银行            0\n", "家用电器          0\n", "非银金融          0\n", "美容护理          0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2426: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_indus_count = pull_start_list[['ts_code', 'industry']].groupby('industry').count()\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2452: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  by=['industry', 'stk_checkperiod_sumratio'], ascending=[True, False]).groupby('industry').head(20)\n"]}], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "trade_date = ['2023-09-22', '2023-12-28']\n", "for date in trade_date:\n", "    # industry_list = ['房地产', '纺织服饰', '建筑装饰', '建筑材料', '轻工制造', '家用电器', '交通运输', '公用事业', \n", "                    #  '煤炭', '农林牧渔']\n", "    # trend_industry_list = ['有色金属', '房地产', '建筑材料', '纺织服饰', '国防军工', '电力设备', '医药生物', '钢铁', '环保', '基础化工']\n", "    # recent_industry_list = ['房地产', '商贸零售']\n", "    trend_industry_list = None\n", "    recent_industry_list = None\n", "    # industry_list = None\n", "    # '2025-03-24', '2025-03-10', '2025-02-28', '2025-02-18', \n", "    end_date, trend_startdate = date, '2023-06-28'\n", "    recent_turndate = ['2023-08-11', '2023-08-18', '2023-08-28', '2023-09-07', '2023-09-13', '2023-10-16', '2023-10-23', '2023-11-08', '2023-11-21', '2023-12-05', '2023-12-20']\n", "    result_df_head, result_df_turn, pull_start_list, pull_start_list_core, result_df_recentindus = track_pullstart_stocks(\n", "        end_date=end_date, trend_startdate=trend_startdate,\n", "        recent_turndate=recent_turndate,\n", "        industry_list=trend_industry_list,\n", "        limit_num=80, store_mode=True,\n", "        recent_indus=recent_industry_list,\n", "        recentindus_calstartdate='2023-06-28',\n", "        rise_stop_signal=True)"]}, {"cell_type": "code", "execution_count": 9, "id": "620f3218", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ts_code", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "industry", "rawType": "category", "type": "unknown"}, {"name": "recent_turndate", "rawType": "object", "type": "string"}, {"name": "Period_TurnDate", "rawType": "object", "type": "string"}, {"name": "Section_StartDate", "rawType": "object", "type": "string"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "Now_PRA_Rate", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MinPRADate2Now_RiseAvg", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PRA_MaxRate_BreachCount", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_BandDiff", "rawType": "float64", "type": "float"}, {"name": "PRA2Cls_Percentile_Diff", "rawType": "float64", "type": "float"}, {"name": "PostNowSecMaxPRA_PostTurnRank5PRA_Diff", "rawType": "float64", "type": "float"}, {"name": "PostNowSecMaxPGV_PostTurnRank5PGV_Diff", "rawType": "float64", "type": "float"}, {"name": "NowSec_PRA2Close_CoverDays_Diff", "rawType": "float64", "type": "float"}, {"name": "PRA2Cls_Percentile_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_MaxPGV_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxPRA_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxCls_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "Recent_MinPRA_Date", "rawType": "object", "type": "string"}, {"name": "Recent_MinPRA_DownCoverDays", "rawType": "float64", "type": "float"}, {"name": "Recent_MinPRA2Now_Days", "rawType": "float64", "type": "float"}, {"name": "Recent_MinPRA_DownCover2PostSec_DaysProp", "rawType": "float64", "type": "float"}, {"name": "PostTurn_VGVRank5_NearNowDate", "rawType": "object", "type": "string"}, {"name": "PostTurn_VGVRank5_NearNowDate_Days", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MinDaily2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Recent_Neg42Now_Days", "rawType": "int64", "type": "integer"}, {"name": "Recent_MaxPRA_Date", "rawType": "object", "type": "string"}, {"name": "Recent_MaxPRA_PRA_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Recent_MaxPRA_Cls_CoverDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MaxRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MinRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_PGV_MaxRollAvg_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxClose_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "CoverDays_BreakDate_RatioBand", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_PGV_MinRollAvg_Date", "rawType": "object", "type": "string"}, {"name": "BullStk_RecentPRA_Signal", "rawType": "float64", "type": "float"}, {"name": "prepost_recentbottom_daysdiff", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_PRA_MaxRate_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_VRA_MaxRate_CoverDays", "rawType": "float64", "type": "float"}, {"name": "peak2recentbottom_lastdays", "rawType": "float64", "type": "float"}, {"name": "recentbottom2now_lastdays", "rawType": "int64", "type": "integer"}, {"name": "PostNowSec_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_LowBand", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_UpBand", "rawType": "float64", "type": "float"}, {"name": "PostNowSec2PreNowPeak_PRV2Price_RatioDiff", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PullStart_Score", "rawType": "float64", "type": "float"}], "ref": "16bb9a24-080d-431f-a4e9-fced806375d2", "rows": [["1710", "000409.SZ", "云鼎科技", "计算机", "2023-12-05", "2023-10-23", "2023-10-23", "2023-11-30", "93.351", "0.936", "2.0", "7.515", "-0.672", "1.633", "2.397", "-63.0", "0.558", "0.888", "0.848", "1.52", "2023-12-01", "20.0", "6.0", "0.667", "2023-12-04", "5.0", "7.0", "31.0", "36", "2023-12-11", "30.0", "93.0", "31.0", "27.0", "30.0", "93.0", "26.19", "2023-12-01", "0.0", "1.0", "32.0", "14.0", "7.0", "8", "0.221", "0.033", "0.248", "65.776", "0.128", "7.0"]], "shape": {"columns": 49, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>name</th>\n", "      <th>industry</th>\n", "      <th>recent_turndate</th>\n", "      <th>Period_TurnDate</th>\n", "      <th>Section_StartDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>Now_PRA_Rate</th>\n", "      <th>PostSecStart_MinPRADate2Now_RiseAvg</th>\n", "      <th>PostNowSec_PRA_MaxRate_BreachCount</th>\n", "      <th>...</th>\n", "      <th>Recent3Day_PRA_MaxRate_CoverDays</th>\n", "      <th>Recent3Day_VRA_MaxRate_CoverDays</th>\n", "      <th>peak2recentbottom_lastdays</th>\n", "      <th>recentbottom2now_lastdays</th>\n", "      <th>PostNowSec_PGV_MaxRollAvg</th>\n", "      <th>Turn2NowSec_PRA_LowBand</th>\n", "      <th>Turn2NowSec_PRA_UpBand</th>\n", "      <th>PostNowSec2PreNowPeak_PRV2Price_RatioDiff</th>\n", "      <th>PreNowPeak_PRV_Top3Mean</th>\n", "      <th>PullStart_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1710</th>\n", "      <td>000409.SZ</td>\n", "      <td>云鼎科技</td>\n", "      <td>计算机</td>\n", "      <td>2023-12-05</td>\n", "      <td>2023-10-23</td>\n", "      <td>2023-10-23</td>\n", "      <td>2023-11-30</td>\n", "      <td>93.351</td>\n", "      <td>0.936</td>\n", "      <td>2.0</td>\n", "      <td>...</td>\n", "      <td>32.0</td>\n", "      <td>14.0</td>\n", "      <td>7.0</td>\n", "      <td>8</td>\n", "      <td>0.221</td>\n", "      <td>0.033</td>\n", "      <td>0.248</td>\n", "      <td>65.776</td>\n", "      <td>0.128</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 49 columns</p>\n", "</div>"], "text/plain": ["        ts_code  name industry recent_turndate Period_TurnDate  \\\n", "1710  000409.SZ  云鼎科技      计算机      2023-12-05      2023-10-23   \n", "\n", "     Section_StartDate Now_SecDate  Now_PRA_Rate  \\\n", "1710        2023-10-23  2023-11-30        93.351   \n", "\n", "      PostSecStart_MinPRADate2Now_RiseAvg  PostNowSec_PRA_MaxRate_BreachCount  \\\n", "1710                                0.936                                 2.0   \n", "\n", "      ...  Recent3Day_PRA_MaxRate_CoverDays  Recent3Day_VRA_MaxRate_CoverDays  \\\n", "1710  ...                              32.0                              14.0   \n", "\n", "      peak2recentbottom_lastdays  recentbottom2now_lastdays  \\\n", "1710                         7.0                          8   \n", "\n", "      PostNowSec_PGV_MaxRollAvg  Turn2NowSec_PRA_LowBand  \\\n", "1710                      0.221                    0.033   \n", "\n", "      Turn2NowSec_PRA_UpBand  PostNowSec2PreNowPeak_PRV2Price_RatioDiff  \\\n", "1710                   0.248                                     65.776   \n", "\n", "      PreNowPeak_PRV_Top3Mean PullStart_Score  \n", "1710                    0.128             7.0  \n", "\n", "[1 rows x 49 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_sorted = pull_start_list[['ts_code', 'name', 'industry', 'recent_turndate', 'Period_TurnDate', 'Section_StartDate', 'Now_SecDate', \n", "                                          'Now_PRA_Rate', 'PostSecStart_MinPRADate2Now_RiseAvg',\n", "                                          'PostNowSec_PRA_MaxRate_BreachCount', 'Turn2NowSec_PRA_BandDiff', \n", "                                          'PRA2Cls_Percentile_Diff', 'PostNowSecMaxPRA_PostTurnRank5PRA_Diff', 'PostNowSecMaxPGV_PostTurnRank5PGV_Diff',\n", "                                          'NowSec_PRA2Close_CoverDays_Diff', \n", "                                          'PRA2Cls_Percentile_Ratio', \n", "                                          'Recent3Day_MaxPGV_Percentile_PostTurn', \n", "                                          'NowSec_MaxPRA_Percentile_PostTurn', \n", "                                          'PostNowSec_MaxCls_Percentile_PostTurn', \n", "                                          'Recent_MinPRA_Date',\n", "                                          'Recent_MinPRA_DownCoverDays',\n", "                                          'Recent_MinPRA2Now_Days',\n", "                                          'Recent_MinPRA_DownCover2PostSec_DaysProp',\n", "                                          'PostTurn_VGVRank5_NearNowDate',\n", "                                          'PostTurn_VGVRank5_NearNowDate_Days',\n", "                                          'PostNowSec_LastDays',\n", "                                          'PostSecStart_MinDaily2Now_LastDays',\n", "                                          'Recent_Neg42Now_Days',\n", "                                          'Recent_MaxPRA_Date',\n", "                                          'Recent_MaxPRA_PRA_CoverDays',\n", "                                          'Recent_MaxPRA_Cls_CoverDays',\n", "                                          'PostSecStart_PGV_MaxRollAvg2Now_LastDays',\n", "                                          'PostSecStart_PGV_MinRollAvg2Now_LastDays',\n", "                                          'NowSec_PGV_MaxRollAvg_UpCoverDays',\n", "                                          'NowSec_MaxClose_UpCoverDays',\n", "                                          'CoverDays_BreakDate_RatioBand',\n", "                                          'PostPreNowPeak_PGV_MinRollAvg_Date',\n", "                                          'BullStk_RecentPRA_Signal',\n", "                                          'prepost_recentbottom_daysdiff',\n", "                                          'Recent3Day_PRA_MaxRate_CoverDays', 'Recent3Day_VRA_MaxRate_CoverDays',\n", "                                          'peak2recentbottom_lastdays',\n", "                                          'recentbottom2now_lastdays','PostNowSec_PGV_MaxRollAvg', 'Turn2NowSec_PRA_LowBand', 'Turn2NowSec_PRA_UpBand', \n", "                                          'PostNowSec2PreNowPeak_PRV2Price_RatioDiff', 'PreNowPeak_PRV_Top3Mean', 'PullStart_Score']\n", "                                         ].sort_values(by=['Recent_MinPRA2Now_Days', 'Recent_MinPRA_DownCover2PostSec_DaysProp'], ascending=[True, False])\n", "pull_start_list_sorted"]}, {"cell_type": "code", "execution_count": 16, "id": "78bfdfd6", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "now_daily_ratio", "rawType": "float64", "type": "float"}, {"name": "now_high_ratio", "rawType": "float64", "type": "float"}, {"name": "Cal_Date", "rawType": "object", "type": "string"}], "ref": "56fdfebc-742b-4fc2-aa57-53daeb8fa7db", "rows": [["1757", "青鸟消防", "10.02", "10.02", "2025-03-04"], ["926", "*ST艾艾", "10.0", "10.0", "2025-03-04"], ["2083", "冠城新材", "9.92", "9.92", "2025-03-04"], ["1903", "信邦智能", "20.02", "20.02", "2025-03-04"], ["723", "*ST立航", "9.99", "9.99", "2025-03-04"], ["1054", "北化股份", "10.05", "10.05", "2025-03-04"], ["2597", "芯原股份", "20.01", "20.01", "2025-03-04"], ["2524", "安路科技", "20.0", "20.0", "2025-03-04"], ["703", "洪都航空", "10.0", "10.0", "2025-03-04"], ["766", "ST炼石", "9.94", "9.94", "2025-03-04"], ["3103", "智微智能", "10.0", "10.0", "2025-03-04"], ["3120", "久其软件", "9.97", "9.97", "2025-03-04"], ["1690", "宁波东力", "9.94", "9.94", "2025-03-04"], ["3136", "达华智能", "9.94", "9.94", "2025-03-04"], ["3161", "朗科科技", "19.98", "19.98", "2025-03-04"], ["791", "新兴装备", "9.99", "9.99", "2025-03-04"], ["2659", "紫光国微", "10.0", "10.0", "2025-03-04"], ["355", "华大智造", "20.0", "20.0", "2025-03-04"], ["200", "福建金森", "9.95", "9.95", "2025-03-04"], ["2487", "格林达", "9.99", "9.99", "2025-03-04"], ["2651", "好上好", "9.99", "9.99", "2025-03-04"], ["1543", "克来机电", "9.99", "9.99", "2025-03-04"], ["1740", "银宝山新", "9.99", "9.99", "2025-03-04"], ["1522", "捷昌驱动", "10.0", "10.0", "2025-03-04"], ["3162", "ST赛为", "20.0", "20.0", "2025-03-04"], ["2308", "兆威机电", "9.93", "10.0", "2025-03-04"], ["2996", "浙大网新", "10.02", "10.02", "2025-03-04"], ["831", "北方长龙", "20.01", "20.01", "2025-03-04"], ["1470", "杭齿前进", "10.01", "10.01", "2025-03-04"], ["2110", "万里股份", "10.02", "10.02", "2025-03-04"]], "shape": {"columns": 4, "rows": 30}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>now_daily_ratio</th>\n", "      <th>now_high_ratio</th>\n", "      <th>Cal_Date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1757</th>\n", "      <td>青鸟消防</td>\n", "      <td>10.02</td>\n", "      <td>10.02</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>926</th>\n", "      <td>*ST艾艾</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2083</th>\n", "      <td>冠城新材</td>\n", "      <td>9.92</td>\n", "      <td>9.92</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1903</th>\n", "      <td>信邦智能</td>\n", "      <td>20.02</td>\n", "      <td>20.02</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>723</th>\n", "      <td>*ST立航</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1054</th>\n", "      <td>北化股份</td>\n", "      <td>10.05</td>\n", "      <td>10.05</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2597</th>\n", "      <td>芯原股份</td>\n", "      <td>20.01</td>\n", "      <td>20.01</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2524</th>\n", "      <td>安路科技</td>\n", "      <td>20.00</td>\n", "      <td>20.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>703</th>\n", "      <td>洪都航空</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>766</th>\n", "      <td>ST炼石</td>\n", "      <td>9.94</td>\n", "      <td>9.94</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3103</th>\n", "      <td>智微智能</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3120</th>\n", "      <td>久其软件</td>\n", "      <td>9.97</td>\n", "      <td>9.97</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1690</th>\n", "      <td>宁波东力</td>\n", "      <td>9.94</td>\n", "      <td>9.94</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3136</th>\n", "      <td>达华智能</td>\n", "      <td>9.94</td>\n", "      <td>9.94</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3161</th>\n", "      <td>朗科科技</td>\n", "      <td>19.98</td>\n", "      <td>19.98</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>791</th>\n", "      <td>新兴装备</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2659</th>\n", "      <td>紫光国微</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>355</th>\n", "      <td>华大智造</td>\n", "      <td>20.00</td>\n", "      <td>20.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>福建金森</td>\n", "      <td>9.95</td>\n", "      <td>9.95</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2487</th>\n", "      <td>格林达</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2651</th>\n", "      <td>好上好</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1543</th>\n", "      <td>克来机电</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1740</th>\n", "      <td>银宝山新</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1522</th>\n", "      <td>捷昌驱动</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3162</th>\n", "      <td>ST赛为</td>\n", "      <td>20.00</td>\n", "      <td>20.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2308</th>\n", "      <td>兆威机电</td>\n", "      <td>9.93</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2996</th>\n", "      <td>浙大网新</td>\n", "      <td>10.02</td>\n", "      <td>10.02</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>831</th>\n", "      <td>北方长龙</td>\n", "      <td>20.01</td>\n", "      <td>20.01</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1470</th>\n", "      <td>杭齿前进</td>\n", "      <td>10.01</td>\n", "      <td>10.01</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2110</th>\n", "      <td>万里股份</td>\n", "      <td>10.02</td>\n", "      <td>10.02</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       name  now_daily_ratio  now_high_ratio    Cal_Date\n", "1757   青鸟消防            10.02           10.02  2025-03-04\n", "926   *ST艾艾            10.00           10.00  2025-03-04\n", "2083   冠城新材             9.92            9.92  2025-03-04\n", "1903   信邦智能            20.02           20.02  2025-03-04\n", "723   *ST立航             9.99            9.99  2025-03-04\n", "1054   北化股份            10.05           10.05  2025-03-04\n", "2597   芯原股份            20.01           20.01  2025-03-04\n", "2524   安路科技            20.00           20.00  2025-03-04\n", "703    洪都航空            10.00           10.00  2025-03-04\n", "766    ST炼石             9.94            9.94  2025-03-04\n", "3103   智微智能            10.00           10.00  2025-03-04\n", "3120   久其软件             9.97            9.97  2025-03-04\n", "1690   宁波东力             9.94            9.94  2025-03-04\n", "3136   达华智能             9.94            9.94  2025-03-04\n", "3161   朗科科技            19.98           19.98  2025-03-04\n", "791    新兴装备             9.99            9.99  2025-03-04\n", "2659   紫光国微            10.00           10.00  2025-03-04\n", "355    华大智造            20.00           20.00  2025-03-04\n", "200    福建金森             9.95            9.95  2025-03-04\n", "2487    格林达             9.99            9.99  2025-03-04\n", "2651    好上好             9.99            9.99  2025-03-04\n", "1543   克来机电             9.99            9.99  2025-03-04\n", "1740   银宝山新             9.99            9.99  2025-03-04\n", "1522   捷昌驱动            10.00           10.00  2025-03-04\n", "3162   ST赛为            20.00           20.00  2025-03-04\n", "2308   兆威机电             9.93           10.00  2025-03-04\n", "2996   浙大网新            10.02           10.02  2025-03-04\n", "831    北方长龙            20.01           20.01  2025-03-04\n", "1470   杭齿前进            10.01           10.01  2025-03-04\n", "2110   万里股份            10.02           10.02  2025-03-04"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# recent_list = result_df_recentindus['ts_code'].values.tolist()\n", "pull_start_list[['name', 'now_daily_ratio', 'now_high_ratio', 'Cal_Date']]"]}, {"cell_type": "code", "execution_count": null, "id": "aa947628", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "recent2bottom_days", "rawType": "int64", "type": "integer"}, {"name": "nowsec_recent_diff", "rawType": "int64", "type": "integer"}], "ref": "05ac3f71-a7a9-4f30-b6bd-a3999c79899a", "rows": [["739", "2025-05-15", "4", "4"]], "shape": {"columns": 3, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Now_SecDate</th>\n", "      <th>recent2bottom_days</th>\n", "      <th>nowsec_recent_diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>739</th>\n", "      <td>2025-05-15</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Now_SecDate  recent2bottom_days  nowsec_recent_diff\n", "739  2025-05-15                   4                   4"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list.query('ts_code==\"688395.SH\"')[['Now_SecDate', 'recent2bottom_days', 'nowsec_recent_diff']]"]}, {"cell_type": "code", "execution_count": 20, "id": "8f7c13e2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:381: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:565: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:636: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["行业: 农林牧渔, 回撤转折日期: 2025-01-24, 转折日期: 2025-01-27, 转折回升幅度: 0.98, 下行天数: 276, 下行幅度: -25.77\n", "行业: 建筑材料, 回撤转折日期: 2025-01-24, 转折日期: 2024-10-31, 转折回升幅度: 1.36, 下行天数: 396, 下行幅度: -34.11\n", "行业: 轻工制造, 回撤转折日期: 2025-01-23, 转折日期: 2024-12-13, 转折回升幅度: 1.25, 下行天数: 146, 下行幅度: -28.08\n", "行业: 银行, 回撤转折日期: 2025-01-22, 转折日期: 2025-01-10, 转折回升幅度: 4.19, 下行天数: 48, 下行幅度: -27.04\n", "行业: 建筑装饰, 回撤转折日期: 2025-01-22, 转折日期: 2024-12-06, 转折回升幅度: 0.73, 下行天数: 345, 下行幅度: -29.11\n", "行业: 钢铁, 回撤转折日期: 2025-01-21, 转折日期: 2024-12-11, 转折回升幅度: 2.78, 下行天数: 379, 下行幅度: -19.38\n", "行业: 非银金融, 回撤转折日期: 2025-01-10, 转折日期: 2025-01-24, 转折回升幅度: 1.00, 下行天数: 46, 下行幅度: -17.09\n", "行业: 传媒, 回撤转折日期: 2025-01-06, 转折日期: 2024-12-13, 转折回升幅度: 6.60, 下行天数: 266, 下行幅度: -58.47\n", "行业: 计算机, 回撤转折日期: 2025-01-06, 转折日期: 2024-12-10, 转折回升幅度: 7.68, 下行天数: 290, 下行幅度: -51.09\n", "处理股票 000972.SZ 时出错: single positional indexer is out-of-bounds\n", "行业排序结果:\n", "1. 计算机: 34.72, 涨停数/总数:7/333\n", "2. 传媒: 20.25, 涨停数/总数:4/129\n", "3. 轻工制造: 6.96, 涨停数/总数:1/153\n", "4. 非银金融: 5.38, 涨停数/总数:2/83\n", "5. 建筑装饰: 4.68, 涨停数/总数:1/157\n", "6. 农林牧渔: 3.94, 涨停数/总数:1/100\n", "7. 钢铁: 2.44, 涨停数/总数:0/45\n", "8. 建筑材料: 2.27, 涨停数/总数:2/71\n", "9. 银行: 1.46, 涨停数/总数:0/42\n", "未存储筛选股票数据\n", "PullStart股票行业分布：\n", "           count\n", "industry       \n", "计算机           3\n", "非银金融          2\n", "传媒            1\n", "建筑装饰          1\n", "轻工制造          0\n", "农林牧渔          0\n", "钢铁            0\n", "建筑材料          0\n", "银行            0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2119: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_start_list['pullstart_sort_position'] = pull_start_list.groupby('industry').cumcount() + 1\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2340: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_indus_count = pull_start_list[['ts_code', 'industry']].groupby('industry').count()\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2366: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  by=['industry', 'stk_checkperiod_sumratio'], ascending=[True, False]).groupby('industry').head(20)\n"]}], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "# trade_date = get_trade_date(start_date='2025-02-05', end_date='2025-03-06')\n", "# trade_date = trade_date[::-1]\n", "trade_date = ['2025-01-27']\n", "for date in trade_date:\n", "    trend_industry_list = ['轻工制造', '银行', '建筑装饰', '钢铁', '非银金融', '计算机', '传媒']\n", "    recent_industry_list = ['农林牧渔', '建筑材料']\n", "    end_date, trend_startdate = date, '2024-12-31'\n", "    recent_turndate = ['2025-01-10', '2025-01-24','2025-01-06']\n", "    result_df_head, result_df_turn, pull_start_list, pull_start_list_core, result_df_recentindus = track_pullstart_stocks(\n", "        end_date=end_date, trend_startdate=trend_startdate,\n", "        recent_turndate=recent_turndate,\n", "        rise_stop_signal=True,\n", "        industry_list=trend_industry_list,\n", "        limit_num=80, store_mode=False,\n", "        recent_indus=recent_industry_list,\n", "        recentindus_calstartdate='2024-12-31')"]}, {"cell_type": "code", "execution_count": 6, "id": "8901bbf2", "metadata": {}, "outputs": [], "source": ["pull_start_list_adj = pull_start_list.sort_values(by=['industry', 'PostNowSec2PreNowPeak_PRV2Price_RatioDiff'], ascending=[True, True])"]}, {"cell_type": "code", "execution_count": null, "id": "bb02a5b7", "metadata": {}, "outputs": [], "source": ["pull_start_list2 = pull_start_list.query('BreakPreNowPeak_Ratio<2.5 & BreakPreNowPeak_Ratio>(-25)')"]}, {"cell_type": "code", "execution_count": null, "id": "948e45b5", "metadata": {}, "outputs": [{"data": {"text/plain": ["['600259.SH',\n", " '000017.SZ',\n", " '002842.SZ',\n", " '300511.SZ',\n", " '300306.SZ',\n", " '000014.SZ',\n", " '603444.SH',\n", " '002631.SZ',\n", " '300805.SZ',\n", " '601801.SH',\n", " '603895.SH',\n", " '002686.SZ',\n", " '603665.SH',\n", " '300233.SZ',\n", " '300031.SZ',\n", " '301200.SZ',\n", " '300467.SZ',\n", " '605080.SH',\n", " '603082.SH',\n", " '300553.SZ']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_core['ts_code'].values.tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "1bae9472", "metadata": {}, "outputs": [], "source": ["pull_start_list_repick = pull_start_list.copy()\n", "pull_start_list_repick['PreNowSec_CheckSignal'] = pull_start_list_repick.apply(\n", "    lambda fn: 1 if fn['PreNowSec_SumRatio']==fn['PostSecStart_Sec_MaxDrop_SumRatio'] or \n", "                    # fn['PreNowSec_AvgRatio'] == fn['PostSecStart_Sec_MaxDrop_AvgRatio'] or \n", "                    fn['PreNowSec_LastDays'] == fn['PostSecStart_Sec_MaxDrop_LastDays']\n", "                    else 0, axis=1)\n", "pull_start_list_repick['Pre_PreNowSec_CheckSignal'] = pull_start_list_repick.apply(\n", "    lambda fn: 1 if fn['Pre_PreNow_Sec_AvgRatio'] < fn['PostSecStart_Sec_Max_AvgRatio'] else 0, axis=1)\n", "pull_start_list_repick = pull_start_list_repick.query('PreNowSec_CheckSignal==1')\n"]}, {"cell_type": "code", "execution_count": null, "id": "96666679", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "PreNowSec_CheckSignal", "rawType": "int64", "type": "integer"}, {"name": "Pre_PreNowSec_CheckSignal", "rawType": "int64", "type": "integer"}, {"name": "Pre_PreNow_Sec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Sec_Max_AvgRatio", "rawType": "float64", "type": "float"}], "ref": "616681e1-01cc-4b90-8574-b7266f668b9e", "rows": [["1374", "1", "1", "1.376", "3.382"]], "shape": {"columns": 4, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PreNowSec_CheckSignal</th>\n", "      <th>Pre_PreNowSec_CheckSignal</th>\n", "      <th>Pre_PreNow_Sec_AvgRatio</th>\n", "      <th>PostSecStart_Sec_Max_AvgRatio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1374</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.376</td>\n", "      <td>3.382</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      PreNowSec_CheckSignal  Pre_PreNowSec_CheckSignal  \\\n", "1374                      1                          1   \n", "\n", "      Pre_PreNow_Sec_AvgRatio  PostSecStart_Sec_Max_AvgRatio  \n", "1374                    1.376                          3.382  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_repick.query('ts_code==\"605099.SH\"')[['PreNowSec_CheckSignal', 'Pre_PreNowSec_CheckSignal', 'Pre_PreNow_Sec_AvgRatio', 'PostSecStart_Sec_Max_AvgRatio']]"]}, {"cell_type": "code", "execution_count": null, "id": "30d8c625", "metadata": {}, "outputs": [{"data": {"text/plain": ["['300236.SZ', '300429.SZ', '605588.SH', '688010.SH', '601665.SH', '605365.SH']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_core['ts_code'].values.tolist()"]}, {"cell_type": "code", "execution_count": 1, "id": "ba42442e", "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_ModelFunc import double_check_pullstart_stocks\n", "double_check_result, recent_doublecheck_data = double_check_pullstart_stocks(double_checkdate='2025-08-25', former_turn_date=['2025-07-31', '2025-08-14'], \n", "                                                    recent_turn_date='2025-08-22', downcheck_point='pick_date')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}