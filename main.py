# 每日任务主程序
from function_ai.Func_Base import get_trade_date
import time
import function_ai.StkQuota_Func_V7 as stkpick3
import data_update.data_func as datafunc


def ai_update(step1='', step2='', step3=''):
    if step1 == '':
        step1 = input('是否执行数据更新？(y/n)')

    # if step3 == '':
    #     step3 = input('是否执行转折点强势行业筛选？(y/n)')
    # recent_turn_date = ['2021-11-02', '2021-11-30', '2021-12-08', '2021-12-13',
    #                     '2021-12-20', '2022-01-28', '2022-02-14', '2022-03-15',
    #                     '2022-04-26', '2022-07-15', '2022-08-03', '2022-09-01',
    #                     '2022-09-26', '2022-10-11', '2022-10-31']
    #
    # indus_turn = None
    # if step3 == 'y':
    #     indus_turn = '2022-04-26'
    get_signal = False
    """ 数据更新,获取上交所/深交所股票最新收盘数据"""
    if step1 == 'y':
        print('第1/3步: 获取最新股票数据')
        starttime1 = time.time()
        while not get_signal:
            get_signal = datafunc.data_update()
        endtime1 = time.time()
        # level1_data = swf.level1_update()
        print('第一步 更新数据耗费时间:' + str(endtime1 - starttime1))
    else:
        get_signal = True

    """数据更新,获取CCTV新闻数据"""
    if step2 == 'y' and (step1 != 'y' or get_signal):
        print('第2/3步:获取CCTV新闻稿数据')
        get_signal = False
        while not get_signal:
            get_signal = datafunc.cctvnews_update()
    else:
        get_signal = True

    """测算数据"""
    if step3 == 'y' and ((step1 != 'y' and step2 != 'y') or get_signal):
        print('第3/3步: 筛选品种')
        time.sleep(60)
        starttime3 = time.time()
        # 按最近转折点筛选强势行业及品种
        # recent_turn_adj = [i for i in recent_turn_date if i >= indus_turn]
        # ResultBreak, ResultIndus = stkpick2.stksfit_result_2(recent_turn_adj, pickmode='pick')
        # print('完成转折点' + '/'.join(recent_turn_adj) + '日期行业测算')

        trade_dates = get_trade_date()

        # 筛选强势行业2_1
        # ResultBreak_N = stkpick2_1.stksfit_result_2_1(end_date=trade_dates[-1],
        #                                               turn_date=recent_turn_adj)

        resultB = stkpick3.stksfit_result_3(end_date=trade_dates[-1], mode='pick')
        endtime3 = time.time()
        print('第三步 转折点筛选耗费时间:' + str(endtime3 - starttime3))
    return


def run_sched():
    t_mode = input('输入选择定时任务("d")或实时任务("s")')
    if t_mode == 'd':
        scheduler = BlockingScheduler()
        scheduler.add_job(ai_update, 'cron', hour=16, minute=45, args=['y', 'y'])
        try:
            scheduler.start()
        except (KeyboardInterrupt, SystemExit):
            scheduler.remove_job(ai_update)
            scheduler.shutdown(wait=False)
    elif t_mode == 's':
        ai_update(step1='y', step2='y', step3='n')
    else:
        print('参数输入错误')
    return


if __name__ == '__main__':
    run_sched()
