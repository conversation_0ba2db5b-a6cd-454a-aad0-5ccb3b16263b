"""计算日内相对均价价差数据"""
import datetime
import random
import time
import pdb
import traceback

import numpy as np
import pandas as pd
from sympy import false
import tushare as ts
from tqdm import tqdm
import matplotlib.pyplot as plt
from scipy import stats
from dateutil.relativedelta import relativedelta
import ruptures as rpt

import os
import sys

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from function_ai.StkPick_Func_V7 import get_result_3
from function_ai.Func_Base import (get_trade_date, get_stock_info, get_stock_data)

pro = ts.pro_api('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')
# ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')

def cal_gapindex(Result_Loc, Result_Common, end_date, trade_df, data_source='api'):
    """计算各种Gap指标

    主要功能:
    - 计算股票日内相对均价的各种价差指标
    - 包括峰值差(Peak Gap)和谷值差(Valley Gap)的统计指标
    - 计算不同时间段的价差变化趋势

    参数:
    Result_Loc: DataFrame的一行,包含股票基本信息和技术指标
    end_date: 计算截止日期
    trade_df: 交易日历数据
    data_source: 数据来源,可选'api'或'local'

    返回:
    Result_Loc: 计算完成后的DataFrame行数据"""
    # 获取股票基本信息
    ts_code = Result_Common['ts_code']  # 股票代码
    total_share = Result_Common['Total_Share']  # 总股本

    # 获取各个关键日期
    Section_PeakDate = Result_Common['Section_PeakDate']  # 区间峰值日期
    Section_StartDate = Result_Common['Section_StartDate']  # 区间起始日期
    Now_SecDate = Result_Common['Now_SecDate']  # 当前区间日期
    PreNow_SecDate = Result_Common['PreNow_SecDate']  # 前一区间日期
    PreNow_PeakDate = Result_Common['PreNow_PeakDate']  # 前一峰值日期
    PreNow_BottomDate = Result_Common['PreNow_BottomDate']  # 前一谷值日期
    PostSecStart_PeakDate = Result_Common['PostSecStart_PeakDate']  # 区间起始后峰值日期
    Period_TurnDate = Result_Common['Period_TurnDate']  # 周期转折日期
    PreTurn_PeakDate = Result_Common['PreTurn_PeakDate']  # 转折前峰值日期
    PreTurnPeak_Sec_StartDate = Result_Common['PreTurnPeak_Sec_StartDate']  # 转折前峰值区间起始日期
    PreSecPeak_Sec_StartDate =Result_Common['PreSecPeak_Sec_StartDate']  # 前一峰值区间起始日期
    PostTurn_PeakDate = Result_Common['PostTurn_PeakDate']  # 转折后峰值日期
    SecConcave_StartDate = Result_Common['SecConcave_StartDate']  # 区间凹点起始日期
    PostPeak_Recent_Neg4_DropDate = Result_Common['PostPeak_Recent_Neg4_DropDate']  # 最近日跌幅日期
    NowSec_SecConcave_StartDate = Result_Common['NowSec_SecConcave_StartDate']  # 当前区间凹点起始日期
    PostNowSec_LastDays = Result_Common['PostNowSec_LastDays']  # 当前区间后天数
    PostSecStart_LastDays = Result_Common['PostSecStart_LastDays']  # 区间起始后天数

    # 检查关键日期是否存在缺失
    if pd.isnull(Section_PeakDate) or pd.isnull(Section_StartDate) or pd.isnull(Now_SecDate) \
            or pd.isnull(PreNow_SecDate) or pd.isnull(PreTurn_PeakDate):
        return Result_Loc

    # 计算指标起始日期:取转折前峰值日期、区间峰值日期和一年前日期的较早者
    indicatore_startdate = PreTurn_PeakDate \
        if PreTurn_PeakDate <= (pd.to_datetime(end_date, format='%Y-%m-%d') - relativedelta(years=2)).strftime('%Y-%m-%d') \
        else (pd.to_datetime(end_date, format='%Y-%m-%d') - relativedelta(years=2)).strftime('%Y-%m-%d')

    # 起始日期前5天
    trade_df_adj = trade_df[(trade_df<indicatore_startdate)]
    mindata_startdate = trade_df_adj[-min(6, len(trade_df_adj))]
    # 获取分钟级别指标数据
    gap_data = get_min_indicators(stk_code=ts_code, start_date=mindata_startdate, end_date=end_date,
                                  trade_df=trade_df, data_source=data_source, total_share=total_share)

    # 如果gap_data存在且不为空,计算各项指标
    if gap_data is not None and len(gap_data) > 0:
        try:
            # 按日期排序并设置索引
            gap_data = gap_data.sort_values(by='trade_date', ascending=True)
            gap_data = gap_data.set_index('trade_date', drop=False)

            # 预先计算常用的数据切片,避免重复计算
            gap_data_now_sec = gap_data.loc[Now_SecDate:].copy()
            gap_data_section_start = gap_data.loc[Section_StartDate:].copy()
            gap_data_prenow_bottom = gap_data.loc[PreNow_BottomDate:].copy()
            gap_data_period_turn = gap_data.loc[Period_TurnDate:].copy()
            gap_data_section_peak = gap_data.loc[Section_PeakDate:Section_StartDate].copy()
            gap_data_secpeak_nowsec = gap_data.loc[Section_PeakDate:].copy()

            # 计算当前区间后的峰值差指标
            Result_Loc['PostNowSec_MaxPeakGap'] = round(gap_data_now_sec['peakgap_ratio'].max(), 3)
            Result_Loc['PostNowSec_MaxPeakGapValue'] = round(gap_data_now_sec['peak_gap'].max(), 3)
            Result_Loc['PostNowSec_MaxPeakGapValue_Date'] = gap_data_now_sec['peak_gap'].idxmax()
            Result_Loc['PostNowSec_MinPeakGap'] = round(gap_data_now_sec['peakgap_ratio'].min(), 3)
            Result_Loc['PostNowSec_MedianPeakGap'] = round(gap_data_now_sec['peakgap_ratio'].median(), 3)
            Result_Loc['PostNowSec_MedianPeakGapValue'] = round(gap_data_now_sec['peak_gap'].median(), 3)
            Result_Loc['PostNowSec_MedianValleyGapValue'] = round(gap_data_now_sec['valley_gap'].median(), 3)
            Result_Loc['PostNowSec_MaxValleyGap'] = round(gap_data_now_sec['valleygap_ratio'].max(), 3)
            Result_Loc['PostNowSec_MaxValleyGapValue'] = round(gap_data_now_sec['valley_gap'].max(), 3)

            # 计算区间峰值后的指标,注意处理数据长度为1的情况
            post_peak_data = gap_data.loc[PostSecStart_PeakDate:]
            if len(post_peak_data) > 0:
                Result_Loc['PostSecPeak_MaxValleyGapValue'] = round(post_peak_data['valley_gap'].sort_values(
                    ascending=False).head(min(3, len(post_peak_data))).mean(), 3)
                Result_Loc['PostSecPeak_MinValleyGapValue'] = round(post_peak_data['valley_gap'].sort_values(
                    ascending=True).head(min(3, len(post_peak_data))).mean(), 3)
                Result_Loc['PostSecPeak_MaxPeakGapValue'] = round(post_peak_data['peak_gap'].sort_values(
                    ascending=False).head(min(3, len(post_peak_data))).mean(), 3)
                Result_Loc['PostSecPeak_MinPeakGapValue'] = round(post_peak_data['peak_gap'].sort_values(
                    ascending=True).head(min(3, len(post_peak_data))).mean(), 3)

                # 计算区间峰值后的中位数指标
                Result_Loc['PostSecPeak_MedianPeakGapValue'] = round(post_peak_data.iloc[1:]['peak_gap'].median(), 3)
                Result_Loc['PostSecPeak_MedianValleyGapValue'] = round(post_peak_data.iloc[1:]['valley_gap'].median(), 3)

            # 计算区间起始后的指标
            Result_Loc['PostSecStart_MaxPeakGap'] = round(gap_data_section_start['peakgap_ratio'].max(), 3)
            # Result_Loc['PostSecStart_MaxPeakGapValue'] = round(gap_data_section_start['peak_gap'].sort_values(
            #     ).tail(min(3, len(gap_data_section_start))).mean(), 3)
            Result_Loc['PostSecStart_MaxValleyGap'] = round(gap_data_section_start['valleygap_ratio'].max(), 3)
            Result_Loc['PostSecStart_MaxValleyGapValue'] = round(gap_data_section_start['valley_gap'].sort_values(
                ascending=False).head(min(3, len(gap_data_section_start))).mean(), 3)
            Result_Loc['PostSecStart_MinPeakGap'] = round(gap_data_section_start['peakgap_ratio'].min(), 3)
            Result_Loc['PostSecStart_MinValleyGap'] = round(gap_data_section_start['valleygap_ratio'].min(), 3)
            Result_Loc['PostSecStart_MedianPeakGap'] = round(gap_data_section_start['peakgap_ratio'].median(), 3)
            Result_Loc['PostSecStart_MedianPeakGapValue'] = round(gap_data_section_start['peak_gap'].median(), 3)
            Result_Loc['PostSecStart_MedianValleyGap'] = round(gap_data_section_start['valleygap_ratio'].median(), 3)
            Result_Loc['PostSecStart_MedianValleyGapValue'] = round(gap_data_section_start['valley_gap'].median(), 3)

            # 计算当前指标
            Result_Loc['Now_PeakGap'] = round(gap_data['peakgap_ratio'].iloc[-1], 3)
            Result_Loc['Now_PeakGapValue'] = round(gap_data['peak_gap'].iloc[-1], 3)
            Result_Loc['Now_ValleyGap'] = round(gap_data['valleygap_ratio'].iloc[-1], 3)
            Result_Loc['Now_ValleyGapValue'] = round(gap_data['valley_gap'].iloc[-1], 3)

            # 计算最近3天的指标
            last_3_days = gap_data.iloc[-3:]
            Result_Loc['Recent3Day_MaxPeakGapValue'] = round(last_3_days['peak_gap'].max(), 3)
            Result_Loc['Recent3Day_MaxValleyGapValue'] = round(last_3_days['valley_gap'].max(), 3)
            Result_Loc['Recent3Day_MinPeakGapValue'] = round(last_3_days['peak_gap'].min(), 3)
            Result_Loc['Recent3Day_MinValleyGapValue'] = round(last_3_days['valley_gap'].min(), 3)

            # 计算峰值相关指标
            Result_Loc['Peak3Day_MaxPeakGapValue'] = round(gap_data.loc[:PostSecStart_PeakDate].iloc[-3:]['peak_gap'].max(), 3)
            Result_Loc['Peak2Sec_MaxPeakGap'] = round(gap_data_section_peak.iloc[1:]['peakgap_ratio'].max(), 3)
            Result_Loc['Peak2Sec_MaxPeakGapValue'] = round(gap_data_section_peak.iloc[1:]['peak_gap'].max(), 3)
            Result_Loc['Peak2Sec_MaxValleyGap'] = round(gap_data_section_peak.iloc[1:]['valleygap_ratio'].max(), 3)
            Result_Loc['Peak2Sec_MaxValleyGapValue'] = round(gap_data_section_peak.iloc[1:]['valley_gap'].max(), 3)

            # 计算前一区间的指标
            Result_Loc['PreNowSec_MinPeakGap'] = round(gap_data.loc[PreNow_SecDate:]['peakgap_ratio'].min(), 3)
            Result_Loc['PreNowSec_MinPeakGap_Date'] = gap_data.loc[PreNow_SecDate:]['peakgap_ratio'].idxmin()
            Result_Loc['PreNowSec_MinValleyGap'] = round(gap_data.loc[PreNow_SecDate:]['valleygap_ratio'].min(), 3)
            Result_Loc['PreNowSec_MaxValleyGap'] = round(gap_data.loc[PreNow_SecDate:]['valleygap_ratio'].max(), 3)
            Result_Loc['PreNowSec_MaxValleyGapValue'] = round(gap_data.loc[PreNow_SecDate:]['valley_gap'].max(), 3)
            Result_Loc['PreNowSec_MedianPeakGap'] = round(gap_data.loc[PreNow_SecDate:]['peakgap_ratio'].median(), 3)
            Result_Loc['PreNowSec_MedianValleyGap'] = round(gap_data.loc[PreNow_SecDate:]['valleygap_ratio'].median(), 3)

            # 计算分位数指标
            Result_Loc['PostSecStart_PeakGap_HighQuntl'] = round(
                gap_data_section_start['peakgap_ratio'].iloc[:-1].mean() +
                gap_data_section_start['peakgap_ratio'].iloc[:-1].std(), 3)
            Result_Loc['PostSecStart_PeakGapValue_HighQuntl'] = round(
                gap_data_section_start['peak_gap'].iloc[:-1].mean() +
                gap_data_section_start['peak_gap'].iloc[:-1].std(), 3)
            Result_Loc['PostSecStart_ValleyGap_HighQuntl'] = round(
                gap_data_section_start['valleygap_ratio'].iloc[:-1].mean() +
                gap_data_section_start['valleygap_ratio'].iloc[:-1].std(), 3)
            Result_Loc['PostSecStart_ValleyGapValue_HighQuntl'] = round(
                gap_data_section_start['valley_gap'].iloc[:-1].mean() +
                gap_data_section_start['valley_gap'].iloc[:-1].std(), 3)
            Result_Loc['PostSecStart_PeakGap_LowQuntl'] = round(
                gap_data_section_start['peakgap_ratio'].iloc[:-1].mean() -
                gap_data_section_start['peakgap_ratio'].iloc[:-1].std(), 3)
            Result_Loc['PostSecStart_PeakGapValue_LowQuntl'] = round(
                gap_data_section_start['peak_gap'].iloc[:-1].mean() -
                gap_data_section_start['peak_gap'].iloc[:-1].std(), 3)
            Result_Loc['PostSecStart_ValleyGap_LowQuntl'] = round(
                gap_data_section_start['valleygap_ratio'].iloc[:-1].mean() -
                gap_data_section_start['valleygap_ratio'].iloc[:-1].std(), 3)
            Result_Loc['PostSecStart_ValleyGapValue_LowQuntl'] = round(
                gap_data_section_start['valley_gap'].iloc[:-1].mean() -
                gap_data_section_start['valley_gap'].iloc[:-1].std(), 3)
            Result_Loc['PostTurn_MedianValleyGapValue'] = round(gap_data_period_turn['valley_gap'].median(), 3)
            Result_Loc['PostTurn_ValleyGapValue_HighQuntl'] = round(
                gap_data_period_turn['valley_gap'].median() +
                gap_data_period_turn['valley_gap'].std(), 3)
            Result_Loc['PostTurn_MaxValleyGapValue'] = round(gap_data_period_turn['valley_gap'].max(), 3)
            Result_Loc['PostTurn_MedianPeakGapValue'] = round(gap_data_period_turn['peak_gap'].median(), 3)
            Result_Loc['PostTurn_PeakGapValue_HighQuntl'] = round(
                gap_data_period_turn['peak_gap'].median() +
                gap_data_period_turn['peak_gap'].std(), 3)
            Result_Loc['PostTurn_MaxPeakGapValue'] = round(gap_data_period_turn['peak_gap'].max(), 3)

            # 计算Section_PeakDate到Section_StartDate期间的高分位指标
            Result_Loc['Peak2Sec_PeakGap_HighQuntl'] = round(
                gap_data_section_peak['peakgap_ratio'].iloc[:-1].mean() +
                gap_data_section_peak['peakgap_ratio'].iloc[:-1].std(), 3)
            Result_Loc['Peak2Sec_ValleyGap_HighQuntl'] = round(
                gap_data_section_peak['valleygap_ratio'].iloc[:-1].mean() +
                gap_data_section_peak['valleygap_ratio'].iloc[:-1].std(), 3)
            Result_Loc['Peak2Sec_PeakGapValue_HighQuntl'] = round(
                gap_data_section_peak['peak_gap'].iloc[:-1].mean() +
                gap_data_section_peak['peak_gap'].iloc[:-1].std(), 3)
            Result_Loc['Peak2Sec_ValleyGapValue_HighQuntl'] = round(
                gap_data_section_peak['valley_gap'].iloc[:-1].mean() +
                gap_data_section_peak['valley_gap'].iloc[:-1].std(), 3)
            Result_Loc['Peak2Sec_MedianValleyGapValue'] = round(
                gap_data_section_peak['valley_gap'].iloc[:-1].median(), 3)
            Result_Loc['Peak2Sec_PeakGap_LowQuntl'] = round(
                gap_data_section_peak['peakgap_ratio'].iloc[:-1].mean() -
                gap_data_section_peak['peakgap_ratio'].iloc[:-1].std(), 3)

            # 仅当有足够数据时(>1)计算滚动均值相关指标
            if len(gap_data_section_peak) > 1:
                # 计算pgv_rollavg的最小值
                Result_Loc['Peak2Sec_PGV_MinRollAvg'] = round(
                    gap_data_section_peak['pgv_rollavg'].min(), 3)
                # 获取pgv_rollavg最小值对应的日期
                Result_Loc['Peak2Sec_PGV_MinRollAvg_Date'] = gap_data_section_peak['pgv_rollavg'].idxmin()
                # 计算最小值与其他值均值的比率(避免除零错误)
                mean_value = gap_data_section_peak['pgv_rollavg'].sort_values(ascending=True).iloc[1:].mean()
                Result_Loc['Peak2Sec_PGV_MinRollAvg2MeanRatio'] = round(
                    Result_Loc['Peak2Sec_PGV_MinRollAvg'] / mean_value, 3) if mean_value != 0 else 0
                # 计算最小值日期到Section_StartDate的天数
                Result_Loc['Peak2Sec_PGV_MinRollAvg2Sec_LastDays'] = len(
                    gap_data_section_peak.loc[Result_Loc['Peak2Sec_PGV_MinRollAvg_Date']:Section_StartDate]) - 1 \
                    if pd.notnull(Result_Loc['Peak2Sec_PGV_MinRollAvg_Date']) else 100
                # 计算最小值日期到Section_StartDate期间的累计涨幅
                if pd.notnull(Result_Loc['Peak2Sec_PGV_MinRollAvg_Date']) \
                       and len(gap_data_section_peak.loc[Result_Loc['Peak2Sec_PGV_MinRollAvg_Date']:]) > 1:
                    daily_ratios = gap_data_section_peak.loc[Result_Loc['Peak2Sec_PGV_MinRollAvg_Date']:Section_StartDate,
                                              'daily_ratio'].iloc[1:] / 100 + 1
                    Result_Loc['Peak2Sec_PGV_MinRollAvg2Sec_SumRatio'] = round((daily_ratios.product() - 1) * 100, 3)
                else:
                    Result_Loc['Peak2Sec_PGV_MinRollAvg2Sec_SumRatio'] = 0
                # 计算pgv_rollavg的最大值
                Result_Loc['Peak2Sec_PGV_MaxRollAvg'] = round(
                    gap_data_section_peak['pgv_rollavg'].max(), 3)
                # 计算pgv_rollavg的33%分位数
                Result_Loc['Peak2Sec_PGV_RollAvg_LowQuntl'] = round(
                    gap_data_section_peak['pgv_rollavg'].quantile(0.33), 3)
                # 计算当前值与33%分位数的比率(避免除零错误)
                low_quantile = Result_Loc['Peak2Sec_PGV_RollAvg_LowQuntl']
                Result_Loc['PGVRollAvg_Now2PreSecLowQuntl_Ratio'] = round(
                    gap_data['pgv_rollavg'].iloc[-1] / low_quantile, 3) if low_quantile > 0 else 0
                # 计算pgv_rollavg的均值
                Result_Loc['Peak2Sec_PGV_MeanRollAvg'] = round(
                    gap_data_section_peak['pgv_rollavg'].mean(), 3)
                # 计算pgv_rollavg的波动区间(66%分位数-33%分位数)
                Result_Loc['Peak2Sec_PGV_RollAvg_VolRange'] = round(
                    gap_data_section_peak['pgv_rollavg'].quantile(0.66) -
                    gap_data_section_peak['pgv_rollavg'].quantile(0.33), 3)

            # 计算前一谷值日期后的最大峰值差和谷值差
            Result_Loc['PostSecBottom_MaxPeakGapValue'] = round(gap_data_prenow_bottom['peak_gap'].max(), 3)
            Result_Loc['PostSecBottom_MaxValleyGapValue'] = round(gap_data_prenow_bottom['valley_gap'].max(), 3)

            # 计算最近3天内的最大valley_gap值
            Result_Loc['NowSec_Recent3D_MaxValleyGapValue'] = round(
                gap_data_now_sec.iloc[-min(3, len(gap_data_now_sec)):]['valley_gap'].max(), 3)

            # 计算前一个转折点区间的最大peak_gap值
            Result_Loc['PreTurnPeak_Sec_MaxPeakGapValue'] = gap_data.loc[
                                                            PreTurnPeak_Sec_StartDate:PostTurn_PeakDate,
                                                            'peak_gap'].max() \
                if pd.notnull(PreSecPeak_Sec_StartDate) and pd.notnull(PostTurn_PeakDate) else None

            # 计算前一个区间的最大peak_gap值
            Result_Loc['PreSecPeak_Sec_MaxPeakGapValue'] = gap_data.loc[
                                                            PreSecPeak_Sec_StartDate:PostSecStart_PeakDate,
                                                            'peak_gap'].max() \
                if pd.notnull(PreSecPeak_Sec_StartDate) and pd.notnull(PostSecStart_PeakDate) else None

            # 获取区间开始后的valley_gap和peak_gap中位数
            postsec_medianvalleygapvalue = Result_Loc["PostSecStart_MedianValleyGapValue"]
            postsec_medianpeakgapvalue = Result_Loc["PostSecStart_MedianPeakGapValue"]

            # 计算最近5天内valley_gap和peak_gap超过中位数的天数
            recent_5d_data = gap_data.iloc[-min(5, len(gap_data)):]
            Result_Loc['Recent5DValley_Over_Median_Num'] = recent_5d_data.query(
                'valley_gap>@postsec_medianvalleygapvalue').shape[0] if postsec_medianvalleygapvalue is not None else None
            Result_Loc['Recent5DPeak_Over_Median_Num'] = recent_5d_data.query(
                'peak_gap>@postsec_medianpeakgapvalue').shape[0] if postsec_medianpeakgapvalue is not None else None

            # 获取最新的valley_gap值并计算覆盖天数
            last_vgv = gap_data['valley_gap'].iloc[-1]
            vgv_larger_data = gap_data.iloc[:-1].query('valley_gap>@last_vgv')
            Result_Loc['Now_PostSecStart_VGV_MaxCoverDays'] = len(gap_data.loc[vgv_larger_data.index[-1]:]) - 1 \
                if len(vgv_larger_data) > 0 else len(gap_data) - 1

            # 获取区间高点后的数据
            gap_data_aft_secpeak = gap_data.loc[PostSecStart_PeakDate:].iloc[1:].copy() \
                if PostSecStart_PeakDate>Section_StartDate and len(gap_data.loc[PostSecStart_PeakDate:]) > 1 \
                else gap_data.loc[PreNow_PeakDate:].copy()

            if len(gap_data_aft_secpeak) > 1:
                # 计算valley_gap的排名和覆盖天数
                gap_data_aft_secpeak['ValleyGapValue_Rank'] = gap_data_aft_secpeak['valley_gap'].rank(
                    method='max', ascending=False)
                Result_Loc['Now_PostSecPeak_VGV_Desc_Rank'] = gap_data_aft_secpeak['ValleyGapValue_Rank'].iloc[-1]

                last_vgv = gap_data_aft_secpeak['valley_gap'].iloc[-1]
                vgv_larger_data = gap_data_aft_secpeak.iloc[:-1].query('valley_gap>@last_vgv')

                if len(vgv_larger_data) > 0:
                    Result_Loc['Now_PostSecPeak_VGV_MaxCoverDays'] = len(gap_data_aft_secpeak.loc[vgv_larger_data.index[-1]:]) - 1
                elif len(gap_data_aft_secpeak) >= 3:
                    Result_Loc['Now_PostSecPeak_VGV_MaxCoverDays'] = len(gap_data_aft_secpeak) - 1

                # 计算最大valley_gap值到现在的天数
                Result_Loc['PostSecPeak_MaxVGV2Now_LastDays'] = len(
                    gap_data_aft_secpeak.loc[gap_data_aft_secpeak['valley_gap'].idxmax():]) - 1

            if len(gap_data_aft_secpeak) > 2:
                # 计算滚动均值相关指标
                max_vgv_rollavg = gap_data_aft_secpeak['vgv_rollavg'].max()
                max_pgv_rollavg = gap_data_aft_secpeak['pgv_rollavg'].max()

                Result_Loc['PostSecPeak_VGV_MinRollAvg2MaxRatio'] = round(
                    gap_data_aft_secpeak['vgv_rollavg'].min() / max_vgv_rollavg, 3) if max_vgv_rollavg != 0 else 0
                Result_Loc['PostSecPeak_PGV_MinRollAvg2MaxRatio'] = round(
                    gap_data_aft_secpeak['pgv_rollavg'].min() / max_pgv_rollavg, 3) if max_pgv_rollavg != 0 else 0
                Result_Loc['PostSecPeak_VGV_NowRollAvg2MaxRatio'] = round(
                    gap_data_aft_secpeak['vgv_rollavg'].iloc[-1] / max_vgv_rollavg, 3) if max_vgv_rollavg != 0 else 0
                Result_Loc['PostSecPeak_PGV_NowRollAvg2MaxRatio'] = round(
                    gap_data_aft_secpeak['pgv_rollavg'].iloc[-1] / max_pgv_rollavg, 3) if max_pgv_rollavg != 0 else 0

                # 计算最小值到现在的天数
                rollavg_idxmin = gap_data_aft_secpeak['vgv_rollavg'].idxmin()
                pgv_rollavg_idxmin = gap_data_aft_secpeak['pgv_rollavg'].idxmin()
                Result_Loc['PostSecPeak_VGV_MinRollAvg2Now_LastDays'] = len(gap_data_aft_secpeak.loc[rollavg_idxmin:])
                Result_Loc['PostSecPeak_PGV_MinRollAvg2Now_LastDays'] = len(gap_data_aft_secpeak.loc[pgv_rollavg_idxmin:]) - 1

                # 计算滚动均值排名
                gap_data_aft_secpeak['vgv_movavg_rank'] = gap_data_aft_secpeak['vgv_rollavg'].rank(method='min', ascending=True)
                gap_data_aft_secpeak['pgv_movavg_rank'] = gap_data_aft_secpeak['pgv_rollavg'].rank(method='min', ascending=True)
                Result_Loc['Now_PostSecPeak_VGV_RollAvg_Asc_Rank'] = gap_data_aft_secpeak['vgv_movavg_rank'].iloc[-1]
                Result_Loc['Now_PostSecPeak_PGV_RollAvg_Asc_Rank'] = gap_data_aft_secpeak['pgv_movavg_rank'].iloc[-1]

                # 计算最近一次滚动均值小于当前值到现在的天数
                vgv_lastmovavg = gap_data_aft_secpeak['vgv_rollavg'].iloc[-1]
                pgv_lastmovavg = gap_data_aft_secpeak['pgv_rollavg'].iloc[-1]

                vgv_smaller_data = gap_data_aft_secpeak.iloc[:-1].query('vgv_rollavg<@vgv_lastmovavg')
                pgv_smaller_data = gap_data_aft_secpeak.iloc[:-1].query('pgv_rollavg<@pgv_lastmovavg')

                cover_index = vgv_smaller_data.index[-1] if len(vgv_smaller_data) > 0 else gap_data_aft_secpeak.index[0]
                pgv_cover_index = pgv_smaller_data.index[-1] if len(pgv_smaller_data) > 0 else gap_data_aft_secpeak.index[0]

                Result_Loc['Now_PostSecPeak_VGV_RollAvg_MinCoverDays'] = len(gap_data_aft_secpeak.loc[cover_index:]) - 1
                Result_Loc['Now_PostSecPeak_PGV_RollAvg_MinCoverDays'] = len(gap_data_aft_secpeak.loc[pgv_cover_index:]) - 1

                # 计算PGV滚动均值相关指标
                Result_Loc['PostSecPeak_PGV_MinRollAvg'] = round(gap_data_aft_secpeak['pgv_rollavg'].min(), 3)
                Result_Loc['PostSecPeak_PGV_MaxRollAvg'] = round(gap_data_aft_secpeak['pgv_rollavg'].max(), 3)

                # 记录PGV滚动均值最小值日期及相关指标
                postsecpeak_pgv_minrollavg_date = gap_data_aft_secpeak['pgv_rollavg'].idxmin()
                Result_Loc['PostSecPeak_PGV_MinRollAvg_Date'] = postsecpeak_pgv_minrollavg_date

                if len(gap_data_aft_secpeak.loc[postsecpeak_pgv_minrollavg_date:]) > 1:
                    post_minrollavg_data = gap_data_aft_secpeak.loc[postsecpeak_pgv_minrollavg_date:]
                    post_minrollavg_maxvgv = post_minrollavg_data['valley_gap'].max()
                    post_minrollavg_maxvgv_date = post_minrollavg_data['valley_gap'].idxmax()

                    vgv_larger_data = gap_data_aft_secpeak.loc[:post_minrollavg_maxvgv_date].query('valley_gap>@post_minrollavg_maxvgv')
                    postminroll_maxvgv_coverindex = vgv_larger_data.index[-1] if len(vgv_larger_data) > 0 else gap_data_aft_secpeak.index[0]

                    Result_Loc['PGV_Post_MinRollAvg_MaxVGV_CoverDays'] = len(
                        gap_data_aft_secpeak.loc[postminroll_maxvgv_coverindex:post_minrollavg_maxvgv_date])
                else:
                    Result_Loc['PGV_Post_MinRollAvg_MaxVGV_CoverDays'] = 0

            # 计算最近一次滚动均值大于当前值到现在的天数
            peak_last_movavg = gap_data['pgv_rollavg'].iloc[-1]
            valley_last_movavg = gap_data['vgv_rollavg'].iloc[-1]

            pgv_larger_data = gap_data.iloc[:-1].query('pgv_rollavg>@peak_last_movavg')
            vgv_larger_data = gap_data.iloc[:-1].query('vgv_rollavg>@valley_last_movavg')

            peak_cover_index = pgv_larger_data.index[-1] if len(pgv_larger_data) > 0 else gap_data.index[0]
            valley_cover_index = vgv_larger_data.index[-1] if len(vgv_larger_data) > 0 else gap_data.index[0]

            Result_Loc['Now_PGV_RollAvg_CoverDays'] = len(gap_data.loc[peak_cover_index:]) - 1
            Result_Loc['Now_VGV_RollAvg_CoverDays'] = len(gap_data.loc[valley_cover_index:]) - 1

            # 获取当前section日期之后的gap数据
            gap_data_aft_nowsec = gap_data.loc[Now_SecDate:].iloc[1:].copy() \
                if len(gap_data.loc[Now_SecDate:]) > 1 else gap_data.loc[Now_SecDate:].copy()
            Result_Loc['Now_PostNowSec_PGV_MaxCoverDays'] = 0
            if len(gap_data_aft_nowsec) > 1:
                # 计算peak_gap的排名(降序)
                gap_data_aft_nowsec['PeakGapValue_Rank'] = gap_data_aft_nowsec['peak_gap'].rank(
                    method='max', ascending=False)
                Result_Loc['Now_PostNowSec_PGV_Desc_Rank'] = gap_data_aft_nowsec['PeakGapValue_Rank'].iloc[-1]
                # 计算最近一次排名小于4的日期到现在的天数
                last_peakrank = 4
                if len(gap_data_aft_nowsec.iloc[:-1].query('PeakGapValue_Rank<@last_peakrank')) > 0:
                    last_peakindex = gap_data_aft_nowsec.iloc[:-1].query('PeakGapValue_Rank<@last_peakrank').index[-1]
                    Result_Loc['Now_PostNowSec_PGV_MaxCoverDays'] = len(gap_data_aft_nowsec.loc[last_peakindex:]) - 1
            # 计算pgv_rollavg的最小值、最大值和均值
            Result_Loc['PostNowSec_PGV_MinRollAvg'] = round(
                gap_data_aft_nowsec['pgv_rollavg'].min(), 3)
            Result_Loc['PostNowSec_PGV_MaxRollAvg'] = round(
                gap_data_aft_nowsec['pgv_rollavg'].max(), 3)
            Result_Loc['PostNowSec_PGV_MeanRollAvg'] = round(
                gap_data_aft_nowsec['pgv_rollavg'].sort_values()[:-1].mean(), 3) \
                if len(gap_data_aft_nowsec)>2 else round(
                gap_data_aft_nowsec['pgv_rollavg'].mean(), 3)
            gap_data_aft_sec = gap_data.loc[Section_StartDate:].copy()
            postsecstart_minroll_date = None
            if len(gap_data_aft_sec) > 1:
                # 计算section开始后的pgv_rollavg最大值与最小值的比值
                min_pgv_rollavg = gap_data_aft_sec.query('pgv_rollavg>0')['pgv_rollavg'].min()
                Result_Loc['PostSecStart_PGV_MaxRollAvg2MinRatio'] = round(
                    gap_data_aft_sec['pgv_rollavg'].max() / min_pgv_rollavg, 3) \
                    if len(gap_data_aft_sec.query('pgv_rollavg>0')) > 1 and min_pgv_rollavg > 0 else 0
                # 计算section开始后的pgv_rollavg最大值与均值的比值
                pgv_rollavg_mean = gap_data_aft_sec.loc[:Now_SecDate, 'pgv_rollavg'].mean() \
                    if Section_StartDate<Now_SecDate else gap_data_aft_sec['pgv_rollavg'].sort_values()[:-1].mean()
                Result_Loc['PostSecStart_PGV_MaxRollAvg2MeanRatio'] = round(
                    gap_data_aft_sec['pgv_rollavg'].max() / pgv_rollavg_mean, 3) \
                    if pgv_rollavg_mean != 0 else 0
                # 计算从最大pgv_rollavg日期到现在的天数
                Result_Loc['PostSecStart_PGV_MaxRollAvg2Now_LastDays'] = len(
                    gap_data_aft_sec.loc[gap_data_aft_sec['pgv_rollavg'].idxmax():]) - 1
                # 计算vgv_rollavg最大值与最小值的比值
                min_vgv_rollavg = gap_data_aft_sec['vgv_rollavg'].min()
                Result_Loc['PostSecStart_VGV_MaxRollAvg2MinRatio'] = round(
                    gap_data_aft_sec['vgv_rollavg'].max() / min_vgv_rollavg, 3) \
                    if min_vgv_rollavg != 0 else 0
                # 计算当前pgv_rollavg与最小值的比值
                min_pgv_rollavg = gap_data_aft_sec['pgv_rollavg'].min()
                Result_Loc['PostSecStart_PGV_NowRollAvg2MinRatio'] = round(
                    gap_data_aft_sec['pgv_rollavg'].iloc[-1] / min_pgv_rollavg, 3) \
                    if min_pgv_rollavg != 0 else 0
                # 计算当前vgv_rollavg与最小值的比值
                min_vgv_rollavg = gap_data_aft_sec['vgv_rollavg'].min()
                Result_Loc['PostSecStart_VGV_NowRollAvg2MinRatio'] = round(
                    gap_data_aft_sec['vgv_rollavg'].iloc[-1] / min_vgv_rollavg, 3) \
                    if min_vgv_rollavg != 0 else 0
                # 计算pgv_rollavg和vgv_rollavg的排名
                gap_data_aft_sec['pgv_rollavg_rank'] = gap_data_aft_sec['pgv_rollavg'].rank(method='max', ascending=False)
                gap_data_aft_sec['pgv_rollavg_ascrank'] = gap_data_aft_sec['pgv_rollavg'].rank(method='min', ascending=True)
                gap_data_aft_sec['vgv_rollavg_rank'] = gap_data_aft_sec[
                    'vgv_rollavg'].rank(method='max', ascending=False)
                Result_Loc['Now_PostSecStart_PGV_RollAvg_Desc_Rank'] = gap_data_aft_sec['pgv_rollavg_rank'].iloc[-1]
                Result_Loc['Now_PostSecStart_VGV_RollAvg_Desc_Rank'] = gap_data_aft_sec['vgv_rollavg_rank'].iloc[-1]
                Result_Loc['Now_PostSecStart_PGV_RollAvg_Asc_Rank'] = gap_data_aft_sec['pgv_rollavg_ascrank'].iloc[-1]
                # 计算从最小pgv_rollavg日期到现在的天数
                postsecstart_minroll_date = gap_data_aft_sec['pgv_rollavg'].iloc[2:].idxmin() \
                    if len(gap_data_aft_sec) > 2 else gap_data_aft_sec['pgv_rollavg'].idxmin()
                Result_Loc['PostSecStart_PGV_MinRollAvg2Now_LastDays'] = len(
                    gap_data_aft_sec.loc[postsecstart_minroll_date:]) - 1
                # 计算pgv_rollavg的最大值、最小值和均值
                Result_Loc['PostSecStart_PGV_MaxRollAvg'] = round(gap_data_aft_sec['pgv_rollavg'].max(), 3)
                Result_Loc['PostSecStart_PGV_MinRollAvg'] = round(
                    gap_data_aft_sec.loc[postsecstart_minroll_date, 'pgv_rollavg'], 3)
                Result_Loc['PostSecStart_PGV_MinRollAvg_Date'] = postsecstart_minroll_date
                Result_Loc['PostSecStart_PGV_MeanRollAvg'] = round(
                    gap_data_aft_sec['pgv_rollavg'].sort_values()[:-1].mean(), 3) \
                    if Section_StartDate == Now_SecDate else round(
                    gap_data_aft_sec.loc[:Now_SecDate, 'pgv_rollavg'].mean(), 3)
                Result_Loc['PostSecStart_VGV_MaxRollAvg'] = round(gap_data_aft_sec['vgv_rollavg'].max(), 3)
                Result_Loc['PostSec_Peak_PGV_RollAvg'] = round(
                    gap_data_aft_sec.loc[PostSecStart_PeakDate, 'pgv_rollavg'], 3)
                Result_Loc['PostSec_Peak_PGV_RollAvg_Desc_Rank'] = round(
                    gap_data_aft_sec.loc[PostSecStart_PeakDate, 'pgv_rollavg_rank'], 3)
                # 记录最大pgv_rollavg和peak_gap的日期
                postsec_pgv_maxrollavg_date = gap_data_aft_sec['pgv_rollavg'].idxmax()
                postsec_maxpgv_date = gap_data_aft_sec['peak_gap'].idxmax()
                postsec_maxpgv = gap_data_aft_sec['peak_gap'].max()
                postsec_meanpgv = gap_data_aft_sec['peak_gap'].sort_values()[:-1].mean()
                postsec_maxvgv = gap_data_aft_sec['valley_gap'].max()
                postsec_maxvgv_date = gap_data_aft_sec['valley_gap'].idxmax()
                postsec_meanvgv = gap_data_aft_sec['valley_gap'].sort_values()[:-1].mean()
                Result_Loc['PostSecStart_PGV_MaxRollAvg_Date'] = postsec_pgv_maxrollavg_date
                Result_Loc['PostSecStart_MaxPGV'] = round(postsec_maxpgv, 3)
                Result_Loc['PostSecStart_MaxPGV_Date'] = postsec_maxpgv_date
                Result_Loc['PostSecStart_MaxPGVDate2Now_LastDays'] = len(
                    gap_data_aft_sec.loc[postsec_maxpgv_date:]) - 1
                Result_Loc['PostSecStart_MaxVGV'] = round(postsec_maxvgv, 3)
                Result_Loc['PostSecStart_MaxVGV_Date'] = postsec_maxvgv_date
                Result_Loc['PostSecStart_MaxVGVDate2Now_LastDays'] = len(
                    gap_data_aft_sec.loc[postsec_maxvgv_date:]) - 1
                Result_Loc['PostSecStart_PGV_Max2Mean_Ratio'] = round(postsec_maxpgv / postsec_meanpgv - 1, 3) \
                    if postsec_meanpgv != 0 else 0
                Result_Loc['PostSecStart_VGV_Max2Mean_Ratio'] = round(postsec_maxvgv / postsec_meanvgv - 1, 3) \
                    if postsec_meanvgv != 0 else 0

                # 计算与peak日期的差距
                rollavg_peakdate_diff = \
                    len(gap_data_aft_sec.loc[PostSecStart_PeakDate:postsec_pgv_maxrollavg_date]) - 1 \
                        if PostSecStart_PeakDate<=postsec_pgv_maxrollavg_date \
                        else len(gap_data_aft_sec.loc[postsec_pgv_maxrollavg_date:PostSecStart_PeakDate]) - 1
                pgv_peakdate_diff = \
                    len(gap_data_aft_sec.loc[PostSecStart_PeakDate:postsec_maxpgv_date]) - 1 \
                        if PostSecStart_PeakDate<=postsec_maxpgv_date \
                        else len(gap_data_aft_sec.loc[postsec_maxpgv_date:PostSecStart_PeakDate]) - 1
                Result_Loc['PostSecStart_PeakDate_Diff'] = min(rollavg_peakdate_diff, pgv_peakdate_diff)

                # 计算最大pgv_rollavg日期之后的统计值
                postsec_PGV_maxrollavg_date = gap_data_aft_sec['pgv_rollavg'].idxmax()
                gap_data_aft_maxroll = gap_data_aft_sec.loc[postsec_PGV_maxrollavg_date:]
                gap_data_pre_maxroll = gap_data_aft_sec.loc[:postsec_PGV_maxrollavg_date]

                Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg'] = round(gap_data_aft_maxroll['pgv_rollavg'].min(), 3)
                Result_Loc['PostSecMaxRollAvg_PGV_MeanRollAvg'] = round(gap_data_aft_maxroll['pgv_rollavg'].mean(), 3)
                Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date'] = gap_data_aft_maxroll['pgv_rollavg'].idxmin()

                # 计算最小值之后的最大值与整体最大值的比值
                max_pgv_rollavg = gap_data_aft_sec['pgv_rollavg'].max()
                min_date = Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']
                Result_Loc['PostSecMaxRollAvg_PGV_PostMin_MaxRollAvg2Max_Ratio'] = round(
                    gap_data_aft_sec.loc[min_date:, 'pgv_rollavg'].max() / max_pgv_rollavg, 3) if max_pgv_rollavg > 0 else 0

                # 计算最小值与最大值的比值
                Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio'] = round(
                    Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg'] / Result_Loc['PostSecStart_PGV_MaxRollAvg'], 3) \
                    if Result_Loc['PostSecStart_PGV_MaxRollAvg'] != 0 else 0

                Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays'] = len(
                    gap_data_aft_sec.loc[gap_data_aft_maxroll['pgv_rollavg'].idxmin():]) - 1

                Result_Loc['PreSecMaxRollAvg_PGV_MinRollAvg'] = round(gap_data_pre_maxroll['pgv_rollavg'].min(), 3)

                # 计算最大值前后最小值的比值
                pre_min_pgv_rollavg = Result_Loc['PreSecMaxRollAvg_PGV_MinRollAvg']
                Result_Loc['Post2Pre_PGV_MinRollAvg_Ratio'] = round(
                    Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg']/pre_min_pgv_rollavg, 3) \
                    if pre_min_pgv_rollavg != 0 else 0

                # 计算当前值与最小值的比值
                post_min_pgv_rollavg = gap_data_aft_maxroll['pgv_rollavg'].iloc[:-1].min()
                Result_Loc['PostSecMaxRollAvg_PGV_RollAvg_Now2Min_Ratio'] = round(
                    gap_data_aft_sec['pgv_rollavg'].iloc[-1] / post_min_pgv_rollavg, 3) \
                    if len(gap_data_aft_maxroll) > 1 and post_min_pgv_rollavg != 0 else 0

                # 计算最大值与最小值的比值
                Result_Loc['PreSecMaxRollAvg_PGV_MaxRollAvg2MinRatio'] = round(
                    Result_Loc['PostSecStart_PGV_MaxRollAvg'] / pre_min_pgv_rollavg, 3) \
                    if pre_min_pgv_rollavg != 0 else 0

                # 计算均值
                Result_Loc['PreSecMaxRollAvg_PGV_MeanRollAvg'] = round(
                    gap_data_pre_maxroll['pgv_rollavg'].iloc[:-1].mean(), 3) \
                    if len(gap_data_pre_maxroll)>2 else 0

                # 计算波动区间
                Result_Loc['PostSecMaxRollAvg_PGV_RollAvg_VolRange'] = round(
                    gap_data_aft_maxroll['pgv_rollavg'].quantile(0.66) -
                    gap_data_aft_maxroll['pgv_rollavg'].quantile(0.33), 3)

                # 计算最大值日期之后的最小peak_gap相关指标
                if len(gap_data_aft_maxroll) > 0:
                    Result_Loc['PostSecMaxRollAvg_MinPGV'] = round(gap_data_aft_maxroll['peak_gap'].min(), 3)
                    check_minpgv_date = gap_data_aft_maxroll['peak_gap'].idxmin()
                    Result_Loc['PostSecMaxRollAvg_MinPGV2Now_LastDays'] = \
                        len(gap_data_aft_sec.loc[check_minpgv_date:]) - 1
                    # 计算最小值与最大值的比值
                    max_peak_gap = Result_Loc['PostSecStart_MaxPGV']
                    Result_Loc['PostSecMaxRollAvg_MinPGV2MaxRatio'] = round(
                        Result_Loc['PostSecMaxRollAvg_MinPGV'] / max_peak_gap, 3) if max_peak_gap > 0 else 0

                # 计算section开始后的pgv_rollavg分位数
                gap_data_section = gap_data.loc[Section_StartDate:]
                Result_Loc['PostSecStart_PGV_RollAvg_LowQuntl'] = round(
                    gap_data_section['pgv_rollavg'].quantile(0.33), 3)
                Result_Loc['PostSecStart_PGV_RollAvg_HighQuntl'] = round(
                    gap_data_section['pgv_rollavg'].quantile(0.66), 3)
                Result_Loc['PostSecStart_PGV_RollAvg_VolRange'] = round(
                    Result_Loc['PostSecStart_PGV_RollAvg_HighQuntl'] -
                    Result_Loc['PostSecStart_PGV_RollAvg_LowQuntl'], 3)

                # 计算section前后的最大值比值
                peak2sec_max_rollavg = Result_Loc['Peak2Sec_PGV_MaxRollAvg']
                Result_Loc['PostSec2PreSec_PGV_MaxRollAvg_Ratio'] = round(
                    Result_Loc['PostSecStart_PGV_MaxRollAvg'] / peak2sec_max_rollavg, 3) \
                    if peak2sec_max_rollavg > 0 else 0

                # 计算当前值与分位数的比值
                low_quntl = Result_Loc['PostSecStart_PGV_RollAvg_LowQuntl']
                high_quntl = Result_Loc['PostSecStart_PGV_RollAvg_HighQuntl']
                now_pgv_rollavg = gap_data_aft_sec['pgv_rollavg'].iloc[-1]
                Result_Loc['PGVRollAvg_Now2PostSecLowQuntl_Ratio'] = round(
                    now_pgv_rollavg / low_quntl, 3) if low_quntl > 0 else 0
                Result_Loc['PGVRollAvg_Now2PostSecHighQuntl_Ratio'] = round(
                    now_pgv_rollavg / high_quntl, 3) if high_quntl > 0 else 0


            # 计算前3天的峰值差
            Result_Loc['Pre3Date_PeakGapValue'] = round(gap_data['peak_gap'].iloc[-3], 3)

            # 计算最近9天的谷值差和峰值差最大值
            recent_9day = gap_data.iloc[-min(9, len(gap_data)-1):]
            Result_Loc['Recent9Day_MaxValleyGapValue'] = round(recent_9day['valley_gap'].max(), 3)
            Result_Loc['Recent9Day_MaxPeakGapValue'] = round(recent_9day['peak_gap'].max(), 3)

            # 计算最近2天的峰值差和谷值差均值
            recent_2day = gap_data.iloc[-min(2, len(gap_data)-1):]
            Result_Loc['Recent2Day_MeanPeakGapValue'] = round(recent_2day['peak_gap'].mean(), 3)
            Result_Loc['Recent2Day_MeanValleyGapValue'] = round(recent_2day['valley_gap'].mean(), 3)

            # 计算前一谷值日期后的滚动均值相关指标
            if len(gap_data_prenow_bottom) > 0:
                gap_data_prenow_bottom['pgv_rollavg_rank'] = gap_data_prenow_bottom['pgv_rollavg'].rank(
                    method='min', ascending=True)
                Result_Loc['PostPreNowBottom_PGV_MinRollAvg'] = round(
                    gap_data_prenow_bottom['pgv_rollavg'].min(), 3)
                Result_Loc['Now_PostPreNowBottom_PGV_RollAvg_Asc_Rank'] = gap_data_prenow_bottom[
                    'pgv_rollavg_rank'].iloc[-1]

            # 计算当前的滚动均值指标
            recent_3day = gap_data.iloc[-min(3, len(gap_data)-1):]
            Result_Loc['Now_PGV_RollAvg'] = round(recent_3day['peak_gap'].mean(), 3)
            Result_Loc['Now_VGV_RollAvg'] = round(recent_3day['valley_gap'].mean(), 3)

            # 计算最近3天的滚动均值最大值和最小值
            if len(gap_data) > 5:
                recent_3day_rollavg = gap_data['pgv_rollavg'].iloc[-3:]
                Result_Loc['Recent3Day_PGV_MaxRollAvg'] = round(recent_3day_rollavg.max(), 3)
                Result_Loc['Recent3Day_PGV_MinRollAvg'] = round(recent_3day_rollavg.min(), 3)
                Result_Loc['Recent3Day_PGV_MeanRollAvg'] = round(recent_3day_rollavg.mean(), 3)

            def truncate_to_two_digits(number, return_style=True):
                """
                将数字截断为两位有效数字
                Args:
                    number: 需要处理的数字
                    return_style: 是否返回截断后的原始值,False则返回首位数字减1后的值
                Returns:
                    处理后的数值
                """
                if number == 0:
                    return 0
                truncated_value = round(number, 2)
                if return_style:
                    return truncated_value

                # 提取首位数字
                first_digit = int(str(abs(truncated_value))[0])

                # 计算新值:首位数字减1,其余数值取零
                if number < 1:
                    new_value = (first_digit - 1) / 100.0  # 保持两位小数
                else:
                    new_value = (first_digit - 1) * (10 ** (len(str(int(abs(truncated_value)))) - 1))
                # 修正:确保只取整数部分
                new_value = int(new_value)
                return new_value

            # 计算区间峰值后的滚动均值四分位数
            if pd.notnull(Section_StartDate) and pd.notnull(PostSecStart_PeakDate)\
                    and PostSecStart_PeakDate>Section_StartDate and len(gap_data.loc[PostSecStart_PeakDate:]) > 1 \
                    and len(gap_data_section_start) > 3:
                Result_Loc['PostSecPeak_PGV_MaxRollAvg_Quarter'] = truncate_to_two_digits(
                    Result_Loc['PostSecStart_PGV_MaxRollAvg'] * 0.3)
            elif pd.notnull(Result_Loc['PostSecPeak_PGV_MaxRollAvg']):
                Result_Loc['PostSecPeak_PGV_MaxRollAvg_Quarter'] = truncate_to_two_digits(
                    Result_Loc['PostSecPeak_PGV_MaxRollAvg'] * 0.3)
            else:
                Result_Loc['PostSecPeak_PGV_MaxRollAvg_Quarter'] = 0

            # 计算转折前峰值后的滚动均值相关指标
            gap_data_after_preturn = gap_data.loc[PreTurn_PeakDate:, 'pgv_rollavg']
            Result_Loc['AftPreTurnPeak_PGV_MinRollAvg'] = round(gap_data_after_preturn.min(), 3)

            max_pgv_rollavg = gap_data_after_preturn.max()
            Result_Loc['AftPreTurnPeak_PGV_MinRollAvg2MaxRatio'] = round(
                gap_data_after_preturn.min() / max_pgv_rollavg, 3) if max_pgv_rollavg > 0 else 0

            Result_Loc['AftPreTurnPeak_PGV_MinRollAvg2Now_LastDays'] = len(
                gap_data.loc[gap_data_after_preturn.idxmin():]) - 1

            # 计算前一峰值到当前区间的滚动均值相关指标
            gap_data_prenow_to_now = gap_data.loc[PreNow_PeakDate:Now_SecDate, 'pgv_rollavg']
            Result_Loc['PreNowPeak_PGV_MeanRollAvg'] = round(gap_data_prenow_to_now.mean(), 3)

            gap_data_after_prenow = gap_data.loc[PreNow_PeakDate:, 'pgv_rollavg']
            Result_Loc['PreNowPeak_PGV_MinRollAvg2Now_LastDays'] = len(
                gap_data.loc[gap_data_after_prenow.idxmin():]) - 1

            max_prenow_pgv_rollavg = gap_data_after_prenow.max()
            Result_Loc['PreNowPeak_PGV_MinRollAvg2MaxRatio'] = round(
                gap_data_after_prenow.min() / max_prenow_pgv_rollavg, 3) if max_prenow_pgv_rollavg > 0 else 0

            Result_Loc['PreNowPeak_PGV_MaxRollAvg'] = round(gap_data_prenow_to_now.max(), 3)

            def cal_trendcount(df_data, mode='rise'):
                """统计高于前述所有数值的占比

                Args:
                    df_data: DataFrame, 包含pgv_rollavg和cor_ratio列的数据
                    mode: str, 'rise'表示上升趋势,'drop'表示下降趋势

                Returns:
                    float: 趋势占比,保留3位小数
                """
                count = 0
                total = len(df_data)
                if total <= 1:
                    return 1
                if mode.lower() == 'rise':
                    for i in range(1, total):
                        if df_data['pgv_rollavg'].iloc[i] > df_data['pgv_rollavg'].iloc[:i].max() \
                            or df_data['cor_ratio'].iloc[i] == 0:
                            count += 1
                    return round(count / (total - 1), 3)
                else:
                    for i in range(1, total):
                        if df_data['pgv_rollavg'].iloc[i] <= df_data['pgv_rollavg'].iloc[:i].min() \
                            or df_data['cor_ratio'].iloc[i] == 0:
                            count += 1
                    return round(count / (total - 1), 3)

            # 计算最小滚动均值后的趋势指标
            if pd.notnull(Result_Loc['PostSecPeak_PGV_MinRollAvg_Date']):
                min_date = Result_Loc['PostSecPeak_PGV_MinRollAvg_Date']
                Result_Loc['PostMinRollAvg_RiseTrend_Prop'] = cal_trendcount(
                    gap_data.loc[min_date:])
                Result_Loc['PostSecPeak_DropTrend_Prop'] = cal_trendcount(
                    gap_data.loc[PostSecStart_PeakDate:min_date], mode='drop')
                Result_Loc['MinRollAvg_NowSec_Diff'] = len(
                    gap_data.loc[Now_SecDate:min_date]) - 1 \
                    if Now_SecDate <= min_date \
                    else len(gap_data.loc[min_date:Now_SecDate]) - 1

                # 计算区间峰值到最小滚动均值日期的均值
                gap_data_peak_to_min = gap_data.loc[PostSecStart_PeakDate:min_date]
                if len(gap_data_peak_to_min) > 1:
                    PostPeakRollAvg_Mean = gap_data_peak_to_min['pgv_rollavg'].iloc[:-1].mean()
                    PostPeakRollAvg_Mean_End = gap_data_peak_to_min['pgv_rollavg'].mean()
                else:
                    PostPeakRollAvg_Mean = None
                    PostPeakRollAvg_Mean_End = None

                Result_Loc['PostSecPeak_PGV_MeanRollAvg_TruncatedValue'] = round(
                    truncate_to_two_digits(PostPeakRollAvg_Mean_End, return_style=False), 3) \
                    if PostPeakRollAvg_Mean_End is not None else -1

                Result_Loc['MinRollAvg_Truncated_Diff'] = round(
                    truncate_to_two_digits(PostPeakRollAvg_Mean, return_style=False) -
                    Result_Loc['PostSecPeak_PGV_MinRollAvg'], 3) \
                    if PostPeakRollAvg_Mean is not None and pd.notnull(Result_Loc['PostSecPeak_PGV_MinRollAvg']) else -1

            # 如果没有PostSecPeak_PGV_MinRollAvg_Date,则使用PostSecMaxRollAvg_PGV_MinRollAvg_Date计算相同指标
            elif pd.notnull(Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']):
                Result_Loc['PostMinRollAvg_RiseTrend_Prop'] = cal_trendcount(
                    gap_data.loc[Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']:])
                Result_Loc['PostSecPeak_DropTrend_Prop'] = cal_trendcount(
                    gap_data.loc[Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']:
                                 Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']], mode='drop')
                Result_Loc['MinRollAvg_NowSec_Diff'] = len(
                    gap_data.loc[Now_SecDate:Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']]) - 1 \
                    if Now_SecDate <= Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date'] \
                    else len(gap_data.loc[Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']:Now_SecDate]) - 1

                # 计算最大滚动均值日期到最小滚动均值日期的均值
                gap_data_maxroll_to_min = gap_data.loc[Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']:
                                                      Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']]
                if pd.notnull(Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']) and len(gap_data_maxroll_to_min) > 1:
                    PostMaxRollAvg_Mean = gap_data_maxroll_to_min['pgv_rollavg'].iloc[:-1].mean()
                    PostMaxRollAvg_Mean_End = gap_data_maxroll_to_min['pgv_rollavg'].mean()
                else:
                    PostMaxRollAvg_Mean = None
                    PostMaxRollAvg_Mean_End = None

                Result_Loc['PostSecPeak_PGV_MeanRollAvg_TruncatedValue'] = round(
                    truncate_to_two_digits(PostMaxRollAvg_Mean_End, return_style=False), 3) \
                    if PostMaxRollAvg_Mean_End is not None else -1

                Result_Loc['MinRollAvg_Truncated_Diff'] = round(
                    truncate_to_two_digits(PostMaxRollAvg_Mean, return_style=False) -
                    Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg'], 3) \
                    if PostMaxRollAvg_Mean is not None and pd.notnull(Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg']) else -1

            # 计算Section开始后的上涨趋势比例
            if len(gap_data_section_start) > 1:
                Result_Loc['PostSecStart_RiseTrend_Prop'] = cal_trendcount(gap_data_section_start)

            # 计算PreNow_PeakDate后的下跌趋势比例
            if len(gap_data_prenow_bottom) > 2:
                Result_Loc['PostPreNowPeak_DropTrend_Prop'] = cal_trendcount(
                    gap_data.loc[PreNow_PeakDate:Now_SecDate], mode='drop')

            def pgvrollavg_lastcount(df_data, mode='rise'):
                """统计自指定日期计算，peak_avg连续上行/下行天数

                参数:
                df_data: DataFrame, 包含pgv_rollavg列的数据
                mode: str, 'rise'表示上行,'drop'表示下行

                返回:
                int: 连续上行/下行的天数
                """
                count = 1
                if len(df_data) <= 1:
                    return count

                if mode.lower() == 'rise':
                    for i in range(2, len(df_data)):
                        if df_data['pgv_rollavg'].iloc[-i] > df_data['pgv_rollavg'].iloc[-i+1:].max():
                            count += 1
                        else:
                            break
                else:
                    for i in range(2, len(df_data)):
                        if df_data['pgv_rollavg'].iloc[-i] < df_data['pgv_rollavg'].iloc[-i+1:].min():
                            count += 1
                        else:
                            break
                return count

            # 计算当前pgv_rollavg的连续上行/下行天数
            Result_Loc['Now_PGVRollAvg_DownCount'] = pgvrollavg_lastcount(gap_data.loc[:end_date], mode='rise')
            Result_Loc['Now_PGVRollAvg_UpCount'] = pgvrollavg_lastcount(gap_data.loc[:end_date], mode='drop')

            # 计算最小滚动均值前的连续下行天数
            if pd.notnull(Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']):
                Result_Loc['BfMinRollAvg_PGVRollAvg_DownCount'] = pgvrollavg_lastcount(
                    gap_data.loc[:Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']].copy())
            elif pd.notnull(Result_Loc['PostSecPeak_PGV_MinRollAvg_Date']):
                Result_Loc['BfMinRollAvg_PGVRollAvg_DownCount'] = pgvrollavg_lastcount(
                    gap_data.loc[:Result_Loc['PostSecPeak_PGV_MinRollAvg_Date']].copy())

            # 计算最大滚动均值日期与峰值日期的差值
            if pd.notnull(Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']):
                Result_Loc['PostSec_MaxRollAvg_PeakDate_Diff'] = len(
                    gap_data.loc[Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']:PostSecStart_PeakDate]) - 1 \
                    if Result_Loc['PostSecStart_PGV_MaxRollAvg_Date'] <= PostSecStart_PeakDate \
                    else len(
                    gap_data.loc[PostSecStart_PeakDate:Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']]) - 1

            # 计算凹谷区间的相关指标
            gap_data_concave = gap_data.loc[SecConcave_StartDate:].copy()
            if len(gap_data_concave) > 1:
                # 计算凹谷区间最小滚动均值
                Result_Loc['SecConcave_PGV_MinRollAvg'] = round(
                    gap_data_concave['pgv_rollavg'].min(), 3)
                Result_Loc['SecConcave_PGV_MinRollAvg_Date'] = gap_data_concave['pgv_rollavg'].idxmin()

                # 计算最小滚动均值与均值的比值(排除最小值)
                mean_without_min = gap_data_concave['pgv_rollavg'].sort_values(ascending=True).iloc[1:].mean()
                Result_Loc['SecConcave_PGV_MinRollAvg2MeanRatio'] = round(
                    Result_Loc['SecConcave_PGV_MinRollAvg'] / mean_without_min, 3) \
                    if mean_without_min != 0 else 0

                # 计算最小滚动均值日期到现在的天数
                seconcave_minrollavg_date = gap_data_concave['pgv_rollavg'].idxmin()
                Result_Loc['SecConcave_PGV_MinRollAvg2Now_LastDays'] = len(
                    gap_data.loc[seconcave_minrollavg_date:]) - 1

            def consecutive_count(df_data, mode='drop'):
                """查找df_data中pgv_rollavg连续三天以上低于/高于前一日数值的统计指标

                Args:
                    df_data: DataFrame, 包含pgv_rollavg和daily_ratio列的数据
                    mode: str, 'drop'表示下降趋势,'rise'表示上升趋势

                Returns:
                    dict: 包含以下统计指标:
                        count: 连续出现的次数
                        consecutive_avglastdays: 平均持续天数
                        consecutive_maxlastdays: 最大持续天数
                        lastcount2now_days: 最后一次连续出现距今天数
                        consecutive_diffratio: 最后一次连续区间首尾pgv_rollavg比值
                        consecutive_perfratio: 最后一次连续区间累计涨跌幅
                        consecutive_start_pgvra: 最后一次连续区间起始pgv_rollavg
                        consecu_enddate: 最后一次连续区间结束日期
                """
                if mode.lower() == 'drop':
                    df_data['diff_previous'] = df_data['pgv_rollavg'] < df_data['pgv_rollavg'].shift(1)
                else:
                    df_data['diff_previous'] = df_data['pgv_rollavg'] >= df_data['pgv_rollavg'].shift(1)
                # 初始化计数器
                count = 0
                consecutive_days = 0
                lastcount2now_days = len(df_data)
                temp_startdate, consecu_startdate, consecu_enddate =None, None, None
                consecutive_diffratio, consecutive_perfratio, consecutive_start_pgvra = 0, 0, 0
                consecutive_lastdays = []
                # 遍历 DataFrame 以统计连续出现的次数
                for index in df_data.index:
                    if df_data.loc[index, 'diff_previous']:
                        if consecutive_days == 0:
                            temp_startdate = df_data.loc[:index].index[-2] if len(df_data.loc[:index]) > 1 else index
                        consecutive_days += 1
                    else:
                        if consecutive_days >= 2:
                            count += 1
                            consecu_startdate = temp_startdate
                            consecu_enddate = df_data.loc[:index].index[-2]
                            lastcount2now_days = len(df_data.loc[index:])
                            if consecu_startdate is not None and consecu_enddate is not None:
                                # 避免除0错误
                                start_pgv = df_data.loc[consecu_startdate, 'pgv_rollavg']
                                consecutive_diffratio = round(
                                    df_data.loc[consecu_enddate, 'pgv_rollavg'] / start_pgv, 3) \
                                    if start_pgv != 0 else 1
                                consecutive_perfratio = round(
                                    ((df_data.loc[consecu_startdate:consecu_enddate, 'daily_ratio'].iloc[1:] / 100 + 1
                                      ).product() - 1) * 100, 3)
                                consecutive_start_pgvra = start_pgv
                                consecutive_lastdays.append(len(df_data.loc[consecu_startdate:consecu_enddate]))
                        consecutive_days = 0

                # 检查最后一段连续天数
                if consecutive_days >= 2:
                    count += 1
                    lastcount2now_days = 0
                    consecu_startdate = temp_startdate
                    if consecu_startdate is not None:
                        consecu_enddate = df_data.index[-1]
                        # 避免除0错误
                        start_pgv = df_data.loc[consecu_startdate, 'pgv_rollavg']
                        consecutive_diffratio = round(
                            df_data['pgv_rollavg'].iloc[-1] / start_pgv, 3) \
                            if start_pgv != 0 else 1
                        consecutive_perfratio = round(
                            ((df_data.loc[consecu_startdate:, 'daily_ratio'].iloc[1:] / 100 + 1
                              ).product() - 1) * 100, 3)
                        consecutive_start_pgvra = start_pgv
                        consecutive_lastdays.append(len(df_data.loc[consecu_startdate:]))
                consecutive_avglastdays = round(np.mean(consecutive_lastdays), 2) \
                    if len(consecutive_lastdays) > 0 else 0
                consecutive_maxlastdays = np.max(consecutive_lastdays) \
                    if len(consecutive_lastdays) > 0 else 0
                return {'count': count,
                        'consecutive_avglastdays': consecutive_avglastdays,
                        'consecutive_maxlastdays': consecutive_maxlastdays,
                        'lastcount2now_days': lastcount2now_days,
                        'consecutive_diffratio': consecutive_diffratio,
                        'consecutive_perfratio': consecutive_perfratio,
                        'consecutive_start_pgvra': consecutive_start_pgvra,
                        'consecu_enddate': consecu_enddate}

            # 计算区间峰值后的连续下跌指标
            if PostSecStart_PeakDate is not None and len(gap_data.loc[PostSecStart_PeakDate:]) > 2:
                postsecpeak_consecutive = consecutive_count(gap_data.loc[PostSecStart_PeakDate:].copy(), mode='drop')
                Result_Loc['PostSecPeak_DownConsecutive_Num'] = postsecpeak_consecutive['count']  # 连续下跌次数
                Result_Loc['PostSecPeak_DownConsecutive_MaxLastDays'] = postsecpeak_consecutive[
                    'consecutive_maxlastdays']  # 最长连续下跌天数
            else:
                Result_Loc['PostSecPeak_DownConsecutive_Num'], \
                    Result_Loc['PostSecPeak_DownConsecutive_MaxLastDays'] = 0, 0

            # 计算前一区间峰值后的连续下跌指标
            if Section_PeakDate is not None and len(gap_data.loc[Section_PeakDate:]) > 2:
                postpresecpeak_consecutive = consecutive_count(
                    gap_data.loc[Section_PeakDate:Section_StartDate].copy(), mode='drop')
                Result_Loc['PostPreSecPeak_DownConsecutive_Num'] = postpresecpeak_consecutive['count']  # 连续下跌次数
                Result_Loc['PostPreSecPeak_DownConsecutive_MaxLastDays'] = postpresecpeak_consecutive[
                    'consecutive_maxlastdays']  # 最长连续下跌天数

            # 计算前一谷值后的连续上涨和下跌指标
            if PreNow_BottomDate is not None and len(gap_data_prenow_bottom) > 2:
                postprebottom_upconsecutive = consecutive_count(gap_data_prenow_bottom.copy(), mode='rise')
                postprebottom_downconsecutive = consecutive_count(gap_data_prenow_bottom.copy(), mode='drop')
                Result_Loc['PostPreNowBottom_UpConsecutive_Num'] = postprebottom_upconsecutive['count']  # 连续上涨次数
                Result_Loc['PostPreNowBottom_UpConsecutive_MaxLastDays'] = postprebottom_upconsecutive[
                    'consecutive_maxlastdays']  # 最长连续上涨天数
                Result_Loc['PostPreNowBottom_DownConsecutive_Num'] = postprebottom_downconsecutive['count']  # 连续下跌次数
                Result_Loc['PostPreNowBottom_DownConsecutive_MaxLastDays'] = postprebottom_downconsecutive[
                    'consecutive_maxlastdays']  # 最长连续下跌天数

            # 计算区间最大均线后的连续下跌指标
            if pd.notnull(Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']) and \
                    len(gap_data.loc[Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']:]) > 2:
                postsecmax_consecutive = consecutive_count(
                    gap_data.loc[Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']:].copy(), mode='drop')
                Result_Loc['PostSecMaxRollAvg_DownConsecutive_Num'] = postsecmax_consecutive['count']  # 连续下跌次数
                Result_Loc['PostSecMaxRollAvg_DownConsecutive_MaxLastDays'] = postsecmax_consecutive[
                    'consecutive_maxlastdays']  # 最长连续下跌天数
            else:
                Result_Loc['PostSecMaxRollAvg_DownConsecutive_Num'], \
                    Result_Loc['PostSecMaxRollAvg_DownConsecutive_MaxLastDays'] = 0, 0

            # 计算区间峰值后的连续上涨和下跌指标
            gap_data_section_peak_copy = gap_data_secpeak_nowsec.copy()
            downconsecutive_result = consecutive_count(gap_data_section_peak_copy, mode='drop')
            Result_Loc['DownConsecutive2Now_LastDays'] = downconsecutive_result['lastcount2now_days']  # 最近一次连续下跌到现在的天数
            Result_Loc['DownConsecutive_PGVRollAvg_DiffRatio'] = downconsecutive_result['consecutive_diffratio']  # 连续下跌期间均线变化比率
            Result_Loc['DownConsecutive_SumRatio'] = downconsecutive_result['consecutive_perfratio']  # 连续下跌期间累计涨跌幅
            Result_Loc['DownConsecutive_Start_PGVRollAvg'] = downconsecutive_result['consecutive_start_pgvra']  # 连续下跌起始均线值
            downconsecutive_enddate = downconsecutive_result['consecu_enddate']  # 连续下跌结束日期

            upconsecutive_result = consecutive_count(gap_data_section_peak_copy, mode='rise')
            Result_Loc['UpConsecutive2Now_LastDays'] = upconsecutive_result['lastcount2now_days']  # 最近一次连续上涨到现在的天数
            Result_Loc['UpConsecutive_PGVRollAvg_DiffRatio'] = upconsecutive_result['consecutive_diffratio']  # 连续上涨期间均线变化比率
            Result_Loc['UpConsecutive_SumRatio'] = upconsecutive_result['consecutive_perfratio']  # 连续上涨期间累计涨跌幅
            Result_Loc['UpConsecutive_Start_PGVRollAvg'] = upconsecutive_result['consecutive_start_pgvra']  # 连续上涨起始均线值
            upconsecutive_enddate = upconsecutive_result['consecu_enddate']  # 连续上涨结束日期

            # 计算突破连续下跌起始均线的天数
            if Result_Loc['DownConsecutive_Start_PGVRollAvg'] != 0 and downconsecutive_enddate is not None \
                    and any(gap_data.loc[downconsecutive_enddate:, 'pgv_rollavg'] >
                            Result_Loc['DownConsecutive_Start_PGVRollAvg']):
                downstart_pgv = Result_Loc['DownConsecutive_Start_PGVRollAvg']
                down_break_date = gap_data.loc[downconsecutive_enddate:].query('pgv_rollavg>@downstart_pgv').index[0]
                Result_Loc['Break_DownSecutiveStart_First2Now_LastDays'] = len(gap_data.loc[down_break_date:]) - 1

            # 计算突破连续上涨起始均线的天数
            if Result_Loc['UpConsecutive_Start_PGVRollAvg'] != 0 and upconsecutive_enddate is not None \
                    and any(gap_data.loc[upconsecutive_enddate:, 'pgv_rollavg'] <
                            Result_Loc['UpConsecutive_Start_PGVRollAvg']):
                upstart_pgv = Result_Loc['UpConsecutive_Start_PGVRollAvg']
                up_break_date = gap_data.loc[upconsecutive_enddate:].query('pgv_rollavg<@upstart_pgv').index[0]
                Result_Loc['Break_UpSecutiveStart_First2Now_LastDays'] = len(gap_data.loc[up_break_date:]) - 1

            # 计算连续下跌最后一次到突破的天数
            Result_Loc['DownConsecutive_Last2Break_LastDays'] = \
                Result_Loc['DownConsecutive2Now_LastDays'] - Result_Loc.get('Break_DownSecutiveStart_First2Now_LastDays', 0) \
                if pd.notnull(Result_Loc.get('Break_DownSecutiveStart_First2Now_LastDays')) else 100

            # 计算连续上涨最后一次到突破的天数
            Result_Loc['UpConsecutive_Last2Break_LastDays'] = \
                Result_Loc['UpConsecutive2Now_LastDays'] - Result_Loc.get('Break_UpSecutiveStart_First2Now_LastDays', 0) \
                if pd.notnull(Result_Loc.get('Break_UpSecutiveStart_First2Now_LastDays')) else 100

            # 计算当前区间后的连续上涨指标
            postnow_data = gap_data.loc[Now_SecDate:]
            if PostNowSec_LastDays > 3 and len(postnow_data) > 2:
                postnowsec_consecutive = consecutive_count(postnow_data.copy(), mode='rise')
                Result_Loc['PostNowSec_UpConsecutive_Num'] = postnowsec_consecutive['count']
                Result_Loc['PostNowSec_UpConsecutive_MaxLastDays'] = postnowsec_consecutive['consecutive_maxlastdays']
            else:
                Result_Loc['PostNowSec_UpConsecutive_Num'], Result_Loc['PostNowSec_UpConsecutive_MaxLastDays'] = 0, 0

            # 计算区间起始后的连续上涨指标
            postsec_data = gap_data.loc[Section_StartDate:]
            if PostSecStart_LastDays > 3 and len(postsec_data) > 2:
                postsecstart_consecutive = consecutive_count(postsec_data.copy(), mode='rise')
                Result_Loc['PostSecStart_UpConsecutive_Num'] = postsecstart_consecutive['count']
                Result_Loc['PostSecStart_UpConsecutive_MaxLastDays'] = postsecstart_consecutive['consecutive_maxlastdays']
            else:
                Result_Loc['PostSecStart_UpConsecutive_Num'], Result_Loc['PostSecStart_UpConsecutive_MaxLastDays'] = 0, 0

            # 计算前一区间的均线均值和最大值
            pre_prenow_date = gap_data.loc[:PreNow_SecDate].index[-2] if len(gap_data.loc[:PreNow_SecDate]) > 1 else PreNow_SecDate
            prenow_data = gap_data.loc[PreNow_SecDate:Now_SecDate, 'pgv_rollavg']
            Result_Loc['PreNowSec_PGV_MeanRollAvg'] = round(prenow_data.mean(), 3)
            Result_Loc['PreNowSec_PGV_MaxRollAvg'] = round(gap_data.loc[pre_prenow_date:Now_SecDate, 'pgv_rollavg'].max(), 3)

            # 计算当前区间后与区间起始的均线均值比率
            if Section_StartDate < Now_SecDate and pd.notnull(Result_Loc['PostNowSec_PGV_MeanRollAvg']):
                sec_start_mean = gap_data.loc[Section_StartDate:Now_SecDate, 'pgv_rollavg'].mean()
                Result_Loc['PostNow2PostSec_PGV_MeanRollAvg_Ratio'] = round(
                    Result_Loc['PostNowSec_PGV_MeanRollAvg'] / sec_start_mean, 3) if sec_start_mean != 0 else 1
            else:
                Result_Loc['PostNow2PostSec_PGV_MeanRollAvg_Ratio'] = 1

            # 计算前一区间与区间起始的均线均值比率
            if Section_StartDate < PreNow_SecDate < Now_SecDate:
                prenow_mean = gap_data.loc[PreNow_SecDate:Now_SecDate, 'pgv_rollavg'].mean()
                sec_start_mean = gap_data.loc[Section_StartDate:PreNow_SecDate, 'pgv_rollavg'].mean()
                Result_Loc['PreNow2PostSec_PGV_MeanRollAvg_Ratio'] = round(
                    prenow_mean / sec_start_mean, 3) if sec_start_mean != 0 else 1
            else:
                Result_Loc['PreNow2PostSec_PGV_MeanRollAvg_Ratio'] = 1

            # 计算当前区间后与前一区间的均线均值比率
            if pd.notnull(Result_Loc['PostNowSec_PGV_MeanRollAvg']):
                prenow_mean = gap_data.loc[PreNow_SecDate:Now_SecDate, 'pgv_rollavg'].mean()
                Result_Loc['PostNow2PreNow_PGV_MeanRollAvg_Ratio'] = round(
                    Result_Loc['PostNowSec_PGV_MeanRollAvg'] / prenow_mean, 3) if prenow_mean != 0 else 1
            else:
                Result_Loc['PostNow2PreNow_PGV_MeanRollAvg_Ratio'] = 1

            # 计算区间最大均线到最小均线期间的累计涨跌幅
            if pd.notnull(Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']) and \
               pd.notnull(Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']):
                max_to_min_data = gap_data.loc[Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']:
                                             Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date'], 'daily_ratio'].iloc[1:]
                Result_Loc['PostSecMaxRollAvg_PGV_MaxRollAvg2Min_SumRatio'] = round(
                    ((max_to_min_data / 100 + 1).product() - 1) * 100, 3)
            else:
                Result_Loc['PostSecMaxRollAvg_PGV_MaxRollAvg2Min_SumRatio'] = -100

            # 计算区间最小均线到现在的累计涨跌幅
            if pd.notnull(Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']):
                min_to_now_data = gap_data.loc[Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']:, 'daily_ratio'].iloc[1:]
                if len(min_to_now_data) > 0:
                    Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg2Now_SumRatio'] = round(
                        ((min_to_now_data / 100 + 1).product() - 1) * 100, 3)
                else:
                    Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg2Now_SumRatio'] = 0
            else:
                Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg2Now_SumRatio'] = 0

            # 计算区间峰值后最小均线到现在的累计涨跌幅
            if pd.notnull(Result_Loc['PostSecPeak_PGV_MinRollAvg_Date']):
                peak_min_to_now_data = gap_data.loc[Result_Loc['PostSecPeak_PGV_MinRollAvg_Date']:, 'daily_ratio'].iloc[1:]
                if len(peak_min_to_now_data) > 0:
                    Result_Loc['PostSecPeak_PGV_MinRollAvg2Now_SumRatio'] = round(
                        ((peak_min_to_now_data / 100 + 1).product() - 1) * 100, 3)
                else:
                    Result_Loc['PostSecPeak_PGV_MinRollAvg2Now_SumRatio'] = 0
            else:
                Result_Loc['PostSecPeak_PGV_MinRollAvg2Now_SumRatio'] = 0

            # 计算区间上升段最大均线值和下降段最小均线值
            if Result_Common['PostSecStart_RiseSecOver10Days_StartDate'] != '-':
                rise_data = gap_data.loc[Result_Common['PostSecStart_RiseSecOver10Days_StartDate']:
                                       Result_Common['PostSecStart_RiseSecOver10Days_EndDate'], 'pgv_rollavg']
                Result_Loc['PostSec_RiseSec10Days_PGV_MaxRollAvg'] = rise_data.max()
            else:
                Result_Loc['PostSec_RiseSec10Days_PGV_MaxRollAvg'] = 0

            if Result_Common['PreNowSec_DropSecOver10Days_StartDate'] != '-':
                drop_data = gap_data.loc[Result_Common['PreNowSec_DropSecOver10Days_StartDate']:
                                       Result_Common['PreNowSec_DropSecOver10Days_EndDate'], 'pgv_rollavg']
                Result_Loc['PreNowSec_DropSec10Days_PGV_MinRollAvg'] = drop_data.min()
            else:
                Result_Loc['PreNowSec_DropSec10Days_PGV_MinRollAvg'] = 0

            # 分析价格走势模式
            if len(gap_data) > 14:
                pattern_result = analyze_pgv_pattern(gap_data, recent_window=7, compare_window=14,
                                                  rise_threshold=0.15, decline_threshold=-0.1)
                Result_Loc.update({
                    'Is_Bottom_Reversal': pattern_result['is_bottom_reversal'],
                    'Is_Expanding': pattern_result['is_expanding'],
                    'Is_More_Volatile': pattern_result['is_more_volatile']
                })
            else:
                Result_Loc.update({
                    'Is_Bottom_Reversal': 0,
                    'Is_Expanding': 0,
                    'Is_More_Volatile': 0
                })

            # turnover_efficiency相关指标计算
            # 计算Section_PeakDate之后的最大turnover效率值及相关指标
            postsecpeak_data = gap_data.loc[Section_PeakDate:]
            postsecpeak_eff = postsecpeak_data['efficiency_ma5']
            Result_Loc.update({
                'PostSecPeak_MaxTO_Eff': round(postsecpeak_eff.max(), 3),
                'PostSecPeak_MaxTO_Eff_Date': postsecpeak_eff.idxmax(),
                'PostSecPeak_MaxTO_Eff2Now_LastDays': len(postsecpeak_data.loc[postsecpeak_eff.idxmax():]) - 1,
                'PostSecPeak_TO_Eff_Max2Min_Ratio': round(postsecpeak_eff.max() / postsecpeak_eff.min(), 3)
                    if postsecpeak_eff.min() != 0 else 0
            })

            # 计算当前turnover效率值
            Result_Loc['Now_TO_Eff'] = round(gap_data['efficiency_ma5'].iloc[-1], 3)

            # 计算PreNow_PeakDate之后的turnover效率相关指标
            postprenow_data = gap_data.loc[PreNow_PeakDate:]
            if len(postprenow_data) > 2:
                postprenow_eff = postprenow_data['efficiency_ma5']
                postprenow_maxeff = postprenow_eff.max()
                postprenow_maxeff_date = postprenow_eff.idxmax()

                Result_Loc.update({
                    'PostPreNowPeak_MaxTO_Eff': round(postprenow_maxeff, 3),
                    'PostPreNowPeak_MaxTO_Eff_Date': postprenow_maxeff_date,
                    'PostPreNowPeak_MaxTO_Eff2Now_LastDays': len(postprenow_data.loc[postprenow_maxeff_date:]) - 1 if pd.notnull(postprenow_maxeff_date) else 99 ,
                    'PostPreNowPeak_TO_Eff_Max2Min_Ratio': round(postprenow_maxeff / postprenow_eff.min(), 3)
                        if postprenow_eff.min() != 0 else 0
                })

                # 计算最大效率值的覆盖天数
                pre_max_data = gap_data.loc[:postprenow_maxeff_date].iloc[:-1] \
                    if pd.notnull(postprenow_maxeff_date) and len(gap_data.loc[:postprenow_maxeff_date]) > 0 else gap_data.iloc[:-1]
                if len(pre_max_data.query('efficiency_ma5>@postprenow_maxeff')) > 0:
                    break_date = pre_max_data.query('efficiency_ma5>@postprenow_maxeff').index[-1]
                    Result_Loc['PostPreNowPeak_MaxTO_Eff_CoverDays'] = len(gap_data.loc[break_date:postprenow_maxeff_date]) - 1
                else:
                    Result_Loc['PostPreNowPeak_MaxTO_Eff_CoverDays'] = len(gap_data.loc[:postprenow_maxeff_date]) - 1 \
                        if pd.notnull(postprenow_maxeff_date) else 999

                # 计算上升/下降效率差值相关指标
                u2d_eff = postprenow_data['up2down_efficiency_ma5']
                u2d_maxeff = u2d_eff.max()
                u2d_maxeff_date = u2d_eff.idxmax()

                Result_Loc.update({
                    'PostPreNowPeak_U2D_MaxTO_Eff': round(u2d_maxeff, 3),
                    'PostPreNowPeak_U2D_MaxTO_Eff_Date': u2d_maxeff_date,
                    'PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays': len(postprenow_data.loc[u2d_maxeff_date:]) - 1
                })

                # 计算上升/下降效率差值的覆盖天数
                pre_u2d_data = gap_data.loc[:u2d_maxeff_date].iloc[:-1]
                if len(pre_u2d_data.query('up2down_efficiency_ma5>@u2d_maxeff')) > 0:
                    u2d_break_date = pre_u2d_data.query('up2down_efficiency_ma5>@u2d_maxeff').index[-1]
                    Result_Loc['PostPreNowPeak_U2D_MaxTO_Eff_CoverDays'] = len(gap_data.loc[u2d_break_date:u2d_maxeff_date]) - 1
                else:
                    Result_Loc['PostPreNowPeak_U2D_MaxTO_Eff_CoverDays'] = len(gap_data.loc[:u2d_maxeff_date]) - 1

                # 计算上升/下降效率差值最小值相关指标
                u2d_mineff = u2d_eff.min()
                u2d_mineff_date = u2d_eff.idxmin()

                Result_Loc.update({
                    'PostPreNowPeak_U2D_MinTO_Eff': round(u2d_mineff, 3),
                    'PostPreNowPeak_U2D_MinTO_Eff_Date': u2d_mineff_date,
                    'PostPreNowPeak_U2D_MinTO_Eff2Now_LastDays': len(postprenow_data.loc[u2d_mineff_date:]) - 1
                })
                # 计算上升/下降效率差值最小值的覆盖天数
                pre_u2d_min_data = gap_data.loc[:u2d_mineff_date].iloc[:-1]
                if len(pre_u2d_min_data.query('up2down_efficiency_ma5<@u2d_mineff')) > 0:
                    u2d_min_break_date = pre_u2d_min_data.query('up2down_efficiency_ma5<@u2d_mineff').index[-1]
                    Result_Loc['PostPreNowPeak_U2D_MinTO_Eff_CoverDays'] = len(gap_data.loc[u2d_min_break_date:u2d_mineff_date]) - 1
                else:
                    Result_Loc['PostPreNowPeak_U2D_MinTO_Eff_CoverDays'] = len(gap_data.loc[:u2d_mineff_date]) - 1

            else:
                Result_Loc['PostPreNowPeak_MaxTO_Eff_Date'] = '-'


            # 计算Section_StartDate之后的turnover效率相关指标
            postsec_data = gap_data.loc[Section_StartDate:]
            if len(postsec_data) > 2:
                postsec_eff = postsec_data['efficiency_ma5']

                # 检查是否有有效数据，避免all-NA的idxmin()警告
                if postsec_eff.notna().any():
                    postsec_mineff = postsec_eff.min()
                    postsec_mineff_date = postsec_eff.idxmin()

                    # 添加空值检查，避免使用NaN进行索引切片
                    postsec_mineff_days = len(postsec_data.loc[postsec_mineff_date:]) - 1 \
                        if pd.notnull(postsec_mineff_date) and postsec_mineff_date in postsec_data.index else 0
                else:
                    # 如果所有值都是NA，设置默认值
                    postsec_mineff = None
                    postsec_mineff_date = None
                    postsec_mineff_days = 0

                Result_Loc.update({
                    'PostSec_MinTO_Eff': postsec_mineff,
                    'PostSec_MinTO_Eff_Date': postsec_mineff_date,
                    'PostSec_MinTO_Eff2Now_LastDays': postsec_mineff_days,
                    'PostSec_TO_Eff_Min2Max_Ratio': round(postsec_mineff / postsec_eff.max(), 3)
                        if postsec_eff.notna().any() and postsec_eff.max() != 0 else 0
                })

                # 只有当postsec_mineff_date有效时才计算相关指标
                if pd.notnull(postsec_mineff_date) and postsec_mineff_date in gap_data.index:
                    # 计算最小效率值的覆盖天数
                    pre_min_data = gap_data.loc[:postsec_mineff_date].iloc[:-1]
                    if len(pre_min_data.query('efficiency_ma5<@postsec_mineff')) > 0:
                        min_break_date = pre_min_data.query('efficiency_ma5<@postsec_mineff').index[-1]
                        Result_Loc['PostSec_MinTO_Eff_CoverDays'] = len(gap_data.loc[min_break_date:postsec_mineff_date]) - 1
                    else:
                        Result_Loc['PostSec_MinTO_Eff_CoverDays'] = len(gap_data.loc[:postsec_mineff_date]) - 1

                    # 效率值相关指标计算
                    if len(gap_data.loc[:postsec_mineff_date]) > 20:
                        Result_Loc['Eff_Recent2Previous_MinChange'] = compare_field_trend(
                            gap_data.loc[:postsec_mineff_date],
                            field='efficiency_ma5',
                            window=10,
                            threshold=0.15
                        )
                    else:
                        Result_Loc['Eff_Recent2Previous_MinChange'] = 0

                    overall_eff_mean = postsec_eff.mean()
                    overall_eff_std = postsec_eff.std()
                    lower_bound = overall_eff_mean - overall_eff_std
                    upper_bound = overall_eff_mean - 0.5 * overall_eff_std

                    Result_Loc['Is_LowBound_Oscillation'] = int(
                        lower_bound <= postsec_mineff <= upper_bound or
                        abs(Result_Loc['Eff_Recent2Previous_MinChange']) < 0.15
                    )
                else:
                    # 当postsec_mineff_date无效时，设置默认值
                    Result_Loc['PostSec_MinTO_Eff_CoverDays'] = 0
                    Result_Loc['Eff_Recent2Previous_MinChange'] = 0
                    Result_Loc['Is_LowBound_Oscillation'] = 0

            # 计算效率值的整体变动趋势
            Result_Loc['Eff_Recent2Previous_Change'] = compare_field_trend(gap_data, field='efficiency_ma5')

            # 计算efficiency_ma5的5日变动率(百分比)
            gap_data['efficiency_change_rate'] = gap_data['efficiency_ma5'].ffill().pct_change(5) * 100

            # 如果存在最近一次效率峰值日期,计算该日期的变动率指标
            if Result_Loc['PostPreNowPeak_MaxTO_Eff_Date'] != '-' and pd.notnull(Result_Loc['PostPreNowPeak_MaxTO_Eff_Date']):
                latest_peak_date = Result_Loc['PostPreNowPeak_MaxTO_Eff_Date']
                change_rate = gap_data.loc[latest_peak_date, 'efficiency_change_rate']
                Result_Loc['Recent_EffPeak_ChangeRate'] = round(
                    change_rate, 3) if not np.isinf(change_rate) else 0

                # 计算变动率在所有正向变动率中的百分位数
                positive_rates = gap_data[gap_data['efficiency_change_rate'] > 0]['efficiency_change_rate']
                if len(positive_rates) > 0:
                    peak_rate_percentile = round(
                        (100 - stats.percentileofscore(positive_rates,
                                            Result_Loc['Recent_EffPeak_ChangeRate'])) / 100, 3)
                    Result_Loc['Recent_EffPeak_ChangeRate_Percentile'] = peak_rate_percentile
                else:
                    Result_Loc['Recent_EffPeak_ChangeRate_Percentile'] = 1
            else:
                Result_Loc['Recent_EffPeak_ChangeRate'] = 0
                Result_Loc['Recent_EffPeak_ChangeRate_Percentile'] = 1

            # 调用函数计算效率带
            eff_data = gap_data['efficiency_ma5']
            threshold = 0.5

            # 计算最大效率值的分位值
            Result_Loc['PostPreNowPeak_MaxTO_Eff_Band'] = calculate_bias_percentile_band(
                Result_Loc.get('PostPreNowPeak_MaxTO_Eff'),
                eff_data,
                threshold
            )

            # 计算最小效率值的分位值
            Result_Loc['PostSec_MinTO_Eff_Band'] = calculate_bias_percentile_band(
                Result_Loc.get('PostSec_MinTO_Eff'),
                eff_data,
                threshold
            )

            # 计算最新效率值的分位值
            Result_Loc['Latest_TO_Eff_Band'] = calculate_bias_percentile_band(
                eff_data.iloc[-1] if len(eff_data) > 0 else None,
                eff_data,
                threshold
            )

            # 获取上升下降效率数据
            u2d_eff_data = gap_data['up2down_efficiency_ma5']

            # 计算上升下降效率的最大值分位值
            Result_Loc['PostPreNowPeak_U2D_MaxTO_Eff_Band'] = calculate_bias_percentile_band(
                Result_Loc.get('PostPreNowPeak_U2D_MaxTO_Eff'),
                u2d_eff_data,
                threshold
            )

            # 只有当数据长度大于14天时才进行效率波动分析
            if len(gap_data) > 14:
                try:
                    eff_analysis = analyze_efficiency_peaks(gap_data, field='efficiency_ma5', window=10, threshold=threshold)
                    Result_Loc['Eff_Avg_Peak_Period'] = eff_analysis.get('avg_peak_period', 0)
                    Result_Loc['Eff_Avg_Valley_Period'] = eff_analysis.get('avg_valley_period', 0)
                    Result_Loc['Eff_Peak_Intensity'] = eff_analysis.get('avg_peak_intensity', 0)
                    Result_Loc['Eff_Valley_Intensity'] = eff_analysis.get('avg_valley_intensity', 0)

                    if eff_analysis.get('last_peak_date'):
                        Result_Loc['Days_From_Last_Peak'] = len(gap_data.loc[eff_analysis['last_peak_date']:]) - 1
                    else:
                        Result_Loc['Days_From_Last_Peak'] = 0

                    if eff_analysis.get('last_valley_date'):
                        Result_Loc['Days_From_Last_Valley'] = len(gap_data.loc[eff_analysis['last_valley_date']:]) - 1
                    else:
                        Result_Loc['Days_From_Last_Valley'] = 0
                except Exception as e:
                    print(f"Error in efficiency analysis: {e}")
                    Result_Loc['Eff_Avg_Peak_Period'] = 0
                    Result_Loc['Eff_Avg_Valley_Period'] = 0
                    Result_Loc['Eff_Peak_Intensity'] = 0
                    Result_Loc['Eff_Valley_Intensity'] = 0
                    Result_Loc['Days_From_Last_Peak'] = 0
                    Result_Loc['Days_From_Last_Valley'] = 0

            # 新增指标20240102：计算pgv_rollavg的分位值
            # 计算PostSecMaxRollAvg_PGV_MinRollAvg的分位值
            if pd.notnull(Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg']):
                Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Band'] = calculate_bias_percentile_band(
                    Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg'],
                    gap_data['pgv_rollavg'],
                    threshold=0.5
                )

                # 计算覆盖天数
                min_value = Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg']
                min_date = Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_Date']
                pre_min_data = gap_data.loc[:min_date].iloc[:-1]

                if len(pre_min_data.query('pgv_rollavg<@min_value')) > 0:
                    break_date = pre_min_data.query('pgv_rollavg<@min_value').index[-1]
                    Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_CoverDays'] = len(gap_data.loc[break_date:min_date]) - 1
                else:
                    Result_Loc['PostSecMaxRollAvg_PGV_MinRollAvg_CoverDays'] = len(gap_data.loc[:min_date]) - 1

            # 计算PostSecStart_PGV_MaxRollAvg的分位值
            if pd.notnull(Result_Loc['PostSecStart_PGV_MaxRollAvg']):
                Result_Loc['PostSecStart_PGV_MaxRollAvg_Band'] = calculate_bias_percentile_band(
                    Result_Loc['PostSecStart_PGV_MaxRollAvg'],
                    gap_data['pgv_rollavg'],
                    threshold=0.5
                )

                # 计算覆盖天数
                max_value = Result_Loc['PostSecStart_PGV_MaxRollAvg']
                max_date = Result_Loc['PostSecStart_PGV_MaxRollAvg_Date']
                pre_max_data = gap_data.loc[:max_date].iloc[:-1]

                if len(pre_max_data.query('pgv_rollavg>@max_value')) > 0:
                    break_date = pre_max_data.query('pgv_rollavg>@max_value').index[-1]
                    Result_Loc['PostSecStart_PGV_MaxRollAvg_CoverDays'] = len(gap_data.loc[break_date:max_date]) - 1
                else:
                    Result_Loc['PostSecStart_PGV_MaxRollAvg_CoverDays'] = len(gap_data.loc[:max_date]) - 1

            Result_Loc['PostNowSec_PGV_MaxRollAvg_Date'] = gap_data.loc[Now_SecDate:, 'pgv_rollavg'].idxmax()

            # 计算PostNowSec_PGV_MaxRollAvg的分位值
            if pd.notnull(Result_Loc['PostNowSec_PGV_MaxRollAvg']):
                Result_Loc['PostNowSec_PGV_MaxRollAvg_Band'] = calculate_bias_percentile_band(
                    Result_Loc['PostNowSec_PGV_MaxRollAvg'],
                    gap_data['pgv_rollavg'],
                    threshold=0.5
                )

                # 计算覆盖天数
                max_value = Result_Loc['PostNowSec_PGV_MaxRollAvg']
                gap_data_now = gap_data.loc[:Now_SecDate].iloc[:-1].copy()

                if len(gap_data_now.query('pgv_rollavg>@max_value')) > 0:
                    break_date = gap_data_now.query('pgv_rollavg>@max_value').index[-1]
                    Result_Loc['PostNowSec_PGV_MaxRollAvg_CoverDays'] = len(
                        gap_data.loc[break_date:Result_Loc['PostNowSec_PGV_MaxRollAvg_Date']]) - 1
                else:
                    Result_Loc['PostNowSec_PGV_MaxRollAvg_CoverDays'] = len(gap_data)

            Result_Loc['PostNowSec_PGVRollAvg_CoverDrop_Diff'] = 0
            if NowSec_SecConcave_StartDate is not None and NowSec_SecConcave_StartDate != '-':
                nowcon_maxpgv = gap_data.loc[NowSec_SecConcave_StartDate:PreNow_BottomDate, 'pgv_rollavg'].max()
                Result_Loc['PostNowSec_PGVRollAvg_CoverDrop_DiffRatio'] = round(
                    Result_Loc['PostNowSec_PGV_MaxRollAvg'] / nowcon_maxpgv, 3) \
                        if pd.notnull(Result_Loc['PostNowSec_PGV_MaxRollAvg']) and nowcon_maxpgv != 0 else 0
                Result_Loc['PostNowSec_PGVRollAvg_CoverDrop_Diff'] = round(
                    Result_Loc['PostNowSec_PGV_MaxRollAvg'] - nowcon_maxpgv, 3) \
                        if pd.notnull(Result_Loc['PostNowSec_PGV_MaxRollAvg']) else 0


            # 计算当前日期pgv_rollavg的rank位置(0-1之间)
            Result_Loc['Now_PGV_RollAvg_Rank'] = gap_data['pgv_rollavg'].rank(pct=True, ascending=True).iloc[-1]

            # 计算pgv_rollavg的变动趋势
            Result_Loc['PGV_RollAvg_Recent2Previous_Change'] = compare_field_trend(gap_data, field='pgv_rollavg')

            # 计算pgv_rollavg在Now_SecDate后的变动趋势
            Result_Loc['PGV_RollAvg_NowSec2Previous_Change'] = compare_field_trend(
                gap_data_now_sec, field='pgv_rollavg', window=len(gap_data_now_sec))

            # 计算pgv_rollavg的波动率比值
            Result_Loc['PGV_RollAvg_VolatilityRatio'] = calculate_volatility_ratio(
                gap_data,
                'pgv_rollavg',
                recent_window=10,
                previous_window=20,
                direction='all'
            )

            # 计算efficiency_ma5的波动率比值
            Result_Loc['Efficiency_VolatilityRatio'] = calculate_volatility_ratio(
                gap_data,
                'efficiency_ma5',
                recent_window=10,
                previous_window=20,
                direction='all'
            )

            # 计算up2down_efficiency_ma5的波动率比值
            Result_Loc['U2D_Efficiency_VolatilityRatio'] = calculate_volatility_ratio(
                gap_data,
                'up2down_efficiency_ma5',
                recent_window=10,
                previous_window=20,
                direction='all'
            )

            # 计算最新efficiency_ma5相对前3天最低值的状态
            # 检查分母是否为0
            pre3_mean = gap_data['efficiency_ma5'].iloc[-4:-1].mean()
            if pre3_mean == 0:
                Result_Loc['Eff_Latest2Pre3Mean'] = 1
            else:
                Result_Loc['Eff_Latest2Pre3Mean'] = round(
                    gap_data['efficiency_ma5'].iloc[-1] / pre3_mean, 3)

            Result_Loc['UpEff_Latest2Pre3Mean'] = round(
                gap_data['up_efficiency_ma5'].iloc[-1] / gap_data['up_efficiency_ma5'].iloc[-4:-1].mean(),
                3) if gap_data['up_efficiency_ma5'].iloc[-4:-1].mean() != 0 else 1

            # 新增指标20250112：计算PreNow_SecDate后的efficiency_ma5的最大值日期,以及当前值距离最大值天数
            if pd.notnull(PreNow_SecDate):
                postprenow_maxeff_date = gap_data.loc[PreNow_SecDate:, 'up_efficiency_ma5'].idxmax()
                Result_Loc['PostPreNow_MaxUpEff_Date'] = postprenow_maxeff_date
                Result_Loc['PostPreNow_MaxUpEff2Now_LastDays'] = len(gap_data.loc[postprenow_maxeff_date:]) - 1

            # 计算efficiency_ma5和up_efficiency_ma5的最近波峰位置
            def find_peak_date(data, field):
                if len(data) <= 8:
                    return None

                for i in range(1, len(data)-8):
                    current_value = data[field].iloc[-i]
                    before_values = data[field].iloc[-i-8:-i]

                    if not before_values.empty and all(current_value > before_values):
                        return data.index[-i]
                return None

            # 计算波峰日期和距今天数
            def calc_peak_metrics(data, peak_date, prefix):
                if peak_date is not None and peak_date in data.index:
                    Result_Loc[f'Latest_{prefix}Peak_Date'] = peak_date
                    Result_Loc[f'Latest_{prefix}Peak2Now_Days'] = len(data.loc[peak_date:]) - 1
                else:
                    Result_Loc[f'Latest_{prefix}Peak_Date'] = None
                    Result_Loc[f'Latest_{prefix}Peak2Now_Days'] = None

            # 计算efficiency_ma5的波峰指标
            eff_peak_date = find_peak_date(gap_data, 'efficiency_ma5')
            calc_peak_metrics(gap_data, eff_peak_date, 'Eff_')

            # 计算up_efficiency_ma5的波峰指标
            upeff_peak_date = find_peak_date(gap_data, 'up_efficiency_ma5')
            calc_peak_metrics(gap_data, upeff_peak_date, 'UpEff_')

            # 获取upefficiency_ma5的分位值
            upeff_data = gap_data['up_efficiency_ma5']

            # 检查PreNow_PeakDate是否存在且有效
            if pd.notnull(PreNow_SecDate) and PreNow_SecDate in upeff_data.index:
                postprenow_upeff_max = upeff_data.loc[PreNow_SecDate:].max()
            else:
                postprenow_upeff_max = None

            # 计算上升下降效率的最大值分位值
            if pd.notnull(postprenow_upeff_max):
                Result_Loc['PostPreNow_UpEff_MaxTO_Eff_Band'] = calculate_bias_percentile_band(
                    postprenow_upeff_max,
                    upeff_data,
                    threshold
                )
            else:
                Result_Loc['PostPreNow_UpEff_MaxTO_Eff_Band'] = None

            # 新增指标20250207：获取指定日期前5日的pgv_rollavg均值
            # 检查PreTurn_PeakDate是否存在且有效
            if pd.notnull(PreTurn_PeakDate) and PreTurn_PeakDate in gap_data.index:
                pre_turn_peak_data = gap_data.loc[:PreTurn_PeakDate, 'pgv_rollavg'].iloc[-5:]
                post_turn_peak_data = gap_data.loc[PreTurn_PeakDate:, 'pgv_rollavg'].iloc[:5]
                combined_data = pd.concat([pre_turn_peak_data, post_turn_peak_data]).sort_values(ascending=False)
                num_values = min(3, len(combined_data))
                Result_Loc['PreTurnPeak_PRV_Top3Mean'] = round(combined_data.iloc[:num_values].mean(), 3)
            else:
                Result_Loc['PreTurnPeak_PRV_Top3Mean'] = None

            # 检查日期是否存在且有效
            if pd.notnull(Section_PeakDate) and Section_PeakDate in gap_data.index:
                pre_section_peak_data = gap_data.loc[:Section_PeakDate, 'pgv_rollavg'].iloc[-5:]
                post_section_peak_data = gap_data.loc[Section_PeakDate:, 'pgv_rollavg'].iloc[:5]
                combined_data = pd.concat([pre_section_peak_data, post_section_peak_data]).sort_values(ascending=False)
                num_values = min(3, len(combined_data))
                Result_Loc['SectionPeak_PRV_Top3Mean'] = round(combined_data.iloc[:num_values].mean(), 3)
            else:
                Result_Loc['SectionPeak_PRV_Top3Mean'] = None

            if pd.notnull(Section_StartDate) and Section_StartDate in gap_data.index:
                pre_section_start_data = gap_data.loc[:Section_StartDate, 'pgv_rollavg'].iloc[-5:]
                post_section_start_data = gap_data.loc[Section_StartDate:, 'pgv_rollavg'].iloc[:5]
                combined_data = pd.concat([pre_section_start_data, post_section_start_data]).sort_values(ascending=True)
                num_values = min(3, len(combined_data))
                Result_Loc['SectionStart_PRV_Top3Mean'] = round(combined_data.iloc[:num_values].mean(), 3)
            else:
                Result_Loc['SectionStart_PRV_Top3Mean'] = None

            if pd.notnull(Now_SecDate) and Now_SecDate in gap_data.index:
                pre_now_sec_data = gap_data.loc[:Now_SecDate, 'pgv_rollavg'].iloc[-5:]
                post_now_sec_data = gap_data.loc[Now_SecDate:, 'pgv_rollavg'].iloc[:5]
                combined_data = pd.concat([pre_now_sec_data, post_now_sec_data]).sort_values(ascending=True)
                num_values = min(3, len(combined_data))
                Result_Loc['NowSec_PRV_Top3Mean'] = round(combined_data.iloc[:num_values].mean(), 3)
            else:
                Result_Loc['NowSec_PRV_Top3Mean'] = None

            if pd.notnull(PreNow_SecDate) and PreNow_SecDate in gap_data.index:
                pre_prenow_sec_data = gap_data.loc[:PreNow_SecDate, 'pgv_rollavg'].iloc[-5:]
                post_prenow_sec_data = gap_data.loc[PreNow_SecDate:, 'pgv_rollavg'].iloc[:5]
                combined_data = pd.concat([pre_prenow_sec_data, post_prenow_sec_data]).sort_values(ascending=False)
                num_values = min(3, len(combined_data))
                Result_Loc['PreNowSec_PRV_Top3Mean'] = round(combined_data.iloc[:num_values].mean(), 3)
            else:
                Result_Loc['PreNowSec_PRV_Top3Mean'] = None

            if pd.notnull(PreNow_PeakDate) and PreNow_PeakDate in gap_data.index:
                pre_prenow_peak_data = gap_data.loc[:PreNow_PeakDate, 'pgv_rollavg'].iloc[-5:]
                post_prenow_peak_data = gap_data.loc[PreNow_PeakDate:, 'pgv_rollavg'].iloc[:5]
                combined_data = pd.concat([pre_prenow_peak_data, post_prenow_peak_data]).sort_values(ascending=False)
                num_values = min(3, len(combined_data))
                Result_Loc['PreNowPeak_PRV_Top3Mean'] = round(combined_data.iloc[:num_values].mean(), 3)
            else:
                Result_Loc['PreNowPeak_PRV_Top3Mean'] = None

            Result_Loc['PostSecPeak_PRV_Top3Mean'] = None
            Result_Loc['PostSecPeak_PRV_Top3Mean_CoverDays'] = None
            Result_Loc['PostSecPeak_PRA2Close_CoverDays_Diff'] = None
            if pd.notnull(PostSecStart_PeakDate) and PostSecStart_PeakDate in gap_data.index:
                pre_postsec_peak_data = gap_data.loc[:PostSecStart_PeakDate, 'pgv_rollavg'].iloc[-5:]
                post_postsec_peak_data = gap_data.loc[PostSecStart_PeakDate:, 'pgv_rollavg'].iloc[:5]
                combined_data = pd.concat([pre_postsec_peak_data, post_postsec_peak_data]).sort_values(ascending=False)
                num_values = min(3, len(combined_data))
                Result_Loc['PostSecPeak_PRV_Top3Mean'] = round(combined_data.iloc[:num_values].mean(), 3)
                postsec_peak_maxpra = combined_data.max()
                postsec_peak_maxpra_date = combined_data.idxmax()
                if len(gap_data.loc[:postsec_peak_maxpra_date].query('pgv_rollavg>@postsec_peak_maxpra')) > 0:
                    postsec_peak_maxpra_break_date = gap_data.loc[:postsec_peak_maxpra_date].query('pgv_rollavg>@postsec_peak_maxpra').index[-1]
                else:
                    postsec_peak_maxpra_break_date = gap_data.index[0]
                Result_Loc['PostSecPeak_PRV_Top3Mean_CoverDays'] = len(gap_data.loc[postsec_peak_maxpra_break_date:postsec_peak_maxpra_date]) - 1
                Result_Loc['PostSecPeak_PRA2Close_CoverDays_Diff'] = Result_Loc['PostSecPeak_PRV_Top3Mean_CoverDays'] - Result_Common['SecStart_MaxClose_UpCoverDays']


            if pd.notnull(Period_TurnDate) and Period_TurnDate in gap_data.index:
                pre_turn_data = gap_data.loc[:Period_TurnDate, 'pgv_rollavg'].iloc[-5:]
                post_turn_data = gap_data.loc[Period_TurnDate:, 'pgv_rollavg'].iloc[:5]
                combined_data = pd.concat([pre_turn_data, post_turn_data]).sort_values(ascending=True)
                num_values = min(3, len(combined_data))
                Result_Loc['Turn_PRV_Top3Mean'] = round(combined_data.iloc[:num_values].mean(), 3)
            else:
                Result_Loc['Turn_PRV_Top3Mean'] = None

            if pd.notnull(PostTurn_PeakDate) and PostTurn_PeakDate in gap_data.index:
                pre_postturn_peak_data = gap_data.loc[:PostTurn_PeakDate, 'pgv_rollavg'].iloc[-5:]
                post_postturn_peak_data = gap_data.loc[PostTurn_PeakDate:, 'pgv_rollavg'].iloc[:5]
                combined_data = pd.concat([pre_postturn_peak_data, post_postturn_peak_data]).sort_values(ascending=False)
                num_values = min(3, len(combined_data))
                Result_Loc['PostTurnPeak_PRV_Top3Mean'] = round(combined_data.iloc[:num_values].mean(), 3)
            else:
                Result_Loc['PostTurnPeak_PRV_Top3Mean'] = None

            # 新增指标20250303：计算最近3天最低pgv_rollavg在Section_StartDate后和Now_SecDate后的排名名次
            if pd.notnull(Section_StartDate) and Section_StartDate in gap_data.index \
                and len(gap_data.loc[Section_StartDate:]) > 2:
                section_start_data = gap_data.loc[Section_StartDate:, 'pgv_rollavg']
                recent_3d_min = gap_data.loc[gap_data.index[-3:], 'pgv_rollavg'].min()
                section_rank_series = section_start_data.rank(method='min', ascending=False)
                section_matched_ranks = section_rank_series[section_start_data == recent_3d_min]
                Result_Loc['Recent3Day_PRA_SectionStart_Rank'] = section_matched_ranks.iloc[0] if len(section_matched_ranks) > 0 else 999
            else:
                Result_Loc['Recent3Day_PRA_SectionStart_Rank'] = 999

            if pd.notnull(Now_SecDate) and Now_SecDate in gap_data.index \
                and len(gap_data.loc[Now_SecDate:]) > 2:
                now_sec_data = gap_data.loc[Now_SecDate:, 'pgv_rollavg']
                recent_3d_min = gap_data.loc[gap_data.index[-3:], 'pgv_rollavg'].min()
                now_sec_rank_series = now_sec_data.rank(method='min', ascending=False)
                now_sec_matched_ranks = now_sec_rank_series[now_sec_data == recent_3d_min]
                Result_Loc['Recent3Day_PRA_NowSec_Rank'] = now_sec_matched_ranks.iloc[0] if len(now_sec_matched_ranks) > 0 else 999
            else:
                Result_Loc['Recent3Day_PRA_NowSec_Rank'] = 999

            # 新增指标20250311 NowSecDate后PRA覆盖天数与Close覆盖天数差值
            # 计算当前日期pgv_rollavg的向上覆盖天数和向下覆盖天数
            nowsec_pgv_maxrollavg = gap_data.loc[Now_SecDate:, 'pgv_rollavg'].max()
            nowsec_pgv_maxrollavg_index = gap_data.loc[Now_SecDate:, 'pgv_rollavg'].idxmax()
            over_now_pgv_index = gap_data.query('pgv_rollavg>@nowsec_pgv_maxrollavg').index[-1] \
                if len(gap_data.query('pgv_rollavg>@nowsec_pgv_maxrollavg')) > 0 else gap_data.index[0]
            coverperiod_pgv_minrollavg = gap_data.loc[over_now_pgv_index:nowsec_pgv_maxrollavg_index, 'pgv_rollavg'].min() \
                if len(gap_data.loc[over_now_pgv_index:nowsec_pgv_maxrollavg_index, 'pgv_rollavg']) > 0 else 0
            Result_Loc['NowSec_PGV_MaxRollAvg_UpCoverDays'] = len(gap_data.loc[over_now_pgv_index:]) - 1
            Result_Loc['NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio'] = round(
                nowsec_pgv_maxrollavg / coverperiod_pgv_minrollavg, 3) \
                    if pd.notnull(nowsec_pgv_maxrollavg) and coverperiod_pgv_minrollavg != 0 else 0

            nowsec_pgv_minrollavg = gap_data.loc[Now_SecDate:, 'pgv_rollavg'].min()
            nowsec_pgv_minrollavg_index = gap_data.loc[Now_SecDate:, 'pgv_rollavg'].idxmin()
            under_now_pgv_index = gap_data.query('pgv_rollavg<@nowsec_pgv_minrollavg').index[-1] \
                if len(gap_data.query('pgv_rollavg<@nowsec_pgv_minrollavg')) > 0 else gap_data.index[0]
            coverperiod_pgv_maxrollavg = gap_data.loc[under_now_pgv_index:nowsec_pgv_minrollavg_index, 'pgv_rollavg'].max() \
                if len(gap_data.loc[under_now_pgv_index:nowsec_pgv_minrollavg_index, 'pgv_rollavg']) > 0 else 0
            Result_Loc['NowSec_PGV_MinRollAvg_DownCoverDays'] = len(gap_data.loc[under_now_pgv_index:]) - 1
            Result_Loc['NowSec_PGV_DownCoverPeriod_Min2Max_DiffRatio'] = round(
                nowsec_pgv_minrollavg / coverperiod_pgv_maxrollavg, 3) \
                    if pd.notnull(nowsec_pgv_minrollavg) and coverperiod_pgv_maxrollavg != 0 else 0

            Result_Loc['NowSec_PRA2Close_CoverDays_Diff'] = Result_Loc['NowSec_PGV_MaxRollAvg_UpCoverDays'] - \
                                                            Result_Common['NowSec_MaxClose_UpCoverDays']

            secstart_pgv_maxrollavg = gap_data.loc[Section_StartDate:, 'pgv_rollavg'].max()
            secstart_pgv_maxrollavg_index = gap_data.loc[Section_StartDate:, 'pgv_rollavg'].idxmax()
            over_secstart_pgv_index = gap_data.query('pgv_rollavg>@secstart_pgv_maxrollavg').index[-1] \
                if len(gap_data.query('pgv_rollavg>@secstart_pgv_maxrollavg')) > 0 else gap_data.index[0]
            coverperiod_pgv_minrollavg = gap_data.loc[over_secstart_pgv_index:secstart_pgv_maxrollavg_index, 'pgv_rollavg'].min() \
                if len(gap_data.loc[over_secstart_pgv_index:secstart_pgv_maxrollavg_index, 'pgv_rollavg']) > 0 else 0
            Result_Loc['SecStart_PGV_MaxRollAvg_UpCoverDays'] = len(gap_data.loc[over_secstart_pgv_index:]) - 1

            Result_Loc['SecStart_PGV_UpCoverPeriod_Max2Min_DiffRatio'] = round(
                secstart_pgv_maxrollavg / coverperiod_pgv_minrollavg, 3) \
                    if pd.notnull(secstart_pgv_maxrollavg) and coverperiod_pgv_minrollavg != 0 else 0

            secstart_pgv_minrollavg = gap_data.loc[Section_StartDate:, 'pgv_rollavg'].min()
            secstart_pgv_minrollavg_index = gap_data.loc[Section_StartDate:, 'pgv_rollavg'].idxmin()
            under_secstart_pgv_index = gap_data.query('pgv_rollavg<@secstart_pgv_minrollavg').index[-1] \
                if len(gap_data.query('pgv_rollavg<@secstart_pgv_minrollavg')) > 0 else gap_data.index[0]
            coverperiod_pgv_maxrollavg = gap_data.loc[under_secstart_pgv_index:secstart_pgv_minrollavg_index, 'pgv_rollavg'].max() \
                if len(gap_data.loc[under_secstart_pgv_index:secstart_pgv_minrollavg_index, 'pgv_rollavg']) > 0 else 0
            Result_Loc['SecStart_PGV_MinRollAvg_DownCoverDays'] = len(gap_data.loc[under_secstart_pgv_index:]) - 1
            Result_Loc['SecStart_PGV_DownCoverPeriod_Min2Max_DiffRatio'] = round(
                secstart_pgv_minrollavg / coverperiod_pgv_maxrollavg, 3) \
                    if pd.notnull(secstart_pgv_minrollavg) and coverperiod_pgv_maxrollavg != 0 else 0

            Result_Loc['SecStart_PRA2Close_CoverDays_Diff'] = Result_Loc['SecStart_PGV_MaxRollAvg_UpCoverDays'] - \
                                                              Result_Common['SecStart_MaxClose_UpCoverDays']

            # if pd.notnull(PreSecPeak_Sec_StartDate) and pd.notnull(Now_SecDate) \
            #     and PreSecPeak_Sec_StartDate < Now_SecDate and PreSecPeak_Sec_StartDate in gap_data.index \
            #     and Now_SecDate in gap_data.index:
            #     gap_data_around_peak = gap_data.loc[PreSecPeak_Sec_StartDate:Now_SecDate].copy()
            #     gap_data_around_peak = gap_data_around_peak.sort_values(by='pgv_rollavg', ascending=False)
            #     Result_Loc['PostSecPeak_PRV_Top3Mean'] = round(
            #         gap_data_around_peak['pgv_rollavg'].iloc[:min(3, len(gap_data_around_peak))].mean(), 3)
            # else:
            #     Result_Loc['PostSecPeak_PRV_Top3Mean'] = None

            # 新增指标：计算NowSecDate后最高和最低PRA的上下轨间的分位值
            if SecConcave_StartDate < Now_SecDate:
                Result_Loc['NowSec_MaxPRA_Percentile_PostTurn'] = calculate_extreme_percentile_band(
                        Result_Loc['PostNowSec_PGV_MaxRollAvg'],
                        gap_data.loc[SecConcave_StartDate:Now_SecDate, 'pgv_rollavg'],
                    )
                Result_Loc['NowSec_MinPRA_Percentile_PostTurn'] = calculate_extreme_percentile_band(
                        Result_Loc['PostNowSec_PGV_MinRollAvg'],
                        gap_data.loc[SecConcave_StartDate:Now_SecDate, 'pgv_rollavg'],
                    )
                # 计算NowSecDate后最高和最低PRA的上下轨（基于波峰波谷检测）
                turn2nowsec_data = gap_data.loc[SecConcave_StartDate:Now_SecDate, 'pgv_rollavg']
                turn2nowsec_data = turn2nowsec_data[turn2nowsec_data != 0]  # 去除0值
                turn2nowsec_length = len(turn2nowsec_data)

                if turn2nowsec_length >= 7:  # 确保有足够数据进行波峰波谷检测（至少7个点）
                    # 检测波峰和波谷
                    peaks = []  # 波峰值
                    valleys = []  # 波谷值

                    # 使用滑动窗口检测波峰波谷（窗口大小为7：前3个+当前+后3个）
                    for i in range(3, len(turn2nowsec_data) - 3):
                        current_val = turn2nowsec_data.iloc[i]

                        # 获取前3个和后3个数值
                        prev_3_vals = turn2nowsec_data.iloc[i-3:i]
                        next_3_vals = turn2nowsec_data.iloc[i+1:i+4]

                        # 波峰：当前值高于前3个及后3个数值
                        if (current_val > prev_3_vals.max()) and (current_val > next_3_vals.max()):
                            peaks.append(current_val)
                        # 波谷：当前值低于前3个及后3个数值
                        elif (current_val < prev_3_vals.min()) and (current_val < next_3_vals.min()):
                            valleys.append(current_val)

                    # 如果检测到的波峰波谷数量不足，使用局部极值补充
                    if len(peaks) < 3:
                        # 补充局部高点
                        sorted_values = turn2nowsec_data.sort_values(ascending=False)
                        additional_peaks = sorted_values.head(max(3, min(5, turn2nowsec_length // 3)))
                        peaks.extend(additional_peaks.tolist())
                        peaks = list(set(peaks))  # 去重

                    if len(valleys) < 3:
                        # 补充局部低点
                        sorted_values = turn2nowsec_data.sort_values(ascending=True)
                        additional_valleys = sorted_values.head(max(3, min(5, turn2nowsec_length // 3)))
                        valleys.extend(additional_valleys.tolist())
                        valleys = list(set(valleys))  # 去重

                    # 去除极值后计算平均值
                    if len(peaks) >= 3:
                        peaks_sorted = sorted(peaks, reverse=True)
                        # 去除最高的极值，取剩余波峰的平均值
                        filtered_peaks = peaks_sorted[1:] if len(peaks_sorted) > 3 else peaks_sorted
                        Result_Loc['Turn2NowSec_PRA_UpBand'] = round(sum(filtered_peaks) / len(filtered_peaks), 3)
                    else:
                        # 数据不足时使用简单方法
                        Result_Loc['Turn2NowSec_PRA_UpBand'] = round(turn2nowsec_data.quantile(0.8), 3)

                    if len(valleys) >= 3:
                        valleys_sorted = sorted(valleys)
                        # 去除最低的极值，取剩余波谷的平均值
                        filtered_valleys = valleys_sorted[1:] if len(valleys_sorted) > 3 else valleys_sorted
                        Result_Loc['Turn2NowSec_PRA_LowBand'] = round(sum(filtered_valleys) / len(filtered_valleys), 3)
                    else:
                        # 数据不足时使用简单方法
                        Result_Loc['Turn2NowSec_PRA_LowBand'] = round(turn2nowsec_data.quantile(0.2), 3)

                else:
                    # 数据不足时使用简单统计方法
                    if turn2nowsec_length > 0:
                        Result_Loc['Turn2NowSec_PRA_UpBand'] = round(turn2nowsec_data.quantile(0.8), 3)
                        Result_Loc['Turn2NowSec_PRA_LowBand'] = round(turn2nowsec_data.quantile(0.2), 3)
                    else:
                        Result_Loc['Turn2NowSec_PRA_UpBand'] = 0
                        Result_Loc['Turn2NowSec_PRA_LowBand'] = 0
                
                Result_Loc['Now_PGV_Percentile_PostTurn'] = calculate_extreme_percentile_band(
                gap_data['peak_gap'].iloc[-1],
                gap_data.loc[SecConcave_StartDate:Now_SecDate, 'peak_gap'],
                )

                Result_Loc['Recent3Day_MaxPGV_Percentile_PostTurn'] = calculate_extreme_percentile_band(
                    gap_data['peak_gap'].iloc[-3:].max(),
                    gap_data.loc[SecConcave_StartDate:Now_SecDate, 'peak_gap'],
                )
                
            else:
                Result_Loc['NowSec_MaxPRA_Percentile_PostTurn'] = 99
                Result_Loc['NowSec_MinPRA_Percentile_PostTurn'] = 99
                Result_Loc['Turn2NowSec_PRA_UpBand'] = 0
                Result_Loc['Turn2NowSec_PRA_LowBand'] = 0
                Result_Loc['Now_PGV_Percentile_PostTurn'] = 99
                Result_Loc['Recent3Day_MaxPGV_Percentile_PostTurn'] = 99

            if Section_PeakDate < Now_SecDate:
                Result_Loc['NowSec_MaxPRA_Percentile_PostSectionPeak'] = calculate_extreme_percentile_band(
                        Result_Loc['PostNowSec_PGV_MaxRollAvg'],
                        gap_data.loc[Section_PeakDate:Now_SecDate, 'pgv_rollavg'],
                    )
                Result_Loc['NowSec_MinPRA_Percentile_PostSectionPeak'] = calculate_extreme_percentile_band(
                        Result_Loc['PostNowSec_PGV_MinRollAvg'],
                        gap_data.loc[Section_PeakDate:Now_SecDate, 'pgv_rollavg'],
                    )
                Result_Loc['SecPeak2NowSec_PRA_UpBand'] = gap_data.loc[Section_PeakDate:Now_SecDate, 'pgv_rollavg'].max()
                Result_Loc['SecPeak2NowSec_PRA_LowBand'] = gap_data.loc[
                    Section_PeakDate:Now_SecDate, 'pgv_rollavg'][gap_data.loc[
                        Section_PeakDate:Now_SecDate, 'pgv_rollavg'] != 0].min()
            else:
                Result_Loc['NowSec_MaxPRA_Percentile_PostSectionPeak'] = 99
                Result_Loc['NowSec_MinPRA_Percentile_PostSectionPeak'] = 99
                Result_Loc['SecPeak2NowSec_PRA_UpBand'] = 99
                Result_Loc['SecPeak2NowSec_PRA_LowBand'] = 99


            # 计算最近3天最大PGV在转折后的排名，添加边界检查
            pgv_rank_series = gap_data.loc[Period_TurnDate:, 'peak_gap'].rank(method='min', ascending=False)
            pgv_match_condition = gap_data.loc[Period_TurnDate:, 'peak_gap'] == gap_data['peak_gap'].iloc[-3:].max()
            pgv_matched_ranks = pgv_rank_series[pgv_match_condition]
            Result_Loc['Recent3Day_MaxPGV_PostTurn_Rank'] = pgv_matched_ranks.iloc[0] if len(pgv_matched_ranks) > 0 else None

            # 计算最近3天最大PRA在转折后的排名，添加边界检查
            pra_rank_series = gap_data.loc[Period_TurnDate:, 'pgv_rollavg'].rank(method='min', ascending=False)
            pra_match_condition = gap_data.loc[Period_TurnDate:, 'pgv_rollavg'] == gap_data['pgv_rollavg'].iloc[-3:].max()
            pra_matched_ranks = pra_rank_series[pra_match_condition]
            Result_Loc['Recent3Day_MaxPRA_PostTurn_Rank'] = pra_matched_ranks.iloc[0] if len(pra_matched_ranks) > 0 else None

            # 计算最近3天最小VGV在转折后的排名，添加边界检查
            vgv_rank_series = gap_data.loc[Period_TurnDate:, 'valley_gap'].rank(method='min', ascending=True)
            vgv_match_condition = gap_data.loc[Period_TurnDate:, 'valley_gap'] == gap_data['valley_gap'].iloc[-3:].min()
            vgv_matched_ranks = vgv_rank_series[vgv_match_condition]
            Result_Loc['Recent3Day_MinVGV_PostTurn_Rank'] = vgv_matched_ranks.iloc[0] if len(vgv_matched_ranks) > 0 else None

            postnowsec_minvgv = gap_data.loc[Now_SecDate:, 'valley_gap'].min()

            # 计算PostNowSec最小VGV在转折后的排名，添加边界检查
            postnowsec_vgv_rank_series = gap_data.loc[Period_TurnDate:, 'valley_gap'].rank(method='min', ascending=True)
            postnowsec_vgv_match_condition = gap_data.loc[Period_TurnDate:, 'valley_gap'] == postnowsec_minvgv
            postnowsec_vgv_matched_ranks = postnowsec_vgv_rank_series[postnowsec_vgv_match_condition]
            Result_Loc['PostNowSec_MinVGV_PostTurn_Rank'] = postnowsec_vgv_matched_ranks.iloc[0] if len(postnowsec_vgv_matched_ranks) > 0 else None

            PostTurn_VGVRank5_Date = gap_data.loc[Period_TurnDate:, 'valley_gap'].sort_values(ascending=True).index[:min(4, len(gap_data.loc[Period_TurnDate:])-1)]

            date_length = 99
            postturn_vgvrank_nearnow = None
            for c_date in PostTurn_VGVRank5_Date:
                check_length = len(gap_data.loc[c_date:]) - 1
                if check_length < date_length:
                    date_length = check_length
                    postturn_vgvrank_nearnow = c_date
            Result_Loc['PostTurn_VGVRank5_NearNowDate'] = postturn_vgvrank_nearnow
            Result_Loc['PostTurn_VGVRank5_NearNowDate_Days'] = date_length


            Result_Loc['PostTurn_Rank5_PGV'] = gap_data.loc[Period_TurnDate:, 'peak_gap'].sort_values(ascending=False).iloc[min(4, len(gap_data.loc[Period_TurnDate:])-1)]

            Result_Loc['PostTurn_Rank5_PRA'] = gap_data.loc[Period_TurnDate:, 'pgv_rollavg'].sort_values(ascending=False).iloc[min(4, len(gap_data.loc[Period_TurnDate:])-1)]

            # Result_Loc['PostTurn_RevRank5_VGV'] = gap_data.loc[Period_TurnDate:, 'valley_gap'].sort_values(ascending=True).iloc[min(4, len(gap_data.loc[Period_TurnDate:])-1)]

            Result_Loc['PostSecStart_MaxPRA_Percentile_PostSectionPeak'] = calculate_extreme_percentile_band(
                Result_Loc['PostSecStart_PGV_MaxRollAvg'],
                gap_data.loc[Section_PeakDate:, 'pgv_rollavg'],
            )
            Result_Loc['PostSecStart_MaxPRA_Percentile_PostTurn'] = calculate_extreme_percentile_band(
                Result_Loc['PostSecStart_PGV_MaxRollAvg'],
                gap_data.loc[Period_TurnDate:, 'pgv_rollavg'],
            )

            Result_Loc['Now_PRA_Percentile_PostSectionPeak'] = calculate_extreme_percentile_band(
                gap_data['pgv_rollavg'].iloc[-1],
                gap_data.loc[Section_PeakDate:Now_SecDate, 'pgv_rollavg']
            )

            Latest_Eff_Peak_Date = Result_Loc['Latest_Eff_Peak_Date']
            Result_Loc['Latest_EffPeak2NowSec_Diff'] = len(
                trade_df[(trade_df >= min(Latest_Eff_Peak_Date, Now_SecDate)) &
                            (trade_df <= max(Latest_Eff_Peak_Date, Now_SecDate))]) - 1

            # 新增指标：计算pgv_rollavg的变动速率
            gap_data['pgv_rollavg_rate'] = round(gap_data['pgv_rollavg'].diff() / gap_data['pgv_rollavg'].shift(1).replace(0, np.nan) * 100, 3)
            gap_data['vgv_rollavg_rate'] = round(gap_data['vgv_rollavg'].diff() / gap_data['vgv_rollavg'].shift(1).replace(0, np.nan) * 100, 3)
            # Result_Loc['PostPreNowPeak_PRA_MaxRate'] = round(gap_data.loc[PreNow_PeakDate:, 'pgv_rollavg_rate、'].max(), 3)
            # Result_Loc['PostPreNowPeak_PRA_MaxRate_Date'] = gap_data.loc[PreNow_PeakDate:, 'pgv_rollavg_rate'].idxmax()
            # Handle PostNowSec period
            now_sec_data = gap_data.loc[Now_SecDate:, 'pgv_rollavg_rate']
            Result_Loc['PostNowSec_PRA_MaxRate'] = round(now_sec_data.max(), 3)
            Result_Loc['PostNowSec_PRA_MaxRate_Date'] = now_sec_data.idxmax() if not now_sec_data.isna().all() else None

            # Handle PostSecStart period
            sec_start_data = gap_data.loc[Section_StartDate:, 'pgv_rollavg_rate']
            Result_Loc['PostSecStart_PRA_MaxRate'] = round(sec_start_data.max(), 3)
            Result_Loc['PostSecStart_PRA_MaxRate_Date'] = sec_start_data.idxmax() if not sec_start_data.isna().all() else None

            sec_start_vgvdata = gap_data.loc[Section_StartDate:, 'vgv_rollavg_rate']
            Result_Loc['PostSecStart_VRA_MaxRate'] = round(sec_start_vgvdata.max(), 3)
            Result_Loc['PostSecStart_VRA_MaxRate_Date'] = sec_start_vgvdata.idxmax() if not sec_start_vgvdata.isna().all() else None

            Result_Loc['PostNowSec_MaxRate2Now_LastDays'] = len(
                gap_data.loc[Result_Loc['PostNowSec_PRA_MaxRate_Date']:]) - 1 \
                    if pd.notnull(Result_Loc['PostNowSec_PRA_MaxRate_Date']) else 999
            Result_Loc['PostNowSec_NowSec2MaxRate_LastDays'] = len(
                gap_data.loc[Now_SecDate:Result_Loc['PostNowSec_PRA_MaxRate_Date']]) - 1 \
                    if pd.notnull(Result_Loc['PostNowSec_PRA_MaxRate_Date']) else 0

            if pd.notnull(Result_Loc['PostNowSec_PRA_MaxRate_Date']) and Result_Loc['PostNowSec_NowSec2MaxRate_LastDays'] > 3:
                Result_Loc['PostNowSec_MaxRate_Post2Pre_DiffRatio'] = round(
                    gap_data.loc[Result_Loc['PostNowSec_PRA_MaxRate_Date']:, 'pgv_rollavg'].mean()/
                    gap_data.loc[Now_SecDate:Result_Loc['PostNowSec_PRA_MaxRate_Date'], 'pgv_rollavg'].iloc[:-1].mean(), 3)
            elif pd.notnull(Result_Loc['PostNowSec_PRA_MaxRate_Date']) and len(gap_data.loc[:Result_Loc['PostNowSec_PRA_MaxRate_Date']]) > 4:
                Result_Loc['PostNowSec_MaxRate_Post2Pre_DiffRatio'] = round(
                    gap_data.loc[Result_Loc['PostNowSec_PRA_MaxRate_Date']:, 'pgv_rollavg'].mean()/
                    gap_data.loc[:Result_Loc['PostNowSec_PRA_MaxRate_Date'], 'pgv_rollavg'].iloc[-4:-1].mean(), 3)
            else:
                Result_Loc['PostNowSec_MaxRate_Post2Pre_DiffRatio'] = 0


            # 检查pgv_rollavg_rate和vgv_rollavg_rate是否存在且有有效数据
            pra_data_valid = ('pgv_rollavg_rate' in gap_data.columns and
                             not gap_data['pgv_rollavg_rate'].empty and
                             not gap_data['pgv_rollavg_rate'].isna().all())
            vra_data_valid = ('vgv_rollavg_rate' in gap_data.columns and
                             not gap_data['vgv_rollavg_rate'].empty and
                             not gap_data['vgv_rollavg_rate'].isna().all())

            # 处理PRA相关指标
            if pra_data_valid:
                Result_Loc['Now_PRA_Rate'] = round(gap_data['pgv_rollavg_rate'].iloc[-1], 3) if pd.notna(gap_data['pgv_rollavg_rate'].iloc[-1]) else 0
                Result_Loc['Recent3Day_PRA_MaxRate'] = round(gap_data['pgv_rollavg_rate'].iloc[-3:].max(), 3) if not gap_data['pgv_rollavg_rate'].iloc[-3:].isna().all() else 0

                # 计算recent3day相关指标
                if not gap_data['pgv_rollavg_rate'].iloc[-3:].isna().all():
                    recent3day_pra_maxrate = gap_data['pgv_rollavg_rate'].iloc[-3:].max()
                    recent3day_pra_maxratedate = gap_data['pgv_rollavg_rate'].iloc[-3:].idxmax()

                    # 查找breach date
                    pra_breach_query = gap_data.loc[:recent3day_pra_maxratedate].query('pgv_rollavg_rate>@recent3day_pra_maxrate')
                    pra_breach_date = pra_breach_query.index[-1] if len(pra_breach_query) > 0 else '-'
                    Result_Loc['Recent3Day_PRA_MaxRate_CoverDays'] = len(gap_data.loc[pra_breach_date:recent3day_pra_maxratedate]) - 1 \
                        if pra_breach_date != '-' else 99
                else:
                    Result_Loc['Recent3Day_PRA_MaxRate_CoverDays'] = 99
            else:
                Result_Loc['Now_PRA_Rate'] = 0
                Result_Loc['Recent3Day_PRA_MaxRate'] = 0
                Result_Loc['Recent3Day_PRA_MaxRate_CoverDays'] = 99

            # 处理VRA相关指标
            if vra_data_valid:
                Result_Loc['Now_VRA_Rate'] = round(gap_data['vgv_rollavg_rate'].iloc[-1], 3) if pd.notna(gap_data['vgv_rollavg_rate'].iloc[-1]) else 0
                Result_Loc['Recent3Day_VRA_MaxRate'] = round(gap_data['vgv_rollavg_rate'].iloc[-3:].max(), 3) if not gap_data['vgv_rollavg_rate'].iloc[-3:].isna().all() else 0

                # 计算recent3day相关指标
                if not gap_data['vgv_rollavg_rate'].iloc[-3:].isna().all():
                    recent3day_vra_maxrate = gap_data['vgv_rollavg_rate'].iloc[-3:].max()
                    recent3day_vra_maxratedate = gap_data['vgv_rollavg_rate'].iloc[-3:].idxmax()

                    # 查找breach date
                    vra_breach_query = gap_data.loc[:recent3day_vra_maxratedate].query('vgv_rollavg_rate>@recent3day_vra_maxrate')
                    vra_breach_date = vra_breach_query.index[-1] if len(vra_breach_query) > 0 else '-'
                    Result_Loc['Recent3Day_VRA_MaxRate_CoverDays'] = len(gap_data.loc[vra_breach_date:recent3day_vra_maxratedate]) - 1 \
                        if vra_breach_date != '-' else 99
                else:
                    Result_Loc['Recent3Day_VRA_MaxRate_CoverDays'] = 99
            else:
                Result_Loc['Now_VRA_Rate'] = 0
                Result_Loc['Recent3Day_VRA_MaxRate'] = 0
                Result_Loc['Recent3Day_VRA_MaxRate_CoverDays'] = 99

            rate_threshold = 50
            # 检查pgv_rollavg_rate字段是否有效
            if pra_data_valid:
                Result_Loc['PostNowSec_PRA_MaxRate_BreachCount'] = len(
                    gap_data.loc[Now_SecDate:].query('pgv_rollavg_rate>=@rate_threshold'))
                Result_Loc['PostSecStart_PRA_MaxRate_BreachCount'] = len(
                    gap_data.loc[Section_StartDate:].query('pgv_rollavg_rate>=@rate_threshold'))
            else:
                Result_Loc['PostNowSec_PRA_MaxRate_BreachCount'] = 0
                Result_Loc['PostSecStart_PRA_MaxRate_BreachCount'] = 0

            # 检查pgv_rollavg字段是否有效
            if ('pgv_rollavg' in gap_data.columns and
                not gap_data['pgv_rollavg'].empty and
                not gap_data['pgv_rollavg'].isna().all() and
                Result_Loc['PostNowSec_PRA_MaxRate_Date'] is not None and
                Result_Loc['PostNowSec_PRA_MaxRate_Date'] != '-' and
                Section_PeakDate is not None and
                Now_SecDate is not None):
                try:
                    Result_Loc['PostNowSec_MaxRate_PRA_Percentile'] = calculate_extreme_percentile_band(
                        gap_data.loc[Result_Loc['PostNowSec_PRA_MaxRate_Date'], 'pgv_rollavg'],
                        gap_data.loc[Section_PeakDate:Now_SecDate, 'pgv_rollavg']
                    )
                except (KeyError, IndexError, TypeError) as e:
                    print(f"计算PostNowSec_MaxRate_PRA_Percentile时发生错误: {str(e)}")
                    Result_Loc['PostNowSec_MaxRate_PRA_Percentile'] = 0
            else:
                Result_Loc['PostNowSec_MaxRate_PRA_Percentile'] = 0

            # 新增指标：计算SectionStart后PRA超越SectionPeak的首个日期，NowSecDate后PRA超越PreNowPeak的首个日期
            secpeck_pra = Result_Loc['SectionPeak_PRV_Top3Mean']
            Result_Loc['SecStart_PRA_FirstBreach_Date'] = '-'
            Result_Loc['SecStart_FirstBreach2Now_LastDays'] = 0
            Result_Loc['SecStart_PRA_LastBreach_Date'] = '-'
            Result_Loc['SecStart_PRA_LastBreach2Now_LastDays'] = 0
            Result_Loc['SecStart_PRA_Breach_Count'] = 0
            Result_Loc['SecStart_PRA_LastBreach_ContinuousDays'] = 0

            # 检查pgv_rollavg字段是否有效
            pgv_rollavg_valid = ('pgv_rollavg' in gap_data.columns and
                               not gap_data['pgv_rollavg'].empty and
                               not gap_data['pgv_rollavg'].isna().all())

            if pd.notnull(secpeck_pra) and pgv_rollavg_valid and \
                len(gap_data.loc[Section_StartDate:].query('pgv_rollavg>@secpeck_pra')) > 0:
                Result_Loc['SecStart_PRA_FirstBreach_Date'] = gap_data.loc[Section_StartDate:].query('pgv_rollavg>@secpeck_pra').index[0]
                Result_Loc['SecStart_FirstBreach2Now_LastDays'] = len(gap_data.loc[Result_Loc['SecStart_PRA_FirstBreach_Date']:]) - 1
                Result_Loc['SecStart_PRA_Breach_Count'] = len(gap_data.loc[Section_StartDate:].query('pgv_rollavg>@secpeck_pra'))
                Result_Loc['SecStart_PRA_LastBreach_Date'] = gap_data.loc[Section_StartDate:].query('pgv_rollavg>@secpeck_pra').index[-1]
                Result_Loc['SecStart_PRA_LastBreach2Now_LastDays'] = len(gap_data.loc[Result_Loc['SecStart_PRA_LastBreach_Date']:]) - 1

                breach_dates = gap_data.loc[Section_StartDate:].query('pgv_rollavg>@secpeck_pra').index
                if len(breach_dates) > 0:
                    last_breach_date = breach_dates[-1]
                    continuous_breach_days = 0
                    for date in reversed(gap_data.loc[Section_StartDate:].index):
                        if date <= last_breach_date and gap_data.loc[date, 'pgv_rollavg'] > secpeck_pra:
                            continuous_breach_days += 1
                        else:
                            break
                    Result_Loc['SecStart_PRA_LastBreach_ContinuousDays'] = continuous_breach_days

            prepeak_pra = Result_Loc['PreNowPeak_PRV_Top3Mean']
            Result_Loc['NowSec_PRA_FirstBreach_Date'] = '-'
            Result_Loc['NowSec_FirstBreach2Now_LastDays'] = 0
            Result_Loc['NowSec_PRA_LastBreach_Date'] = '-'
            Result_Loc['NowSec_LastBreach2Now_LastDays'] = 0
            Result_Loc['NowSec_PRA_Breach_Count'] = 0
            Result_Loc['NowSec_PRA_LastBreach_ContinuousDays'] = 0

            if pd.notnull(prepeak_pra) and pgv_rollavg_valid and \
                len(gap_data.loc[Now_SecDate:].query('pgv_rollavg>@prepeak_pra')) > 0:
                Result_Loc['NowSec_PRA_FirstBreach_Date'] = gap_data.loc[Now_SecDate:].query('pgv_rollavg>@prepeak_pra').index[0]
                Result_Loc['NowSec_PRA_LastBreach_Date'] = gap_data.loc[Now_SecDate:].query('pgv_rollavg>@prepeak_pra').index[-1]
                Result_Loc['NowSec_FirstBreach2Now_LastDays'] = len(gap_data.loc[Result_Loc['NowSec_PRA_FirstBreach_Date']:]) - 1
                Result_Loc['NowSec_PRA_Breach_Count'] = len(gap_data.loc[Now_SecDate:].query('pgv_rollavg>@prepeak_pra'))
                Result_Loc['NowSec_LastBreach2Now_LastDays'] = len(gap_data.loc[Result_Loc['NowSec_PRA_LastBreach_Date']:]) - 1
                # 计算最近连续突破天数
                breach_dates = gap_data.loc[Now_SecDate:].query('pgv_rollavg>@prepeak_pra').index
                if len(breach_dates) > 0:
                    last_breach_date = breach_dates[-1]
                    continuous_breach_days = 0
                    for date in reversed(gap_data.loc[Now_SecDate:].index):
                        if date <= last_breach_date and gap_data.loc[date, 'pgv_rollavg'] > prepeak_pra:
                            continuous_breach_days += 1
                        else:
                            break
                    Result_Loc['NowSec_PRA_LastBreach_ContinuousDays'] = continuous_breach_days

            postsec_pgv_mean = gap_data.loc[Section_StartDate:, 'peak_gap'].iloc[:-min(3, len(gap_data.loc[Section_StartDate:]))
                                                                                 ].mean()
            Result_Loc['PostSecStart_PGV_Now2Mean_Ratio'] = round(
                gap_data['peak_gap'].iloc[-min(3, len(gap_data.loc[Section_StartDate:])):].mean() /
                postsec_pgv_mean - 1, 3) \
                    if len(gap_data.loc[Section_StartDate:]) > 0 and postsec_pgv_mean > 0 else 0
            Result_Loc['PostNowSec_PGV_Now2Mean_Ratio'] = round(
                gap_data['peak_gap'].iloc[-1] / gap_data.loc[Now_SecDate:, 'peak_gap'].mean() - 1, 3) \
                    if len(gap_data.loc[Now_SecDate:]) > 0 and gap_data.loc[Now_SecDate:, 'peak_gap'].mean()>0 else 0
            Result_Loc['PostNowSec_PGV_Max2Mean_Ratio'] = round(
                gap_data.loc[Now_SecDate:, 'peak_gap'].max() / gap_data.loc[Now_SecDate:, 'peak_gap'].mean() - 1, 3) \
                    if len(gap_data.loc[Now_SecDate:]) > 0 and gap_data.loc[Now_SecDate:, 'peak_gap'].mean()>0 else 0
            Result_Loc['PGV_Now2Pre3Days_Ratio'] = round(
                gap_data['peak_gap'].iloc[-1] / gap_data['peak_gap'].iloc[-4:-1].mean() - 1, 3) \
                    if len(gap_data) > 4 and gap_data['peak_gap'].iloc[-4:-1].mean()>0 else 0

            if len(gap_data.loc[Section_StartDate:]) > 5:
                turnpoint_date, absolute_change, relative_change, postturnpoint_days = \
                    cal_turnpoint(df_data=gap_data.loc[Section_StartDate:],
                                  stk_indicator='peak_gap',
                                  show_info=False)
                Result_Loc['PostSec_PGV_TurnP_Date'] = turnpoint_date
                Result_Loc['PostSec_PGV_TurnP_AbsChange'] = round(absolute_change, 3)
                Result_Loc['PostSec_PGV_TurnP_RelaChange'] = round(relative_change, 3)
                Result_Loc['PostSec_PGV_PostTurnP_LastDays'] = postturnpoint_days

            if len(gap_data.loc[Section_PeakDate:Section_StartDate]) > 5:
                turnpoint_date, absolute_change, relative_change, postturnpoint_days = \
                    cal_turnpoint(df_data=gap_data.loc[Section_PeakDate:Section_StartDate],
                                  stk_indicator='peak_gap',
                                  show_info=False)
                Result_Loc['Peak2Sec_PGV_TurnP_Date'] = turnpoint_date
                Result_Loc['Peak2Sec_PGV_TurnP_AbsChange'] = round(absolute_change, 3)
                Result_Loc['Peak2Sec_PGV_TurnP_RelaChange'] = round(relative_change, 3)
                Result_Loc['Peak2Sec_PGV_PostTurnP_LastDays'] = postturnpoint_days

            if len(gap_data.loc[PreNow_PeakDate:]) > 0:
                postprenow_minpra_date = gap_data.loc[PreNow_PeakDate:, 'pgv_rollavg'].idxmin()
                Result_Loc['PostPreNowPeak_PGV_MinRollAvg'] = round(
                    gap_data.loc[PreNow_PeakDate:, 'pgv_rollavg'].min(), 3)
                Result_Loc['PostPreNowPeak_PGV_MinRollAvg_Date'] = gap_data.loc[postprenow_minpra_date:, 'pgv_rollavg'].idxmin()
                Result_Loc['PostPreNowPeak_PostMinPRA_MaxPRA'] = round(
                    gap_data.loc[postprenow_minpra_date:, 'pgv_rollavg'].max(), 3)
                PostPreNowPeak_PostMinPRA_MaxPRA_Date = gap_data.loc[postprenow_minpra_date:, 'pgv_rollavg'].idxmax()
                Result_Loc['PostPreNowPeak_PostMinPRA_MaxPRA2Now_Days'] = len(
                    gap_data.loc[PostPreNowPeak_PostMinPRA_MaxPRA_Date:]) - 1
                
                Result_Loc['PostPreNowPeak_MinPRA_Percentile_PostTurn'] = calculate_extreme_percentile_band(
                    Result_Loc['PostPreNowPeak_PGV_MinRollAvg'],
                    gap_data.loc[Period_TurnDate:, 'pgv_rollavg'],
            )
                

            if len(gap_data.loc[Section_PeakDate:]) > 0:
                postsecpeak_minpra_date = gap_data.loc[Section_PeakDate:, 'pgv_rollavg'].idxmin()
                Result_Loc['PostSectionPeak_PGV_MinRollAvg'] = round(
                    gap_data.loc[Section_PeakDate:, 'pgv_rollavg'].min(), 3)
                Result_Loc['PostSectionPeak_PGV_MinRollAvg_Date'] = gap_data.loc[postsecpeak_minpra_date:, 'pgv_rollavg'].idxmin()
                Result_Loc['PostSectionPeak_PostMinPRA_MaxPRA'] = round(
                    gap_data.loc[postsecpeak_minpra_date:, 'pgv_rollavg'].max(), 3)
                PostSectionPeak_PostMinPRA_MaxPRA_Date = gap_data.loc[postsecpeak_minpra_date:, 'pgv_rollavg'].idxmax()
                Result_Loc['PostSectionPeak_PostMinPRA_MaxPRA2Now_Days'] = len(
                    gap_data.loc[PostSectionPeak_PostMinPRA_MaxPRA_Date:]) - 1


            # 新增指标：计算最近PRA波峰相关指标
            
            try:
                # 从最新日期前5个交易日开始往前回溯，定位距离该日期最近的PRA波峰日期
                if len(gap_data) >= 25:  # 确保有足够的数据进行波峰检测（至少需要前5天+前后10天的窗口）
                    # 从最新日期前5个交易日开始往前回溯搜索
                    search_start_idx = len(gap_data)-1  # 最新日期前5个交易日的位置

                    # 寻找PRA波峰（前后10个交易日的PRA最大值对应日期）
                    recent_maxpra_date = None
                    recent_minpra_date = None
                    recent_maxpra_value = 0
                    recent_minpra_value = 0

                    # 从search_start_idx位置开始往前回溯，一直找到最近的波峰
                    # 确保每个检查位置都有前后10个交易日的数据
                    maxpra_signal=0
                    minpra_signal=0
                    for i in range(search_start_idx, 9, -1):  # 从前5天位置开始往前，确保后面有10天数据
                        current_pra = gap_data.iloc[i]['pgv_rollavg']

                        # 检查前后10个交易日的窗口
                        window_start = max(0, i - 10)
                        window_end = min(len(gap_data), i + 11)
                        window_data = gap_data.iloc[window_start:window_end]['pgv_rollavg']

                        # 如果当前值是窗口内的最大值，则认为是波峰
                        if current_pra == window_data.max() and maxpra_signal==0:
                            recent_maxpra_date = gap_data.index[i]
                            recent_maxpra_value = current_pra
                            maxpra_signal=1   # 找到最近的波峰
                        
                        if current_pra == window_data.min() and minpra_signal==0:
                            recent_minpra_date = gap_data.index[i]
                            recent_minpra_value = current_pra
                            minpra_signal=1
                        if maxpra_signal==1 and minpra_signal==1:
                            break

                    if recent_maxpra_date is not None:
                        Result_Loc['Recent_MaxPRA_Date'] = recent_maxpra_date
                        recent_maxpra_close = gap_data.loc[recent_maxpra_date, 'close'] if 'close' in gap_data.columns else None

                        # 计算Recent_MaxPRA_Date对应PRA向前覆盖天数
                        # pra_value_at_peak = gap_data.loc[recent_maxpra_date, 'pgv_rollavg']
                        pra_larger_data = gap_data.loc[:recent_maxpra_date].iloc[:-1].query('pgv_rollavg > @recent_maxpra_value')
                        if len(pra_larger_data) > 0:
                            last_larger_date = pra_larger_data.index[-1]
                            Result_Loc['Recent_MaxPRA_PRA_CoverDays'] = len(gap_data.loc[last_larger_date:recent_maxpra_date]) - 1
                        else:
                            Result_Loc['Recent_MaxPRA_PRA_CoverDays'] = len(gap_data.loc[:recent_maxpra_date]) - 1

                        # 计算Recent_MaxPRA_Date对应收盘价向前覆盖天数
                        if recent_maxpra_close is not None:
                            close_larger_data = gap_data.loc[:recent_maxpra_date].iloc[:-1].query('close > @recent_maxpra_close')
                            if len(close_larger_data) > 0:
                                last_larger_close_date = close_larger_data.index[-1]
                                Result_Loc['Recent_MaxPRA_Cls_CoverDays'] = len(gap_data.loc[last_larger_close_date:recent_maxpra_date]) - 1
                            else:
                                Result_Loc['Recent_MaxPRA_Cls_CoverDays'] = len(gap_data.loc[:recent_maxpra_date]) - 1
                        else:
                            Result_Loc['Recent_MaxPRA_Cls_CoverDays'] = None

                        # 计算Recent_MaxPRA_Date距今间隔交易日天数
                        Result_Loc['Recent_MaxPRA2Now_Days'] = len(gap_data.loc[recent_maxpra_date:]) - 1

                        # 计算最新日期PRA相对Recent_MaxPRA_Date对应PRA的比值
                        current_pra = gap_data['pgv_rollavg'].iloc[-1]
                        Result_Loc['Now2RecentMaxPRA_PRA_Ratio'] = round(current_pra / recent_maxpra_value, 3) if recent_maxpra_value > 0 else 0

                        # 计算最新日期收盘价相对Recent_MaxPRA_Date收盘价的比值
                        if recent_maxpra_close is not None and 'close' in gap_data.columns:
                            current_close = gap_data['close'].iloc[-1]
                            Result_Loc['Now2RecentMaxPRA_Cls_Ratio'] = round(current_close / recent_maxpra_close, 3) if recent_maxpra_close > 0 else 0
                        else:
                            Result_Loc['Now2RecentMaxPRA_Cls_Ratio'] = None
                        
                        if Result_Loc['Recent_MaxPRA_PRA_CoverDays'] > Result_Loc['Recent_MaxPRA_Cls_CoverDays'] \
                            and Result_Loc['Now2RecentMaxPRA_PRA_Ratio'] < 1 and Result_Loc['Now2RecentMaxPRA_Cls_Ratio'] > 1:
                                Result_Loc['BullStk_RecentPRA_Signal'] = 1
                        else:
                            Result_Loc['BullStk_RecentPRA_Signal'] = 0
                        
                    else:
                        # 如果没有找到波峰，设置默认值
                        Result_Loc['Recent_MaxPRA_Date'] = None
                        Result_Loc['Recent_MaxPRA_PRA_CoverDays'] = 99
                        Result_Loc['Recent_MaxPRA_Cls_CoverDays'] = 99
                        Result_Loc['Recent_MaxPRA2Now_Days'] = 99
                        Result_Loc['Now2RecentMaxPRA_PRA_Ratio'] = -1
                        Result_Loc['Now2RecentMaxPRA_Cls_Ratio'] = -1
                        Result_Loc['BullStk_RecentPRA_Signal'] = 0
                    
                    if recent_minpra_date is not None:
                        Result_Loc['Recent_MinPRA_Date'] = recent_minpra_date
                        postsecstart_minpra_cover_date = gap_data.loc[Section_StartDate:recent_minpra_date].query('pgv_rollavg<@recent_minpra_value').index[-1] \
                            if len(gap_data.loc[Section_StartDate:recent_minpra_date].query('pgv_rollavg<@recent_minpra_value')) > 0 else Section_StartDate
                        postsecstart_minpra_coverdays = len(gap_data.loc[postsecstart_minpra_cover_date:recent_minpra_date]) - 1
                        Result_Loc['Recent_MinPRA_DownCoverDays'] = postsecstart_minpra_coverdays
                        Result_Loc['Recent_MinPRA2Now_Days'] = len(gap_data.loc[recent_minpra_date:]) - 1
                        Result_Loc['Recent_MinPRA_DownCover2PostSec_DaysProp'] = round(postsecstart_minpra_coverdays / len(gap_data.loc[Section_StartDate:recent_minpra_date]), 3) \
                            if len(gap_data.loc[Section_StartDate:recent_minpra_date]) > 0 else 0
                    else:
                        Result_Loc['Recent_MinPRA_Date'] = None
                        Result_Loc['Recent_MinPRA_DownCoverDays'] = 99
                        Result_Loc['Recent_MinPRA2Now_Days'] = 99
                        Result_Loc['Recent_MinPRA_DownCover2PostSec_DaysProp'] = -1
                    
                else:
                    # 数据不足，设置默认值
                    Result_Loc['Recent_MaxPRA_Date'] = None
                    Result_Loc['Recent_MaxPRA_PRA_CoverDays'] = 99
                    Result_Loc['Recent_MaxPRA_Cls_CoverDays'] = 99
                    Result_Loc['Recent_MaxPRA2Now_Days'] = 99
                    Result_Loc['Now2RecentMaxPRA_PRA_Ratio'] = -1
                    Result_Loc['Now2RecentMaxPRA_Cls_Ratio'] = -1
                    Result_Loc['BullStk_RecentPRA_Signal'] = 0

            except Exception as e:
                print(f"计算Recent_MaxPRA相关指标时发生错误: {str(e)}")
                # 设置默认值
                Result_Loc['Recent_MaxPRA_Date'] = None
                Result_Loc['Recent_MaxPRA_PRA_CoverDays'] = None
                Result_Loc['Recent_MaxPRA_Cls_CoverDays'] = None
                Result_Loc['Recent_MaxPRA2Now_Days'] = None
                Result_Loc['Now2RecentMaxPRA_PRA_Ratio'] = None
                Result_Loc['Now2RecentMaxPRA_Cls_Ratio'] = None
            
            # 计算PostSecStart_MinPRADate到Now的上涨比例和平均涨幅
            if 'close' in gap_data.columns and pd.notnull(postsecstart_minroll_date):
                # recent_minpra_date = Result_Loc['Recent_MinPRA_Date']
                if len(gap_data.loc[postsecstart_minroll_date:]) > 1:
                    # gap_data.loc[recent_minpra_date:, 'close'].idxmax() == gap_data.index[-1]:
                    post_minroll_maxcls = gap_data.loc[postsecstart_minroll_date:, 'close'].iloc[:-1].max()
                    post_minroll_maxdate = gap_data.loc[postsecstart_minroll_date:, 'close'].iloc[:-1].idxmax()
                else:
                    post_minroll_maxcls = gap_data.loc[postsecstart_minroll_date, 'close']
                    post_minroll_maxdate = postsecstart_minroll_date
                Result_Loc['PostSecStart_MinPRADate2Now_RiseRatio'] = round(
                    (post_minroll_maxcls / gap_data.loc[postsecstart_minroll_date, 'close'] - 1) * 100, 3)
                Result_Loc['PostSecStart_MinPRADate2Now_RiseAvg'] = round(
                    Result_Loc['PostSecStart_MinPRADate2Now_RiseRatio'] / (len(gap_data.loc[postsecstart_minroll_date:post_minroll_maxdate])-1), 3) \
                        if len(gap_data.loc[postsecstart_minroll_date:post_minroll_maxdate]) > 1 else 0
            
            Result_Loc['Now2UpBand_PRA_Ratio'] = round(Result_Loc['Recent3Day_PGV_MaxRollAvg'] / Result_Loc['Turn2NowSec_PRA_UpBand'], 3) \
                if Result_Loc['Turn2NowSec_PRA_UpBand'] > 0 else 0
            
        except Exception as e:
            print(ts_code, '计算gap_data数据指标报错，错误为：', e)
            print(ts_code, '计算gap_data数据指标报错')
            _, _, exc_tb = sys.exc_info()
            # filename = exc_tb.tb_frame.f_code.co_filename
            line_no = exc_tb.tb_lineno
            print(f"错误发生在第 {line_no} 行")
            print(f"错误详情: {str(e)}")

    else:
        print(ts_code, '的gap_data数据为空！')
    return Result_Loc


# 新增波动率比值指标
def calculate_volatility_ratio(data, field, recent_window=10, previous_window=20, direction='all'):
    """
    计算近期波动率与前期波动率的比值

    参数:
    data: DataFrame, 包含待分析数据
    field: str, 需要分析的字段名称
    recent_window: int, 近期窗口大小
    previous_window: int, 前期窗口大小
    direction: str, 波动率计算方向,'up'表示正向,'down'表示负向,'all'表示全部,默认'all'

    返回:
    float: 波动率比值,范围在0-100之间
    """
    try:
        if len(data) < (recent_window + previous_window):
            return 0

        # 计算近期波动率
        recent_data = data[field].iloc[-recent_window:]
        if direction == 'up':
            recent_data = recent_data[recent_data > recent_data.mean()]
        elif direction == 'down':
            recent_data = recent_data[recent_data < recent_data.mean()]

        # 使用clip限制极端值
        recent_mean = recent_data.mean()
        if len(recent_data) > 0 and recent_mean != 0:
            recent_std = recent_data.clip(-1e6, 1e6).std()
            recent_vol = min(recent_std / abs(recent_mean), 100)
        else:
            recent_vol = 0

        # 计算前期波动率
        previous_data = data[field].iloc[-(recent_window+previous_window):-recent_window]
        if direction == 'up':
            previous_data = previous_data[previous_data > previous_data.mean()]
        elif direction == 'down':
            previous_data = previous_data[previous_data < previous_data.mean()]

        # 使用clip限制极端值
        previous_mean = previous_data.mean()
        if len(previous_data) > 0 and previous_mean != 0:
            previous_std = previous_data.clip(-1e6, 1e6).std()
            previous_vol = min(previous_std / abs(previous_mean), 100)
        else:
            previous_vol = 0

        # 计算比值并限制范围在0-100之间
        if previous_vol != 0:
            vol_ratio = min(round(recent_vol / previous_vol, 3), 100)
        else:
            vol_ratio = 0 if recent_vol == 0 else 100

        return vol_ratio

    except Exception as e:
        print(f"计算波动率比值时发生错误: {str(e)}")
        return 0


def compare_field_trend(data, window=10, field='efficiency_ma5', threshold=0.15):
    """
    比较时间序列数据中指定字段的变动趋势

    参数:
    data: DataFrame, 包含待分析的时间序列数据
    window: int, 计算均值的时间窗口大小,默认20
    field: str, 需要分析的字段名称,默认'efficiency_ma5'
    threshold: float, 变动趋势的阈值,默认0.15

    返回:
    float: 当前窗口均值相对于前一窗口均值的变动比率,如果数据无效则返回0

    说明:
    - 通过比较相邻时间窗口的均值来判断变动趋势
    - 如果window小于10,则使用10作为最小窗口
    - 对于空值使用前向填充方法处理
    - 返回值为正表示上升趋势,为负表示下降趋势
    """
    try:
        # 检查数据是否为空
        if data is None or len(data) == 0 or field not in data.columns or window < 3:
            return 0

        # 检查数据是否包含空值
        if data[field].isnull().any():
            data = data.copy()
            data[field] = data[field].ffill()

        # 计算当前窗口的均值
        curr_mean = data[field].iloc[-window:].mean()

        # 计算前一个窗口之前所有数据的均值
        # 如果window小于10,则使用10作为最小窗口
        min_window = max(window, 10)
        prev_mean = data[field].iloc[:-2*min_window].mean() if len(data) > 2*min_window else data[field].iloc[:-min_window].mean()

        # 检查均值是否为None或无穷大
        if prev_mean is None or curr_mean is None or \
           np.isinf(prev_mean) or np.isinf(curr_mean):
            return 0

        # 限制极端值
        prev_mean = np.clip(prev_mean, -1e6, 1e6)
        curr_mean = np.clip(curr_mean, -1e6, 1e6)

        # 计算变动幅度并限制范围
        if prev_mean != 0:
            change_ratio = (curr_mean - prev_mean) / prev_mean
            change_ratio = np.clip(change_ratio, -100, 100)
            change_ratio = round(change_ratio, 3)
        else:
            change_ratio = 0

        return change_ratio

    except Exception as e:
        print(f"计算趋势变动时发生错误: {str(e)}")
        return 0


def calculate_bias_percentile_band(value, data, threshold=0.5):
    """
    计算效率值的分位数带

    参数:
    value: float, 需要计算分位值的效率值
    data: Series, 效率值数据序列
    threshold: float, 计算上下轨的阈值系数,默认0.5

    返回:
    float: 计算出的分位值(0-1之间)
    """
    try:
        if pd.isnull(value) or len(data) == 0:
            return 0

        # 处理极端值
        data = data.replace([np.inf, -np.inf], np.nan)
        data = data.dropna()
        value = np.clip(value, -1e6, 1e6)

        # 计算均值和标准差作为上下轨
        mean = data.mean()
        std = data.std()

        # 检查计算结果是否有效
        if pd.isnull(mean) or pd.isnull(std):
            return 0

        lower_bound = mean - threshold * std
        upper_bound = mean + threshold * std

        # 计算分位值并限制范围在0-1之间
        if (upper_bound - lower_bound) > 0:
            band = (value - lower_bound) / (upper_bound - lower_bound)
            # band = np.clip(band, 0, 1)
            band = round(band, 3)
        else:
            band = 0

        return band

    except Exception as e:
        print(f"计算分位数带时发生错误: {str(e)}")
        return 0


def calculate_extreme_percentile_band(value, data, threshold=0.5):
    """
    计算效率值的分位数带

    参数:
    value: float, 需要计算分位值的效率值
    data: Series, 效率值数据序列
    threshold: float, 计算上下轨的阈值系数,默认0.5

    返回:
    float: 计算出的分位值(0-1之间)
    """
    try:
        # 检查输入参数的有效性
        if pd.isnull(value) or data is None or len(data) == 0:
            return 0

        # 检查data是否全为nan
        if pd.isna(data).all():
            return 0

        # 处理极端值和0值
        data = data.replace([np.inf, -np.inf], np.nan)
        data = data.dropna()

        # 再次检查处理后是否还有数据
        if len(data) == 0:
            return 0

        data = data[data != 0]  # 剔除0值

        # 最终检查剔除0值后是否还有数据
        if len(data) == 0:
            return 0

        # 检查value是否为有效数值
        if pd.isnull(value) or not np.isfinite(value):
            return 0

        value = np.clip(value, -1e6, 1e6)

        lower_bound = min(data)
        upper_bound = max(data)

        # 计算分位值并限制范围在0-1之间
        if (upper_bound - lower_bound) > 0:
            band = (value - lower_bound) / (upper_bound - lower_bound)
            # band = np.clip(band, 0, 1)
            band = round(band, 3)
        else:
            band = 0

        return band

    except Exception as e:
        print(f"计算分位数带时发生错误: {str(e)}")
        return 0


def analyze_efficiency_peaks(gap_data, field='efficiency_ma5', window=10, threshold=0.5):
    """
    分析时间序列数据的波峰波谷特征

    参数:
    gap_data: DataFrame, 包含待分析的时间序列数据
    field: str, 需要分析的字段名称
    window: int, 寻找局部极值的窗口大小
    threshold: float, 波峰波谷的阈值(相对于均值的偏离程度)

    返回:
    dict: 包含波峰波谷分析结果
    """
    # 检查输入数据是否为空
    if gap_data is None or len(gap_data) == 0 or field not in gap_data.columns:
        return {
            'peak_count': 0,
            'valley_count': 0,
            'avg_peak_period': 0.0,
            'avg_valley_period': 0.0,
            'avg_peak_intensity': 0.0,
            'avg_valley_intensity': 0.0,
            'last_peak_date': None,
            'last_valley_date': None
        }

    # 计算局部极大值和极小值
    data = gap_data[field].copy()
    if data.isnull().any():  # 检查是否有空值
        data = data.ffill()  # 用前值填充

    peaks = []
    valleys = []

    # 使用滚动窗口计算动态阈值
    rolling_window = min(20, len(data)//2)  # 使用较小的滚动窗口
    rolling_mean = data.rolling(window=rolling_window, center=True).mean()
    rolling_std = data.rolling(window=rolling_window, center=True).std()

    # 处理开始和结束的NA值
    rolling_mean = rolling_mean.bfill().ffill()
    rolling_std = rolling_std.bfill().ffill()

    if (rolling_mean == 0).all() or (rolling_std == 0).all():  # 防止除零错误
        return {
            'peak_count': 0,
            'valley_count': 0,
            'avg_peak_period': 0.0,
            'avg_valley_period': 0.0,
            'avg_peak_intensity': 0.0,
            'avg_valley_intensity': 0.0,
            'last_peak_date': None,
            'last_valley_date': None
        }

    # 检查常规的波峰波谷
    for i in range(window, len(data) - window):
        # 使用动态阈值
        upper_threshold = rolling_mean.iloc[i] + threshold * rolling_std.iloc[i]
        lower_threshold = rolling_mean.iloc[i] - threshold * rolling_std.iloc[i]

        # 检查波峰
        is_peak = all(data.iloc[i] > data.iloc[i-j] for j in range(1, window+1)) and \
                 all(data.iloc[i] > data.iloc[i+j] for j in range(1, window+1))
        if is_peak and data.iloc[i] > upper_threshold:
            peaks.append(i)

        # 检查波谷
        is_valley = all(data.iloc[i] < data.iloc[i-j] for j in range(1, window+1)) and \
                   all(data.iloc[i] < data.iloc[i+j] for j in range(1, window+1))
        if is_valley and data.iloc[i] < lower_threshold:
            valleys.append(i)

    # 检查末尾区间的波峰波谷
    if len(data) >= window:
        for i in range(len(data) - window, len(data)):
            # 使用动态阈值
            upper_threshold = rolling_mean.iloc[i] + threshold * rolling_std.iloc[i]
            lower_threshold = rolling_mean.iloc[i] - threshold * rolling_std.iloc[i]

            # 检查波峰 - 对于末尾区间,只需要检查前window个点
            is_peak = all(data.iloc[i] > data.iloc[i-j] for j in range(1, window+1)) and \
                     all(data.iloc[i] > data.iloc[i+j] for j in range(1, len(data)-i))
            if is_peak and data.iloc[i] > upper_threshold:
                peaks.append(i)

            # 检查波谷 - 对于末尾区间,只需要检查前window个点
            is_valley = all(data.iloc[i] < data.iloc[i-j] for j in range(1, window+1)) and \
                       all(data.iloc[i] < data.iloc[i+j] for j in range(1, len(data)-i))
            if is_valley and data.iloc[i] < lower_threshold:
                valleys.append(i)

    # 计算统计指标
    total_days = len(data)
    peak_count = len(peaks)
    valley_count = len(valleys)

    # 计算频率指标(每100天)
    peak_freq = peak_count * 100 / total_days if total_days > 0 else 0
    valley_freq = valley_count * 100 / total_days if total_days > 0 else 0

    # 获取最近的波峰和波谷日期
    last_peak = data.index[peaks[-1]] if peaks else None
    last_valley = data.index[valleys[-1]] if valleys else None

    # 计算强度，使用动态均值
    peak_intensities = [(data.iloc[i] / rolling_mean.iloc[i] - 1) for i in peaks] if peaks else [0]
    valley_intensities = [(1 - data.iloc[i] / rolling_mean.iloc[i]) for i in valleys] if valleys else [0]

    avg_peak_intensity = float(np.mean(peak_intensities)) if peak_intensities else 0.0
    avg_valley_intensity = float(np.mean(valley_intensities)) if valley_intensities else 0.0

    return {
        'peak_count': peak_count,
        'valley_count': valley_count,
        'avg_peak_period': round(peak_freq, 1),
        'avg_valley_period': round(valley_freq, 1),
        'avg_peak_intensity': round(avg_peak_intensity, 3),
        'avg_valley_intensity': round(avg_valley_intensity, 3),
        'last_peak_date': last_peak,
        'last_valley_date': last_valley
    }


def gap_multiprocess(Result_GapValue, Result_Common, cal_date, multi_pro=True, cal_indexnum='multi', data_source='api'):
    """多进程计算gap指标"""
    trade_df = get_trade_date()
    print('计算Gap指标：')
    from multiprocessing import Pool
    p = Pool(7)
    pool_data_list = []
    Results = pd.DataFrame()
    if multi_pro:
        if cal_indexnum == 'multi':
            for index in tqdm(Result_GapValue.index):
                ts_code = Result_GapValue.loc[index, 'ts_code']
                if len(Result_Common.query('ts_code==@ts_code')) == 0:
                    continue
                result_common = Result_Common.query('ts_code==@ts_code').iloc[0].copy()
                pool_data_list.append(p.apply_async(func=cal_gapindex,
                                                    args=(Result_GapValue.loc[index, :].copy(), result_common,
                                                          cal_date, trade_df, data_source)))
                # temp = cal_gapindex(Result_Break.loc[index, :].copy(), cal_date, trade_df)
        else:
            for index in tqdm(Result_GapValue.index):
                ts_code = Result_GapValue.loc[index, 'ts_code']
                result_common = Result_Common.query('ts_code==@ts_code').iloc[0].copy()
                pool_data_list.append(p.apply_async(func=cal_single_gapindex,
                                                    args=(Result_GapValue.loc[index, :].copy(), result_common,
                                                          cal_date, trade_df, data_source)))

        p.close()
        p.join()
        for pool_data in pool_data_list:
            if len(Results) == 0:
                Results = pd.DataFrame(pool_data.get()).T
            else:
                Results = pd.concat([Results, pd.DataFrame(pool_data.get()).T], axis=0)
        Result_Output = Results[Result_GapValue.columns].copy()
    else:
        for index in tqdm(Result_GapValue.index):
            ts_code = Result_GapValue.loc[index, 'ts_code']
            result_common = Result_Common.query('ts_code==@ts_code').iloc[0].copy()
            result_temp = cal_gapindex(Result_GapValue.loc[index, :].copy(), result_common, cal_date, trade_df, data_source)
            Results = pd.concat([Results, result_temp], ignore_index=True, axis=0)
        Result_Output = Results[Result_GapValue.columns].copy()
    return Result_Output


def get_gap_mindata(stk_code=None, start_date=None, end_date=None,
                    trade_df=None, data_source='api', style='Stock', draw_pic=False):
    """更新股票日行业的PeakGap和ValleyGap数据"""
    gap_data = pd.DataFrame()
    if start_date is None:
        start_date = end_date
    if trade_df is None:
        trade_df = get_trade_date()
    start_date = trade_df[trade_df<start_date][-3]
    if stk_code is None:
        stk_info = get_stock_info()
        stk_codes = stk_info['ts_code'].tolist()
        # try:
        #     new_data = pro_api.query('stock_basic', exchange='', list_status='L',
        #                              fields='ts_code,name,industry,list_date')
        # except:
        #     time.sleep(1)
        # stk_codes = new_data['ts_code'].unique()
    elif isinstance(stk_code, str):
        stk_codes = [stk_code]
    else:
        stk_codes = stk_code
    if data_source.lower() == 'api':
        # pro_api = ts.pro_api()
        start_time = datetime.datetime.combine(
            pd.to_datetime(start_date), datetime.time(9, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')
        end_time = datetime.datetime.combine(
            pd.to_datetime(end_date), datetime.time(17, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')
            # try:
            #     trade_df = pro_api.trade_cal(exchange='SSE',
            #                                  start_date=pd.to_datetime(
            #                                          start_date).strftime('%Y%m%d'),
            #                                  end_date=pd.to_datetime(
            #                                          end_date).strftime('%Y%m%d'),
            #                                  is_open=1)
            # except Exception as e:
            #     print(e)
            # trade_df = trade_df.rename(columns={'cal_date': 'trade_date'})
            # trade_df['trade_date'] = trade_df['trade_date'].apply(lambda fn: pd.to_datetime(fn).strftime('%Y-%m-%d'))
            # trade_df = trade_df['trade_date'].values
            # trade_df = trade_df.sort_values(by='trade_date', ascending=True).set_index('trade_date', drop=False)
        # data_length = len(trade_df.loc[start_date:end_date]) * 241
        # 计算每天的分钟数据点数
        data_length = len(trade_df[(trade_df >= start_date) & (trade_df <= end_date)]) * 241
        limit = 7000
        max_offset = 99000

        for ts_code in stk_codes:
            daily_data = pd.DataFrame()
            offset = 0

            # 当数据长度超过max_offset时,按时间分段获取
            if data_length > max_offset:
                # 计算需要分成几段
                date_list = trade_df[(trade_df >= start_date) & (trade_df <= end_date)]
                segment_days = int(max_offset / 241)  # 每段最多天数
                date_segments = [date_list[i:i+segment_days] for i in range(0, len(date_list), segment_days)]

                # 按时间段分别获取数据
                for dates in date_segments:
                    seg_start = datetime.datetime.combine(
                        pd.to_datetime(dates[0]), datetime.time(9, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')
                    seg_end = datetime.datetime.combine(
                        pd.to_datetime(dates[-1]), datetime.time(17, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')

                    while True:
                        min_data = pd.DataFrame()
                        for _ in range(3):
                            try:
                                min_data = ts.pro_bar(**{"ts_code": ts_code,
                                                     "freq": "1min",
                                                     "start_date": seg_start,
                                                     "end_date": seg_end,
                                                     "limit": limit,
                                                     "offset": offset})
                                time.sleep(random.uniform(1, 3))
                            except Exception as e:
                                print(f"Error occurred: {e}")
                                time.sleep(2)
                            else:
                                break

                        if len(min_data) > 0:
                            daily_data = pd.concat([daily_data, min_data], ignore_index=True)
                            offset += limit
                            if offset >= max_offset:
                                offset = 0
                                break
                        else:
                            offset = 0
                            break
            else:
                # 数据量较小时直接获取
                while True:
                    min_data = pd.DataFrame()
                    for _ in range(3):
                        try:
                            min_data = ts.pro_bar(**{"ts_code": ts_code,
                                                 "freq": "1min",
                                                 "start_date": start_time,
                                                 "end_date": end_time,
                                                 "limit": limit,
                                                 "offset": offset})
                            time.sleep(random.uniform(1, 3))
                        except Exception as e:
                            print(f"Error occurred: {e}")
                            time.sleep(2)
                        else:
                            break

                    if len(min_data) > 0:
                        daily_data = pd.concat([daily_data, min_data], ignore_index=True)
                        offset += limit
                    else:
                        break

            if len(daily_data) > 0 and style.lower() == 'stock':
                temp_df = cal_stk_gapvalue(daily_data, ts_code)
                gap_data = pd.concat([gap_data, temp_df], ignore_index=True)
            elif len(daily_data) > 0 and style.lower() == 'index':
                temp_df = cal_index_gapvalue(daily_data, ts_code)
                gap_data = pd.concat([gap_data, temp_df], ignore_index=True)
            elif len(daily_data) == 0:
                print('api获取gap_data数据为空')
                return None, None
        gap_data = gap_data.sort_values(by='trade_date', ascending=True)
        # if check_signal:
        #     peakdate = input_to_str(input('请输入标的近期波峰日期：'), type='str')
        #     highquntl_value = (gap_data['valley_gap'].mean() + gap_data['valley_gap'].std()) * 0.9
        #     over_highquntl_date = gap_data.query(
        #         'trade_date>@peakdate & valley_gap>@highquntl_value')['trade_date'].iloc[0] \
        #         if len(gap_data.query('trade_date>@peakdate & valley_gap>@highquntl_value')) > 0 else None
        #     if (over_highquntl_date is not None
        #        and gap_data['valley_gap'].iloc[-1] >= gap_data['valley_gap'].median()
        #        and len(gap_data.query('trade_date>@over_highquntl_date')) > 1):
        #         print('出现标的入场时机：', gap_data['trade_date'].iloc[-1])

        # if storemode and stk_codes is None:
        #     from sqlalchemy import create_engine
        #     import config.config_Ali as config_Ali
        #     conf_a = config_Ali.configModel()
        #     engine_ali = create_engine(
        #         'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
        #             conf_a.DC_DB_PORT) + '/stocksfit')
        #     for _ in range(3):
        #         try:
        #             pd.io.sql.to_sql(
        #                 gap_data, 'stock_dailygap', engine_ali, index=False, schema='stocksfit', if_exists='append')
        #         except:
        #             time.sleep(3)
        #         else:
        #             break
        #     engine_ali.dispose()
    elif data_source.lower() == 'local' and style.lower() == 'stock':
        from sqlalchemy import create_engine
        import config.config_local as config_local
        conf_a = config_local.configModel()
        engine_local = create_engine(
            'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
                conf_a.DC_DB_PORT) + '/stocksfit')

        # 分块获取数据的大小
        chunk_size = 10000

        for ts_code in tqdm(stk_codes):
            offset = 0
            daily_data = pd.DataFrame()

            while True:
                # 分块查询SQL
                sql = f"""select * from stocksfit.stock_freqdata
                         where trade_date between '{start_date}' and '{end_date}'
                         and ts_code = '{ts_code}'
                         limit {chunk_size} offset {offset}"""

                # 尝试3次获取数据
                for _ in range(3):
                    try:
                        chunk_data = pd.read_sql_query(sql=sql, con=engine_local)
                    except Exception as e:
                        print(f"Error occurred: {e}")
                        time.sleep(2)
                    else:
                        break

                if len(chunk_data) > 0:
                    daily_data = pd.concat([daily_data, chunk_data], ignore_index=True)
                    offset += chunk_size
                else:
                    break

            if len(daily_data) > 0:
                temp_df = cal_stk_gapvalue(daily_data, ts_code)
                gap_data = pd.concat([gap_data, temp_df], ignore_index=True)
            else:
                print('sql获取gap_data数据为空')
                return None, None

        gap_data = gap_data.sort_values(by='trade_date', ascending=True)
        engine_local.dispose()
    else:
        print('wrong data_source!')
        return None, None
    valley_highquntl_thre = (gap_data['valley_gap'].mean() + gap_data['valley_gap'].std()) * 0.9
    peak_highquntl_thre = (gap_data['peak_gap'].mean() + gap_data['peak_gap'].std()) * 0.9
    gap_data['maxvalley_in_recent5'] = gap_data['valley_gap'].rolling(window=3).max()
    gap_data['maxpeak_in_recent5'] = gap_data['peak_gap'].rolling(window=3).max()
    gap_data['vgv_rollavg'] = round(gap_data['valley_gap'].rolling(window=3).mean(), 4)
    gap_data['pgv_rollavg'] = round(gap_data['peak_gap'].rolling(window=3).mean(), 4)
    peak_signal_date = gap_data.query(
        'peak_gap>=@peak_highquntl_thre & maxpeak_in_recent5==peak_gap')['trade_date'].iloc[-1] if len(
        gap_data.query('peak_gap>=@peak_highquntl_thre & maxpeak_in_recent5==peak_gap')) > 0 else '-'
    valley_signal_date = gap_data.query(
        'valley_gap>=@valley_highquntl_thre & maxvalley_in_recent5==valley_gap')['trade_date'].iloc[-1] if len(
        gap_data.query('valley_gap>=@valley_highquntl_thre & maxvalley_in_recent5==valley_gap')) > 0 else '-'
    gapdata_stat = {'Median_PeakGap': round(gap_data['peak_gap'].median(), 4),
                    'HighQuntl_PeakGap': round(gap_data['peak_gap'].mean() + gap_data['peak_gap'].std(), 4),
                    'Max_PeakGap': round(gap_data['peak_gap'].max(), 4),
                    'Median_ValleyGap': round(gap_data['valley_gap'].median(), 4),
                    'HighQuntl_ValleyGap': round(gap_data['valley_gap'].mean() + gap_data['valley_gap'].std(), 4),
                    'Max_ValleyGap': round(gap_data['valley_gap'].max(), 4),
                    'Min_ValleyGap_RollAvg': round(gap_data['vgv_rollavg'].min(), 4),
                    'Median_PeakGapRatio': round(gap_data['peakgap_ratio'].median(), 4),
                    'Peak_Signal_Date': peak_signal_date,
                    'Valley_Signal_Date': valley_signal_date}

    if draw_pic and stk_code is not None:
        gap_data_sorted = gap_data.sort_values(by='trade_date', ascending=True
                                        ).set_index('trade_date')
        # 使用subplots创建图形和轴
        fig, ax1 = plt.subplots(figsize=(10, 6))

        # 创建第二个y轴
        ax2 = ax1.twinx()

        # 在第一个y轴上绘制pgv_rollavg
        line1 = ax1.plot(range(0, len(gap_data_sorted)), gap_data_sorted['pgv_rollavg'].values,
                        'm-', label='pgv_rollavg')
        ax1.set_ylabel('Rolling Average (pgv)', fontsize=12, color='m')
        ax1.tick_params(axis='y', labelcolor='m')

        # 在第二个y轴上绘制valley_gap
        line2 = ax2.plot(range(0, len(gap_data_sorted)), gap_data_sorted['valley_gap'].values,
                        'b-', label='valley_gap')
        ax2.set_ylabel('Valley Gap', fontsize=12, color='b')
        ax2.tick_params(axis='y', labelcolor='b')

        # 添加标题和x轴标签
        plt.title(f'{stk_code} - Rolling Average', fontsize=16)
        ax1.set_xlabel('Trade Date', fontsize=12)

        # 设置x轴刻度标签,增加刻度数量
        xticks = range(0, len(gap_data_sorted), int(len(gap_data_sorted) / 20))  # 将10改为20,增加刻度数量
        ax1.set_xticks(xticks)
        ax1.set_xticklabels(gap_data_sorted.index[xticks].astype(str), rotation=45)

        # 合并两个图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper right')

        # 自动调整布局，防止标签重叠
        plt.tight_layout()

        # 显示图形
        plt.show()
    return gap_data, gapdata_stat


def cal_index_gapvalue(daily_data, ts_code):
    daily_data = daily_data.sort_values(
        by='trade_time', ascending=True)
    daily_data['amount_cumsum'] = daily_data[
        ['trade_date', 'amount']].groupby(['trade_date'])['amount'].cumsum()
    daily_data['amount_weighted_close'] = (daily_data['close'] + daily_data['open']
                                           ) * daily_data['amount'] / 2
    daily_data['weight_avg'] = daily_data[['trade_date', 'amount_weighted_close']
                               ].groupby(['trade_date'])['amount_weighted_close'].cumsum() / \
                               daily_data['amount_cumsum']
    daily_data['valley_gap'] = daily_data.apply(lambda fn: round(fn['weight_avg'] - fn['low'], 4)
    if fn['low'] > 0 else round(fn['weight_avg'] - fn['close'], 4), axis=1)
    daily_data['peak_gap'] = daily_data.apply(lambda fn: round(fn['high'] - fn['weight_avg'], 4)
    if fn['high'] > 0 else round(fn['close'] - fn['weight_avg'], 4), axis=1)
    daily_data['peakgap_ratio'] = round(daily_data['peak_gap'] * 100 / daily_data['weight_avg'], 4)
    daily_data['valleygap_ratio'] = round(daily_data['valley_gap'] * 100 / daily_data['weight_avg'], 4)
    peak_df = daily_data[['trade_date', 'peak_gap']].groupby(
        ['trade_date'], as_index=False)['peak_gap'].max()
    valley_df = daily_data[['trade_date', 'valley_gap']].groupby(
        ['trade_date'], as_index=False)['valley_gap'].max()
    peakratio_df = daily_data[['trade_date', 'peakgap_ratio']].groupby(
        ['trade_date'], as_index=False)['peakgap_ratio'].max()
    valleyratio_df = daily_data[['trade_date', 'valleygap_ratio']].groupby(
        ['trade_date'], as_index=False)['valleygap_ratio'].max()
    peak_df['trade_date'] = pd.to_datetime(peak_df['trade_date'])
    peak_df['trade_date'] = peak_df['trade_date'].apply(
        lambda fn: fn.strftime('%Y-%m-%d'))
    temp_df = pd.merge(
        peak_df, valley_df[['valley_gap']], left_index=True, right_index=True, how='left')
    temp_df = pd.merge(temp_df, peakratio_df[['peakgap_ratio']],
                       left_index=True, right_index=True, how='left')
    temp_df = pd.merge(temp_df, valleyratio_df[['valleygap_ratio']],
                       left_index=True, right_index=True, how='left')
    temp_df['ts_code'] = ts_code
    return temp_df


def cal_stk_gapvalue(daily_data, ts_code):
    daily_data = daily_data.sort_values(
        by='trade_time', ascending=True)
    daily_data['vol_cumsum'] = daily_data[
        ['ts_code', 'trade_date', 'vol']].groupby(['ts_code', 'trade_date'])['vol'].cumsum()
    daily_data['amount_cumsum'] = daily_data[
        ['ts_code', 'trade_date', 'amount']].groupby(['ts_code', 'trade_date'])['amount'].cumsum()
    # daily_data['weight_avg'] = daily_data['amount_cumsum'] / \
    #                            daily_data['vol_cumsum']
    # daily_data['weight_avg'] = daily_data.apply(
    #     lambda fn: fn['amount_cumsum']*100/fn['vol_cumsum']
    #     if fn['vol_cumsum'] % 100 == 0 and abs(fn['amount_cumsum']/fn['vol_cumsum']/fn['close'] - 1) > 0.5
    #     else fn['amount_cumsum']/fn['vol_cumsum'], axis=1)
    daily_data['weight_avg'] = daily_data.apply(cal_avg_nozero, axis=1)
    daily_data['valley_gap'] = round(daily_data['weight_avg'] - daily_data['low'], 3)
    daily_data['peak_gap'] = round(daily_data['high'] - daily_data['weight_avg'], 3)
    daily_data['peakgap_ratio'] = round(daily_data['peak_gap'] * 100 / daily_data['weight_avg'], 4)
    daily_data['valleygap_ratio'] = round(daily_data['valley_gap'] * 100 / daily_data['weight_avg'], 4)
    peak_df = daily_data[['trade_date', 'peak_gap']].groupby(
        ['trade_date'], as_index=False)['peak_gap'].max()
    valley_df = daily_data[['trade_date', 'valley_gap']].groupby(
        ['trade_date'], as_index=False)['valley_gap'].max()
    peakratio_df = daily_data[['trade_date', 'peakgap_ratio']].groupby(
        ['trade_date'], as_index=False)['peakgap_ratio'].max()
    valleyratio_df = daily_data[['trade_date', 'valleygap_ratio']].groupby(
        ['trade_date'], as_index=False)['valleygap_ratio'].max()
    peak_df['trade_date'] = pd.to_datetime(peak_df['trade_date'])
    peak_df['trade_date'] = peak_df['trade_date'].apply(
        lambda fn: fn.strftime('%Y-%m-%d'))
    temp_df = pd.merge(
        peak_df, valley_df[['valley_gap']], left_index=True, right_index=True, how='left')
    temp_df = pd.merge(temp_df, peakratio_df[['peakgap_ratio']],
                       left_index=True, right_index=True, how='left')
    temp_df = pd.merge(temp_df, valleyratio_df[['valleygap_ratio']],
                       left_index=True, right_index=True, how='left')
    temp_df['ts_code'] = ts_code

    def cal_daily_cor_change(group):
        daily_open = group['open'].iloc[0]
        daily_close = group['close'].iloc[-1]
        change = round((daily_close - daily_open) / daily_open * 100, 3)
        return change
    cor_df = daily_data.groupby('trade_date', as_index=False).apply(cal_daily_cor_change).reset_index()
    cor_df.columns = ['Index', 'trade_date', 'cor_ratio']
    cor_df['trade_date'] = pd.to_datetime(cor_df['trade_date'])
    cor_df['trade_date'] = cor_df['trade_date'].apply(lambda fn: fn.strftime('%Y-%m-%d'))
    daily_close = daily_data[['trade_date', 'close']].groupby('trade_date', as_index=False)['close'].last()
    daily_close['daily_ratio'] = round(daily_close['close'].pct_change() * 100, 3)
    daily_close['trade_date'] = pd.to_datetime(daily_close['trade_date'])
    daily_close['trade_date'] = daily_close['trade_date'].apply(lambda fn: fn.strftime('%Y-%m-%d'))
    temp_df = pd.merge(temp_df, cor_df[['trade_date', 'cor_ratio']], on='trade_date', how='left')
    temp_df = pd.merge(temp_df, daily_close[['trade_date', 'daily_ratio', 'close']], on='trade_date', how='left')
    return temp_df


def cal_avg_nozero(dailydata, mode='History'):
    if mode == 'History':
        """计算weight_avg,排除分母为0情况"""
        if dailydata['vol_cumsum'] == 0:
            return dailydata['close']
        elif abs(dailydata['amount_cumsum'] / dailydata['vol_cumsum'] / dailydata['close'] - 1) > 0.5:
            return round(dailydata['amount_cumsum'] * 100 / dailydata['vol_cumsum'], 3)
        else:
            return round(dailydata['amount_cumsum'] / dailydata['vol_cumsum'], 3)
    else:
        if dailydata['VOLUME'] == 0:
            return dailydata['PRICE']
        elif abs(dailydata['AMOUNT'] / dailydata['VOLUME'] / dailydata['PRICE'] - 1) > 0.5:
            return round(dailydata['AMOUNT'] / dailydata['VOLUME'] / 100, 3)
        else:
            return round(dailydata['AMOUNT'] / dailydata['VOLUME'], 3)


def cal_single_gapindex(Result_Loc, end_date, trade_df):
    """计算各种Gap指标"""
    ts_code = Result_Loc['ts_code']
    Section_PeakDate = Result_Loc['Section_PeakDate']
    Section_StartDate = Result_Loc['Section_StartDate']
    Now_SecDate = Result_Loc['Now_SecDate']
    PreNow_SecDate = Result_Loc['PreNow_SecDate']
    PreTurn_PeakDate = Result_Loc['PreTurn_PeakDate']
    if pd.isnull(Section_PeakDate) or pd.isnull(Section_StartDate) or pd.isnull(Now_SecDate) \
            or pd.isnull(PreNow_SecDate) or pd.isnull(PreTurn_PeakDate):
        return Result_Loc
    gap_data = get_min_indicators(stk_code=ts_code, start_date=PreTurn_PeakDate, end_date=end_date,
                                  trade_df=trade_df, data_source='api', calc_turnover=True, calc_gap=True)
    if gap_data is not None and len(gap_data) > 0:
        def consecutive_count(df_data, mode='drop'):
            """查找df_data中pgv_rollavg连续三天以上低于前一日数值，出现次数"""
            if mode.lower() == 'drop':
                df_data['diff_previous'] = df_data['pgv_rollavg'] < df_data['pgv_rollavg'].shift(1)
            else:
                df_data['diff_previous'] = df_data['pgv_rollavg'] >= df_data['pgv_rollavg'].shift(1)
            # 初始化计数器
            count = 0
            consecutive_days = 0
            lastcount2now_days = len(df_data)
            consecu_startdate, consecu_enddate = None, None
            consecutive_diffratio, consecutive_perfratio, consecutive_start_pgvra = 0, 0, 0
            consecutive_lastdays = []
            # 遍历 DataFrame 以统计连续出现的次数
            for index in df_data.index:
                if df_data.loc[index, 'diff_previous']:
                    if consecutive_days == 0:
                        consecu_startdate = df_data.loc[:index].index[-2] if len(df_data.loc[:index]) > 1 else index
                    consecutive_days += 1
                    consecu_enddate = index
                else:
                    if consecutive_days >= 2:
                        count += 1
                        lastcount2now_days = len(df_data.loc[index:])
                        if consecu_startdate is not None and consecu_enddate is not None:
                            consecutive_diffratio = round(
                                df_data.loc[consecu_enddate, 'pgv_rollavg'] /
                                df_data.loc[consecu_startdate, 'pgv_rollavg'], 3) \
                                if df_data.loc[consecu_startdate, 'pgv_rollavg'] > 0 else 1
                            consecutive_perfratio = round(
                                ((df_data.loc[consecu_startdate:consecu_enddate, 'daily_ratio'].iloc[1:] / 100 + 1
                                  ).product() - 1) * 100, 3)
                            consecutive_start_pgvra = df_data.loc[consecu_startdate, 'pgv_rollavg']
                            consecutive_lastdays.append(len(df_data.loc[consecu_startdate:consecu_enddate]))
                    consecutive_days = 0

            # 检查最后一段连续天数
            if consecutive_days >= 2:
                count += 1
                lastcount2now_days = 0
                if consecu_startdate is not None:
                    consecu_enddate = df_data.index[-1]
                    consecutive_diffratio = round(
                        df_data['pgv_rollavg'].iloc[-1] /
                        df_data.loc[consecu_startdate, 'pgv_rollavg'], 3) \
                        if df_data.loc[consecu_startdate, 'pgv_rollavg'] > 0 else 1
                    consecutive_perfratio = round(
                        ((df_data.loc[consecu_startdate:, 'daily_ratio'].iloc[1:] / 100 + 1
                          ).product() - 1) * 100, 3)
                    consecutive_start_pgvra = df_data.loc[consecu_startdate, 'pgv_rollavg']
                    consecutive_lastdays.append(len(df_data.loc[consecu_startdate:]))
            consecutive_avglastdays = round(np.mean(consecutive_lastdays), 2) \
                if len(consecutive_lastdays) > 0 else 0
            consecutive_maxlastdays = np.max(consecutive_lastdays) \
                if len(consecutive_lastdays) > 0 else 0
            return {'count': count,
                    'consecutive_avglastdays': consecutive_avglastdays,
                    'consecutive_maxlastdays': consecutive_maxlastdays,
                    'lastcount2now_days': lastcount2now_days,
                    'consecutive_diffratio': consecutive_diffratio,
                    'consecutive_perfratio': consecutive_perfratio,
                    'consecutive_start_pgvra': consecutive_start_pgvra,
                    'consecu_enddate': consecu_enddate}
        downconsecutive_result = consecutive_count(gap_data.loc[Section_PeakDate:].copy(), mode='drop')
        Result_Loc['DownConsecutive2Now_LastDays'] = downconsecutive_result['lastcount2now_days']
        upconsecutive_result = consecutive_count(gap_data.loc[Section_PeakDate:].copy(), mode='rise')
        Result_Loc['UpConsecutive2Now_LastDays'] = upconsecutive_result['lastcount2now_days']
    return Result_Loc


def analyze_pgv_pattern(gap_data, recent_window=7, compare_window=14,
                        rise_threshold=0.15, decline_threshold=-0.1):
    """
    分析pgv_rollavg的波动变化和底部回升情况

    参数:
    gap_data: DataFrame, 包含pgv_rollavg字段的时间序列数据
    recent_window: int, 最近期间的天数
    compare_window: int, 对比期间的天数
    rise_threshold: float, 上升速度阈值
    decline_threshold: float, 下降速度阈值

    返回:
    dict: 包含波动分析和底部回升判断结果
    """
    # 确保数据按时间排序
    gap_data = gap_data.sort_index()

    # 获取最近期间和对比期间的数据
    recent_data = gap_data['pgv_rollavg'].tail(recent_window)
    compare_data = gap_data['pgv_rollavg'].tail(compare_window).head(compare_window - recent_window)

    # 计算各项统计指标
    recent_mean = recent_data.mean()
    compare_mean = compare_data.mean()
    recent_std = recent_data.std()
    compare_std = compare_data.std()

    # 计算变化百分比
    mean_change_pct = ((recent_mean - compare_mean) / compare_mean) * 100 if compare_mean > 0 else 0
    std_change_pct = ((recent_std - compare_std) / compare_std) * 100 if compare_std > 0 else 0

    # 判断波动变化情况
    is_expanding = mean_change_pct > 10  # 均值增加超过10%
    is_more_volatile = std_change_pct > 20  # 标准差增加超过20%

    # 检查底部回升
    window = 5  # 使用固定的5天窗口检查底部回升
    if len(gap_data) >= window * 2:
        pct_change = gap_data['pgv_rollavg'].pct_change(window)
        current_change = pct_change.iloc[-1]
        previous_change = pct_change.iloc[-window-1]
        is_bottom_reversal = (current_change > rise_threshold) and (previous_change < decline_threshold)
    else:
        is_bottom_reversal = False

    result = {
        'is_expanding': int(is_expanding),
        'is_more_volatile': int(is_more_volatile),
        'is_bottom_reversal': int(is_bottom_reversal),
    }

    return result


def cal_turnover_efficiency(daily_data, total_share=None, period_minutes=30, style='Stock'):
    """计算换手效率指标"""
    # 计算每个周期的价格变动效率
    daily_data = daily_data.sort_values(['trade_date','trade_time'])

    # 按日期和时间周期分组
    daily_data['period'] = daily_data.groupby('trade_date').cumcount() // period_minutes
    period_data = daily_data.groupby(['trade_date','period']).agg({
        'close':['first','last'],
        'vol':'sum',
        'amount':'sum'
    }).reset_index()

    # 计算价格变动效率
    period_data['price_change'] = abs(period_data['close']['last'] - period_data['close']['first'])/period_data['close']['first']

    # 根据style计算turnover
    if style.lower() == 'stock':
        period_data['turnover'] = period_data['amount']['sum'] / (total_share * period_data['close']['first'])
        daily_data['turnover'] = daily_data['amount'] / (total_share * daily_data['close'])
    else:
        period_data['turnover'] = period_data['amount']['sum']/100000000

    period_data['efficiency'] = round(period_data['price_change'] * 10000/ period_data['turnover'],3)
    period_data = period_data.reset_index()

    # 计算每日效率指标
    daily_efficiency = period_data.groupby('trade_date')['efficiency'].agg([
        ('mean','mean'),
        ('std','std')
    ]).reset_index()

    daily_efficiency.columns = ['trade_date','avg_efficiency','std_efficiency']

    # 处理2024-09-27异常数据
    abnormal_date = '2024-09-27'
    if abnormal_date in daily_efficiency['trade_date'].values:
        abnormal_idx = daily_efficiency[daily_efficiency['trade_date']==abnormal_date].index[0]
        if abnormal_idx > 0 and abnormal_idx < len(daily_efficiency)-1:
            # 使用前后2天的均值替代
            daily_efficiency.loc[abnormal_idx, 'avg_efficiency'] = round(
                daily_efficiency.loc[abnormal_idx-1:abnormal_idx+1, 'avg_efficiency']
                .mean(), 4)
            daily_efficiency.loc[abnormal_idx, 'std_efficiency'] = round(
                daily_efficiency.loc[abnormal_idx-1:abnormal_idx+1, 'std_efficiency']
                .mean(), 4)

    # 计算日内最大连续上涨/下跌区间
    daily_data['price_change'] = daily_data['close'].pct_change()

    up_down_stats = []
    for date, group in daily_data.groupby('trade_date'):
        # 初始化变量
        max_up_return = 0
        max_up_turnover = 0
        max_down_return = 0
        max_down_turnover = 0

        # 计算连续上涨/下跌
        curr_up_return = 0
        curr_up_turnover = 0
        curr_down_return = 0
        curr_down_turnover = 0

        for i in range(1, len(group)):
            change = group.iloc[i]['price_change']
            turnover = group.iloc[i]['turnover'] if style.lower() == 'stock' else group.iloc[i]['amount']/100000000

            if change > 0:
                curr_up_return += change
                curr_up_turnover += turnover
                curr_down_return = 0
                curr_down_turnover = 0
            elif change < 0:
                curr_down_return += abs(change)
                curr_down_turnover += turnover
                curr_up_return = 0
                curr_up_turnover = 0

            max_up_return = max(max_up_return, curr_up_return)
            max_up_turnover = max(max_up_turnover, curr_up_turnover)
            max_down_return = max(max_down_return, curr_down_return)
            max_down_turnover = max(max_down_turnover, curr_down_turnover)

        up_efficiency = round(max_up_return * 1000 / max_up_turnover, 3) if max_up_turnover > 0 else 0
        down_efficiency = round(max_down_return * 1000 / max_down_turnover, 3) if max_down_turnover > 0 else 0

        up_down_stats.append({
            'trade_date': date,
            'up_efficiency': round(up_efficiency, 4),
            'down_efficiency': round(down_efficiency, 4)
        })

    up_down_df = pd.DataFrame(up_down_stats)
    daily_efficiency = pd.merge(daily_efficiency, up_down_df, on='trade_date')

    # 计算效率统计指标
    daily_efficiency['efficiency_ma5'] = round(daily_efficiency['avg_efficiency'].rolling(5).mean(), 4)
    daily_efficiency['up_efficiency_ma5'] = round(daily_efficiency['up_efficiency'].rolling(5).mean(), 4)
    daily_efficiency['down_efficiency_ma5'] = round(daily_efficiency['down_efficiency'].rolling(5).mean(), 4)
    daily_efficiency['up2down_efficiency_ma5'] = daily_efficiency['up_efficiency'] - daily_efficiency['down_efficiency']

    daily_efficiency['trade_date'] = pd.to_datetime(daily_efficiency['trade_date'])
    daily_efficiency['trade_date'] = daily_efficiency.apply(lambda x: x['trade_date'].strftime('%Y-%m-%d'), axis=1)

    return daily_efficiency


def get_min_indicators(stk_code, start_date=None, end_date=None,
                       trade_df=None, total_share=None,
                       data_source='api', style='Stock', period_minutes=30,
                       calc_turnover=True, calc_gap=True,
                       draw_turnover=False, draw_gap=False):
    """获取单只股票分钟数据并计算相关指标"""
    if start_date is None:
        start_date = end_date
    if trade_df is None:
        trade_df = get_trade_date()
    start_date = trade_df[trade_df<start_date][-3]

    # 获取股本数据
    if (total_share is None or pd.isnull(total_share)) and calc_turnover and style.lower() == 'stock':
        stock_data = get_stock_data(stk_code=stk_code,
                                    start_date=start_date, end_date=end_date) if calc_turnover and style.lower() == 'stock' else None
        if calc_turnover and style.lower() == 'stock' and len(stock_data) == 0:
            print('获取股本数据失败')
            return None
        total_share = stock_data.iloc[-1]['total_share'] if calc_turnover and style.lower() == 'stock' else None

    daily_data = pd.DataFrame()

    if data_source.lower() == 'api':
        # pro_api = ts.pro_api()
        start_time = datetime.datetime.combine(
            pd.to_datetime(start_date), datetime.time(9, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')
        end_time = datetime.datetime.combine(
            pd.to_datetime(end_date), datetime.time(17, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')

        data_length = len(trade_df[(trade_df >= start_date) & (trade_df <= end_date)]) * 241
        limit = 7000
        max_offset = 91000
        offset = 0

        if data_length > max_offset:
            date_list = trade_df[(trade_df >= start_date) & (trade_df <= end_date)]
            segment_days = int(max_offset / 241)
            date_segments = [date_list[i:i+segment_days] for i in range(0, len(date_list), segment_days)]

            for dates in date_segments:
                seg_start = datetime.datetime.combine(
                    pd.to_datetime(dates[0]), datetime.time(9, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')
                seg_end = datetime.datetime.combine(
                    pd.to_datetime(dates[-1]), datetime.time(17, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')

                while True:
                    min_data = pd.DataFrame()
                    for _ in range(3):
                        try:
                            min_data = ts.pro_bar(**{"ts_code": stk_code,
                                                 "freq": "1min",
                                                 "start_date": seg_start,
                                                 "end_date": seg_end,
                                                 "limit": limit,
                                                 "offset": offset})
                            time.sleep(random.uniform(1, 3))
                        except Exception as e:
                            print(f"Error occurred: {e}")
                            time.sleep(2)
                        else:
                            break

                    if len(min_data) > 0:
                        daily_data = pd.concat([daily_data, min_data], ignore_index=True)
                        offset += limit
                        if offset >= max_offset:
                            offset = 0
                            break
                    else:
                        offset = 0
                        break
        else:
            while True:
                min_data = pd.DataFrame()
                for _ in range(3):
                    try:
                        min_data = ts.pro_bar(**{"ts_code": stk_code,
                                             "freq": "1min",
                                             "start_date": start_time,
                                             "end_date": end_time,
                                             "limit": limit,
                                             "offset": offset})
                        time.sleep(random.uniform(1, 3))
                    except Exception as e:
                        print(f"Error occurred: {e}")
                        time.sleep(2)
                    else:
                        break

                if len(min_data) > 0:
                    daily_data = pd.concat([daily_data, min_data], ignore_index=True)
                    offset += limit
                else:
                    break

    elif data_source.lower() == 'local':
        from sqlalchemy import create_engine
        import config.config_local as config_local
        conf_a = config_local.configModel()
        engine_local = create_engine(
            'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
                conf_a.DC_DB_PORT) + '/stocksfit')

        chunk_size = 10000
        offset = 0

        while True:
            sql = f"""select * from stocksfit.stock_freqdata
                     where trade_date between '{start_date}' and '{end_date}'
                     and ts_code = '{stk_code}'
                     limit {chunk_size} offset {offset}"""

            for _ in range(3):
                try:
                    chunk_data = pd.read_sql_query(sql=sql, con=engine_local)
                except Exception as e:
                    print(f"Error occurred: {e}")
                    time.sleep(2)
                else:
                    break

            if len(chunk_data) > 0:
                daily_data = pd.concat([daily_data, chunk_data], ignore_index=True)
                offset += chunk_size
            else:
                break

        engine_local.dispose()
    else:
        print('wrong data_source!')
        return None

    if len(daily_data) == 0:
        print(f'{data_source}获取数据为空')
        return None

    result_data = pd.DataFrame()

    # 计算换手效率
    if calc_turnover:
        turnover_df = cal_turnover_efficiency(daily_data, total_share if style.lower() == 'stock' else None, period_minutes, style=style)
        turnover_df['ts_code'] = stk_code
        # 确保trade_date列为字符串格式
        turnover_df['trade_date'] = turnover_df['trade_date'].astype(str)

    # 计算gap指标
    if calc_gap:
        if style.lower() == 'stock':
            gap_df = cal_stk_gapvalue(daily_data, stk_code)
        elif style.lower() == 'index':
            gap_df = cal_index_gapvalue(daily_data, stk_code)
        else:
            print('style参数错误!')
            return None
        # 确保trade_date列为字符串格式
        gap_df['trade_date'] = gap_df['trade_date'].astype(str)
        gap_df['ts_code'] = stk_code
        gap_df['vgv_rollavg'] = round(gap_df['valley_gap'].rolling(window=3).mean(), 4)
        gap_df['pgv_rollavg'] = round(gap_df['peak_gap'].rolling(window=3).mean(), 4)

    # 合并结果
    if calc_turnover and calc_gap:
        result_data = pd.merge(turnover_df, gap_df, on=['trade_date', 'ts_code'], how='outer')
    elif calc_turnover:
        result_data = turnover_df
    else:
        result_data = gap_df

    result_data = result_data.sort_values(by='trade_date', ascending=True)

    # 绘图
    if draw_turnover and calc_turnover:
        plt.figure(figsize=(12,6))
        plt.plot(result_data['trade_date'], result_data['efficiency_ma5'], 'b-', label='MA5')
        plt.plot(result_data['trade_date'], result_data['up_efficiency_ma5'], 'r-', label='Up_MA5')
        plt.plot(result_data['trade_date'], result_data['up2down_efficiency_ma5'], 'g-', label='Up2Down_MA5')
        plt.title(f'{stk_code} Turnover Efficiency Trend')
        plt.xlabel('Date')
        plt.ylabel('Efficiency')
        plt.legend()
        plt.gca().xaxis.set_major_locator(plt.MaxNLocator(20))
        plt.xticks(rotation=30)
        plt.tight_layout()
        plt.show()

    if draw_gap and calc_gap:
        result_data_sorted = result_data.sort_values(by='trade_date', ascending=True
                                        ).set_index('trade_date')
        fig, ax1 = plt.subplots(figsize=(10, 6))
        ax2 = ax1.twinx()

        line1 = ax1.plot(range(0, len(result_data_sorted)), result_data_sorted['pgv_rollavg'].values,
                        'm-', label='pgv_rollavg')
        ax1.set_ylabel('Rolling Average (pgv)', fontsize=12, color='m')
        ax1.tick_params(axis='y', labelcolor='m')

        line2 = ax2.plot(range(0, len(result_data_sorted)), result_data_sorted['valley_gap'].values,
                        'b-', label='valley_gap')
        ax2.set_ylabel('Valley Gap', fontsize=12, color='b')
        ax2.tick_params(axis='y', labelcolor='b')

        plt.title(f'{stk_code} - Rolling Average', fontsize=16)
        ax1.set_xlabel('Trade Date', fontsize=12)

        xticks = range(0, len(result_data_sorted), int(len(result_data_sorted) / 20))
        ax1.set_xticks(xticks)
        ax1.set_xticklabels(result_data_sorted.index[xticks].astype(str), rotation=45)

        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper right')

        plt.tight_layout()
        plt.show()

    return result_data


def cal_turnpoint(stk_code=None, start_date=None, end_date=None,
                  stk_indicator='turnover', df_data=None, show_info=False):
    min_period_for_avg = 4  # 用于计算平均值的最小天数
    t_date = '-'
    absolute_change = 0
    relative_change_percent = 0
    post_change_days = 0

    if stk_code is not None:
        if stk_indicator == 'turnover':
            stock_data = get_stock_data(stk_code=stk_code, start_date=start_date, end_date=end_date)
            df_data = stock_data.set_index('trade_date')
        else:
            gap_data = get_min_indicators(stk_code=stk_code, start_date=start_date, end_date=end_date, draw_turnover=False,
                                          draw_gap=False)
            df_data = gap_data.set_index('trade_date')

    if len(df_data) <= min_period_for_avg * 2:
        return t_date, absolute_change, relative_change_percent, post_change_days

    data = df_data[stk_indicator].dropna().values.reshape(-1, 1)
    dates_index = df_data.index

    # 检查数据是否足够进行分割
    if len(data) < min_period_for_avg * 2:
        return t_date, absolute_change, relative_change_percent, post_change_days

    # 检查数据方差，如果方差为零（所有值相同），无法进行有效分割
    if np.var(data) == 0:
        return t_date, absolute_change, relative_change_percent, post_change_days

    try:
        detector = rpt.Dynp(model="l2", min_size=min_period_for_avg).fit(data)
        result_indices = detector.predict(n_bkps=1)
    except Exception as e:
        # 如果分割失败，返回默认值
        if show_info:
            print(f"变点检测失败: {str(e)}")
        return t_date, absolute_change, relative_change_percent, post_change_days

    # data_variance = np.var(data)
    # if np.isclose(data_variance, 0):  # 如果方差接近零
    #     data_variance = 1e-10  # 设置一个很小的默认值

    # penalty_value = np.log(len(data)) * data_variance * 0.1  # 这是一个尝试性的启发式值，可能需要调整
    # if show_info:
    #     print(f"\n使用的 penalty 参数: {penalty_value:.2f}")

    # result_indices = detector.predict(pen=penalty_value)

    if result_indices and result_indices[0] < len(data):
        change_point_array_index = result_indices[0]

        # 将 numpy 数组索引映射回 DataFrame 的日期索引
        # change_point_array_index 是新段开始的索引，所以对应的日期就是 T 日
        t_date = dates_index[change_point_array_index]
        if show_info:
            print(f"检测到变动起始日期: {t_date}")

        if change_point_array_index >= min_period_for_avg and \
                (len(data) - change_point_array_index) >= min_period_for_avg:

            # 使用检测到的分界点来划分前期和后期数据
            pre_change_data = data[:change_point_array_index].flatten()  # .flatten() 转换为1D
            post_change_data = data[change_point_array_index:].flatten()

            post_change_days = len(post_change_data)

            mean_before = np.mean(pre_change_data)
            mean_after = np.mean(post_change_data)

            absolute_change = mean_after - mean_before
            relative_change_percent = (absolute_change / mean_before) * 100 if mean_before != 0 else np.nan

            if show_info:
                print(
                    f"\n前期 ({dates_index[0]} 至 {dates_index[change_point_array_index - 1]}) "
                    f"{stk_indicator}平均数值: {mean_before:.2f}")
                print(
                    f"后期 ({dates_index[change_point_array_index]} 至 {dates_index[-1]}) "
                    f"{stk_indicator}平均数值: {mean_after:.2f}")
                print(f"{stk_indicator}绝对变动幅度: {absolute_change:.2f}")
                if not np.isnan(relative_change_percent):
                    print(f"{stk_indicator}相对变动幅度: {relative_change_percent:.2f}")
                else:
                    print("前期平均数值为0，无法计算相对变动幅度。")
    else:
        if show_info:
            print('未检测到变动起始日期')
    return t_date, absolute_change, relative_change_percent, post_change_days


if __name__ == '__main__':
    from function_ai.StkPick_Func_V7 import get_result_3
    from function_ai.StkQuota_Func_V7 import set_resultindexs
    end_date = '2025-08-20'
    stk_code = '002217.SZ'
    result = get_result_3(end_date=end_date, mode='First_Half')
    from function_ai.Func_Base import get_trade_date
    trade_df = get_trade_date()
    stk_temp = result.query('ts_code==@stk_code')

    common_column_names, gapvalue_column_names = set_resultindexs()
    # column_names = common_column_names + gapvalue_column_names
    Result_GapValue = pd.concat([stk_temp[['ts_code']], pd.DataFrame(columns=gapvalue_column_names)], sort=False, axis=1)

    result_gapvalue = pd.DataFrame()
    result_break = cal_gapindex(Result_GapValue.iloc[-1], stk_temp.iloc[-1].copy(), end_date=end_date, trade_df=trade_df, data_source='local')

    # 股票dailytrack
    # stk_daily_track(track=False)

    # 指数dailytrack
    # index_daily_track(indexperiod_startdate='2024-02-05')

    # 测试单一股票是否符合dailytrack筛选标准，用于debug
    # from function_ai.DailyPick import get_stocktrack_result
    # stocktrack = get_stocktrack_result(Now_Date=['2024-02-28', '2024-03-07', '2024-03-14', '2024-03-27'],
    #                                    Turn_Date=['2024-01-23', '2024-02-07'], mode='All')
    # result_track = stocktrack.query('ts_code=="600362.SH"')
    # dailytrack, signal = get_result_for_dailytrack(Now_SecDate=['2024-02-28', '2024-03-07', '2024-03-14', '2024-03-27'],
    #                                                pick_date=['2024-02-28', '2024-03-07', '2024-03-14', '2024-03-27'],
    #                                                result_track=result_track, track=False)
    # dailytrack_result = stk_daily_track(track=False, now_date='2024-05-07', storemode=True)
    # dailytrack_result_adj = dailytrack_result[
    #     ['name', 'PreSecPeak_Sec_AvgRatio', 'PreSecPeak_Sec_LastDays', 'PostSecStart_MaxDrop_LastDays',
    #      'PostSecPeak2Now_LastDays']]

    # 获取指定起止日期内的dailytrack结果
    # collect_result = collect_dailytrack_results(start_date='2024-03-27', end_date='2024-04-12',
    #                                             section_dates="'2024-01-23','2024-02-07','2024-03-27'",
    #                                             pick_date="'2024-02-28', '2024-03-07', '2024-03-14', '2024-03-27'",
    #                                             now_secdate="'2024-02-28', '2024-03-07', '2024-03-14', '2024-03-27'")
    # collect_result, pick_results = collect_dailytrack_results(start_date='2024-03-27', end_date='2024-04-19')
    # result = get_result_3(end_date='2024-05-16')
    # Result_Break = gap_multiprocess(Result_Break=result.iloc[10:15], cal_date='2024-05-16')