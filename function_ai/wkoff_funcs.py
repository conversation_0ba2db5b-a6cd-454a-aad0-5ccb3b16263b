"""函数库：wyckoff市场特征识别"""
import pdb

import pandas as pd
import numpy as np
from function_ai.Func_Base import *


def min_close(data):
    return data.iloc[:-1].min()


def identify_ShockDate(stk_data, mode=None):
    """识别输入价格序列内最新终极震仓日期"""
    shock_date, shock_date_last = '-', '-'
    if len(stk_data) > 3:
        stk_data = stk_data.copy()
        stk_data['ratio'] = (stk_data['close']/stk_data['close'].shift(1) - 1) * 100

        rolling_window = 4
        stk_data['min3'] = stk_data['close'].rolling(rolling_window).apply(min_close)
        if mode is None:
            stk_ratio_threshold = -3
            stk_drop_threshold = stk_data.query('ratio<0')['ratio'].quantile(0.3)
            # stk_turnover_threshold = stk_data.query('ratio<0')['turnover'].quantile(0.5)
            bottom_price = stk_data['high'].iloc[0] * 1.01
            mid_price = (stk_data['close'].iloc[0] + stk_data['close'].max()) / 2
            if len(stk_data.query('(ratio<=@stk_drop_threshold | '
                                  'ratio<@stk_ratio_threshold) & '
                                  'low<=@bottom_price & '
                                  'low<min3')) > 0:
                shock_date = stk_data.query('(ratio<=@stk_drop_threshold | '
                                            'ratio<@stk_ratio_threshold) & '
                                            'low<=@bottom_price &'
                                            'low<min3').index[0]
                shock_date_last = stk_data.query('(ratio<=@stk_drop_threshold | '
                                                 'ratio<@stk_ratio_threshold) & '
                                                 'low<=@bottom_price &'
                                                 'low<min3').index[-1]
            elif len(stk_data.query('(ratio<=@stk_drop_threshold | '
                                    'ratio<@stk_ratio_threshold) & '
                                    'low<=@mid_price & '
                                    'low<min3')) > 0:
                shock_date = stk_data.query('(ratio<=@stk_drop_threshold | '
                                            'ratio<@stk_ratio_threshold) & '
                                            'low<=@mid_price &'
                                            'low<min3').index[0]
                shock_date_last = stk_data.query('(ratio<=@stk_drop_threshold | '
                                                 'ratio<@stk_ratio_threshold) & '
                                                 'low<=@mid_price &'
                                                 'low<min3').index[-1]
        else:
            stk_ratio_threshold = -3
            stk_drop_threshold = stk_data.query('ratio<0')['ratio'].quantile(0.2)
            # stk_turnover_threshold = stk_data.query('ratio<0')['turnover'].quantile(0.5)
            if len(stk_data.query('(ratio<=@stk_drop_threshold | '
                                  'ratio<@stk_ratio_threshold) & '
                                  'low<min3')) > 0:
                shock_date_last = stk_data.query('(ratio<=@stk_drop_threshold | '
                                                 'ratio<@stk_ratio_threshold) & '
                                                 'low<min3').index[-1]
    return shock_date, shock_date_last


def identify_SpringDate(stk_data):
    """识别Spring日期"""
    lastnums = 3
    stk_data = stk_data.copy()
    spring_date = '-'
    if len(stk_data) > 3:
        stk_data['halfgap'] = abs(stk_data['close'] - stk_data['open']) / 2
        stk_data['downshadow'] = stk_data.apply(lambda fn: min(fn['close'], fn['open'])-fn['low'], axis=1)
        rolling_window = 4
        stk_data['min3'] = stk_data['low'].rolling(rolling_window).apply(min_close)
        stk_data_adj = stk_data.iloc[-min(lastnums, len(stk_data)):]
        if len(stk_data_adj.query('downshadow>=halfgap & low<min3')) > 0:
            spring_date = stk_data_adj.query('downshadow>=halfgap & low<min3').index[-1]
    return spring_date


def identify_SOSDate(stk_data):
    """识别SOS日期"""
    window = 3
    stk_data = stk_data.copy()
    stk_data['ratio'] = (stk_data['close']/stk_data['close'].shift(1) - 1) * 100
    stk_data['rolling_max'] = stk_data['close'].rolling(window=window).max().shift(1)
    stk_ratio_threshold = 3
    sos_date = '-'
    stk_rise_threshold = stk_data.query('ratio>0')['ratio'].quantile(0.7)
    stk_turnover_threshold = stk_data['turnover'].quantile(0.7)
    if len(stk_data.query('turnover>=@stk_turnover_threshold & '
                          'ratio>@stk_ratio_threshold & '
                          'ratio>=@stk_rise_threshold & '
                          'close>rolling_max')) > 0:
        sos_date = stk_data.query(
            'turnover>=@stk_turnover_threshold & '
            'ratio>@stk_ratio_threshold & '
            'ratio>=@stk_rise_threshold & '
            'close>rolling_max').index[0]
    shrink_date = '-'
    if sos_date != '-':
        max_date = stk_data.loc[sos_date:, 'close'].idxmax()
        bottom_price = stk_data.loc[:sos_date, 'close'].min()
        start_price = stk_data['close'].iloc[0]
        price_threshold = abs(start_price - bottom_price) / 3 + bottom_price
        if stk_data.loc[max_date:, 'close'].min() >= price_threshold \
                and stk_data.loc[sos_date:max_date, 'turnover'].mean() > stk_data.loc[max_date:, 'turnover'].mean():
            # stk_data.loc[max_date:, 'close'].min() > stk_data.loc[sos_date, 'low'] and
            stk_data['CO_ratio'] = abs(stk_data['close'] / stk_data['open'] - 1) * 100
            test_turnover_threshold = stk_data.loc[sos_date:]['turnover'].quantile(0.3)
            test_CO_threshold = stk_data.loc[max_date:]['CO_ratio'].quantile(0.2)
            CO_ratio_threshold = 2
            stk_data_adj = stk_data.loc[max_date:]
            if len(stk_data_adj) > 2 \
                    and len(stk_data_adj.query('CO_ratio<=@test_CO_threshold & '
                                               'CO_ratio<@CO_ratio_threshold & '
                                               'turnover<=@test_turnover_threshold')) > 0:
                shrink_date = stk_data_adj.query('CO_ratio<=@test_CO_threshold & '
                                                 'CO_ratio<@CO_ratio_threshold & '
                                                 'turnover<=@test_turnover_threshold').index[-1]

    return sos_date, shrink_date


def identify_StepBackDate(stk_data, section_rise=None, turn_date=None, shock_date=None):
    """识别回踩日期"""
    window = 3
    stk_data = stk_data.copy()
    stk_data['ratio'] = (stk_data['close']/stk_data['close'].shift(1) - 1) * 100
    stk_data['rolling_max'] = stk_data['close'].rolling(window=window).max().shift(1)
    stk_ratio_threshold = 3
    sos_date = '-'

    lastrisesec_start = turn_date
    stk_rise_threshold = stk_data.loc[lastrisesec_start:].query('ratio>0')['ratio'].quantile(0.7)
    if shock_date != '-' and stk_data.loc[lastrisesec_start:, 'close'].max() > \
            min(stk_data.loc[shock_date, 'close'], stk_data.loc[shock_date, 'open']):
        breakhigh_date = stk_data.loc[
                         lastrisesec_start:, 'high'][stk_data.loc[lastrisesec_start:, 'close'] >
                                                     min(stk_data.loc[shock_date, 'close'],
                                                         stk_data.loc[shock_date, 'open'])].index[0]
        # breaklist_date = section_rise.query('start_date<=@breakfirst_date & start_date>=@lastrisesec_start'
        #                                     )['start_date']
        # breakhigh_date = stk_data.loc[breaklist_date][stk_data.loc[breaklist_date, 'close'] <=
        #                                               min(stk_data.loc[shock_date, 'close'],
        #                                                   stk_data.loc[shock_date, 'open'])].index[-1]
        if len(stk_data.loc[breakhigh_date:].query('ratio>=@stk_rise_threshold | '
                                                   'ratio>=@stk_ratio_threshold')) > 0:
            sos_date = stk_data.loc[breakhigh_date:].query('ratio>=@stk_rise_threshold | '
                                                           'ratio>=@stk_ratio_threshold').index[0]
    stepback_date = '-'
    stepback_gapratio = -1
    stepback_days = 0
    if sos_date != '-' and len(section_rise.query('end_date>@sos_date')) > 0:
        # max_date = stk_data.loc[sos_date:, 'close'].idxmax()
        post_sosdate = section_rise.query('end_date>@sos_date')['end_date'].iloc[0]
        # if postsos_maxdate == sos_date or len(stk_data.loc[sos_date:]) < 3:
        #     post_sosdate = stk_data.loc[sos_date:, 'close'].iloc[1:].idxmax()
        # elif len(stk_data.loc[sos_date:]) >= 3:
        #     post_sosdate = postsos_maxdate
        # else:
        #     post_sosdate = None
        if post_sosdate is not None:
            stk_data_adj = stk_data.loc[post_sosdate:]
            sos_high = stk_data.loc[sos_date, 'high'] * 1.01
            if len(stk_data_adj) >= 2 \
                    and len(stk_data_adj.query('close<@sos_high')) > 0 \
                    and (stk_data_adj['low'].min() / stk_data.loc[lastrisesec_start, 'low'] - 1) > -0.15:
                stepback_date = stk_data_adj.query('close<@sos_high')['close'].idxmin()
                stepback_days = len(stk_data_adj.query('close<@sos_high'))

            if stepback_date != '-':
                stepback_gapratio = round(
                    (max(stk_data.loc[sos_date, 'open'], stk_data.loc[sos_date, 'close']) /
                     stk_data.loc[stepback_date, 'close'] - 1) * 100, 3)
            else:
                low_date = stk_data_adj['close'].idxmin()
                stepback_gapratio = round(
                    (max(stk_data.loc[sos_date, 'open'], stk_data.loc[sos_date, 'close']) /
                     stk_data.loc[low_date, 'close'] - 1) * 100, 3)
    return sos_date, stepback_date, stepback_gapratio, stepback_days