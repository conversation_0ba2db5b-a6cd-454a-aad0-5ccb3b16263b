"""股票相对指数收益对比的函数"""
import pdb

import pandas as pd


def cal_stk_beta(stk_code=None, start_date=None, end_date=None, stk_data=None, index_data=None):
    """计算股票的beta系数"""
    if stk_data is None and index_data is None:
        from function_ai.Func_Base import get_stock_data, get_index_data
        stk_data = get_stock_data(start_date=start_date, end_date=end_date, stk_code=stk_code)
        index_data = get_index_data(start_date=start_date, end_date=end_date, stk_code='000906.SH')
    stk_data['stk_ratio'] = stk_data['close'] / stk_data['close'].shift(1) - 1
    index_data['index_ratio'] = index_data['close'] / index_data['close'].shift(1) - 1
    stk_beta_df = pd.merge(stk_data[['trade_date', 'stk_ratio']], index_data[['trade_date', 'index_ratio']],
                           how='inner', on='trade_date')
    stk_beta = stk_beta_df['stk_ratio'].cov(stk_beta_df['index_ratio']) / stk_beta_df['index_ratio'].var()
    # print('stk_code:', stk_code, 'beta:', stk_beta, '\nstart_date:', start_date, 'end_date:', end_date, '\n')
    return stk_beta


def collect_stk_betas(result_pick, start_date, end_date):
    """收集股票的beta系数"""
    from function_ai.Func_Base import get_stock_data, get_index_data
    from tqdm import tqdm
    stock_data = get_stock_data(start_date=start_date, end_date=end_date)
    index_data = get_index_data(start_date=start_date, end_date=end_date, stk_code='000906.SH'
                                ).sort_values(by='trade_date')
    for index in tqdm(result_pick.index):
        stk_code = result_pick.loc[index, 'ts_code']
        stk_data = stock_data.query('ts_code == @stk_code'
                                    ).copy().sort_values(by='trade_date')
        stk_beta = cal_stk_beta(stk_data=stk_data,
                                index_data=index_data)
        result_pick.loc[index, 'Beta'] = stk_beta
    return result_pick


def cal_relative_return(stk_data, index_data, start_date=None, end_date=None, sec_date=None):
    """计算相对强弱指数"""
    # 如start_date和end_date不为空，则取对应时间段的数据
    if start_date is not None and end_date is not None:
        stk_data = stk_data.query('trade_date >= @start_date & trade_date <= @end_date').copy()
        index_data = index_data.query('trade_date >= @start_date & trade_date <= @end_date').copy()
    if len(stk_data) > 1:
        stk_data['stk_return'] = (stk_data['close'] / stk_data['close'].shift(1) - 1) * 100
        stk_data['abs_stk_return'] = stk_data['stk_return'].abs()
        abs_stk_return_lowquntl = stk_data['abs_stk_return'].quantile(0.4)
        index_data['idx_return'] = (index_data['close'] / index_data['close'].shift(1) - 1) * 100
        if sec_date is None:
            sec_date = stk_data['trade_date'].iloc[-1]
        relative_sumratio = (stk_data.query('trade_date==@sec_date')['close'].iloc[-1] /
                             stk_data['close'].iloc[0] - 1) * 100 - \
                            (index_data.query('trade_date==@sec_date')['close'].iloc[-1] /
                             index_data['close'].iloc[0] - 1) * 100
        idx_return_lowquntl = index_data['idx_return'].quantile(0.2)
        merge_data = pd.merge(stk_data[['trade_date', 'stk_return', 'abs_stk_return']],
                              index_data[['trade_date', 'idx_return']],
                              how='inner', on='trade_date')
        merge_data['relative_return'] = merge_data['stk_return'] - merge_data['idx_return']
        relative_rise_diff = (len(merge_data.query('stk_return>0')) -
                              len(merge_data.query('idx_return>0'))) / len(merge_data)
        # 统计relative_return为正值天数的占比
        relative_count = len(merge_data.query('idx_return <= @idx_return_lowquntl & relative_return>0')) / \
                          len(merge_data.query('idx_return <= @idx_return_lowquntl'))
        relative_abs_count = len(merge_data.query('idx_return <= @idx_return_lowquntl & '
                                                  'abs_stk_return <= @abs_stk_return_lowquntl')) / \
                                len(merge_data.query('idx_return <= @idx_return_lowquntl'))
    else:
        relative_count = 0
        relative_abs_count = 0
        relative_sumratio = 0
        relative_rise_diff = 0
    return relative_count, relative_abs_count, relative_sumratio, relative_rise_diff


def collect_relative_state(result_pick=None, start_date=None, end_date=None):
    """收集相对强弱指数"""
    from function_ai.Func_Base import get_stock_data, get_index_data
    from tqdm import tqdm
    if 'Pick_Now_SecDate' not in result_pick.columns:
        Pick_date = 'Now_SecDate'
    else:
        Pick_date = 'Pick_Now_SecDate'
    # Pick_date = 'Period_TurnDate'
    if start_date is None:
        data_startdate = result_pick[Pick_date].min()
    else:
        data_startdate = start_date
    stock_data = get_stock_data(start_date=data_startdate, end_date=end_date)
    index_data = get_index_data(start_date=data_startdate, end_date=end_date, stk_code='000906.SH'
                                ).sort_values(by='trade_date')
    for index in tqdm(result_pick.index):
        stk_code = result_pick.loc[index, 'ts_code']
        stk_start = result_pick.loc[index, Pick_date] if start_date is None else start_date
        sec_date = result_pick.loc[index, Pick_date]
        stk_data = stock_data.query('ts_code == @stk_code'
                                    ).copy().sort_values(by='trade_date')
        relative_count, relative_abs_count, relative_sumratio, relative_rise_diff = \
            cal_relative_return(stk_data=stk_data,
                                index_data=index_data,
                                start_date=stk_start,
                                end_date=end_date,
                                sec_date=sec_date)
        result_pick.loc[index, 'relative2Index_count'] = round(relative_count, 3)
        result_pick.loc[index, 'relative2Index_abs_count'] = round(relative_abs_count, 3)
        result_pick.loc[index, 'relative2Index_sumratio'] = round(relative_sumratio, 3)
        result_pick.loc[index, 'relative2Index_rise_diff'] = round(relative_rise_diff, 3)
    return result_pick


def cal_relative_comp2mean(stk_data, index_data):
    """计算相对强弱指数"""
    stk_data['rolling_close_mean'] = stk_data['close'].rolling(3).mean()
    stk_data['rolling_open_mean'] = stk_data['open'].rolling(3).mean()
    stk_data['rolling_mean'] = stk_data[['rolling_close_mean', 'rolling_open_mean']].mean(axis=1)
    stk_data['stk_chg'] = (stk_data['close'] / stk_data['rolling_mean'].shift(1) - 1) * 100
    index_data['rolling_close_mean'] = index_data['close'].rolling(3).mean()
    index_data['rolling_open_mean'] = index_data['open'].rolling(3).mean()
    index_data['rolling_mean'] = index_data[['rolling_close_mean', 'rolling_open_mean']].min(axis=1)
    # index_data['close_chg'] = (index_data['close'] / index_data['close'].shift(1) - 1) * 100
    index_data['idx_chg'] = (index_data['close'] / index_data['rolling_mean'].shift(1) - 1) * 100
    stk_data = pd.merge(stk_data[['trade_date', 'stk_chg']], index_data[['trade_date', 'idx_chg']],
                        how='inner', on='trade_date')
    # 统计idx_chg为负值时，stk_chg的均值
    rise_relative_mean = stk_data.query('idx_chg > 0')['stk_chg'].mean()
    fall_relative_mean = stk_data.query('idx_chg < 0')['stk_chg'].mean()
    rise_relative_count = stk_data.query('idx_chg>0 & stk_chg>0')['stk_chg'].count()/len(stk_data.query('idx_chg > 0'))
    fall_relative_count = stk_data.query('idx_chg<0 & stk_chg>0')['stk_chg'].count()/len(stk_data.query('idx_chg < 0'))
    return rise_relative_mean, fall_relative_mean, rise_relative_count, fall_relative_count


def collect_relative_comp2mean(result_pick, start_date, end_date):
    """收集相对强弱指数"""
    from function_ai.Func_Base import get_stock_data, get_index_data
    from tqdm import tqdm
    stock_data = get_stock_data(start_date=start_date, end_date=end_date)
    index_data = get_index_data(start_date=start_date, end_date=end_date, stk_code='000906.SH'
                                ).sort_values(by='trade_date')
    for index in tqdm(result_pick.index):
        stk_code = result_pick.loc[index, 'ts_code']
        stk_data = stock_data.query('ts_code == @stk_code'
                                    ).copy().sort_values(by='trade_date')
        rise_relative_mean, fall_relative_mean, rise_relative_count, fall_relative_count = \
            cal_relative_comp2mean(stk_data=stk_data, index_data=index_data)
        result_pick.loc[index, 'rise_relative_comp2mean'] = round(rise_relative_mean, 3)
        result_pick.loc[index, 'fall_relative_comp2mean'] = round(fall_relative_mean, 3)
        result_pick.loc[index, 'rise_relative_count'] = round(rise_relative_count, 3)
        result_pick.loc[index, 'fall_relative_count'] = round(fall_relative_count, 3)
    # if len(result_pick) > 0:
    #     result_pick = result_pick.sort_values(by='fall_relative_comp2mean', ascending=False)
    return result_pick


# 计算相关系数
def cal_corr(stk_data, index_data):
    """计算相关系数"""
    stk_data['stk_ratio'] = stk_data['close'] / stk_data['close'].shift(1) - 1
    index_data['index_ratio'] = index_data['close'] / index_data['close'].shift(1) - 1
    stk_data = pd.merge(stk_data[['trade_date', 'stk_ratio']], index_data[['trade_date', 'index_ratio']],
                        how='inner', on='trade_date')
    corr = stk_data['stk_ratio'].corr(stk_data['index_ratio'])
    return corr

