"""基础DownConsecutive或UpConsecutive指标筛选品种"""

import pdb
from audioop import reverse

import numpy as np
import pandas as pd
from tqdm import tqdm

from function_ai.StkPick_Func_V7 import get_result_3, conf_nowdiff
from function_ai.Func_Base import get_trade_date, get_stock_data
from function_ai.DailyGap_Func import get_gap_mindata, get_min_indicators


def pickstk_method_consecu(pick_date=None, turn_industry=None, lead_industry=None,
                           section_startdate=None, now_secdate=None, pick_mode='track'):
    """筛选Section_StartDate转折品种及持续强势行业上行品种"""
    if now_secdate is None:
        now_secdate = section_startdate
    result_origin = get_result_3(end_date=pick_date)
    trade_dates = get_trade_date()
    drop_avg = -1.5
    if section_startdate is not None:
        result_origin['Now_SecDiff'] = result_origin.apply(
            lambda fn: conf_nowdiff(fn['Now_SecDate'], now_secdate, trade_dates),
            axis=1)
        result_origin['SecStart_Diff'] = result_origin.apply(
            lambda fn: conf_nowdiff(fn['Section_StartDate'], section_startdate, trade_dates),
            axis=1)
    if section_startdate is not None:
        result_turn_pick = result_origin.query('Section_StartDate==Now_SecDate & '
                                               'SecStart_Diff<=2 & '
                                               'PostNowSec_PGV_MeanRollAvg>PreNowSec_PGV_MeanRollAvg & '
                                               'PostSecPeak_PGV_MinRollAvg_Date>PreNow_SecDate'
                                               ).sort_values(by=['industry', 'PostNowSec_PGV_MeanRollAvg'],
                                                             ascending=[True, False]).copy()
    else:
        result_turn_pick = result_origin.query('Section_StartDate==Now_SecDate & '
                                               'PostNowSec_LastDays<=5 & '
                                               'PostNowSec_PGV_MeanRollAvg>PreNowSec_PGV_MeanRollAvg & '
                                               'PostSecPeak_PGV_MinRollAvg_Date>PreNow_SecDate'
                                               ).sort_values(by=['industry', 'PostNowSec_PGV_MeanRollAvg'],
                                                             ascending=[True, False]).copy()
    if turn_industry is not None:
        if isinstance(turn_industry, str):
            turn_industry = [turn_industry]
        result_turn_pick = result_turn_pick.query('industry in @turn_industry').copy()
    if pick_mode.lower() == 'lag':
        result_turn_pick = result_turn_pick.query('Break_DownSecutiveStart_First2Now_LastDays<=5'
                                                  ).sort_values(by=['DownConsecutive_Last2Break_LastDays',
                                                                    'PostNowSec_PGV_MeanRollAvg'],
                                                                ascending=[True, False]).copy()
    else:
        result_turn_pick = result_turn_pick.query('Break_DownSecutiveStart_First2Now_LastDays.isnull()').copy()
    result_turn_pick['Track_Categ'] = 'Bottom'
    result_turn_strong = result_turn_pick.query('((Peak2Sec_LastDays>=30 & '
                                                 'Peak2Sec_AvgRatio>@drop_avg & '
                                                 'Post_PreSecPeak_RecovRatio>=0.5 & '
                                                 'Post_PreSecPeak_Und4Count>1 & '
                                                 'Peak2Sec_PGV_MinRollAvg2MeanRatio<=0.5 & '
                                                 'Peak2Sec_PGV_MinRollAvg_Date>PreSec_StartDate) | ('
                                                 'SecConcave_LastDays>40 & '
                                                 'SecConcave_RatioBand<40 & '
                                                 'SecConcave_TO_Sum>50 & '
                                                 'SecConcave_PGV_MinRollAvg2MeanRatio<0.6'
                                                 ).sort_values(
                                                    by=['Cal_Date', 'industry', 'Post_PreSecPeak_RecovRatio',
                                                        'Peak2Sec_LastDays'],
                                                    ascending=[False, True, False, False]).copy()

    if now_secdate is not None:
        result_lead_pick = result_origin.query('Section_StartDate<Now_SecDate & '
                                               'Now_SecDiff<=2 & '
                                               'DownConsecutive2Now_LastDays<=2 & '
                                               'PreNow2PostSec_PGV_MeanRollAvg_Ratio>1 & '
                                               'Break_UpSecutiveStart_First2Now_LastDays.isnull()'
                                               ).sort_values(by=['industry', 'DownConsecutive_PGVRollAvg_DiffRatio'],
                                                             ascending=[True, False]).copy()
    else:
        result_lead_pick = result_origin.query('Section_StartDate<Now_SecDate & '
                                               'PostNowSec_LastDays<=3 & '
                                               'DownConsecutive2Now_LastDays<=2 & '
                                               'PreNow2PostSec_PGV_MeanRollAvg_Ratio>1 & '
                                               'Break_UpSecutiveStart_First2Now_LastDays.isnull()'
                                               ).sort_values(by=['industry', 'DownConsecutive_PGVRollAvg_DiffRatio'],
                                                             ascending=[True, False]).copy()
    if lead_industry is not None:
        if isinstance(lead_industry, str):
            lead_industry = [lead_industry]
        result_lead_pick = result_lead_pick.query('industry in @lead_industry').copy()
    if pick_mode.lower() == 'lag':
        result_lead_pick = result_lead_pick.query('Break_DownSecutiveStart_First2Now_LastDays<5'
                                                  ).sort_values(by=['DownConsecutive_Last2Break_LastDays',
                                                                    'PostNowSec_PGV_MeanRollAvg'],
                                                                ascending=[True, False]).copy()
    else:
        result_lead_pick = result_lead_pick.query('Break_DownSecutiveStart_First2Now_LastDays.isnull()').copy()
    result_lead_pick['Track_Categ'] = 'Lead'

    result_lead_strong = result_lead_pick.query('((Peak2Sec_LastDays>=30 & '
                                                  'Peak2Sec_AvgRatio>@drop_avg & '
                                                  'Post_PreSecPeak_RecovRatio>0.5 & '
                                                  'Post_PreSecPeak_Und4Count>1 & '
                                                  'Peak2Sec_PGV_MinRollAvg2MeanRatio<=0.5 & '
                                                  'Peak2Sec_PGV_MinRollAvg_Date>PreSec_StartDate) | ('
                                                  'SecConcave_LastDays>40 & '
                                                  'SecConcave_RatioBand<40 & '
                                                  'SecConcave_TO_Sum>50 & '
                                                  'SecConcave_PGV_MinRollAvg2MeanRatio<0.6))'
                                                  ).sort_values(
                                                        by=['industry', 'Post_PreSecPeak_RecovRatio',
                                                            'Peak2Sec_LastDays'],
                                                        ascending=[True, False, False]).copy()
    result_strong = pd.concat([result_turn_strong, result_lead_strong], ignore_index=True, axis=0)

    return result_turn_pick, result_lead_pick, result_strong


def cal_pgvrollavg_diffratio(cal_date=None, turn_date=None):
    """计算指定转折日期turn_date后pgv_meanrollavg与转折日期前的比值，并排序"""
    result_now = get_result_3(end_date=cal_date)
    if result_now is None or result_now.empty:
        print('指定cal_date无指标数据')
        return
    trade_dates = get_trade_date()
    result_now['Now_SecDiff'] = result_now.apply(
        lambda fn: conf_nowdiff(fn['Now_SecDate'], turn_date, trade_dates),
        axis=1)
    result_now['SecStart_Diff'] = result_now.apply(
        lambda fn: conf_nowdiff(fn['Section_StartDate'], turn_date, trade_dates),
        axis=1)
    result_now_adj = result_now.query('Now_SecDiff<=2 | SecStart_Diff<=2').copy()
    result_now_adj['pgv_meanrollavg_diffratio'] = 0

    from multiprocessing import Pool
    p = Pool(7)
    pool_data_list = []
    Results = pd.DataFrame()
    for index in tqdm(result_now_adj.index):
        pool_data_list.append(p.apply_async(func=cal_pgv_diffratio,
                                            args=(result_now_adj.loc[index, :].copy(), turn_date)))
    p.close()
    p.join()
    for pool_data in pool_data_list:
        if len(Results) == 0:
            Results = pd.DataFrame(pool_data.get()).T
        else:
            Results = pd.concat([Results, pd.DataFrame(pool_data.get()).T], axis=0)
    Results = Results.sort_values(by='pgv_meanrollavg_diffratio', ascending=False)
    return Results

def cal_pgv_diffratio(result, turn_date):
    """用于multiprocess，计算pgv_meanrollavg比值"""
    gap_startdate = result['Section_PeakDate']
    gap_enddate = result['Cal_Date']
    ts_code = result['ts_code']
    gap_data = get_min_indicators(stk_code=ts_code, start_date=gap_startdate, end_date=gap_enddate)
    gap_data = gap_data.set_index('trade_date')
    if result['SecStart_Diff'] <= 2:
        preturn_date = result['Section_PeakDate']
    else:
        preturn_date = result['PreNow_PeakDate']
    result['pgv_meanrollavg_diffratio'] = round(gap_data.loc[turn_date:, 'pgv_rollavg'].mean() /
                                                gap_data.loc[preturn_date:turn_date, 'pgv_rollavg'].mean(), 3)
    return result
