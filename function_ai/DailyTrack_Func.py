"""日内股票跟踪程序汇总"""
import datetime
import pdb
import time

import numpy as np
import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from tqdm import tqdm

from config import config_Ali as config
from function_ai.Pick_RepProg import get_stocktrack_result

from function_ai.Func_Base import get_trade_date, input_to_str, get_index_data, get_stock_data
from function_ai.DailyGap_Func import get_gap_mindata, cal_avg_nozero, get_min_indicators
from function_ai.StkPick_Func_V7 import get_result_for_dailytrack, get_result_3, \
    pickstk_method_indus_head, get_predictdata_from_sql
from function_ai.StkPick_ModelFunc import prepare_dailytrack_list, get_turntrack_result
from machine_learn.Func_MacinLn import cal_osci_ratio


def cal_realtime_gapidx(stk_list=None, mode='Stk', data_module='tushare', source='sina'):
    """跟踪实时Gap指标
       sina支持股票，dc支持单只股票"""
    if mode.lower() == 'stk':
        if data_module == "tushare":
            import tushare as ts
            ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')
            realtime_quote = pd.DataFrame()
            if source == 'sina': # 来源新浪
                for i in range(0, len(stk_list), 49):
                    stk_list_temp = stk_list[i: i + 49]
                    if isinstance(stk_list_temp, list):
                        stk_list_temp = ','.join(stk_list_temp)
                    elif isinstance(stk_list_temp, np.ndarray):
                        stk_list_temp = ','.join(stk_list_temp.tolist())
                    realtime_quote_temp = ts.realtime_quote(ts_code=stk_list_temp, src=source)
                    if i == 0:
                        realtime_quote = realtime_quote_temp
                    else:
                        realtime_quote = pd.concat([realtime_quote, realtime_quote_temp], axis=0)
                    time.sleep(1)
            elif source == 'dc': # 来源东方财富
                for i in range(0, len(stk_list)):
                    realtime_quote_temp = ts.realtime_quote(ts_code=stk_list[i], src=source)
                    if i == 0:
                        realtime_quote = realtime_quote_temp
                    else:
                        realtime_quote = pd.concat([realtime_quote, realtime_quote_temp], axis=0)
                    time.sleep(0.5)
        elif data_module == 'akshare':
            import akshare as aks
            from function_ai.Func_Base import get_stock_info
            stock_info = get_stock_info()
            realtime_quote = aks.stock_zh_a_spot_em()
            realtime_quote = realtime_quote.rename(columns={'代码': 'ts_code',
                                                            '成交量': 'AMOUNT',
                                                            '成交额': 'VOLUME',
                                                            '最新价': 'PRICE',
                                                            '昨收': 'PRE_CLOSE'})
            stock_info['ts_code_prefix'] = stock_info['ts_code'].str[:6]
            realtime_quote = pd.merge(realtime_quote, stock_info[['ts_code', 'ts_code_prefix']],
                                      left_on='ts_code', right_on='ts_code_prefix', how='left')
            realtime_quote['ts_code'] = realtime_quote['ts_code_y']
            realtime_quote.drop(columns=['ts_code_x', 'ts_code_prefix', 'ts_code_y'], inplace=True)
        else:
            print('module参数错误，退出')
            return
        realtime_quote['AMOUNT'] = realtime_quote['AMOUNT'].astype(float)
        realtime_quote['VOLUME'] = realtime_quote['VOLUME'].astype(float)
        realtime_quote['PRICE'] = realtime_quote['PRICE'].astype(float)
        realtime_quote['PRE_CLOSE'] = realtime_quote['PRE_CLOSE'].astype(float)
        realtime_quote['weight_avg'] = realtime_quote.apply(
            lambda fn: round(fn['AMOUNT'] / fn['VOLUME']/100, 4) if fn['VOLUME'] > 0 else fn['PRICE'], axis=1)
        realtime_quote['weight_avg'] = realtime_quote.apply(cal_avg_nozero, args=('realtime',), axis=1)
        realtime_quote['daily_ratio'] = round((realtime_quote['PRICE'] - realtime_quote['PRE_CLOSE']) /
                                              realtime_quote['PRE_CLOSE'] * 100, 4)
        realtime_quote['Now2Avg_Ratio'] = round((realtime_quote['PRICE'] - realtime_quote['weight_avg']) /
                                                realtime_quote['weight_avg'] * 100, 4)
        realtime_quote['realtime_gap'] = realtime_quote['PRICE'] - realtime_quote['weight_avg']
        realtime_quote['realtime_gap_value'] = abs(realtime_quote['weight_avg'] - realtime_quote['PRICE'])
        realtime_quote['realtime_gap_ratio'] = round(realtime_quote['realtime_gap_value'] * 100 /
                                                     realtime_quote['weight_avg'], 4)
        realtime_quote = realtime_quote.rename(columns={'TS_CODE': 'ts_code',
                                                        'NAME': 'name',
                                                        'PRICE': 'price',
                                                        'TIME': 'time'})
        realtime_quote = realtime_quote[['time', 'ts_code', 'name', 'price', 'daily_ratio', 'weight_avg',
                                         'realtime_gap', 'realtime_gap_value',
                                         'realtime_gap_ratio', 'Now2Avg_Ratio']]
    elif mode.lower() == 'index':
        if data_module == 'tushare':
            import tushare as ts
            ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')
            if isinstance(stk_list, list):
                stk_list = stk_list[0]
            realtime_quote = ts.realtime_quote(ts_code=stk_list, src=source)
            if len(realtime_quote) == 0:
                print('指数数据为空，退出')
                return
            realtime_quote['AMOUNT'] = realtime_quote['AMOUNT'].astype(float)
            realtime_quote['VOLUME'] = realtime_quote['VOLUME'].astype(float)
            realtime_quote['PRICE'] = realtime_quote['PRICE'].astype(float)
            realtime_quote['PRE_CLOSE'] = realtime_quote['PRE_CLOSE'].astype(float)
            realtime_quote = realtime_quote.rename(columns={'TS_CODE': 'ts_code',
                                                            'NAME': 'name',
                                                            'PRICE': 'price',
                                                            'TIME': 'time',
                                                            'AMOUNT': 'amount'})
            realtime_quote = realtime_quote[['time', 'ts_code', 'name', 'price', 'amount']]
        else:
            print('Index数据参数错误,退出')
            return
    return realtime_quote


def track_realtime_gapidx(result_pick=None, style=None, track_signal=0):
    """建立实时跟踪程序，每15分钟刷新实时行情计算实时Gap指标，当超过阈值时，发出提示"""
    import datetime
    conf = config.configModel()
    print('交易时间内，价差Gap跟踪Start！')
    last_tradedate = get_trade_date(loc=-1)
    realtime_quotes_day = pd.DataFrame()
    track_record = pd.DataFrame()
    track_count = 0
    while True:
        now_time = datetime.datetime.now()
        # 时间9:30
        datetime_start1 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(9, 20, 0))
        # 时间11:30
        datetime_end1 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(11, 30, 0))
        # 时间13:00
        datetime_start2 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(13, 00, 0))
        # 时间14:30
        datetime_check = datetime.datetime.combine(
            datetime.date.today(), datetime.time(14, 30, 0))
        # 时间15:00
        datetime_end2 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(15, 0, 0))
        if datetime_start1 <= now_time < datetime_end1 or datetime_start2 <= now_time < datetime_end2:
            realtime_quote_origin = cal_realtime_gapidx(stk_list=result_pick['ts_code'].tolist())
            if len(realtime_quotes_day) == 0:
                realtime_quotes_day = pd.concat([realtime_quotes_day, realtime_quote_origin], axis=0)
            realtime_peakgap_max = realtime_quotes_day.groupby(
                ['ts_code', 'name'], as_index=False)['realtime_gap'].max()
            # realtime_peakgap_max['realtime_gap'] = realtime_peakgap_max['realtime_gap'].apply(
            #     lambda fn: abs(fn) if fn > 0 else fn)
            realtime_peakgap_max = realtime_peakgap_max.rename(
                columns={'realtime_gap': 'realtime_peakgap_max'})
            realtime_peakgap_max['postmaxpeak_maxgapdiff'] = realtime_peakgap_max.apply(
                lambda fn: get_postmax_mingapvalue(fn['ts_code'], fn['realtime_peakgap_max'], realtime_quotes_day),
                axis=1)
            realtime_peakgap_max['maxpeakgap_price'] = realtime_peakgap_max.apply(
                lambda fn: get_maxgap_price(fn['ts_code'], fn['realtime_peakgap_max'], realtime_quotes_day),
                axis=1)
            realtime_valleygap_max = realtime_quotes_day.groupby(
                ['ts_code', 'name'], as_index=False)['realtime_gap'].min()
            realtime_valleygap_max = realtime_valleygap_max.rename(
                columns={'realtime_gap': 'realtime_valleygap_max'})
            realtime_valleygap_max['maxvalleygap_price'] = realtime_valleygap_max.apply(
                lambda fn: get_maxgap_price(fn['ts_code'], fn['realtime_valleygap_max'], realtime_quotes_day),
                axis=1)
            realtime_valleygap_max['realtime_valleygap_max'] = abs(realtime_valleygap_max['realtime_valleygap_max'])
            (Up_Alert_List1, Up_Alert_List2, Up_Alert_List3,
             Down_Alert_List0, Down_Alert_List1, Down_Alert_List2, Down_Alert_List3, Down_Alert_List4,
             Narrow_Alert_List) = \
                pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), \
                pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
            if style == '3':
                realtime_quote = pd.merge(realtime_quote_origin.copy(),
                                          result_pick[['ts_code', 'name', 'industry',
                                                       'PostSecStart_PeakGap_HighQuntl',
                                                       'PostSecStart_PeakGapValue_HighQuntl',
                                                       'PostSecStart_MaxPeakGap',
                                                       'PostSecStart_MaxPeakGapValue',
                                                       'PostSecStart_PeakGap_LowQuntl',
                                                       'PostSecStart_ValleyGap_HighQuntl',
                                                       'PostSecStart_ValleyGapValue_HighQuntl',
                                                       'PostSecStart_ValleyGap_LowQuntl',
                                                       'PostSecStart_MaxValleyGap',
                                                       'PostSecStart_MaxValleyGapValue',
                                                       'PostSecStart_MedianValleyGap',
                                                       'PostSecStart_MedianValleyGapValue',
                                                       'PostSecStart_MedianPeakGap',
                                                       'PostSecStart_MedianPeakGapValue',
                                                       'PostNowSec_MaxPeakGap',
                                                       'PostNowSec_MaxValleyGap',
                                                       'Peak2Sec_MaxPeakGap',
                                                       'Peak2Sec_MaxValleyGap',
                                                       'PreNowSec_MaxValleyGap',
                                                       'PreNowSec_MedianPeakGap',
                                                       'PreNowSec_MedianValleyGap',
                                                       'PreNowSec_LastDays',
                                                       'PostNowSec_LastDays',
                                                       'PreNowSec_ExtreRatio',
                                                       'Recent3Day_MaxPeakGapValue',
                                                       'Recent3Day_MaxValleyGapValue',
                                                       'Now_ValleyGapValue',
                                                       'Now_PeakGapValue',
                                                       'Now_DayRatio',
                                                       'Now_SecDate',
                                                       'Now_Recent3Min']],
                                          on=['ts_code', 'name'], how='left')
            else:
                realtime_quote = pd.merge(realtime_quote_origin.copy(),
                                          result_pick[['ts_code', 'name', 'industry',
                                                       'PostSecStart_PeakGap_HighQuntl',
                                                       'PostSecStart_PeakGapValue_HighQuntl',
                                                       'PostSecStart_MaxPeakGap',
                                                       'PostSecStart_MaxPeakGapValue',
                                                       'PostSecStart_PeakGap_LowQuntl',
                                                       'PostSecStart_ValleyGap_HighQuntl',
                                                       'PostSecStart_ValleyGapValue_HighQuntl',
                                                       'PostSecStart_ValleyGap_LowQuntl',
                                                       'PostSecStart_MaxValleyGap',
                                                       'PostSecStart_MaxValleyGapValue',
                                                       'PostSecStart_MedianValleyGap',
                                                       'PostSecStart_MedianValleyGapValue',
                                                       'PostSecStart_MedianPeakGap',
                                                       'PostSecStart_MedianPeakGapValue',
                                                       'PostNowSec_MaxPeakGap',
                                                       'PostNowSec_MaxValleyGap',
                                                       'Peak2Sec_MaxPeakGap',
                                                       'Peak2Sec_MaxValleyGap',
                                                       'PreNowSec_MaxValleyGap',
                                                       'PreNowSec_MedianPeakGap',
                                                       'PreNowSec_MedianValleyGap',
                                                       'PreNowSec_LastDays',
                                                       'PostNowSec_LastDays',
                                                       'PreNowSec_ExtreRatio',
                                                       'Recent3Day_MaxPeakGapValue',
                                                       'Recent3Day_MaxValleyGapValue',
                                                       'Now_ValleyGapValue',
                                                       'Now_PeakGapValue',
                                                       'Now_SecDate',
                                                       'Now_SecDiff',
                                                       'Now_DayRatio',
                                                       'Now_Recent3Min']],
                                          on=['ts_code', 'name'], how='left')
            pick_columns = result_pick.columns
            if 'Pick_Mode' in pick_columns:
                realtime_quote = pd.merge(realtime_quote, result_pick[['ts_code', 'Pick_Mode']],
                                          on=['ts_code'], how='left')
            else:
                realtime_quote['Pick_Mode'] = '-'
            realtime_quote = pd.merge(
                realtime_quote, realtime_peakgap_max[['ts_code', 'realtime_peakgap_max',
                                                      'postmaxpeak_maxgapdiff', 'maxpeakgap_price']],
                on=['ts_code'], how='left')
            realtime_quote = pd.merge(
                realtime_quote, realtime_valleygap_max[['ts_code', 'realtime_valleygap_max', 'maxvalleygap_price']],
                on=['ts_code'], how='left')
            realtime_quote['realtime_valleygap_diff'] = realtime_quote['realtime_peakgap_max'] - \
                                                        realtime_quote['realtime_gap']
            if style == '1':
                Up_Alert_List2 = realtime_quote.query(
                    'realtime_gap>0 & '
                    'realtime_gap_ratio >= PostSecStart_PeakGap_HighQuntl & '
                    'realtime_gap_ratio < PostSecStart_MaxPeakGap').copy()
                Down_Alert_List2 = realtime_quote.query(
                    'realtime_gap<0 & daily_ratio<0 & '
                    'realtime_gap_ratio >= PostSecStart_ValleyGap_HighQuntl & '
                    'realtime_gap_ratio < PostSecStart_MaxValleyGap & '
                    'Recent3Day_MaxPeakGapValue < PostSecStart_MaxPeakGapValue & '
                    'PreNowSec_LastDays>=3 & Now_SecDiff<=2').copy()
                Down_Alert_List3 = realtime_quote.query(
                    'realtime_gap<0 & daily_ratio<0 & '
                    'realtime_gap_ratio >= PostSecStart_MaxValleyGap & '
                    'Recent3Day_MaxPeakGapValue < PostSecStart_MaxPeakGapValue').copy()
            elif style == '2' or style == '3':
                Down_Alert_List0 = realtime_quote.query(
                    'realtime_gap<0 & '
                    'daily_ratio<PreNowSec_ExtreRatio & '
                    'PreNowSec_LastDays>=3 & PostNowSec_LastDays<3 & '
                    'realtime_gap_value >= realtime_valleygap_max*0.98').copy()
                Down_Alert_List1 = realtime_quote.query(
                    'realtime_gap<0 & daily_ratio<0 & '
                    'realtime_gap_value>=realtime_valleygap_max*0.98 & '
                    '(realtime_gap_value>=Recent3Day_MaxValleyGapValue*0.98 | Pick_Mode=="Sec上行") & '
                    'realtime_gap_value>=PostSecStart_MedianValleyGapValue*0.98'
                    ).copy().sort_values(by=['Pick_Mode', 'realtime_gap_ratio'],
                                         ascending=[True, False])
                Down_Alert_List2 = realtime_quote.query(
                    'realtime_gap<0 & daily_ratio<0 & '
                    'realtime_gap_value >= realtime_valleygap_max*0.98 & '
                    '(realtime_gap_ratio >= PostSecStart_ValleyGap_HighQuntl | '
                    'realtime_gap_ratio >= PostSecStart_MaxValleyGap)'
                    ).copy().sort_values(by=['Pick_Mode', 'realtime_gap_ratio'],
                                         ascending=[True, False])
                Down_Alert_List3 = realtime_quote.query(
                    'realtime_gap<0 & daily_ratio<0 & '
                    'realtime_valleygap_max >= PostSecStart_MedianValleyGapValue & '
                    'realtime_valleygap_max >= Recent3Day_MaxValleyGapValue & '
                    'price < maxvalleygap_price & '
                    'realtime_gap_value <= realtime_valleygap_max'
                    ).copy().sort_values(by=['Pick_Mode', 'realtime_gap_ratio'],
                                         ascending=[True, False])
                Down_Alert_List4 = realtime_quote.query(
                    'realtime_gap<0 & '
                    '(realtime_gap_value >= realtime_valleygap_max*0.98 & '
                    'realtime_valleygap_diff >= PostSecStart_MedianValleyGapValue*0.98)'
                    ).copy().sort_values(by=['Pick_Mode', 'realtime_gap_ratio'],
                                         ascending=[True, False])
                if now_time > datetime_check:
                    Narrow_Alert_List = realtime_quote.query(
                        'Now_ValleyGapValue > PostSecStart_MedianValleyGapValue & '
                        'realtime_valleygap_max <= PostSecStart_MedianValleyGapValue & '
                        'realtime_valleygap_max < Now_ValleyGapValue & '
                        'daily_ratio > Now_DayRatio').copy()
                Up_Alert_List1 = realtime_quote.query(
                    'realtime_gap>0 & daily_ratio>0 & '
                    'realtime_gap_value >= realtime_peakgap_max*0.98 & '
                    'realtime_gap_value >= Recent3Day_MaxPeakGapValue*0.98 & '
                    'realtime_gap_value >= PostSecStart_MedianPeakGapValue*0.98'
                    ).copy().sort_values(by=['Pick_Mode', 'realtime_gap_ratio'],
                                         ascending=[True, False])
                Up_Alert_List2 = realtime_quote.query(
                    'realtime_gap>0 & '
                    '(realtime_gap_value >= PostSecStart_PeakGapValue_HighQuntl | '
                    'realtime_gap_value >= PostSecStart_MaxPeakGapValue)'
                    ).copy().sort_values(by=['Pick_Mode', 'realtime_gap_ratio'],
                                         ascending=[True, False])
            # elif style == '3':
            #     # Down_Alert_List0 = realtime_quote.query(
            #     #     'realtime_gap<0 & '
            #     #     'realtime_valleygap_diff > PostSecStart_ValleyGapValue_HighQuntl*0.98 & '
            #     #     'realtime_valleygap_diff >= postmaxpeak_maxgapdiff*0.98').copy()
            #     Down_Alert_List1 = realtime_quote.query(
            #         'realtime_gap<0 & daily_ratio<0 & '
            #         'realtime_gap_value >= realtime_valleygap_max*0.98 & '
            #         'realtime_gap_value >= PostSecStart_MedianValleyGapValue*0.98').copy()
            #     Down_Alert_List2 = realtime_quote.query(
            #         'realtime_gap<0 & daily_ratio<0 & '
            #         '(realtime_gap_ratio >= PostSecStart_ValleyGap_HighQuntl | '
            #         'realtime_gap_ratio >= PostSecStart_MaxValleyGap)').copy()
            #     Down_Alert_List3 = realtime_quote.query(
            #         'realtime_gap<0 & daily_ratio<0 & '
            #         'realtime_valleygap_max >= PostSecStart_MedianValleyGapValue & '
            #         'realtime_valleygap_max >= Recent3Day_MaxValleyGapValue & '
            #         'price < maxvalleygap_price & '
            #         'realtime_gap_value <= realtime_valleygap_max').copy()
            #     Up_Alert_List1 = realtime_quote.query(
            #         'realtime_gap>0 & daily_ratio>0 & '
            #         'realtime_gap_value >= realtime_peakgap_max*0.98 & '
            #         '(realtime_gap_value >= Recent3Day_MaxPeakGapValue*0.98 & '
            #         'realtime_gap_value >= PostSecStart_MedianPeakGapValue*0.98)').copy()
            #     Up_Alert_List2 = realtime_quote.query(
            #         'realtime_gap>0 & '
            #         '(realtime_gap_value >= PostSecStart_PeakGapValue_HighQuntl | '
            #         'realtime_gap_value >= PostSecStart_MaxPeakGapValue)').copy()
            #     Up_Alert_List3 = realtime_quote.query(
            #         'realtime_gap>0 & '
            #         'realtime_peakgap_max >= PostSecStart_MedianPeakGapValue & '
            #         'realtime_peakgap_max >= Recent3Day_MaxPeakGapValue & '
            #         'price > maxpeakgap_price & '
            #         'realtime_gap_value <= realtime_peakgap_max').copy()
            else:
                print('Style输入有误，程序结束')
                return None
            signal = False
            columns_restore = ['ts_code', 'name', 'industry', 'Pick_Mode',
                               'Track_Date', 'Track_Time', 'Track_Type']
            if len(Narrow_Alert_List) > 0:
                print('时间：', now_time)
                print('ValleyGap收窄提示(Narrow_In)：')
                Narrow_Alert_List['Buy_Price'] = Narrow_Alert_List['weight_avg'] - \
                                                 Narrow_Alert_List['PostSecStart_MedianValleyGapValue']/2
                with pd.option_context('expand_frame_repr', False):
                    print(Narrow_Alert_List[['ts_code', 'name', 'industry', 'Pick_Mode', 'Now_SecDate', 'Buy_Price',
                                             'realtime_valleygap_max', 'Now_ValleyGapValue',
                                             'PostSecStart_MedianValleyGapValue',
                                             'PostSecStart_ValleyGapValue_HighQuntl']])
                Narrow_Alert_List = Narrow_Alert_List[['ts_code', 'name', 'industry', 'Pick_Mode']].copy()
                Narrow_Alert_List['Track_Date'] = pd.to_datetime(datetime.date.today()).strftime('%Y-%m-%d')
                Narrow_Alert_List['Track_Time'] = pd.to_datetime(datetime.datetime.now()).strftime('%H:%M:%S')
                Narrow_Alert_List['Track_Type'] = 'Narrow_In'
                track_record = pd.concat([track_record, Narrow_Alert_List[columns_restore]], axis=0)
                signal = True
            if len(Up_Alert_List1) > 0:
                print('时间：', now_time)
                print('上涨突破日内最大PeakGap提示(Up_Median)：')
                Up_Alert_List1['Sell_Price'] = Up_Alert_List1['weight_avg'] + \
                                               (Up_Alert_List1['PostSecStart_MedianPeakGapValue'] +
                                                Up_Alert_List1['PostSecStart_PeakGapValue_HighQuntl'])/2
                with pd.option_context('expand_frame_repr', False):
                    print(Up_Alert_List1[['ts_code', 'name', 'industry', 'Pick_Mode', 'Now_SecDate', 'Sell_Price',
                                         'realtime_gap', 'realtime_peakgap_max', 'PostSecStart_MedianPeakGapValue',
                                          'PostSecStart_PeakGapValue_HighQuntl']])
                Up_Alert_List1 = Up_Alert_List1[['ts_code', 'name', 'industry', 'Pick_Mode']].copy()
                Up_Alert_List1['Track_Date'] = pd.to_datetime(datetime.date.today()).strftime('%Y-%m-%d')
                Up_Alert_List1['Track_Time'] = pd.to_datetime(datetime.datetime.now()).strftime('%H:%M:%S')
                Up_Alert_List1['Track_Type'] = 'Up_Median'
                track_record = pd.concat([track_record, Up_Alert_List1[columns_restore]], axis=0)
                signal = True
            if len(Up_Alert_List2) > 0:
                print('时间：', now_time)
                print('上涨突破HighQuntl或Sec_Max提示(Up_HighQuntl/Sec_Max)：')
                Up_Alert_List2['Sell_Price'] = Up_Alert_List2['weight_avg'] + \
                                               (Up_Alert_List2['PostSecStart_PeakGapValue_HighQuntl'] +
                                                Up_Alert_List2['PostSecStart_MaxPeakGapValue'])/2
                with pd.option_context('expand_frame_repr', False):
                    print(Up_Alert_List2[['ts_code', 'name', 'industry', 'Pick_Mode', 'Sell_Price',
                                          'Now_SecDate', 'realtime_gap',
                                          'PostSecStart_PeakGapValue_HighQuntl', 'PostSecStart_MaxPeakGapValue']])
                Up_Alert_List2 = Up_Alert_List2[['ts_code', 'name', 'industry', 'Pick_Mode']].copy()
                Up_Alert_List2['Track_Date'] = pd.to_datetime(datetime.date.today()).strftime('%Y-%m-%d')
                Up_Alert_List2['Track_Time'] = pd.to_datetime(datetime.datetime.now()).strftime('%H:%M:%S')
                Up_Alert_List2['Track_Type'] = 'Up_HighQuntl/Sec_Max'
                track_record = pd.concat([track_record, Up_Alert_List2[columns_restore]], axis=0)
                signal = True
            if len(Up_Alert_List3) > 0:
                print('时间：', now_time)
                print('上涨突破Median二次确认提示(Up_Median_Confirm)：')
                Up_Alert_List3['Sell_Price'] = Up_Alert_List3['weight_avg'] + \
                                               Up_Alert_List3['PostSecStart_MedianPeakGapValue']
                with pd.option_context('expand_frame_repr', False):
                    print(Up_Alert_List3[['ts_code', 'name', 'industry', 'Pick_Mode', 'Sell_Price',
                                          'Now_SecDate', 'realtime_gap',
                                          'PostSecStart_MedianPeakGapValue', 'PostSecStart_MaxPeakGapValue',]])
                Up_Alert_List3 = Up_Alert_List3[['ts_code', 'name', 'industry', 'Pick_Mode']].copy()
                Up_Alert_List3['Track_Date'] = pd.to_datetime(datetime.date.today()).strftime('%Y-%m-%d')
                Up_Alert_List3['Track_Time'] = pd.to_datetime(datetime.datetime.now()).strftime('%H:%M:%S')
                Up_Alert_List3['Track_Type'] = 'Up_Median_Confirm'
                track_record = pd.concat([track_record, Up_Alert_List3[columns_restore]], axis=0)
                signal = True
            if len(Down_Alert_List0) > 0:
                print('时间：', now_time)
                print('出现PreNowSec以来最大单日跌幅提示(Down_MaxDropRatio)：')
                Down_Alert_List0['Buy_Price'] = Down_Alert_List0['weight_avg'] - \
                                                Down_Alert_List0['PostSecStart_MedianValleyGapValue']
                with pd.option_context('expand_frame_repr', False):
                    print(Down_Alert_List0[['ts_code', 'name', 'industry', 'Pick_Mode', 'Now_SecDate', 'Buy_Price',
                                            'realtime_gap', 'PreNowSec_ExtreRatio',
                                            'PostSecStart_MedianValleyGapValue',
                                            'PostSecStart_ValleyGapValue_HighQuntl']])
                Down_Alert_List0 = Down_Alert_List0[['ts_code', 'name', 'industry', 'Pick_Mode']].copy()
                Down_Alert_List0['Track_Date'] = pd.to_datetime(datetime.date.today()).strftime('%Y-%m-%d')
                Down_Alert_List0['Track_Time'] = pd.to_datetime(datetime.datetime.now()).strftime('%H:%M:%S')
                Down_Alert_List0['Track_Type'] = 'Down_MaxDropRatio'
                track_record = pd.concat([track_record, Down_Alert_List0[columns_restore]], axis=0)
                signal = True
            if len(Down_Alert_List1) > 0:
                print('时间：', now_time)
                print('下跌突破Median提示(Down_Median)：')
                Down_Alert_List1['Buy_Price'] = Down_Alert_List1['weight_avg'] - \
                                                (Down_Alert_List1['PostSecStart_MedianValleyGapValue'] +
                                                 Down_Alert_List1['PostSecStart_ValleyGapValue_HighQuntl'])/2
                with pd.option_context('expand_frame_repr', False):
                    print(Down_Alert_List1[['ts_code', 'name', 'industry', 'Pick_Mode', 'Now_SecDate', 'Buy_Price',
                                            'realtime_gap', 'realtime_valleygap_max',
                                            'Recent3Day_MaxValleyGapValue',
                                            'PostSecStart_MedianValleyGapValue',
                                            'PostSecStart_ValleyGapValue_HighQuntl']])
                Down_Alert_List1 = Down_Alert_List1[['ts_code', 'name', 'industry', 'Pick_Mode']].copy()
                Down_Alert_List1['Track_Date'] = pd.to_datetime(datetime.date.today()).strftime('%Y-%m-%d')
                Down_Alert_List1['Track_Time'] = pd.to_datetime(datetime.datetime.now()).strftime('%H:%M:%S')
                Down_Alert_List1['Track_Type'] = 'Down_Median'
                track_record = pd.concat([track_record, Down_Alert_List1[columns_restore]], axis=0)
                signal = True
            if len(Down_Alert_List2) > 0:
                print('时间：', now_time)
                print('下跌突破HighQuntl或Sec_Max提示(Down_HighQuntl/Sec_Max)：')
                Down_Alert_List2['Buy_Price'] = Down_Alert_List2['weight_avg'] - \
                                                (Down_Alert_List2['PostSecStart_ValleyGapValue_HighQuntl'] +
                                                 Down_Alert_List2['PostSecStart_MaxValleyGapValue'])/2
                with pd.option_context('expand_frame_repr', False):
                    print(Down_Alert_List2[['ts_code', 'name', 'industry', 'Pick_Mode', 'Buy_Price',
                                            'Now_SecDate', 'realtime_gap',
                                            'PostSecStart_ValleyGapValue_HighQuntl',
                                            'PostSecStart_MaxValleyGapValue']])
                Down_Alert_List2 = Down_Alert_List2[['ts_code', 'name', 'industry', 'Pick_Mode']].copy()
                Down_Alert_List2['Track_Date'] = pd.to_datetime(datetime.date.today()).strftime('%Y-%m-%d')
                Down_Alert_List2['Track_Time'] = pd.to_datetime(datetime.datetime.now()).strftime('%H:%M:%S')
                Down_Alert_List2['Track_Type'] = 'Down_HighQuntl/Sec_Max'
                track_record = pd.concat([track_record, Down_Alert_List2[columns_restore]], axis=0)
                signal = True
            if len(Down_Alert_List3) > 0:
                print('时间：', now_time)
                print('下跌突破Median二次确认提示(Down_Second_Confirm)：')
                Down_Alert_List3['Buy_Price'] = Down_Alert_List3['weight_avg'] - \
                                                Down_Alert_List3['PostSecStart_MedianValleyGapValue']
                with pd.option_context('expand_frame_repr', False):
                    print(Down_Alert_List3[['ts_code', 'name', 'industry', 'Pick_Mode', 'Buy_Price',
                                            'Now_SecDate', 'realtime_gap',
                                            'PostSecStart_MedianValleyGapValue',
                                            'PostSecStart_ValleyGapValue_HighQuntl',
                                            'PostSecStart_MaxValleyGapValue']])
                Down_Alert_List3 = Down_Alert_List3[['ts_code', 'name', 'industry', 'Pick_Mode']].copy()
                Down_Alert_List3['Track_Date'] = pd.to_datetime(datetime.date.today()).strftime('%Y-%m-%d')
                Down_Alert_List3['Track_Time'] = pd.to_datetime(datetime.datetime.now()).strftime('%H:%M:%S')
                Down_Alert_List3['Track_Type'] = 'Down_Median_Confirm'
                track_record = pd.concat([track_record, Down_Alert_List3[columns_restore]], axis=0)
                signal = True
            if len(Down_Alert_List4) > 0 and track_signal == 2:
                print('时间：', now_time)
                print('相对MaxPeak下跌突破MedianValleyGap提示(Down_RelativeMedian)：')
                Down_Alert_List4['Buy_Price'] = Down_Alert_List4['weight_avg'] - \
                                                (Down_Alert_List4['PostSecStart_ValleyGapValue_HighQuntl'] -
                                                 Down_Alert_List4['realtime_peakgap_max'])
                with pd.option_context('expand_frame_repr', False):
                    print(Down_Alert_List4[['ts_code', 'name', 'industry', 'Pick_Mode', 'Buy_Price',
                                            'Now_SecDate', 'realtime_gap',
                                            'realtime_valleygap_diff', 'PostSecStart_MedianValleyGapValue',
                                            'PostSecStart_ValleyGapValue_HighQuntl', 'PostSecStart_MaxValleyGapValue']])
                Down_Alert_List4 = Down_Alert_List4[['ts_code', 'name', 'industry', 'Pick_Mode']].copy()
                Down_Alert_List4['Track_Date'] = pd.to_datetime(datetime.date.today()).strftime('%Y-%m-%d')
                Down_Alert_List4['Track_Time'] = pd.to_datetime(datetime.datetime.now()).strftime('%H:%M:%S')
                Down_Alert_List4['Track_Type'] = 'Down_RelativeMedian'
                track_record = pd.concat([track_record, Down_Alert_List4[columns_restore]], axis=0)
                signal = True
            if not signal:
                print('时间：', now_time)
                print('No Signal!')
            realtime_quotes_day = pd.concat([realtime_quotes_day, realtime_quote_origin], axis=0)
            track_count += 1
            if len(track_record) > 0 and style == '2' and track_count % 5 == 0:
                engine = create_engine(
                    'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                        conf.DC_DB_PORT) + '/stocksfit')
                pd.io.sql.to_sql(track_record, 'dailytrack_record', engine,
                                 index=False, schema='stocksfit', if_exists='append')
                engine.dispose()
                track_record = pd.DataFrame()
            time.sleep(60)
        elif datetime_end2 <= now_time:
            print('时间：', now_time)
            print('非交易时间，停止跟踪')
            break
        else:
            continue
    if len(track_record) > 0 and style == '2':
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')
        pd.io.sql.to_sql(track_record, 'dailytrack_record', engine,
                         index=False, schema='stocksfit', if_exists='append')
        engine.dispose()
    return track_record


def get_postmax_mingapvalue(ts_code=None, max_gapvalue=None, realtime_quote=None):
    """获取指定ts_code在日内realtime_quote中等于max_gapvalue之后的最小gap_value"""
    if len(realtime_quote) == 0 or \
            len(realtime_quote.query('ts_code==@ts_code & realtime_gap_value==@max_gapvalue & realtime_gap>0')) == 0:
        return 100
    max_timeidx = realtime_quote.query(
        'ts_code==@ts_code & realtime_gap_value==@max_gapvalue & realtime_gap>0')['time'].iloc[0]
    postmax_mingapvalue = abs(
        realtime_quote.query('ts_code==@ts_code & time>@max_timeidx')['realtime_gap_value'].min() - max_gapvalue)
    return postmax_mingapvalue


def get_maxgap_price(ts_code=None, real_extreme_gap=None, realtime_quote=None):
    """获取指定ts_code在real_valleygap_min对应的价格"""
    if len(realtime_quote) == 0 or \
            len(realtime_quote.query('ts_code==@ts_code & realtime_gap==@real_extreme_gap')) == 0:
        return 0
    extreme_gap_price = realtime_quote.query(
        'ts_code==@ts_code & realtime_gap==@real_extreme_gap')['price'].iloc[0]
    return extreme_gap_price


def stk_daily_track(source='Valley', industry=None, track=True,
                    style=None, section_dates=None, pick_date=None,
                    now_secdate=None, index_startdate=None, index_enddate=None, now_date=None,
                    storemode=False):
    """日内跟踪程序
   source: 跟踪品种类型，Valley/All
    """
    # 选择跟踪类型
    if style is None:
        style = input('选择跟踪类型：\n1.筛选&跟踪\n2.日内波动跟踪\n3.自定义品种跟踪\n请输入数字：')
    track_signal = 0
    trade_dates = get_trade_date(end_date=datetime.datetime.today().strftime('%Y-%m-%d'))
    trade_dates = trade_dates[trade_dates < datetime.datetime.today().strftime('%Y-%m-%d')]
    if style == '1' or style == '2':
        if now_date is None:
            now_date = trade_dates[-1]
        if track:
            from sqlalchemy import create_engine, text
            import config.config_Ali as config
            conf = config.configModel()
            engine = create_engine(
                'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                    conf.DC_DB_PORT) + '/stocksfit')
            sql = f"""select * from stocksfit.dailytrack_stock where DailyTrack_Date= '{now_date}'"""
            result_track = pd.read_sql_query(sql, engine)
            if len(result_track) == 0:
                print('日跟踪筛选结果未存储！退出程序！重新进入程序并设定storemode为True')
                return
            else:
                print('实时跟踪启动，跟踪品种数量：', len(result_track), '个\n')
                num_count = result_track[['ts_code', 'Pick_Mode']].groupby('Pick_Mode').size()
                print('筛选类型数量：\n', num_count)
                print('跟踪品种包括：\n', '/ '.join(result_track['name'].to_list()))
                track_realtime_gapidx(result_pick=result_track, style=style,
                                      track_signal=track_signal)
        else:
            if section_dates is None:
                section_dates = input_to_str(input("请输入Section_StartDate对应指数转折日期，多个日期用英文逗号隔开："),
                                             type='list')
            else:
                section_dates = input_to_str(section_dates, type='list')
            if pick_date is None:
                pick_date = input_to_str(input('如需指定品种筛选日期，请输入日期，否则直接回车：'), type='list')
            else:
                pick_date = input_to_str(pick_date, type='list')
            if now_secdate is None:
                Now_SecDate = input_to_str(input('如需指定Now_SecDate，请输入日期，否则直接回车：'), type='list')
            else:
                Now_SecDate = input_to_str(now_secdate, type='list')
            if index_startdate is None:
                Index_StartDate = input_to_str(input('如需指定Index_StartDate，请输入日期，否则直接回车：'))
            else:
                Index_StartDate = index_startdate
            if index_enddate is None:
                Index_EndDate = input_to_str(input('如需指定Index_EndDate，请输入日期，否则直接回车：'))
            else:
                Index_EndDate = index_enddate

            if industry is None:
                industry = input("如需要限定行业范围，请输入行业名称，多个行业用[]包含（否则直接回车）：")

            if pick_date == '':
                pick_date = [trade_dates[-1]]
            # 读取数据
            result_track1 = get_stocktrack_result(Now_Date=pick_date, Turn_Date=section_dates, mode=source.lower())
            result_track1 = result_track1.drop_duplicates(subset='ts_code', keep='last')
            if len(result_track1) == 0:
                print('跟踪股票清单为空，程序结束！')
                return None
            if Now_SecDate != '':
                result_track1, track_signal = get_result_for_dailytrack(
                    Now_SecDate=Now_SecDate, pick_date=pick_date,
                    trade_dates=trade_dates, result_track=result_track1, track=track,
                    NowDate=now_date)
            if Index_StartDate != '' and Index_EndDate != '' and len(result_track1) > 0:
                result_track1 = cal_indexperiod_ratio(result_track1, Index_StartDate, Index_EndDate)
                avg_ipr = min(result_track1['index_period_ratio'].median(), 0)
                result_track1 = result_track1.query('index_period_ratio>=@avg_ipr')
            if industry != '' and industry != '[]' and len(result_track1) > 0:
                if isinstance(industry, str):
                    industry = [industry]
                result_track1 = result_track1.query('industry in @industry')
            if len(result_track1) == 0:
                print('跟踪股票清单为空，程序结束！')
                return pd.DataFrame()
            result_track = result_track1.drop_duplicates(subset=['ts_code'], keep='last').copy()
            result_track['DailyTrack_Date'] = now_date
            # 实时跟踪Gap指标
            if Index_StartDate != '' and Index_EndDate != '':
                result_track = result_track.sort_values(by=['Pick_Mode', 'index_period_ratio'], ascending=[True, False])
            else:
                result_track = result_track.sort_values(by=['Pick_Mode', 'PreSecPeak_Sec_AvgRatio'],
                                                        ascending=[True, True])
            if storemode:
                from sqlalchemy import create_engine, text
                import config.config_Ali as config
                conf = config.configModel()
                engine = create_engine(
                    'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                        conf.DC_DB_PORT) + '/stocksfit')
                Sesstion = sessionmaker(bind=engine)
                session = Sesstion()
                try:
                    delete_sql = text("delete from stocksfit.dailytrack_stock where DailyTrack_Date= :now_date")
                    with session.begin():
                        session.execute(delete_sql, {"now_date": now_date})
                    session.commit()
                except Exception as e:
                    session.rollback()
                    print('An error occured:', e)
                try:
                    pd.io.sql.to_sql(result_track, 'dailytrack_stock', engine, index=False, schema='stocksfit',
                                     if_exists='append')
                except:
                    pdb.set_trace()
                session.close()
                engine.dispose()
                print('筛选结果存储到数据库表: dailytrack_stock')
    elif style == '3':
        stk_list = input_to_str(input("请输入持仓品种列表，多个品种用英文逗号隔开："), type='list')
        # 读取股票指标数据
        last_trade_date = trade_dates[-1]
        last_trade_date_1 = trade_dates[-2]
        result_track_temp = get_result_3(start_date=last_trade_date_1, end_date=last_trade_date, stk_list=stk_list)
        result_track = result_track_temp.query('Cal_Date==@last_trade_date')
        result_track_1 = result_track_temp.query('Cal_Date==@last_trade_date_1')
        result_track_1 = result_track_1.rename(columns={'Now_Over3Mean': 'Now_Over3Mean_1',
                                                        'Now_PeakGapValue': 'Now_PeakGapValue_1'})
        result_track = pd.merge(result_track, result_track_1[['ts_code', 'Now_Over3Mean_1',
                                                              'Now_PeakGapValue_1']], on=['ts_code'],
                                how='left')
        if len(result_track) == 0:
            print('股票指标数据结果为空，检查数据！')
            return None
        print('实时跟踪程序启动，跟踪品种包括：\n', '/ '.join(result_track['name'].to_list()))
        result_alert_list_1 = result_track.query(
            '((Now_Over3Mean_1=="-" & Now_Over3Mean=="-") | Now_SecDate==@last_trade_date)')
        if len(result_alert_list_1) > 0:
            print("连续两天低于前3日均价 or Now_SecDate为当前日期的持仓品种警示！待止损！：\n",
                  result_alert_list_1[['ts_code', 'name']])
        result_alert_list_2 = result_track.query('PostNowSec_MaxPeakGap >= PostSecStart_PeakGap_HighQuntl')
        if len(result_alert_list_2) > 0:
            print("PostNowSec_MaxPeakGap突破警示！待止盈！：\n", result_alert_list_2[['ts_code', 'name']])
        result_alert_list_3 = result_track.query('Now_PeakGapValue_1 >= PostSecStart_MedianPeakGapValue & '
                                                 'Now_PeakGapValue >= PostSecStart_MedianPeakGapValue')
        if len(result_alert_list_3) > 0:
            print("Now_PeakGapValue连续两日突破均值警示！待止盈！：\n", result_alert_list_3[['ts_code', 'name']])
        if track:
            track_realtime_gapidx(result_pick=result_track, style=style)
    else:
        print('输入有误，程序结束')
        result_track = pd.DataFrame()
    return result_track


def cal_indexperiod_ratio(result_track=None, index_startdate=None, index_enddate=None):
    """计算指定指数周期内的备选品种涨跌幅"""
    index_data = get_index_data(stk_code='000906.SH', start_date=index_startdate, end_date=index_enddate)
    ts_codes = result_track['ts_code'].to_list()
    stock_data = get_stock_data(stk_code=ts_codes, start_date=index_startdate, end_date=index_enddate)
    index_ratio = (index_data['close'].iloc[-1] / index_data['close'].iloc[0] - 1) * 100
    stock_ratio = (stock_data.groupby('ts_code')['close'].last() /
                   stock_data.groupby('ts_code')['close'].first() - 1) * 100 - index_ratio
    result_track['index_period_ratio'] = result_track['ts_code'].map(stock_ratio)
    return result_track


def index_daily_track(stk_code='000906.SH', indexperiod_startdate=''):
    """指数日内跟踪，如超过指定日期至最新日期的medianpeakgapvalue或medianvalleygapvalue，进行提示"""
    trade_dates = get_trade_date(end_date=datetime.datetime.today().strftime('%Y-%m-%d'))
    indexperiod_enddate = trade_dates[trade_dates < datetime.datetime.today().strftime('%Y-%m-%d')][-1]
    if indexperiod_startdate == '':
        indexperiod_startdate = input_to_str(input('如需指定IndexPeriod_StartDate，请输入日期：'))
        if indexperiod_startdate == '':
            print('未指定IndexPeriod_StartDate，程序结束！')
            return None
        else:
            indexperiod_startdate = indexperiod_startdate.replace("'", "")
            indexperiod_startdate = indexperiod_startdate.replace("[", "")
            indexperiod_startdate = indexperiod_startdate.replace("]", "")
            indexperiod_startdate = indexperiod_startdate.replace(" ", "")
    indexperiod_gap_data = get_min_indicators(stk_code='000906.SH', start_date=indexperiod_startdate,
                                              end_date=indexperiod_enddate, style='Index')
    print('交易时间内，价差Gap跟踪Start！')
    realtime_quotes_day = pd.DataFrame()
    while True:
        now_time = datetime.datetime.now()
        # 时间9:30
        datetime_start1 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(9, 20, 0))
        # 时间11:30
        datetime_end1 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(11, 30, 0))
        # 时间13:00
        datetime_start2 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(13, 00, 0))
        # 时间14:30
        datetime_check = datetime.datetime.combine(
            datetime.date.today(), datetime.time(14, 30, 0))
        # 时间15:00
        datetime_end2 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(15, 0, 0))
        if datetime_start1 <= now_time < datetime_end1 or datetime_start2 <= now_time < datetime_end2:
            realtime_quote_origin = cal_realtime_gapidx(stk_list=stk_code, mode='Index')
            realtime_quotes_day = pd.concat(
                [realtime_quotes_day, realtime_quote_origin], axis=0).reset_index(drop=True)
            realtime_quotes_day['amount_diff'] = realtime_quotes_day['amount'].diff()
            realtime_quotes_day.loc[realtime_quotes_day.index[0], 'amount_diff'] = \
                realtime_quotes_day.loc[realtime_quotes_day.index[0], 'amount']
            realtime_quotes_day['weight_price'] = realtime_quotes_day['price'] * realtime_quotes_day['amount_diff']
            realtime_quotes_day['weight_avg'] = round(realtime_quotes_day['weight_price'].cumsum() /
                                                      realtime_quotes_day['amount'], 4)
            realtime_quotes_day['realtime_gap'] = realtime_quotes_day['price'] - realtime_quotes_day['weight_avg']
            realtime_quotes_maxpeakgap = realtime_quotes_day['realtime_gap'].max()
            realtime_quotes_maxvalleygap = abs(realtime_quotes_day['realtime_gap'].min())
            if realtime_quotes_maxpeakgap >= index_gapdata['Max_PeakGap']:
                print('时间：', now_time)
                print('指数突破Period_MaxPeakGap，可能见顶!')
                print('日内最大PeakGapValue：', realtime_quotes_maxpeakgap,
                      '\nMaxPeakGapValue：', index_gapdata['Max_PeakGap'])
            elif realtime_quotes_maxvalleygap >= index_gapdata['HighQuntl_PeakGap'] \
                    or realtime_quotes_maxvalleygap >= 30:
                print('时间：', now_time)
                print('指数突破Period_PeakGap_HighQuntl，可能见顶!')
                print('日内最大PeakGapValue：', realtime_quotes_maxvalleygap,
                      '\nPeakGapValue_HighQuntl：', index_gapdata['HighQuntl_PeakGap'])
            elif realtime_quotes_maxvalleygap < index_gapdata['Median_PeakGap']:
                print('时间：', now_time)
                print('指数低于Period_MedianPeakGap，可能见顶!')
                print('日内最大PeakGapValue：', realtime_quotes_maxvalleygap,
                      '\nMedianPeakGapValue：', index_gapdata['Median_PeakGap'])

            if realtime_quotes_maxvalleygap >= index_gapdata['Max_ValleyGap']:
                print('时间：', now_time)
                print('指数突破Period_MaxValleyGap，可能见底!')
                print('日内最大ValleyGapValue：', realtime_quotes_maxvalleygap,
                      '\nMaxValleyGapValue：', index_gapdata['Max_ValleyGap'])
            elif realtime_quotes_maxvalleygap >= index_gapdata['HighQuntl_ValleyGap']:
                print('时间：', now_time)
                print('指数突破Period_ValleyGap_HighQuntl，可能见底!')
                print('日内最大ValleyGapValue：', realtime_quotes_maxvalleygap,
                      '\nValleyGapValue_HighQuntl：', index_gapdata['HighQuntl_ValleyGap'])
            # elif realtime_quotes_maxvalleygap >= index_gapdata['Median_ValleyGap']:
            #     print('时间：', now_time)
            #     print('指数突破Period_MedianValleyGap，关注!')
            #     print('日内最大ValleyGapValue：', realtime_quotes_maxvalleygap,
            #           '\nMedianValleyGapValue：', index_gapdata['Median_ValleyGap'])
            time.sleep(60)
        elif datetime_end2 <= now_time:
            print('时间：', now_time)
            print('非交易时间，停止跟踪')
            break
        else:
            continue
    return None


def collect_dailytrack_results(
        start_date=None, end_date=None, section_dates=None, pick_date=None,
        now_secdate=None):
    """获取指定起止日期内的dailytrack数据,并确认趋势未变化"""
    trade_dates = get_trade_date()
    dates = trade_dates[(trade_dates >= start_date) & (trade_dates <= end_date)].tolist()
    if section_dates is not None and pick_date is not None and now_secdate is not None:
        pick_results = pd.DataFrame()
        for date in tqdm(dates):
            pick_result = stk_daily_track(source='Valley', track=False,
                                          style='2', section_dates=section_dates,
                                          pick_date=pick_date, now_secdate=now_secdate,
                                          index_startdate='', index_enddate='', industry='',
                                          now_date=date, storemode=False)
            if pick_result is not None and len(pick_result) > 0:
                pick_results = pd.concat([pick_results, pick_result], ignore_index=True)
    else:
        import config.config_Ali as config
        conf = config.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')
        sql = f"""select * from stocksfit.dailytrack_stock 
                  where DailyTrack_Date<='{end_date}' and DailyTrack_Date>='{start_date}'"""
        pick_results = pd.read_sql_query(sql=sql, con=engine)
        engine.dispose()
        print('取数起始日期：', pick_results['DailyTrack_Date'].min(),
              '\n取数结束日期：', pick_results['DailyTrack_Date'].max())
    if len(pick_results) > 0:
        ts_codes = pick_results['ts_code'].unique()
        result_now = get_result_3(end_date=end_date)
        pick_results = pick_results.drop_duplicates(subset=['ts_code', 'Section_StartDate_Turn'], keep='last')
        result_now = pd.merge(result_now, pick_results[['ts_code', 'Section_StartDate_Turn']],
                              on='ts_code', how='inner')
        result_now_pick = result_now.query('Section_StartDate==Section_StartDate_Turn & '
                                           'PostSecPeak2Now_LastDays<=5 & '
                                           'PostSecStart_PeakDate<@end_date & '
                                           'PostSecStart_MaxDrop_LastDays<=5 & '
                                           'PostNowSec_MaxPeakGapValue<PostSecStart_MaxPeakGapValue & '
                                           '(PostNowSec_LastDays<=3 | PostNowSec_SumRatio<15)')
        result_now_pick = result_now_pick.sort_values(
            by=['PostPeak_Recent_Neg4_DropDate', 'PreSecPeak_Sec_AvgRatio'],
            ascending=[False, True])
        pick_results = pick_results.sort_values(by=['PostPeak_Recent_Neg4_DropDate', 'PreSecPeak_Sec_AvgRatio'],
                                                ascending=[False, True])
    else:
        print('筛选结果为空。')
        result_now_pick = None
    return result_now_pick, pick_results


def get_pre15day_gapdata(track_quota=None, trade_dates=None, check_date=None):
    """获取前15天的日ValleyGap数据，用于判定日内跟踪超越天数"""
    # trade_dates = get_trade_date()
    end_date = track_quota['Cal_Date'].max()
    start_date = trade_dates[trade_dates <= end_date][-15]
    if check_date is not None:
        start_date = min(start_date, check_date)
    gap_data = pd.DataFrame()
    for ts_code in track_quota['ts_code'].unique():
        temp_gapdata = get_min_indicators(stk_code=ts_code, start_date=start_date,
                                          end_date=end_date, trade_df=trade_dates)
        gap_data = pd.concat([gap_data, temp_gapdata], ignore_index=True)
    gap_data = gap_data.sort_values(by=['ts_code', 'trade_date'], ascending=[True, True]
                                    ).set_index(['ts_code', 'trade_date'])
    return gap_data



def realtime_daily_track_in(track_quota=None, Drop_Signal=False):
    """依据track_quota股票列表，实时跟踪入场时机，当满足触发条件时，刷新提示信息"""
    import datetime
    conf = config.configModel()
    print('交易入场时机跟踪Start！')
    trade_dates = get_trade_date()
    last_tradedate = trade_dates[-1]
    realtime_quotes_day = pd.DataFrame()
    track_record = pd.DataFrame()
    track_count = 0
    # start_show_signal=True
    predaily_gapdata = get_pre15day_gapdata(track_quota=track_quota, trade_dates=trade_dates)

    def valleymax_coverdays(result, gapdata):
        realtime_valleygap_max = result['realtime_valleygap_max']
        ts_code = result['ts_code']
        stk_gapdata = gapdata.loc[ts_code]
        if len(stk_gapdata.query('valley_gap>@realtime_valleygap_max')) > 0:
            return len(stk_gapdata[stk_gapdata.query('valley_gap>@realtime_valleygap_max').index[-1]:])
        else:
            return len(stk_gapdata)

    def pgv_rollavg_mincoverdays(result, gapdata):
        pgv_rollavg = result['PGV_DailyRollAvg']
        ts_code = result['ts_code']
        stk_gapdata = gapdata.query('ts_code == @ts_code')
        if len(stk_gapdata.query('pgv_rollavg<@pgv_rollavg')) > 0:
            return len(stk_gapdata[stk_gapdata.query('pgv_rollavg<@pgv_rollavg').index[-1]:])
        else:
            return len(stk_gapdata)

    Break_PGV_MinRollAvg = pd.DataFrame()
    Drop_Break_VGV_HighQuntl = pd.DataFrame()
    while True:
        now_time = datetime.datetime.now()
        # 时间9:30
        first_half_starttime = datetime.datetime.combine(
            datetime.date.today(), datetime.time(9, 20, 0))
        # 时间11:30
        first_half_endtime = datetime.datetime.combine(
            datetime.date.today(), datetime.time(11, 30, 0))
        # 时间13:00
        second_half_starttime = datetime.datetime.combine(
            datetime.date.today(), datetime.time(13, 00, 0))
        # 时间14:30
        second_half_check1 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(14, 15, 0))
        # 时间15:00
        second_half_endtime = datetime.datetime.combine(
            datetime.date.today(), datetime.time(15, 0, 0))
        first_half_check1 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(9, 31, 0)
        )
        first_half_check2 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(11, 15, 0))
        if first_half_starttime <= now_time < first_half_endtime or \
                second_half_starttime <= now_time < second_half_endtime:
            realtime_quote_origin = cal_realtime_gapidx(stk_list=track_quota['ts_code'].tolist())
            if len(realtime_quotes_day) == 0:
                realtime_quotes_day = pd.concat([realtime_quotes_day, realtime_quote_origin], axis=0)
            realtime_peakgap_max = realtime_quotes_day.groupby(
                ['ts_code', 'name'], as_index=False)['realtime_gap'].max()
            # realtime_peakgap_max['realtime_gap'] = realtime_peakgap_max['realtime_gap'].apply(
            #     lambda fn: abs(fn) if fn > 0 else fn)
            realtime_peakgap_max = realtime_peakgap_max.rename(
                columns={'realtime_gap': 'realtime_peakgap_max'})
            realtime_price_max = realtime_quotes_day.groupby(['ts_code', 'name'], as_index=False)['price'].max()
            realtime_price_max = realtime_price_max.rename(columns={'price':'max_price'})
            realtime_peakgap_max['realtime_peakgap_max'] = abs(realtime_peakgap_max['realtime_peakgap_max'])
            realtime_peakgap_max['postmaxpeak_maxgapdiff'] = realtime_peakgap_max.apply(
                lambda fn: get_postmax_mingapvalue(fn['ts_code'], fn['realtime_peakgap_max'], realtime_quotes_day),
                axis=1)
            realtime_peakgap_max['maxpeakgap_price'] = realtime_peakgap_max.apply(
                lambda fn: get_maxgap_price(fn['ts_code'], fn['realtime_peakgap_max'], realtime_quotes_day),
                axis=1)
            realtime_valleygap_max = realtime_quotes_day.groupby(
                ['ts_code', 'name'], as_index=False)['realtime_gap'].min()
            realtime_valleygap_max = realtime_valleygap_max.rename(
                columns={'realtime_gap': 'realtime_valleygap_max'})
            realtime_valleygap_max['maxvalleygap_price'] = realtime_valleygap_max.apply(
                lambda fn: get_maxgap_price(fn['ts_code'], fn['realtime_valleygap_max'], realtime_quotes_day),
                axis=1)
            realtime_valleygap_max['realtime_valleygap_max'] = abs(realtime_valleygap_max['realtime_valleygap_max'])

            realtime_quote = pd.merge(realtime_quote_origin.copy(), track_quota,
                                       on=['ts_code', 'name'], how='left')

            realtime_quote = pd.merge(
                realtime_quote, realtime_peakgap_max[['ts_code', 'realtime_peakgap_max',
                                                      'postmaxpeak_maxgapdiff', 'maxpeakgap_price']],
                on=['ts_code'], how='left')
            realtime_quote = pd.merge(
                realtime_quote, realtime_valleygap_max[['ts_code', 'realtime_valleygap_max', 'maxvalleygap_price']],
                on=['ts_code'], how='left')
            realtime_quote = pd.merge(
                realtime_quote, realtime_price_max[['ts_code', 'max_price']], on=['ts_code'], how='left')

            realtime_quote['realtime_valleygap_diff'] = realtime_quote['realtime_peakgap_max'] - \
                                                        realtime_quote['realtime_gap']

            realtime_quote['PGV_DailyRollAvg'] = round((realtime_quote['realtime_peakgap_max'] +
                                                        realtime_quote['Recent2Day_MeanPeakGapValue']) / 2, 3)
            realtime_quote['PGV_DailyRollAvg_Now2MaxRatio'] = round(
                realtime_quote['PGV_DailyRollAvg'] / realtime_quote['PostSecStart_PGV_MaxRollAvg'], 3)
            realtime_quote['PGV_DailyRollAvg_Now2MinRatio'] = round(
                realtime_quote['PGV_DailyRollAvg'] / realtime_quote['PostSecMaxRollAvg_PGV_MinRollAvg'], 3)
            realtime_quote['Realtime_VGV_CoverDays'] = realtime_quote.apply(func=valleymax_coverdays,
                                                       args=(predaily_gapdata, ),
                                                       axis=1, result_type='expand')
            realtime_quote['PGV_DailyRollAvg_MinCoverDays'] = realtime_quote.apply(func=pgv_rollavg_mincoverdays,
                                                       args=(predaily_gapdata, ),
                                                       axis=1, result_type='expand')
            realtime_quote['Check_Post2Pre_PGV_RollAvg_Ratio'] = round(
                realtime_quote['PGV_DailyRollAvg'] / realtime_quote['PreSecMaxRollAvg_PGV_MinRollAvg'], 3)
            realtime_quote['Check_Truncated_Diff'] = round(realtime_quote['PostSecPeak_PGV_MeanRollAvg_TruncatedValue'] -
                                                         realtime_quote['PGV_DailyRollAvg'], 3)
            realtime_quote['PGV_DailyRollAvg_Now2PreNow_Ratio'] = round(
                realtime_quote['PGV_DailyRollAvg'] / realtime_quote['PreNowPeak_PGV_MeanRollAvg'], 3)

            # 跟踪信号分为三种类型：
            # （1）盘中超越MedianVGV或Recent3Day_MaxVGV；
            # （2）盘中超越VGV_HighQuntl；
            # （3）尾盘出现PGV_MinRollAvg

            # 20241108 跟踪信号调整为两种
            # （1）尾盘PGV_DailyRollAvg高于Recent3Day_PGV_MinRollAvg
            # （2）盘中突破Recent3Day_MaxVGV,和VGV_HighQuntl的较小值

            # 盘中超越Recent3Day_MaxVGV或High_Quntl信号，埋单信号
            Drop_Break_Recent3VGV = realtime_quote.query('realtime_gap<0 & '
                                                        '(realtime_gap_value>PostSecStart_MedianValleyGapValue*0.8 | '
                                                        'realtime_gap_value>Recent3Day_MaxValleyGapValue*0.8)'
                                                        ).copy()

            # 盘中超越VGV_HighQuntl信号
            if len(Drop_Break_Recent3VGV) > 0:
                Drop_Break_VGV_HighQuntl = realtime_quote.query(
                    'realtime_gap<0 & '
                    'realtime_gap_value>PostSecStart_ValleyGapValue_HighQuntl*0.8'
                    ).copy()
                if len(Drop_Break_VGV_HighQuntl) > 0:
                    ts_codes = Drop_Break_VGV_HighQuntl['ts_code'].tolist()
                    if len(Drop_Break_Recent3VGV.query('ts_code not in @ts_codes')) > 0:
                        Drop_Break_Recent3VGV = Drop_Break_Recent3VGV.query('ts_code not in @ts_codes')
                    else:
                        Drop_Break_Recent3VGV = pd.DataFrame()

            # 出现PGV_MinRollAvg突破信号
            Break_PGV_MinRollAvg = realtime_quote.query(
                'PGV_DailyRollAvg>Now_PGV_RollAvg & '
                'PGV_DailyRollAvg>PostSecPeak_PGV_MinRollAvg & '
                'Break_UpSecutiveStart_First2Now_LastDays.isnull() & '
                'max_price>=price_threshold'
                ).copy().sort_values(by='PreNow2PostSec_PGV_MeanRollAvg_Ratio', ascending=True)

            # 出现突破DownConsecutive_Start信号
            Break_PGV_RollAvg_DownStart = realtime_quote.query(
                'PGV_DailyRollAvg>DownConsecutive_Start_PGVRollAvg & '
                'PGV_DailyRollAvg>PostSecPeak_PGV_MinRollAvg & '
                'Break_DownSecutiveStart_First2Now_LastDays.isnull() & '
                'Break_UpSecutiveStart_First2Now_LastDays.isnull() & '
                'max_price>=price_threshold'
            ).copy().sort_values(by='PreNow2PostSec_PGV_MeanRollAvg_Ratio', ascending=True)

            # 展示跟踪品种低于前三天MaxVGV和Median_VGV的对应入场价格
            # if start_show_signal and now_time > first_half_check1 \
            #         and len(realtime_quote) > 0:
            #     Track_Price_Check = realtime_quote.copy()
            #     Track_Price_Check['Buy_Price_UndMedian'] = round(
            #         Track_Price_Check['weight_avg'] - Track_Price_Check['PostSecStart_MedianValleyGapValue'], 2)
            #     Track_Price_Check['Buy_Price_Und3Day'] = round(
            #         Track_Price_Check['weight_avg'] - Track_Price_Check['Recent3Day_MaxValleyGapValue'], 2)
            #     print('入场价格测算: 低于MedianVGV、前三天MaxVGV对应埋单价格：')
            #     with pd.option_context('expand_frame_repr', False):
            #         print(Track_Price_Check[['ts_code', 'name', 'industry',
            #                                  'Buy_Price_UndMedian', 'Buy_Price_Und3Day',
            #                                  'PostSecStart_MedianValleyGapValue',
            #                                  'Recent3Day_MaxValleyGapValue']])
            #     start_show_signal = False

            signal = False
            if Drop_Signal:
                # 盘中超越Median_VGV提示
                if len(Drop_Break_Recent3VGV) > 0:
                    print('时间：', now_time)
                    print('Break_MedianVGV: 突破MedianVGV或者Recent3Day信号')
                    Drop_Break_Recent3VGV['Buy_Price_Median'] = round(
                        Drop_Break_Recent3VGV['weight_avg'] - Drop_Break_Recent3VGV['PostSecStart_MedianValleyGapValue'], 2)
                    Drop_Break_Recent3VGV['Buy_Price_3DayMax'] = round(
                        Drop_Break_Recent3VGV['weight_avg'] - Drop_Break_Recent3VGV['Recent3Day_MaxValleyGapValue'], 2)
                    with pd.option_context('expand_frame_repr', False):
                        print(Drop_Break_Recent3VGV[['ts_code', 'name', 'industry',
                                                    'Buy_Price_Median', 'Buy_Price_3DayMax',
                                                    'realtime_gap_value', 'realtime_valleygap_max',
                                                    'PostSecStart_MedianValleyGapValue',
                                                    'Recent3Day_MaxValleyGapValue',
                                                    'PostSecStart_ValleyGap_HighQuntl']])
                    signal = True

                # 超越VGV_HighQuntl触发提示
                if len(Drop_Break_VGV_HighQuntl) > 0:
                    print('时间：', now_time)
                    print('Break_VGV_HighQuntl：突破近VGV_HighQuntl信号')
                    Drop_Break_VGV_HighQuntl['Buy_Price_HighQuntl'] = round(
                        Drop_Break_VGV_HighQuntl['weight_avg'] -
                        Drop_Break_VGV_HighQuntl['PostSecStart_ValleyGapValue_HighQuntl'], 2)
                    with pd.option_context('expand_frame_repr', False):
                        print(Drop_Break_VGV_HighQuntl[['ts_code', 'name', 'industry',
                                                 'Buy_Price_HighQuntl',
                                                 'realtime_gap_value', 'realtime_valleygap_max',
                                                 'PostSecStart_ValleyGap_HighQuntl',
                                                 'Recent3Day_MaxValleyGapValue']])
                    signal = True

            # 触发PGV_MinRollAvg信号
            if len(Break_PGV_MinRollAvg) > 0:
                print('时间：', now_time)
                print('PGV_RollAvg突破信号: 突破前一日PGVRollAvg：')
                with pd.option_context('expand_frame_repr', False):
                    print(Break_PGV_MinRollAvg[['ts_code', 'name', 'industry',
                                               'PGV_DailyRollAvg', 'PGV_DailyRollAvg_Now2PreNow_Ratio',
                                               'Now_PGV_RollAvg', 'PostSecPeak_DownConsecutive_Num',
                                               'PostSecPeak_PGV_MinRollAvg2Now_LastDays',
                                               'PostSecStart_MedianValleyGapValue',
                                                'PostSecStart_ValleyGapValue_HighQuntl'
                                               ]])
                signal = True

            # 突破PGV_RollAvg_DownConsecutiveStart信号
            if len(Break_PGV_RollAvg_DownStart) > 0:
                print('时间：', now_time)
                print('PGV_RollAvg突破信号: 突破DownConsecutive下行起始PGVRollAvg：')
                with pd.option_context('expand_frame_repr', False):
                    print(Break_PGV_RollAvg_DownStart[['ts_code', 'name', 'industry',
                                                'PGV_DailyRollAvg', 'DownConsecutive_Start_PGVRollAvg',
                                                'PostSecPeak_DownConsecutive_Num',
                                                'DownConsecutive2Now_LastDays',
                                                'PostSecStart_MedianValleyGapValue',
                                                'PostSecStart_ValleyGapValue_HighQuntl'
                                                ]])
                signal = True

            if not signal:
                print('时间：', now_time)
                print('No Signal!')
            realtime_quotes_day = pd.concat([realtime_quotes_day, realtime_quote_origin], axis=0)
            track_count += 1
            time.sleep(60)
        elif second_half_endtime <= now_time:
            print('时间：', now_time)
            print('非交易时间，停止跟踪')
            break
        else:
            continue
    return track_record



def realtime_daily_track_out(track_quota=None):
    """依据track_quota股票列表，实时跟踪交易时机，当满足触发条件时，刷新提示信息
    持仓品种跟踪分为两种情况：
    止盈、止损"""
    import datetime
    conf = config.configModel()
    print('交易离场时机跟踪Start！')
    zhiSun_Signal=True
    signal = False
    end_date = track_quota['Cal_Date'].max()
    stk_startdate = pd.to_datetime(track_quota['Section_StartDate']).min().strftime('%Y-%m-%d')
    stock_data = get_stock_data(stk_code=track_quota['ts_code'].values.tolist(), start_date=stk_startdate)
    trade_dates = get_trade_date(end_date=end_date)
    day_return = 1

    predaily_gapdata = get_pre15day_gapdata(track_quota=track_quota, trade_dates=trade_dates,
                                            check_date=track_quota['PostSecStart_PGV_MaxRollAvg_Date'].min())

    def get_postconcave_maxrollavg_date(result, gap_data):
        """获取MaxRollAvg之后Concave的MaxRollAvgDate"""
        postmaxrollavg_mindate = result['PostSecMaxRollAvg_PGV_MinRollAvg_Date']
        stk_gap_data = gap_data.loc[result['ts_code']]
        if pd.notnull(postmaxrollavg_mindate) and len(stk_gap_data.loc[postmaxrollavg_mindate:]) > 0:
            postconcave_date = stk_gap_data.loc[postmaxrollavg_mindate:, 'pgv_rollavg'].idxmax()
        else:
            postconcave_date = result['PostSecStart_PGV_MaxRollAvg_Date']
        return postconcave_date

    # 提前排序并设置索引，减少每次查询时的重复排序操作
    stock_data = stock_data.sort_values(by=['ts_code', 'trade_date']).set_index(['ts_code', 'trade_date'])

    def cal_undminrolldate_state(ts_code, minrollavg_date, stock_data):
        """计算是否最近2天低于minrollavg_date收盘价"""
        stk_data = stock_data.loc[ts_code]
        recent2day_close = stk_data['close'].iloc[-2:]  # 最近两天的收盘价
        recent2day_meancls = recent2day_close.mean()
        # 检查索引中是否存在 minrollavg_date，避免出错
        if minrollavg_date in stk_data.index:
            recent2day_mincls2mindate = round(
                (recent2day_close.min() / stk_data.loc[minrollavg_date, 'close'] - 1) * 100, 2)

            if len(stk_data.loc[minrollavg_date:]) > 2 and recent2day_meancls < stk_data.loc[minrollavg_date, 'close'] \
                    and recent2day_mincls2mindate < -4.5:
                return 'True'

        return '-'

    track_quota['UndMinRollDate_State'] = track_quota.apply(
        lambda fn: cal_undminrolldate_state(fn['ts_code'], fn['PostSecMaxRollAvg_PGV_MinRollAvg_Date'], stock_data),
        axis=1)
    realtime_quotes_day = pd.DataFrame()

    while True:
        now_time = datetime.datetime.now()
        # 时间9:30
        first_half_starttime = datetime.datetime.combine(
            datetime.date.today(), datetime.time(9, 30, 0))
        # 时间11:15
        first_half_check = datetime.datetime.combine(
            datetime.date.today(), datetime.time(11, 15, 0))
        # 时间11:30
        first_half_endtime = datetime.datetime.combine(
            datetime.date.today(), datetime.time(11, 30, 0))
        # 时间13:00
        second_half_starttime = datetime.datetime.combine(
            datetime.date.today(), datetime.time(13, 00, 0))
        # 时间14:30
        second_half_check1 = datetime.datetime.combine(
            datetime.date.today(), datetime.time(14, 20, 0))
        # 时间15:00
        second_half_endtime = datetime.datetime.combine(
            datetime.date.today(), datetime.time(15, 0, 0))
        if first_half_starttime <= now_time < first_half_endtime \
                or second_half_starttime <= now_time < second_half_endtime:
            realtime_quote_origin = cal_realtime_gapidx(stk_list=track_quota['ts_code'].tolist())
            if len(realtime_quotes_day) == 0:
                realtime_quotes_day = pd.concat([realtime_quotes_day, realtime_quote_origin], axis=0)
            realtime_peakgap_max = realtime_quotes_day.groupby(
                ['ts_code', 'name'], as_index=False)['realtime_gap'].max()
            # realtime_peakgap_max['realtime_gap'] = realtime_peakgap_max['realtime_gap'].apply(
            #     lambda fn: abs(fn) if fn > 0 else fn)
            realtime_peakgap_max = realtime_peakgap_max.rename(
                columns={'realtime_gap': 'realtime_peakgap_max'})
            realtime_peakgap_max['postmaxpeak_maxgapdiff'] = realtime_peakgap_max.apply(
                lambda fn: get_postmax_mingapvalue(fn['ts_code'], fn['realtime_peakgap_max'], realtime_quotes_day),
                axis=1)
            realtime_peakgap_max['maxpeakgap_price'] = realtime_peakgap_max.apply(
                lambda fn: get_maxgap_price(fn['ts_code'], fn['realtime_peakgap_max'], realtime_quotes_day),
                axis=1)
            realtime_valleygap_max = realtime_quotes_day.groupby(
                ['ts_code', 'name'], as_index=False)['realtime_gap'].min()
            realtime_valleygap_max = realtime_valleygap_max.rename(
                columns={'realtime_gap': 'realtime_valleygap_max'})
            realtime_valleygap_max['maxvalleygap_price'] = realtime_valleygap_max.apply(
                lambda fn: get_maxgap_price(fn['ts_code'], fn['realtime_valleygap_max'], realtime_quotes_day),
                axis=1)
            realtime_valleygap_max['realtime_valleygap_max'] = abs(realtime_valleygap_max['realtime_valleygap_max'])
            realtime_quote = pd.merge(realtime_quote_origin.copy(), track_quota,
                                       on=['ts_code', 'name'], how='left')
            realtime_quote = pd.merge(
                realtime_quote, realtime_peakgap_max[['ts_code', 'realtime_peakgap_max',
                                                      'postmaxpeak_maxgapdiff', 'maxpeakgap_price']],
                on=['ts_code'], how='left')
            realtime_quote = pd.merge(
                realtime_quote, realtime_valleygap_max[['ts_code', 'realtime_valleygap_max', 'maxvalleygap_price']],
                on=['ts_code'], how='left')
            realtime_quote['realtime_valleygap_diff'] = realtime_quote['realtime_peakgap_max'] - \
                                                        realtime_quote['realtime_gap']

            realtime_quote['PGV_DailyRollAvg'] = round((realtime_quote['realtime_peakgap_max'] +
                                                        realtime_quote['Recent2Day_MeanPeakGapValue']) / 3, 3)
            realtime_quote['PGV_DailyRollAvg2PostNowMax_Ratio'] = round(
                realtime_quote['PGV_DailyRollAvg'] / realtime_quote['PostNowSec_PGV_MaxRollAvg'], 3)
            # realtime_quote['PostNow2PreNow_PGV_MeanRollAvg_Ratio'] = round(
            #     realtime_quote['PostNowSec_PGV_MaxRollAvg'] / realtime_quote['PreNowSec_PGV_MeanRollAvg'], 3)
            realtime_quote['PostSecMax_Concave_MaxRollAvg_Date'] = realtime_quote.apply(
                func=get_postconcave_maxrollavg_date,
                args=(predaily_gapdata, ),
                axis=1, result_type='expand')

            realtime_quote['PostNow2PreNow_PGV_MeanRollAvg_Ratio'] = realtime_quote.apply(
                lambda fn: round(fn['PostNowSec_PGV_MaxRollAvg'] / fn['PreNowSec_PGV_MeanRollAvg'], 3)
                if pd.notnull(fn['PreNowSec_PGV_MeanRollAvg']) else
                round(fn['PostNowSec_PGV_MaxRollAvg'] / fn['PreNowSec_PGV_MeanRollAvg'], 3),
                axis=1)

            # 止盈1：PGVRollAvg较PostNowSec_PGV_MaxRollAvg回落
            if now_time > second_half_check1:
                Zhiyin_Break_RollAvg = realtime_quote.query(
                    '(Now_PGV_RollAvg==PostNowSec_PGV_MaxRollAvg | PostSecMax_Concave_MaxRollAvg_Date==Cal_Date)& '
                    'PostNow2PreNow_PGV_MeanRollAvg_Ratio>=2 & '
                    'PGV_DailyRollAvg<Now_PGV_RollAvg').copy()
            else:
                Zhiyin_Break_RollAvg = pd.DataFrame()

            # 止盈2：日内出现PostNowSec_MaxPGV的次高值
            # Zhiyin_Break_PGV = realtime_quote.query(
            #     'realtime_peakgap_max<PostNowSec_MaxPeakGapValue & '
            #     'Now_PeakGapValue==PostNowSec_MaxPeakGapValue')

            # 止盈3：日内突破PostSecStart_MaxVGV
            Zhiyin_Break_VGV = realtime_quote.query(
                'realtime_valleygap_max>PostNowSec_MaxValleyGapValue & '
                'PostNow2PreNow_PGV_MeanRollAvg_Ratio>=2').copy()

            # 止损：连续两天低于MinRollAvg日期收盘价，跌幅超过-4.5%
            ZhiSun_Drop = realtime_quote.query(
                'UndMinRollDate_State=="True"').copy()

            # 止损提示
            if zhiSun_Signal and now_time > first_half_starttime and len(Zhiyin_Break_RollAvg) > 0:
                print('时间：', now_time)
                print('止损：触发止损条件的品种：')
                with pd.option_context('expand_frame_repr', False):
                    print(ZhiSun_Drop[['ts_code', 'name', 'industry']])
                zhiSun_Signal = False

            # 止盈1: 较PostNowSec_PGV_MaxRollAvg回落提示
            if len(Zhiyin_Break_RollAvg) > 0:
                print('时间：', now_time)
                print('止盈: PGV_RollAvg较PostNowSec_MaxRollAvg出现回落，出现止盈信号：')
                Zhiyin_Break_RollAvg['Sell_Price'] = round(
                    Zhiyin_Break_RollAvg['weight_avg'] * 0.995 + Zhiyin_Break_RollAvg[
                        'PostSecStart_MedianPeakGapValue'], 2)
                with pd.option_context('expand_frame_repr', False):
                    print(Zhiyin_Break_RollAvg[['ts_code', 'name', 'industry', 'Sell_Price',
                                                'PGV_DailyRollAvg', 'PostNowSec_PGV_MaxRollAvg',
                                                'PostSecStart_MedianPeakGapValue', 'realtime_peakgap_max'
                                                ]])
                signal = True

            # 止盈3：VGV突破PostNowSec_MaxValleyGapValue
            if len(Zhiyin_Break_VGV) > 0:
                print('时间：', now_time)
                print('止盈: 日内出现突破PostNowSec_MaxVGV，出现止盈信号：')
                Zhiyin_Break_VGV['Sell_Price'] = round(
                    Zhiyin_Break_VGV['weight_avg'] * 0.995 + Zhiyin_Break_VGV[
                        'PostSecStart_MedianPeakGapValue'], 2)
                with pd.option_context('expand_frame_repr', False):
                    print(Zhiyin_Break_VGV[['ts_code', 'name', 'industry',
                                            'Sell_Price', 'realtime_peakgap_max',
                                            'PostNowSec_MaxPeakGapValue',
                                            'PostSecStart_MaxPeakGapValue'
                                            ]])
                signal = True

            if not signal:
                print('时间：', now_time)
                print('No Signal!')
            realtime_quotes_day = pd.concat([realtime_quotes_day, realtime_quote_origin], axis=0)
            time.sleep(60)
        elif second_half_endtime <= now_time:
            print('时间：', now_time)
            print('非交易时间，停止跟踪')
            break
        else:
            continue
    return


def process_dailytrack(trade_side='In', stk_list=None,
                       pick_date=None, Drop_Signal=False):
    """启动日内监控程序，监控名单来源数据库或录入"""
    if stk_list is not None and isinstance(stk_list, str):
        stk_list = [stk_list]
    if trade_side.upper() == 'IN':
        if stk_list is not None:
            track_quota = get_result_3(stk_list=stk_list, end_date=pick_date)
            lag_date = get_trade_date(end_date=pick_date, loc=-2)
            stock_data = get_stock_data(stk_code=stk_list, start_date=lag_date, end_date=pick_date)
            stock_data_adj = pd.merge(stock_data.query('trade_date==@lag_date')[['ts_code', 'low']],
                                      stock_data.query('trade_date==@pick_date')[['ts_code', 'close']],
                                      how='left', on='ts_code')
            stock_data_adj['price_threshold'] = stock_data_adj.apply(lambda fn: max(fn['low'], fn['close']), axis=1)
            track_quota = pd.merge(track_quota, stock_data_adj[['ts_code', 'price_threshold']],
                                   how='left', on='ts_code')
        else:
            print('参数录入错误，退出')
            return
        if len(track_quota) > 0:
            print('实时跟踪股票数量： ', len(track_quota))
            print('跟踪股票名单：', '/'.join(track_quota['name'].values.tolist()))
            track_record = realtime_daily_track_in(track_quota=track_quota, Drop_Signal=Drop_Signal)
            return track_record
        else:
            print('跟踪品种数量为空')
            return
    elif trade_side.upper() == 'OUT':
        if stk_list is not None:
            track_quota = get_result_3(end_date=pick_date, stk_list=stk_list)
            realtime_daily_track_out(track_quota)
        else:
            print('跟踪品种为空')
        return
    else:
        print('trade_side参数错误，退出')
        return


def verify_dailytrack_result(pick_startdate=None, pick_enddate=None, verify_date=None,
                             limit_num=20, industry=None, lag_check=True):
    """从modelrf数据库获取筛选结果，依据指定verify_date的收盘数据，按照dailytrack识别信号进行筛选，查看结果状况"""
    trade_dates = get_trade_date()
    preverify_date = trade_dates[(trade_dates<verify_date)][-1]
    ts_list = get_predictdata_from_sql(start_date=pick_startdate, end_date=pick_enddate,
                                       limit_num=limit_num, industry=industry)
    track_quota = prepare_dailytrack_list(ts_list=ts_list, end_date=preverify_date)
    track_list = track_quota['ts_code'].unique().tolist()
    if len(track_list) == 0:
        print('备选品种为空，退出')
        return
    verify_quota = get_result_3(end_date=verify_date, stk_list=track_list)
    verify_quota = verify_quota.rename(
        columns={'Now_ValleyGapValue': 'Veri_Now_ValleyGapValue',
                 'Now_PostSecPeak_PGV_RollAvg_Asc_Rank': 'Veri_Now_PostSecPeak_PGV_RollAvg_Asc_Rank',
                 'PostSecPeak_PGV_MinRollAvg2MaxRatio': 'Veri_PostSecPeak_PGV_MinRollAvg2MaxRatio',
                 'Now_PostSecPeak_VGV_MaxCoverDays': 'Veri_Now_PostSecPeak_VGV_MaxCoverDays',
                 'Now_PostSecPeak_PGV_RollAvg_MinCoverDays': 'Veri_Now_PostSecPeak_PGV_RollAvg_MinCoverDays',
                 'Now_PGV_RollAvg':'Veri_Now_PGV_RollAvg'})
    track_quota = pd.merge(track_quota, verify_quota[['ts_code', 'Veri_Now_ValleyGapValue',
                                                      'Veri_Now_PostSecPeak_PGV_RollAvg_Asc_Rank',
                                                      'Veri_PostSecPeak_PGV_MinRollAvg2MaxRatio',
                                                      'Veri_Now_PostSecPeak_VGV_MaxCoverDays',
                                                      'Veri_Now_PostSecPeak_PGV_RollAvg_MinCoverDays',
                                                      'Veri_Now_PGV_RollAvg']],
                           how='left', on='ts_code')
    track_quota_risesec = track_quota.query('Track_Categ=="RiseSec_B" & '
                                            'Veri_Now_ValleyGapValue>PostSecStart_MedianValleyGapValue').copy()
    track_quota_riseminrollavg = track_quota.query('Track_Categ=="RiseSec_MinRollAvg" & '
                                               'Veri_Now_PostSecPeak_PGV_RollAvg_MinCoverDays>5 & '
                                               'Veri_Now_PGV_RollAvg<Now_PGV_RollAvg & '
                                               'Veri_Now_PostSecPeak_PGV_RollAvg_MinCoverDays>'
                                               'Now_PostSecPeak_PGV_RollAvg_MinCoverDays').copy()
    track_quota_dropminrollavg = track_quota.query('Track_Categ=="DropSec_MinRollAvg" & '
                                                   'Veri_Now_PostSecPeak_PGV_RollAvg_MinCoverDays>5 & '
                                                   'Veri_Now_PGV_RollAvg<Now_PGV_RollAvg & '
                                                   'Veri_Now_PostSecPeak_PGV_RollAvg_MinCoverDays>'
                                                   'Now_PostSecPeak_PGV_RollAvg_MinCoverDays & '
                                                   'Veri_PostSecPeak_PGV_MinRollAvg2MaxRatio<0.25 & '
                                                   'Veri_Now_PostSecPeak_PGV_RollAvg_Asc_Rank==1').copy()
    track_quota_maxvgv = track_quota.query('Track_Categ=="DropSec_MaxVGV" & '
                                           'Veri_Now_PostSecPeak_VGV_MaxCoverDays>=5 & '
                                           'Veri_Now_PostSecPeak_VGV_MaxCoverDays>'
                                           'Now_PostSecPeak_VGV_MaxCoverDays').copy()
    pick_quota = pd.concat([track_quota_risesec, track_quota_riseminrollavg,
                            track_quota_dropminrollavg, track_quota_maxvgv],
                           axis=0, ignore_index=True)
    pick_quota = pick_quota.dropna(axis=0, how='all')
    if lag_check and len(trade_dates[trade_dates>verify_date]) > 1:
        stock_data = get_stock_data(start_date=verify_date, stk_code=pick_quota['ts_code'].unique().tolist())
        pick_quota['Lag_Ratio'] = pick_quota.apply(func=cal_osci_ratio,
                                                       args=(stock_data, trade_dates, verify_date,),
                                                       axis=1, result_type='expand')
    return pick_quota, track_quota


def check_pgv_state_manual(ts_list=None, end_date=None):
    if ts_list is None:
        ts_list = input_to_str(input("请输入查询品种代码，多个品种用英文逗号隔开："), type='list')
    max_pgv = []
    for ts_code in ts_list:
        input_pgv = float(input('输入'+ts_code+'日内最大PGV:'))
        max_pgv.append(input_pgv)
    input_data = pd.DataFrame({'ts_code': ts_list, 'max_pgv': max_pgv}, index=range(0, len(ts_list)))
    result = get_result_3(end_date=end_date)
    result_adj = pd.merge(result, input_data, on='ts_code', how='inner')
    result_adj['today_pgv_rollavg'] = round((result_adj['max_pgv'] + result_adj['Recent2Day_MeanPeakGapValue']) / 3, 3)
    result_adj['today_pgv_minrollavg2maxratio'] = round(
        result_adj.apply(lambda fn: min(fn['today_pgv_rollavg'], fn['PostSecMaxRollAvg_PGV_MinRollAvg']), axis=1)/
        result_adj['PostSecStart_PGV_MaxRollAvg'], 3)
    result_adj['today_pgv_minrollavg2minratio'] = round(
        result_adj.apply(lambda fn: min(fn['today_pgv_rollavg'], fn['PostSecMaxRollAvg_PGV_MinRollAvg']), axis=1) /
        result_adj['PostSecMaxRollAvg_PGV_MinRollAvg'], 3)
    result_adj['PreNow2PostSec_MedianPG_Ratio'] = round(
        result_adj['PreNowSec_MedianPeakGap'] / result_adj['PostSecStart_MedianPeakGap'], 3)
    result_pick = result_adj.query('today_pgv_rollavg<PostSecMaxRollAvg_PGV_MinRollAvg & '
                                   'today_pgv_minrollavg2maxratio<0.5').copy().sort_values(
        by='PreNow2PostSec_MedianPG_Ratio', ascending=False)
    return result_pick[['ts_code', 'name', 'industry', 'today_pgv_rollavg', 'today_pgv_minrollavg2minratio',
                        'PostSecMaxRollAvg_PGV_MinRollAvg',
                        'PostSecStart_PGV_MaxRollAvg', 'PostSecPeak2Now_LastDays',
                        'PostSecStart_MedianPeakGap', 'PreNowSec_MedianPeakGap']],\
           result_adj


if __name__ == '__main__':
    # track_record = process_dailytrack(trade_side='In', pick_startdate='2024-08-30', pick_enddate='2024-09-09',
    #                                   limit_num=100, track_downturn=True)
    # verify_quota, all_quota = verify_dailytrack_result(pick_startdate='2024-08-19',
    #                                                    pick_enddate='2024-08-23',
    #                                                    verify_date='2024-08-24', limit_num=30)

    # end_date = '2024-09-11'
    # industry = ['公用事业', '建筑装饰', '钢铁']
    # prepared_quota, slowup = get_turntrack_result(pick_date=end_date, sec_pickdate='2024-09-11',
    #                                               limit_num=0, bottom_industry=industry)
    # result = get_result_3(end_date='2024-09-12')
    # prepared_quota_adj = pd.merge(prepared_quota,
    #                               result[['ts_code', 'PostSecMaxRollAvg_PGV_MinRollAvg',
    #                                       'PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio',
    #                                       'Now_PGV_RollAvg',
    #                                       'Now_ValleyGapValue']],
    #                               on='ts_code', how='left')
    # prepared_quota_adj_pick = prepared_quota_adj.query(
    #     '(PostSecMaxRollAvg_PGV_MinRollAvg_x>PostSecMaxRollAvg_PGV_MinRollAvg_y & '
    #     'PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio_y<0.5) | '
    #     'Now_PGV_RollAvg_y<Recent3Day_PGV_MinRollAvg | '
    #     'Now_ValleyGapValue_y>Recent3Day_MaxValleyGapValue')

    stk_list = ['603600.SH', '002884.SZ', '000651.SZ', '600642.SH', '000791.SZ']
    track_recored = process_dailytrack(trade_side='Out', stk_list=stk_list, pick_date='2024-11-08')