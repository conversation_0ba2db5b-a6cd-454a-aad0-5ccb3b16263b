"""SW一级&二级行业数据更新"""

# import config.config_fof as config_fof
from sqlalchemy import create_engine
import akshare as ak
import time

import os
import sys

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from function_ai.Func_Base import retracement_for_swindex, maxrise_for_swindex, turn_for_swindex, period_stat
from dateutil.relativedelta import relativedelta
import pandas as pd
import config.config_<PERSON> as config_Ali

import sys, os
# sys.path.append('/Users/<USER>/PycharmProjects/AI_Stock')
base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(base_dir)


def get_swindex_data(start_date=None, end_date=None):
    """获取申万行业指数数据"""
    conf_a = config_Ali.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')
    if start_date is None:
        start_date = end_date
    sw_sql = f"""select * from stocksfit.swindex_data where trade_date between '{start_date}' and '{end_date}'"""
    for _ in range(3):
        try:
            sw_index_hqdf = pd.read_sql_query(sql=sw_sql, con=engine)
        except:
            time.sleep(3)
        else:
            break
    engine.dispose()
    sw_index_hqdf = sw_index_hqdf.sort_values('trade_date', ascending=True)
    sw_index_hqdf = sw_index_hqdf.set_index('trade_date', drop=True)
    # start_date = sw_index_hqdf.index[0] if start_date is None else start_date
    # end_date = sw_index_hqdf.index[-1] if end_date is None else end_date
    if 'id' in sw_index_hqdf.columns:
        sw_index_hqdf = sw_index_hqdf.drop(columns=['id'])
    return sw_index_hqdf


def get_zzindex_data(start_date=None, end_date=None, index_code="sh000906", mode='fof'):
    """获取市场指数行情数据"""
    if mode == 'ak':
        stock_zh_index_daily_df = ak.stock_zh_index_daily(symbol=index_code)
        stock_zh_index_daily_df['date'] = stock_zh_index_daily_df['date'].apply(lambda fn: fn.strftime('%Y-%m-%d'))
        stock_zh_index_daily_df = stock_zh_index_daily_df.rename(columns={'date': 'trade_date'})
    else:
        if index_code[0].isalpha():
            index_code = index_code[2:] + '.' + index_code[:2].upper()
        conf_a = config_Ali.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
                conf_a.DC_DB_PORT) + '/stocksfit')
        sw_sql = """select * from stocksfit.index_data where ts_code=%(indexcode)s """
        for _ in range(3):
            try:
                stock_zh_index_daily_df = pd.read_sql_query(sql=sw_sql, con=engine, params={'indexcode': index_code})
            except:
                time.sleep(3)
            else:
                break
        engine.dispose()
        # stock_zh_index_daily_df = stock_zh_index_daily_df.rename(columns={'trade_date':'date'})
        stock_zh_index_daily_df = stock_zh_index_daily_df.sort_values('trade_date', ascending=True)

    stock_zh_index_daily_df = stock_zh_index_daily_df.set_index('trade_date', drop=True)
    start_date = stock_zh_index_daily_df.index[0] if start_date is None else start_date
    end_date = stock_zh_index_daily_df.index[-1] if end_date is None else end_date
    return stock_zh_index_daily_df.loc[start_date:end_date].copy()


def cal_sw_diffratio(start_date=None, end_date=None, indus=None, year=1, transform=False, roll_window=1):
    """计算所有申万行业指数相对收益率"""
    if start_date is None:
        start_date = (pd.to_datetime(end_date, format='%Y-%m-%d') - relativedelta(years=year)).strftime('%Y-%m-%d')
    sw_index_df = get_swindex_data(start_date=start_date, end_date=end_date)
    sw_startdate, sw_enddate = sw_index_df.index[0], sw_index_df.index[-1]
    zz_index_df = get_zzindex_data(start_date=sw_startdate, end_date=sw_enddate, mode='stkcloud')
    sw_index_ratio = sw_index_df.pct_change(fill_method=None)
    zz_index_ratio = zz_index_df.loc[sw_startdate:, 'close'].pct_change()
    diff_ratio = sw_index_ratio.sub(zz_index_ratio, axis=0) + 1
    sw_diffratio = (diff_ratio.cumprod(axis=0) - 1) * 100
    # sw_diffratio = sw_index_ratio.sub(zz_index_ratio, axis=0)
    if roll_window > 1:
        sw_diffratio = sw_diffratio.rolling(window=roll_window).mean()
    if indus is not None:
        sw_diffratio = sw_diffratio[indus].copy()
    if transform:
        sw_diffratio = sw_diffratio.T.sort_values(by=end_date, ascending=False)
        # sw_diffratio = sw_diffratio[sw_diffratio[end_date]>0].copy()
    return sw_diffratio


def cal_swindex_state(end_date=None, index_preturn_date=None):
    """计算所有申万行业跌幅和升幅列表"""
    # 获取股票市值数据
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    mv_sql = f"""select sd.ts_code, si.name, si.industry, sd.total_mv, sd.trade_date
                 from stock_data sd 
                 inner join (select ts_code,name, industry from stockinfo) si on sd.ts_code =si.ts_code 
                 where sd.trade_date = '{end_date}' 
                 order by sd.ts_code desc"""
    stock_mvs = pd.read_sql_query(sql=mv_sql, con=engine)
    engine.dispose()
    
    # 获取指数数据
    if end_date is None:
        zz_index = get_zzindex_data(mode='stkcloud')
        end_date = zz_index.index[-1]
    if index_preturn_date is None:
        index_preturn_date = end_date
    start_date = (pd.to_datetime(index_preturn_date, format='%Y-%m-%d') - relativedelta(years=2)).strftime('%Y-%m-%d')
    sw_diffratio = cal_sw_diffratio(start_date=start_date, end_date=end_date)
    end_date = sw_diffratio.index[-1]
    index_data = get_zzindex_data(end_date=end_date)
    
    # print('测试日期：', end_date)

    # 初始化结果DataFrame
    bottom_columns = ['indus', 'MaxDrop', 'start_date', 'end_date', 'Drop_Lastdays', 
                     'AvgDrop', 'End2Max_Ratio', 'End2Max_LastDays', 'End2Max_MaxDate', 'Indus_MV',
                     'Bottom_State', 'Bottom_Date', 'Bottom2Max_Ratio',
                     'Recent_Drop_EndDate', 'Recent_Drop_Sum', 'Recent_Drop_Avg', 'Recent_Drop_LastDays', 
                     'Drop_Seg_MaxDrop_EndDate', 'Drop_Seg_MaxDrop_Sum', 
                     'Drop_Seg_MaxDrop_Avg', 'Drop_Seg_MaxDrop_LastDays', 
                     'Drop_Seg_MaxDrop_End_To_Now_Days', 'Drop_Seg_Und1_Num']
    Bottom_List = pd.DataFrame(columns=bottom_columns)
    Bottom_List = Bottom_List.astype({col: 'object' for col in bottom_columns})
    
    cumret_columns = ['indus', 'MaxDrop', 'start_date', 'end_date', 'Drop_Lastdays',
                     'End2Max_Ratio', 'End2Max_LastDays', 'End2Max_MaxDate',
                     'MinCumRet_Date', 'MinCumRet_SumRatio', 'MinCumRet_AvgRatio', 'MinCumRet_DropDays',
                     'Ret2Now_Ratio', 'Ret2Now_LastDays', 'Ret2Now_AvgRatio', 
                     'Ret2Now_MaxDate', 'RetMax2Now_LastDays', 
                     'RecentTurn2Now_Ratio',
                     'MinCumRet_Seg_MaxRise_EndDate', 
                     'MinCumRet_Seg_MaxRise_Sum', 'MinCumRet_Seg_MaxRise_Avg', 
                     'MinCumRet_Seg_MaxRise_LastDays', 'MinCumRet_Seg_MaxRise_End_To_Now_Days',
                     'MinCumRet_Seg_Over1_Num']
    Cumret_List = pd.DataFrame(columns=cumret_columns)
    Cumret_List = Cumret_List.astype({col: 'object' for col in cumret_columns})

    rise_columns = ['indus', 'MaxRise', 'AvgRatio', 'start_date', 'end_date', 'Rise_Lastdays',
                    'Recent_Rise_EndDate', 'Recent_Rise_Sum', 'Recent_Rise_Avg', 'Recent_Rise_LastDays', 
                    'Recent2Max_Ratio', 'RiseEnd2Now_Lastdays', 'Rise_Seg_MaxRise_EndDate', 
                    'Rise_Seg_MaxRise_Sum', 'Rise_Seg_MaxRise_Avg',
                    'Rise_Seg_MaxRise_LastDays', 'Rise_Seg_Over1_Num']
    Rise_List = pd.DataFrame(columns=rise_columns)
    Rise_List = Rise_List.astype({col: 'object' for col in rise_columns})

    # 计算最大回撤
    for column in sw_diffratio.columns:
        temp_df = pd.DataFrame()
        diff_ratio = sw_diffratio[column]
        half_loc = diff_ratio.index[int(len(diff_ratio)/2)]
        posthalf_bottom = diff_ratio.loc[half_loc:].idxmin()
        prehalf_bottom = diff_ratio.loc[:half_loc].idxmin()
        middle_peak = diff_ratio.loc[prehalf_bottom:posthalf_bottom].idxmax()
        posthalf_bottom = diff_ratio.loc[middle_peak:].idxmin()
        
        # 确定底部日期
        if diff_ratio.loc[middle_peak] - diff_ratio.loc[posthalf_bottom] > 10:
            bottom_date = posthalf_bottom
        else:
            bottom_date = prehalf_bottom
        
        allbottom_date = diff_ratio.idxmin()
        maxdate_afterbottom = diff_ratio.loc[allbottom_date:].idxmax()
        mindate_aftermax = diff_ratio.loc[maxdate_afterbottom:].idxmin()

        indus_mv = round(stock_mvs.query('industry==@column')['total_mv'].sum()/10000, 2)
        prebottom_peak = diff_ratio.loc[:bottom_date].idxmax()
        maxdrop, drop_startdate, drop_enddate = retracement_for_swindex(diff_ratio.loc[prebottom_peak:])
        _, peak_mean, bottom_mean, recent_rise, recent_drop = \
            turn_for_swindex(diff_ratio.loc[bottom_date:], index_data)
        
        bottom_state = 'True' if bottom_date == drop_enddate else '-'

        postbottom_max = diff_ratio.loc[drop_enddate:].max()
        cover_days = len(
            diff_ratio.loc[diff_ratio.loc[:drop_enddate]
                           [diff_ratio.loc[:drop_enddate] > postbottom_max].index[-1]:drop_enddate]) \
            if len(diff_ratio.loc[:drop_enddate][diff_ratio.loc[:drop_enddate] > postbottom_max]) > 0 \
            else len(diff_ratio.loc[:drop_enddate])
        postb_maxdate = diff_ratio.loc[bottom_date:].idxmax()
        postm_mindate = diff_ratio.loc[postb_maxdate:].idxmin()
        longdrop_ratio = diff_ratio.loc[drop_enddate] - diff_ratio.loc[:drop_enddate].max()
        longdrop_lastdays = len(diff_ratio.loc[diff_ratio.loc[:drop_enddate].idxmax():drop_enddate])
        postend_maxdate = diff_ratio.loc[drop_enddate:].idxmax()
        postmax_mindate = diff_ratio.loc[postend_maxdate:].idxmin()
        
        if drop_enddate < index_preturn_date:
            end2max_rise_enddate = diff_ratio.loc[drop_enddate:index_preturn_date].idxmax()
        else:
            end2max_rise_enddate = diff_ratio.loc[drop_enddate:].idxmax()
        min_cumret_date = diff_ratio.loc[end2max_rise_enddate:].idxmin()
        end2max_rise_enddate = diff_ratio.loc[drop_enddate:min_cumret_date].idxmax()
        
        # if diff_ratio.loc[min_cumret_date:].max() > diff_ratio.loc[end2max_rise_enddate]:
        #     end2max_rise_enddate = diff_ratio.loc[min_cumret_date:].idxmax()
        #     min_cumret_date = diff_ratio.loc[end2max_rise_enddate:].idxmin()
        
        min_cumret_dropdays = len(diff_ratio.loc[end2max_rise_enddate:min_cumret_date]) - 1
        min_cumret_sumratio = diff_ratio.loc[min_cumret_date] - diff_ratio.loc[end2max_rise_enddate]
        min_cumret_avgratio = min_cumret_sumratio / min_cumret_dropdays \
                if min_cumret_dropdays > 0 else 0
        ret2now_enddate = diff_ratio.loc[min_cumret_date:].idxmax()
        
        postretmax_idxmin = diff_ratio.loc[ret2now_enddate:].idxmin()
        
        if end2max_rise_enddate == min_cumret_date or (
            min_cumret_dropdays < 10  and
            len(diff_ratio.loc[drop_enddate:end2max_rise_enddate]) < 20 and
            diff_ratio.iloc[-1] - diff_ratio.loc[drop_enddate] >= 1):
            min_cumret_date = diff_ratio.loc[drop_enddate:].idxmin()
            cumret_startdate = diff_ratio.loc[drop_startdate:min_cumret_date].idxmax()
            min_cumret_dropdays = len(diff_ratio.loc[cumret_startdate:min_cumret_date]) - 1
            min_cumret_sumratio = diff_ratio.loc[min_cumret_date] - diff_ratio.loc[cumret_startdate]
            min_cumret_avgratio = min_cumret_sumratio / min_cumret_dropdays \
                    if min_cumret_dropdays > 0 else 0
            ret2now_enddate = diff_ratio.loc[min_cumret_date:].idxmax()
        elif len(diff_ratio.loc[postretmax_idxmin:]) > 3 and len(diff_ratio.loc[ret2now_enddate:]) > 15:
            min_cumret_date = diff_ratio.loc[ret2now_enddate:].idxmin()
            min_cumret_dropdays = len(diff_ratio.loc[ret2now_enddate:min_cumret_date]) - 1
            min_cumret_sumratio = diff_ratio.loc[min_cumret_date] - diff_ratio.loc[ret2now_enddate]
            min_cumret_avgratio = min_cumret_sumratio / min_cumret_dropdays \
                    if min_cumret_dropdays > 0 else 0
            ret2now_enddate = diff_ratio.loc[min_cumret_date:].idxmax()
            
        # if column == '医药生物':
            # print(min_cumret_date, min_cumret_dropdays, min_cumret_sumratio, min_cumret_avgratio)
        
        ret2now_ratio = diff_ratio.iloc[-1] - diff_ratio.loc[min_cumret_date]
        ret2now_lastdays = len(diff_ratio.loc[min_cumret_date:]) - 1
        retenddate2now_lastdays = len(diff_ratio.loc[ret2now_enddate:]) - 1
        
        diff_ratio_3d = diff_ratio.loc[:min_cumret_date].rolling(3).mean()
        diff_ratio_check = diff_ratio.loc[:min_cumret_date].copy()
        num = -1
        while abs(num) < len(diff_ratio_check) - 1 and (
                diff_ratio_check.iloc[num] < diff_ratio_3d.iloc[num-1]):
            num -= 1
        
        # 继续回溯直至找到波峰位置
        while abs(num) < len(diff_ratio_check) - 1 and (
                diff_ratio_check.iloc[num] < diff_ratio_check.iloc[num-1]):
            num -= 1
            
        recent_drop_lastdays = abs(num) - 1
        recent_drop_sum = diff_ratio_check.iloc[-1] - diff_ratio_check.iloc[num]
        recent_drop_avg = recent_drop_sum / recent_drop_lastdays if recent_drop_lastdays > 0 else 0
        
        diff_ratio_d = sw_diffratio.loc[drop_startdate:drop_enddate, column].copy()
        drop_continuous_segments = []
        drop_current_segment_start = None
        
        # 遍历从rise_startdate到rise_enddate的区间
        for i in range(len(diff_ratio_d) - 1):
            current_date = diff_ratio_d.index[i]
            next_date = diff_ratio_d.index[i + 1]
            
            # 如果下一天的值高于当天，表示上涨
            if diff_ratio_d.loc[next_date] < diff_ratio_d.loc[current_date]:
                # 如果还没有开始一个新的上涨区段，则标记开始
                if drop_current_segment_start is None:
                    drop_current_segment_start = current_date
            # 如果不是上涨且已经有一个上涨区段在进行中，则结束当前区段
            elif drop_current_segment_start is not None:
                segment_end = current_date
                segment_change = diff_ratio_d.loc[segment_end] - diff_ratio_d.loc[drop_current_segment_start]
                segment_days = len(diff_ratio_d.loc[drop_current_segment_start:segment_end]) - 1
                avg_daily_change = segment_change / segment_days if segment_days > 0 else 0
                
                drop_continuous_segments.append({
                    'start_date': drop_current_segment_start,
                    'end_date': segment_end,
                    'days': segment_days,
                    'total_change': segment_change,
                    'avg_daily_change': avg_daily_change
                })
                
                drop_current_segment_start = None
        
        # 处理最后一个可能的上涨区段
        if drop_current_segment_start is not None:
            segment_end = drop_enddate
            segment_change = diff_ratio_d.loc[segment_end] - diff_ratio_d.loc[drop_current_segment_start]
            segment_days = len(diff_ratio_d.loc[drop_current_segment_start:segment_end]) - 1
            avg_daily_change = segment_change / segment_days if segment_days > 0 else 0
            
            drop_continuous_segments.append({
                'start_date': drop_current_segment_start,
                'end_date': segment_end,
                'days': segment_days,
                'total_change': round(segment_change, 3),
                'avg_daily_change': round(avg_daily_change, 3)
            })
         
        drop_max_avg_change = 0
        drop_max_sum_change = 0
        drop_max_change_end_date = None
        drop_max_change_end_to_now_days = 0
        drop_max_change_lastdays = 0
        
        recent_drop_avg_change = 0
        recent_drop_sum_change = 0
        recent_drop_lastdays = 0
        recent_drop_end_date = None
        
        drop_seg_und1_Num = 0
        
        if len(drop_continuous_segments) > 0:
            filtered_drop_segments = []
            filtered_drop_und1_segments = []
            for segment in drop_continuous_segments:
                if pd.to_datetime(segment['end_date']) <= pd.to_datetime(drop_enddate) and segment['days'] > 2:
                    filtered_drop_segments.append(segment)
                if pd.to_datetime(segment['end_date']) <= pd.to_datetime(drop_enddate) and segment['days'] > 2 and segment['avg_daily_change'] < -1:
                    filtered_drop_und1_segments.append(segment)
            # 如果需要，可以将过滤后的结果排序或进行其他处理
            if len(filtered_drop_segments) > 0:
                drop_max_segment = min(filtered_drop_segments, key=lambda x: x['total_change'])
                drop_max_avg_change = drop_max_segment['avg_daily_change']
                drop_max_sum_change = drop_max_segment['total_change']
                drop_max_change_end_date = drop_max_segment['end_date']
                drop_max_change_end_to_now_days = len(diff_ratio_d.loc[drop_max_change_end_date:]) - 1
                drop_max_change_lastdays = len(
                    diff_ratio_d.loc[drop_max_segment['start_date']:drop_max_change_end_date]) - 1
                
                recent_drop_avg_change = filtered_drop_segments[-1]['avg_daily_change']
                recent_drop_sum_change = filtered_drop_segments[-1]['total_change']
                recent_drop_lastdays = len(
                    diff_ratio_d.loc[filtered_drop_segments[-1]['start_date']:filtered_drop_segments[-1]['end_date']]) - 1
                recent_drop_end_date = filtered_drop_segments[-1]['end_date']
                
            drop_seg_und1_Num = len(filtered_drop_und1_segments)
                
        bottom_temp_df = pd.DataFrame(
            {'indus': column, 
             'MaxDrop': maxdrop, 
             'start_date': drop_startdate, 
             'end_date': drop_enddate,
             'Drop_Lastdays': len(diff_ratio.loc[drop_startdate:drop_enddate]),
             'AvgDrop': round(maxdrop / len(diff_ratio.loc[drop_startdate:drop_enddate]), 3),
             'End2Max_Ratio': round((diff_ratio.loc[drop_enddate:].max() - diff_ratio.loc[drop_enddate]), 3),
             'End2Max_LastDays': len(diff_ratio.loc[drop_enddate:end2max_rise_enddate]) - 1,
             'End2Max_MaxDate': end2max_rise_enddate,
             'Indus_MV': indus_mv,
             'Bottom_State': bottom_state,
             'Bottom_Date': bottom_date,
             'Bottom2Max_Ratio': round((diff_ratio.loc[bottom_date:].max() - diff_ratio.loc[bottom_date]), 3),
             'Recent_Drop_EndDate': recent_drop_end_date,
             'Recent_Drop_Sum': round(recent_drop_sum_change, 3),
             'Recent_Drop_Avg': round(recent_drop_avg_change, 3),
             'Recent_Drop_LastDays': recent_drop_lastdays,
             'Drop_Seg_MaxDrop_EndDate': drop_max_change_end_date,
             'Drop_Seg_MaxDrop_Sum': round(drop_max_sum_change, 3),
             'Drop_Seg_MaxDrop_Avg': round(drop_max_avg_change, 3),
             'Drop_Seg_MaxDrop_LastDays': drop_max_change_lastdays,
             'Drop_Seg_MaxDrop_End_To_Now_Days': drop_max_change_end_to_now_days,
             'Drop_Seg_Und1_Num': drop_seg_und1_Num,
             }, index=range(0, 1))
             
        if not bottom_temp_df.empty and not bottom_temp_df.isna().all().all():
            Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)
        
         # 获取continuous_rise_segments中开始日期大于min_cumret_date的数据
        # 识别连续上涨区段
        diff_ratio_r = sw_diffratio.loc[bottom_date:, column]
        continuous_rise_segments = []
        current_segment_start = None
        
        # 遍历从rise_startdate到rise_enddate的区间
        for i in range(len(diff_ratio_r) - 1):
            current_date = diff_ratio_r.index[i]
            next_date = diff_ratio_r.index[i + 1]
            
            # 如果下一天的值高于当天，表示上涨
            if diff_ratio_r.loc[next_date] > diff_ratio_r.loc[current_date]:
                # 如果还没有开始一个新的上涨区段，则标记开始
                if current_segment_start is None:
                    current_segment_start = current_date
            # 如果不是上涨且已经有一个上涨区段在进行中，则结束当前区段
            elif current_segment_start is not None:
                segment_end = current_date
                segment_change = diff_ratio_r.loc[segment_end] - diff_ratio_r.loc[current_segment_start]
                segment_days = len(diff_ratio_r.loc[current_segment_start:segment_end]) - 1
                avg_daily_change = segment_change / segment_days if segment_days > 0 else 0
                
                continuous_rise_segments.append({
                    'start_date': current_segment_start,
                    'end_date': segment_end,
                    'days': segment_days,
                    'total_change': segment_change,
                    'avg_daily_change': avg_daily_change
                })
                
                current_segment_start = None
        
        # 处理最后一个可能的上涨区段
        if current_segment_start is not None:
            segment_end = diff_ratio_r.index[-1]
            segment_change = diff_ratio_r.loc[segment_end] - diff_ratio_r.loc[current_segment_start]
            segment_days = len(diff_ratio_r.loc[current_segment_start:segment_end]) - 1
            avg_daily_change = segment_change / segment_days if segment_days > 0 else 0
            
            continuous_rise_segments.append({
                'start_date': current_segment_start,
                'end_date': segment_end,
                'days': segment_days,
                'total_change': round(segment_change, 3),
                'avg_daily_change': round(avg_daily_change, 3)
            })
         
        mincumret_max_avg_change = 0
        mincumret_max_sum_change = 0
        mincumret_max_change_end_date = None
        mincumret_max_change_end_to_now_days = 0
        mincumret_max_change_lastdays = 0
        mincumret_rise_seg_over1_Num = 0
        if len(continuous_rise_segments) > 0:
            filtered_rise_segments = []
            filtered_rise_over1_segments = []
            for segment in continuous_rise_segments:
                if pd.to_datetime(segment['start_date']) >= pd.to_datetime(min_cumret_date) and segment['days'] > 2:
                    filtered_rise_segments.append(segment)
                if pd.to_datetime(segment['start_date']) >= pd.to_datetime(min_cumret_date) and segment['days'] > 2 \
                    and segment['avg_daily_change'] > 1:
                    filtered_rise_over1_segments.append(segment)
        
            # 如果需要，可以将过滤后的结果排序或进行其他处理
            if len(filtered_rise_segments) > 0:
                mincumret_max_segment = max(filtered_rise_segments, key=lambda x: x['avg_daily_change'])
                mincumret_max_avg_change = mincumret_max_segment['avg_daily_change']
                mincumret_max_sum_change = mincumret_max_segment['total_change']
                mincumret_max_change_end_date = mincumret_max_segment['end_date']
                mincumret_max_change_end_to_now_days = len(diff_ratio_r.loc[mincumret_max_change_end_date:]) - 1
                mincumret_max_change_lastdays = len(diff_ratio_r.loc[mincumret_max_segment['start_date']:mincumret_max_change_end_date]) - 1
                
            mincumret_rise_seg_over1_Num = len(filtered_rise_over1_segments)
         
        # 识别end2max_rise_enddate至min_cumret_date之间的连续下跌区段
        mincumret_decline_segments = []
        decline_current_segment_start = None
        diff_ratio_d = sw_diffratio.loc[end2max_rise_enddate:min_cumret_date, column].copy()

        # 遍历从end2max_rise_enddate到min_cumret_date的区间
        for i in range(len(diff_ratio_d) - 1):
            current_date = diff_ratio_d.index[i]
            next_date = diff_ratio_d.index[i + 1]
            
            # 如果下一天的值低于当天，表示下跌
            if diff_ratio_d.loc[next_date] < diff_ratio_d.loc[current_date]:
                # 如果还没有开始一个新的下跌区段，则标记开始
                if decline_current_segment_start is None:
                    decline_current_segment_start = current_date
            # 如果不是下跌且已经有一个下跌区段在进行中，则结束当前区段
            elif decline_current_segment_start is not None:
                segment_end = current_date
                segment_change = diff_ratio_d.loc[segment_end] - diff_ratio_d.loc[decline_current_segment_start]
                segment_days = len(diff_ratio_d.loc[decline_current_segment_start:segment_end]) - 1
                avg_daily_change = segment_change / segment_days if segment_days > 0 else 0
                
                mincumret_decline_segments.append({
                    'start_date': decline_current_segment_start,
                    'end_date': segment_end,
                    'days': segment_days,
                    'total_change': segment_change,
                    'avg_daily_change': avg_daily_change
                })
                
                decline_current_segment_start = None

        # 处理最后一个可能的下跌区段
        if decline_current_segment_start is not None:
            segment_end = diff_ratio_d.index[-1]
            segment_change = diff_ratio_d.loc[segment_end] - diff_ratio_d.loc[decline_current_segment_start]
            segment_days = len(diff_ratio_d.loc[decline_current_segment_start:segment_end]) - 1
            avg_daily_change = segment_change / segment_days if segment_days > 0 else 0
            
            mincumret_decline_segments.append({
                'start_date': decline_current_segment_start,
                'end_date': segment_end,
                'days': segment_days,
                'total_change': round(segment_change, 3),
                'avg_daily_change': round(avg_daily_change, 3)
            })

        # 计算最大快速下跌区段的指标
        mincumret_max_decline_avg_change = 0
        mincumret_max_decline_sum_change = 0
        mincumret_max_decline_end_date = None
        mincumret_max_decline_lastdays = 0
        mincumret_decline_seg_und1_num = 0

        if len(mincumret_decline_segments) > 0:
            filtered_decline_segments = []
            filtered_decline_und1_segments = []
            for segment in mincumret_decline_segments:
                if segment['days'] > 2:
                    filtered_decline_segments.append(segment)
                if segment['days'] > 2 and segment['avg_daily_change'] < -1:
                    filtered_decline_und1_segments.append(segment)
            
            # 找出平均日跌幅最大的区段
            if len(filtered_decline_segments) > 0:
                mincumret_max_decline_segment = min(filtered_decline_segments, key=lambda x: x['avg_daily_change'])
                mincumret_max_decline_avg_change = mincumret_max_decline_segment['avg_daily_change']
                mincumret_max_decline_sum_change = mincumret_max_decline_segment['total_change']
                mincumret_max_decline_end_date = mincumret_max_decline_segment['end_date']
                mincumret_max_decline_lastdays = len(diff_ratio_d.loc[mincumret_max_decline_segment['start_date']:mincumret_max_decline_end_date]) - 1
            
            # mincumret_decline_seg_und1_num = len(filtered_decline_und1_segments)
         
        cumret_temp_df = pd.DataFrame(
            {'indus': column, 
             'MaxDrop': maxdrop, 
             'start_date': drop_startdate, 
             'end_date': drop_enddate,
             'Drop_Lastdays': len(diff_ratio.loc[drop_startdate:drop_enddate]),
             'End2Max_Ratio': round((diff_ratio.loc[drop_enddate:].max() - diff_ratio.loc[drop_enddate]), 3),
             'End2Max_LastDays': len(diff_ratio.loc[drop_enddate:end2max_rise_enddate]) - 1,
             'End2Max_MaxDate': end2max_rise_enddate,
             'MinCumRet_Date': min_cumret_date, 
             'MinCumRet_SumRatio': round(min_cumret_sumratio, 3), 
             'MinCumRet_AvgRatio': round(min_cumret_avgratio, 3),
             'MinCumRet_DropDays': min_cumret_dropdays,
             'Ret2Now_Ratio': round(ret2now_ratio, 3),
             'Ret2Now_LastDays': ret2now_lastdays,
             'Ret2Now_AvgRatio': round(ret2now_ratio / ret2now_lastdays, 3) if ret2now_lastdays > 0 else 0,
             'RecentTurn2Now_Ratio': round(diff_ratio.iloc[-1]-diff_ratio.loc[index_preturn_date], 3),
             'Ret2Now_MaxDate': ret2now_enddate,
             'RetMax2Now_LastDays': retenddate2now_lastdays,
             'MinCumRet_Seg_MaxRise_EndDate': mincumret_max_change_end_date,
             'MinCumRet_Seg_MaxRise_Sum': round(mincumret_max_sum_change, 3),
             'MinCumRet_Seg_MaxRise_Avg': round(mincumret_max_avg_change, 3),
             'MinCumRet_Seg_MaxRise_LastDays': mincumret_max_change_lastdays,
             'MinCumRet_Seg_MaxRise_End_To_Now_Days': mincumret_max_change_end_to_now_days,
             'MinCumRet_Seg_Over1_Num': mincumret_rise_seg_over1_Num,
             # 新增字段：最大快速下跌区段相关指标
             'MinCumRet_Seg_MaxDecline_EndDate': mincumret_max_decline_end_date,
             'MinCumRet_Seg_MaxDecline_Sum': round(mincumret_max_decline_sum_change, 3),
             'MinCumRet_Seg_MaxDecline_Avg': round(mincumret_max_decline_avg_change, 3),
             'MinCumRet_Seg_MaxDecline_LastDays': mincumret_max_decline_lastdays,
            #  'MinCumRet_Seg_Und1_Num': mincumret_decline_seg_und1_num,
             }, index=range(0, 1))
        
        if not cumret_temp_df.empty and not cumret_temp_df.isna().all().all():
            Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)
        
        # 计算最大上涨幅度
        max_rise, rise_startdate, rise_enddate = maxrise_for_swindex(
            diff_ratio_r.loc[diff_ratio_r.loc[start_date:].idxmin():])
        
        # 获取最大变动数值及其结束日期
        recent_rise_avg_change = 0
        recent_rise_sum_change = 0
        recent_rise_end_date = None
        recent_rise_lastdays = 0
        
        max_rise_avg_change = 0
        max_rise_sum_change = 0
        max_rise_end_date = None
        max_rise_lastdays = 0
        
        rise_seg_over1_Num = 0
        
        if len(continuous_rise_segments) > 0:
            rise_filtered_segments = []
            rise_filtered_over1_segments = []
            for segment in continuous_rise_segments:
                if pd.to_datetime(segment['end_date']) <= pd.to_datetime(rise_enddate) and segment['days'] > 2:
                    rise_filtered_segments.append(segment)
                if pd.to_datetime(segment['end_date']) <= pd.to_datetime(rise_enddate) and segment['days'] > 2 \
                    and segment['avg_daily_change'] > 1:
                    rise_filtered_over1_segments.append(segment)
                    
            if rise_filtered_segments:
                recent_rise_segment = rise_filtered_segments[-1]
                recent_rise_avg_change = recent_rise_segment['avg_daily_change']
                recent_rise_sum_change = recent_rise_segment['total_change']
                recent_rise_end_date = recent_rise_segment['end_date']
                recent_rise_lastdays = len(diff_ratio_r.loc[recent_rise_segment['start_date']:recent_rise_end_date]) - 1
                
                max_rise_segment = max(rise_filtered_segments, key=lambda x: x['avg_daily_change'])
                max_rise_avg_change = max_rise_segment['avg_daily_change']
                max_rise_sum_change = max_rise_segment['total_change']
                max_rise_end_date = max_rise_segment['end_date']
                max_rise_lastdays = len(diff_ratio_r.loc[max_rise_segment['start_date']:max_rise_end_date]) - 1
                
            rise_seg_over1_Num = len(rise_filtered_over1_segments)
        
        # if column == '机械设备':
        #     print('机械设备')
        
        try:
            rise_temp_df = pd.DataFrame(
                {'indus': column,
                'MaxRise': max_rise,
                'AvgRatio': round(max_rise / len(diff_ratio.loc[rise_startdate:rise_enddate]), 3),
                'start_date': rise_startdate, 'end_date': rise_enddate,
                'Rise_Lastdays': len(diff_ratio.loc[rise_startdate:rise_enddate]),
                'Recent_Rise_EndDate': recent_rise_end_date,
                'Recent_Rise_Sum': round(recent_rise_sum_change, 3),
                'Recent_Rise_Avg': round(recent_rise_avg_change, 3), 
                'Recent_Rise_LastDays': recent_rise_lastdays, 
                'Recent2Max_Ratio': round(recent_rise_sum_change / max_rise, 3) if max_rise != 0 else 0,
                'RiseEnd2Now_Lastdays': len(diff_ratio.loc[rise_enddate:]),
                'Rise_Seg_MaxRise_EndDate': max_rise_end_date,
                'Rise_Seg_MaxRise_Sum': round(max_rise_sum_change, 3),
                'Rise_Seg_MaxRise_Avg': round(max_rise_avg_change, 3),
                'Rise_Seg_MaxRise_LastDays': max_rise_lastdays,
                'Rise_Seg_Over1_Num': rise_seg_over1_Num,
                }, index=range(0, 1))
        except Exception as e:
            print(f"Error in cal_swindex_state: {e}")
            print('indus:', column)
        
        if not rise_temp_df.empty and not rise_temp_df.isna().all().all():
            Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)
            
    return Bottom_List, Cumret_List, Rise_List


def cal_prepeak_date(stock_indexhq, end_date):
    """获取当前日期向前回溯的前一波峰位置的日期时点"""
    # stock_indexhq = get_zzindex_data(index_code='sh000001')
    periodchang, _ = period_stat(stk_close=stock_indexhq['close'],
                                 stk_open=stock_indexhq['open'],
                                 end_date=end_date,
                                 style='Index')
    if periodchang['avg_ratio'].iloc[-1] > 0:
        turn_date = periodchang['end_date'].iloc[-2]
    else:
        turn_date = periodchang['end_date'].iloc[-1]
    if periodchang['avg_ratio'].iloc[-1] > 0 \
            and len(periodchang) > 2 \
            and stock_indexhq.loc[periodchang['start_date'].iloc[-1], 'close'] > \
            stock_indexhq.loc[periodchang['start_date'].iloc[-3], 'close']:
        preperiod_startdate = periodchang['end_date'].iloc[-3]
    else:
        period_preturn = periodchang.query('start_date<@turn_date & avg_ratio<0')
        if len(period_preturn) > 1:
            pre_num = -2
            while abs(pre_num) < len(period_preturn) \
                    and stock_indexhq.loc[period_preturn['start_date'].iloc[pre_num], 'close'] > \
                    stock_indexhq.loc[period_preturn['start_date'].iloc[pre_num + 1], 'close'] \
                    and stock_indexhq.loc[period_preturn['end_date'].iloc[pre_num], 'close'] > \
                    stock_indexhq.loc[period_preturn['start_date'].iloc[-1]:, 'close'].min():
                pre_num -= 1
            preperiod_startdate = period_preturn['start_date'].iloc[pre_num + 1]
        else:
            preperiod_startdate = period_preturn['start_date'].iloc[-1] if len(period_preturn) > 0 else turn_date
    start_date = (pd.to_datetime(end_date, format='%Y-%m-%d') - relativedelta(years=1)).strftime('%Y-%m-%d')
    if start_date > preperiod_startdate and start_date < turn_date:
        preperiod_startdate = stock_indexhq.loc[start_date:turn_date, 'close'].idxmax()
    elif start_date > preperiod_startdate and start_date >= turn_date:
        preperiod_startdate = stock_indexhq.loc[start_date:end_date, 'close'].idxmax()
    return preperiod_startdate


def judge_index_valley(end_date, prepeak_date, bottom_list, rise_list, index_MinDate=None):
    """判定前一波峰位置以来是否出现指数波谷转折点,
        如是,返回强势行业
    """
    # 计算指数最低点日期
    zz_index_data = get_zzindex_data(mode='stkcloud')
    if index_MinDate is None:
        index_MinDate = zz_index_data.loc[prepeak_date:end_date, 'close'].idxmin()

    ## 判定指数触及低点为大幅下跌低点或者回档下行低点
    judge_startdate = (pd.to_datetime(prepeak_date, format='%Y-%m-%d') - relativedelta(years=2)).strftime('%Y-%m-%d')
    if zz_index_data.loc[index_MinDate, 'close'] < zz_index_data.loc[judge_startdate:prepeak_date,
                                                   'close'].min() * 0.95:
        bottom_style = 'LongBottom'
    else:
        bottom_style = 'ShortBottom'
    drop_middate = zz_index_data.loc[prepeak_date:index_MinDate].index[
        int(len(zz_index_data.loc[prepeak_date:index_MinDate]) / 2)]

    rise_list_adj = rise_list[['indus', 'end_date', 'RiseEnd2Now_Lastdays']].rename(
        columns={'end_date': 'Rise_EndDate'})
    bottom_list_adj = pd.merge(bottom_list.drop(
        columns=['Recent_Drop_LastDays', 'Recent_Drop_Avg', 'Recent_Drop_Sum', 'Recent_Drop_StartDate']),
                               rise_list_adj,
                               on='indus', how='left')
    bottom_list_1 = bottom_list_adj.query('Bottom_Date>=@index_MinDate & Bottom_Date<@end_date') \
        if index_MinDate < end_date \
        else bottom_list_adj.query('Bottom_Date>=@index_MinDate')

    bottom_list_2 = bottom_list_adj.query('(Bottom_Date>=@prepeak_date & '
                                          'Bottom2Max_Ratio>10 & Drop_Lastdays>=90) | Bottom_Date==@index_MinDate')
    # predrop_adj = (pd.to_datetime(prepeak_date, format='%Y-%m-%d') + relativedelta(days=5)).strftime('%Y-%m-%d')
    indexmin_adj = (pd.to_datetime(index_MinDate, format='%Y-%m-%d') + relativedelta(days=5)).strftime('%Y-%m-%d')
    # bottom_list_3 = bottom_list_adj.query('Bottom_State=="True" & Bottom_Date>=@prepeak_date & '
    #                                       'RiseSec_IdxSum<0 & DownPeriod_Ratio>0')
    # # bottom_list_2 = pd.concat([bottom_list_2, bottom_list_3], ignore_index=True)
    bottom_list_2 = bottom_list_2.drop_duplicates(subset=['indus'], keep='last')
    # if bottom_style == 'ShortBottom':
    #     qsindus_list = bottom_list_2.query('Bottom_Date<=@drop_middate & Bottom_State=="True"').sort_values(
    #         'Bottom2Max_Ratio', ascending=False).iloc[:min(6, len(bottom_list_2))].copy()
    # else:
    #     qsindus_list = bottom_list_adj.query('end_date>=@index_MinDate & end_date<=@indexmin_adj').sort_values(
    #         'MaxDrop', ascending=True).iloc[:min(6, len(bottom_list_2))].copy()
    # if len(bottom_list_2.query('Bottom2Max_Ratio>10 & Bottom_State=="True"')) > 0:
    #     qsindus_list = bottom_list_2.query('Bottom2Max_Ratio>10 & Bottom_State=="True"').sort_values(
    #         'Bottom2Max_Ratio', ascending=False).iloc[:min(6, len(bottom_list_2))].copy()
    # else:
    #     qsindus_list = bottom_list_2.sort_values(
    #             'Bottom2Max_Ratio', ascending=False).iloc[:min(6, len(bottom_list_2))].copy()
    # if len(bottom_list_3) > 0:
    #     qsindus_list = bottom_list_3.sort_values(
    #         'Bottom2Max_Ratio', ascending=False).iloc[:min(8, len(bottom_list_3))].copy()
    if len(bottom_list_2) > 0:
        qsindus_list = bottom_list_2.query('Rise_EndDate>@index_MinDate').sort_values(
            'Bottom2Max_Ratio', ascending=False).iloc[:min(
            8, len(bottom_list_2.query('Rise_EndDate>@index_MinDate')))].copy()
    else:
        qsindus_list = bottom_list_adj.query('end_date>=@index_MinDate & end_date<=@indexmin_adj').sort_values(
            'MaxDrop', ascending=True).iloc[:min(8, len(bottom_list_2))].copy()

    if len(bottom_list_1) >= 2:
        # print('市场出现波谷转折点，日期为：', index_MinDate)
        Index_BottomDate = index_MinDate
    else:
        Index_BottomDate = None
    combo_num = len(bottom_list_1)
    # if len(qsindus_list) > 0:
    #     qsindus_list['indus'] = qsindus_list.apply(lambda fn: fn['indus'][:-4], axis=1)
    return Index_BottomDate, qsindus_list, combo_num, bottom_style


def judge_index_peak(end_date, index_bottomdate, bottom_list, qsindus_list):
    """判定是否出现指数波峰转折点,
        如是,返回继任强势行业
    """
    rise_enddate = qsindus_list['Rise_EndDate'].max()
    zz_indexdata = get_zzindex_data(mode='stkcloud')
    zz_maxdate = zz_indexdata.loc[index_bottomdate:end_date, 'high'].idxmax()
    price_limit = zz_indexdata.loc[zz_maxdate, 'close'] - \
                  (zz_indexdata.loc[zz_maxdate, 'close'] - zz_indexdata.loc[index_bottomdate, 'close'])*0.3
    if len(qsindus_list) > 0 \
            and len(zz_indexdata.loc[rise_enddate:end_date]) >= 10 \
            and len(zz_indexdata.loc[index_bottomdate:end_date]) > 60 \
            and (len(zz_indexdata.loc[zz_maxdate:end_date]) > 20 or
                 zz_indexdata.loc[zz_maxdate:end_date, 'close'].min() <= price_limit or
                 zz_indexdata.loc[zz_maxdate:, 'close'].min()/zz_indexdata.loc[zz_maxdate, 'close']-1 < -0.2):
        # print('市场出现波峰转折点，日期为：', rise_enddate)
        Index_PeakDate = zz_indexdata.loc[index_bottomdate:end_date, 'close'].idxmax()
        if len(bottom_list.query('Bottom_Date>=@rise_enddate')) > 0:
            jrqs_induslist = bottom_list.query('Bottom_Date>=@rise_enddate').sort_values(
                'MaxDrop', ascending=True).iloc[:min(3, len(bottom_list.query('Bottom_Date>=@rise_enddate')))].copy()
            # jrqs_induslist['indus'] = jrqs_induslist.apply(lambda fn: fn['indus'][:-4], axis=1)
        else:
            jrqs_induslist = None
            # print('指数顶点出现,但尚未出现见底行业')
    else:
        Index_PeakDate = None
        jrqs_induslist = None
    return Index_PeakDate, jrqs_induslist


def cal_sw_periodratio(start_date=None, end_date=None):
    """计算申万行业指数指定日期区间的收益率"""
    if end_date is None:
        end_date = (pd.to_datetime(start_date, format='%Y-%m-%d') + relativedelta(months=6)).strftime('%Y-%m-%d')
    sw_index_df = get_swindex_data(start_date=start_date, end_date=end_date)
    period_ratio = sw_index_df.loc[start_date:end_date, :].max() / sw_index_df.loc[start_date, :] - 1
    period_ratio = period_ratio.sort_values(ascending=False)
    return period_ratio


def cal_recent_valley(end_date=None, bottom_list=None, recent_peakdate=None, predrop_startdate=None):
    """测算近期行业指数转折点位置"""
    if end_date is None:
        zz_index = get_zzindex_data(mode='stkcloud')
        end_date = zz_index.index[-1]
    start_date = (pd.to_datetime(predrop_startdate, format='%Y-%m-%d') - relativedelta(years=2)).strftime('%Y-%m-%d')
    sw_diffratio = cal_sw_diffratio(start_date=start_date, end_date=end_date)
    swidx_data = get_swindex_data(start_date=start_date, end_date=end_date)
    sw_index_ratio = (swidx_data / swidx_data.iloc[0] - 1) * 100

    recent_valley_list = pd.DataFrame()
    for column in sw_diffratio.columns:
        drop_enddate = bottom_list.query('indus==@column')['end_date'].iloc[-1]
        indus_mv = bottom_list.query('indus==@column')['Indus_MV'].iloc[-1]
        # bottom_date = bottom_list.query('indus==@column')['Bottom_Date'].iloc[-1]
        bottom_state = bottom_list.query('indus==@column')['Bottom_State'].iloc[-1]
        end2max_ratio = bottom_list.query('indus==@column')['End2Max_Ratio'].iloc[-1]
        postallbottom_drop2rise = bottom_list.query('indus==@column')['PostAllBottom_Drop2Rise'].iloc[-1]
        postallbottom_lastdays = bottom_list.query('indus==@column')['PostAllBottom_LastDays'].iloc[-1]
        diff_ratio = sw_diffratio[column]
        sw_ratio = sw_index_ratio[column]
        rela_prepeak_date = diff_ratio.loc[drop_enddate:recent_peakdate].idxmax() \
            if drop_enddate < recent_peakdate else diff_ratio.loc[drop_enddate:].idxmax()
        rela_valley_date = diff_ratio.loc[rela_prepeak_date:].idxmin()
        rela_postvalley_ratio = diff_ratio.iloc[-1] - diff_ratio.loc[rela_valley_date]
        rela_prevalley_ratio = diff_ratio.loc[rela_valley_date] - diff_ratio.loc[rela_prepeak_date]
        rela_prevalley_lastdays = len(diff_ratio.loc[rela_prepeak_date:rela_valley_date])
        recent_valley_date = sw_ratio.loc[recent_peakdate:].idxmin()
        recent_postvalley_ratio = sw_ratio.iloc[-1] - sw_ratio.loc[recent_valley_date]
        postenddate_drop2rise = bottom_list.query('indus==@column')['PostDropEnd_Drop2Rise'].iloc[-1]
        valleydate_state = 'True' if diff_ratio.loc[rela_prepeak_date:].idxmin() == rela_valley_date else '-'
        temp_df = pd.DataFrame({'indus': column,
                                'Indus_MV': indus_mv,
                                'Bottom_State': bottom_state,
                                'End2Max_Ratio': end2max_ratio,
                                'Drop_EndDate': drop_enddate,
                                'Rela_PreValley_PeakDate': rela_prepeak_date,
                                'Rela_ValleyDate': rela_valley_date,
                                'Rela_PreValley_LastDays': rela_prevalley_lastdays,
                                'PostDropEnd_Drop2Rise': postenddate_drop2rise,
                                'PostAllBottom_Drop2Rise': postallbottom_drop2rise,
                                'PostAllBottom_LastDays': postallbottom_lastdays,
                                'ValleyDate_State': valleydate_state,
                                'Rela_PreValley_Ratio': rela_prevalley_ratio,
                                'Rela_PostValley_Ratio': rela_postvalley_ratio,
                                'SW_Recent_ValleyDate': recent_valley_date,
                                'SW_PostValley_Ratio': recent_postvalley_ratio
                                },
                               index=range(0, 1))
        recent_valley_list = pd.concat([recent_valley_list, temp_df], ignore_index=True)
    recent_valley_list['Turn_Match'] = recent_valley_list.apply(
        lambda fn: 'True' if fn['Rela_ValleyDate'] == fn['SW_Recent_ValleyDate'] else '-', axis=1)
    recent_valley_list = recent_valley_list.sort_values(by=['Rela_ValleyDate', 'End2Max_Ratio'],
                                                        ascending=[False, False])
    return recent_valley_list


if __name__ == '__main__':
    # level1_data = level1_update()
    # level2_data = level2_update()

    # sw_data = initialize_level1()

    # end_date = '2023-05-08'
    # # peak_testdate = '2020-09-11'
    # # pre_valleydate = None
    # # zzindex_daily_df = get_zzindex_data(mode='stkcloud')
    # # prepeak_date = cal_prepeak_date(zzindex_daily_df, end_date)
    # prepeak_date = '2023-01-30'
    # Bottom_List, Rise_List = cal_swindex_state(end_date=end_date, predrop_startdate=prepeak_date)
    # Index_BottomDate, Qs_List, Combo_Num, bottom_style = judge_index_valley(end_date=end_date,
    #                                                                         prepeak_date=prepeak_date,
    #                                                                         bottom_list=Bottom_List,
    #                                                                         rise_list=Rise_List)

    # prepeak_date = cal_prepeak_date(zzindex_daily_df, peak_testdate)
    # Bottom_List, Rise_List = cal_swindex_state(end_date=peak_testdate, predrop_startdate=prepeak_date)
    # index_peakdate, jrqs_induslist = judge_index_peak(end_date=peak_testdate,
    #                                                   index_bottomdate=Index_BottomDate,
    #                                                   bottom_list=Bottom_List,
    #                                                   qsindus_list=Qs_List)

    bottomlist, riselist = cal_swindex_state(end_date='2025-02-25', index_preturn_date='2025-02-25')

    # indus = '电力设备'
    # end_date = '2024-04-30'
    # years, months = 1, 6
    # start_date = (pd.to_datetime(end_date, format='%Y-%m-%d') - relativedelta(years=years)).strftime('%Y-%m-%d')
    # end_date_adj = (pd.to_datetime(end_date, format='%Y-%m-%d') + relativedelta(months=months)).strftime('%Y-%m-%d')
    # diff_ratio = cal_sw_diffratio(start_date=start_date, end_date=end_date_adj, indus=indus)
    # swidx_data = get_swindex_data(start_date=start_date, end_date=end_date_adj)[indus]
    # sw_ratio = (swidx_data / swidx_data.iloc[0] - 1) * 100