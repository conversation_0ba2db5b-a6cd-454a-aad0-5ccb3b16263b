# 测试筛选标准，如何筛选强势行业
import pdb

import pandas as pd
import numpy as np
from function_ai.Func_Base import get_Result_First, get_Result_Second, get_stock_data, period_stat, \
    section_stat, cal_target_price
from tqdm import tqdm

if __name__ == '__main__':
    # 输入指数转折点
    index_turn = '2021-12-20'
    end_date = '2021-12-31'
    test_end_date = '2021-12-31'
    # 输入判定符号
    JudgeN = input('是否重新计算数据：(Y/N)')

    # 获取一级筛选结果和行情数据
    Result_First = get_Result_First(end_date=end_date)
    Stock_Data = get_stock_data(end_date=test_end_date)
    trade_date = Stock_Data['trade_date'].unique()

    stock_info = pd.read_excel('/Users/<USER>/PycharmProjects/AI_Stock/dataset/全部A股行业归属.xlsx')
    indus_num1 = stock_info['ts_code'].groupby(stock_info['industry_one']).count()
    indus_num2 = stock_info['ts_code'].groupby(stock_info['industry_two']).count()
    indus_num = pd.DataFrame(indus_num1.append(indus_num2))
    indus_num.columns = ['Indus_Num']

    if JudgeN == 'Y' or JudgeN == 'y':
        Result_First['PostTurn_P_AvgRatio'] = np.nan  # 平均上涨幅度
        Result_First['PostTurn_P_SumRatio'] = np.nan  # 累计上涨幅度
        Result_First['PostTurn_P_LastDays'] = np.nan  # 上行区间持续天数
        Result_First['PostTurn_S_ContiRiseDays'] = np.nan  # 最大连续上涨天数
        Result_First['PostTurn_S_RecentDays'] = np.nan  # 最近连续运行天数
        Result_First['PostTurn_S_SlowRiseDays'] = np.nan  # 上行趋势平均上涨幅度
        Result_First['RiseDayRatio'] = np.nan  # 上行趋势上涨天数占比
        Result_First['PostTurn_P_StartDate'] = np.nan  # 最近上行趋势起始日期
        Result_First['BreakPeak_Days'] = np.nan  # 当前突破多少日新高
        Result_First['BreakPeak_Days_10'] = np.nan  # 上涨10%突破多少个交易日新高
        Result_First['BreakPeak_Days_20'] = np.nan  # 上涨10%突破多少个交易日新高
        Result_First['BreakPeak_Days_30'] = np.nan  # 上涨10%突破多少个交易日新高
        Result_First['Days_Gap_10'] = np.nan  # 上涨10%突破交易日天数与当前突破差值
        Result_First['Days_Gap_20'] = np.nan  # 上涨10%突破交易日天数与当前突破差值
        Result_First['Days_Gap_30'] = np.nan  # 上涨10%突破交易日天数与当前突破差值
        Result_First['PreTurn_P_AvgRatio'] = np.nan  # 前一下行趋势平均下行幅度
        Result_First['PreTurn_P_SumRatio'] = np.nan  # 前一下行趋势累计下行幅度
        Result_First['PreTurn_P_LastDays'] = np.nan  # 前一下行趋势持续天数
        Result_First['PreTurn_S_ContiDropDays'] = np.nan  # 前一下行趋势连续下行天数
        Result_First['Rise_S_Num'] = np.nan  # 拉升区段数量
        Result_First['Bottom_RiseRatio'] = np.nan  # 自突破区间最低点升幅
        Result_First['Bottom_LastDays_10'] = np.nan  # 最低点距当前天数

        # Result_First = Result_First.query('ts_code=="002370.SZ"')

        # 循环筛选
        for index in tqdm(Result_First.index):
            ts_code = Result_First.loc[index, 'ts_code']
            stk_data = Stock_Data.query('ts_code==@ts_code').sort_values('trade_date', ascending=True).set_index(
                'trade_date', drop=False)
            periodchang, trend_stat = period_stat(stk_close=stk_data['close'],
                                                  stk_open=stk_data['open'], end_date=end_date)
            if len(periodchang) > 0 and periodchang['sum_ratio'].iloc[-1] > 0:
                minturn = min(Result_First.loc[index, 'Turn_Date'], Result_First.loc[index, 'Lowloc_Date'])
                section_rise, _, day_rise = section_stat(stk_data=stk_data,  # stk_close=stk_data['close'], stk_open=stk_data['open'],
                                                         s_date=periodchang['start_date'].iloc[-1],
                                                         e_date=periodchang['end_date'].iloc[-1])
                _, section_drop, day_drop = section_stat(stk_data=stk_data,  # stk_close=stk_data['close'], stk_open=stk_data['open'],
                                                         s_date=periodchang['start_date'].iloc[-2],
                                                         e_date=periodchang['end_date'].iloc[-2])
                section_count, _, _ = section_stat(stk_data=stk_data,  # stk_close=stk_data['close'], stk_open=stk_data['open'],
                                                   s_date=minturn,
                                                   e_date=periodchang['end_date'].iloc[-1])
                section_slow_rise = section_rise.query('avgratio<1.5 & lastdays>=5')
                section_slow_drop = section_drop.query('avgratio>-1.5 & lastdays>=5')
                # 计算目标价格及收益率
                Target_Price, _ = cal_target_price(Result_First.loc[index, 'Turn_Date'], stk_data, periodchang)
                Result_First.loc[index, 'Target_Price'] = Target_Price
                Result_First.loc[index, 'Target_Ratio'] = (Target_Price / stk_data['close'].iloc[-1] - 1) * 100
                Result_First.loc[index, 'Long_State'] = trend_stat['long_state'].iloc[-1]
                Result_First.loc[index, 'PostTurn_P_AvgRatio'] = round(periodchang['avg_ratio'].iloc[-1], 2)
                Result_First.loc[index, 'PostTurn_P_SumRatio'] = round(
                    stk_data.loc[periodchang['start_date'].iloc[-1]:, 'close'].max()
                    / stk_data.loc[periodchang['start_date'].iloc[-1], 'close'] - 1, 2)
                Result_First.loc[index, 'PostTurn_P_LastDays'] = periodchang['last_days'].iloc[-1]
                Result_First.loc[index, 'PostTurn_S_ContiRiseDays'] = section_rise['lastdays'].max()
                Result_First.loc[index, 'PostTurn_S_SlowRiseDays'] = section_slow_rise['lastdays'].max() \
                    if len(section_slow_rise) > 0 else np.nan
                pick_mode = '涨' if day_rise['day_state'].iloc[-1] == '涨' else '跌'
                count_num = -1
                while abs(count_num) < len(day_rise) and day_rise['day_state'].iloc[count_num] == pick_mode:
                    count_num -= 1
                Result_First.loc[index, 'PostTurn_S_RecentDays'] = abs(count_num + 1) \
                    if pick_mode == '涨' else -abs(count_num + 1)
                Result_First.loc[index, 'RiseDayRatio'] = len(day_rise.query('day_state=="涨"')) / len(day_rise)
                Result_First.loc[index, 'PostTurn_P_StartDate'] = periodchang['start_date'].iloc[-1]
                max_close = stk_data.loc[periodchang['end_date'].iloc[-1], 'close']
                cal_enddate = periodchang['start_date'].iloc[-1] \
                    if stk_data['close'].iloc[-1] > min(stk_data['close'].iloc[-4:-1].min(),
                                                        stk_data['open'].iloc[-4:-1].min()) \
                    else periodchang['end_date'].iloc[-1]
                Break_Peak_Days = stk_data.loc[:cal_enddate].query('close>=@max_close').index[-1] \
                    if len(stk_data.loc[:cal_enddate].query('close>=@max_close')) > 0 \
                    else stk_data.index[0]
                Break_Peak_Days_10 = stk_data.loc[:cal_enddate].query('close>=@max_close*1.1').index[-1] \
                    if len(stk_data.loc[:cal_enddate].query('close>=@max_close*1.1')) > 0 \
                    else stk_data.index[0]
                Break_Peak_Days_20 = stk_data.loc[:cal_enddate].query('close>=@max_close*1.2').index[-1] \
                    if len(stk_data.loc[:cal_enddate].query('close>=@max_close*1.2')) > 0 \
                    else stk_data.index[0]
                Break_Peak_Days_30 = stk_data.loc[:cal_enddate].query('close>=@max_close*1.3').index[-1] \
                    if len(stk_data.loc[:cal_enddate].query('close>=@max_close*1.3')) > 0 \
                    else stk_data.index[0]
                Result_First.loc[index, 'BreakPeak_Days'] = len(stk_data.loc[Break_Peak_Days:])
                Result_First.loc[index, 'BreakPeak_Days_10'] = len(stk_data.loc[Break_Peak_Days_10:])
                Result_First.loc[index, 'BreakPeak_Days_20'] = len(stk_data.loc[Break_Peak_Days_20:])
                Result_First.loc[index, 'BreakPeak_Days_20'] = len(stk_data.loc[Break_Peak_Days_30:])
                Result_First.loc[index, 'Days_Gap_10'] = len(stk_data.loc[Break_Peak_Days_10:]) - \
                                                      len(stk_data.loc[Break_Peak_Days:])
                Result_First.loc[index, 'Days_Gap_20'] = len(stk_data.loc[Break_Peak_Days_20:]) - \
                                                         len(stk_data.loc[Break_Peak_Days:])
                Result_First.loc[index, 'Days_Gap_30'] = len(stk_data.loc[Break_Peak_Days_30:]) - \
                                                         len(stk_data.loc[Break_Peak_Days:])
                Result_First.loc[index, 'Long_State'] = trend_stat['long_state'].iloc[-1]
                Result_First.loc[index, 'PreTurn_P_AvgRatio'] = round(periodchang['avg_ratio'].iloc[-2], 2)
                Result_First.loc[index, 'PreTurn_P_SumRatio'] = round(periodchang['sum_ratio'].iloc[-2], 2)
                Result_First.loc[index, 'PreTurn_P_LastDays'] = periodchang['last_days'].iloc[-2]
                Result_First.loc[index, 'PreTurn_S_ContiDropDays'] = section_drop['lastdays'].max()
                Result_First.loc[index, 'Rise_S_Num'] = len(section_count.query('(lastdays>=5 & (avgratio>2.5 | '
                                                                                'extre_ratio>7)) | sumratio>21'))
                Result_First.loc[index, 'Bottom_RiseRatio'] = round(
                    stk_data.loc[periodchang['start_date'].iloc[-1]:, 'close'].max() /
                    stk_data.loc[Break_Peak_Days_10:periodchang['start_date'].iloc[-1], 'close'].min() - 1, 2)
                Result_First.loc[index, 'Bottom_LastDays_10'] = len(
                    stk_data.loc[stk_data.loc[Break_Peak_Days_10:, 'close'].idxmin():])
        # Result_First.to_csv('/Users/<USER>/PycharmProjects/AI_Stock/res_data/CSV/Result_First_Test.csv',
        #                     index=False, encoding='utf-8-sig')
        #         if ts_code == '002370.SZ':
        #             pdb.set_trace()
    else:
        Result_First = pd.read_csv('/Users/<USER>/PycharmProjects/AI_Stock/res_data/CSV/Result_First_Test.csv')
    # 统计规律，按照行业标识分别统计
    Result_First['Turn_Same'] = Result_First['PostTurn_P_StartDate'].apply(
        lambda fn: 'Yes' if pd.notnull(fn) and 0 <= max(
            len(trade_date[(trade_date >= index_turn) & (trade_date <= fn)]),
            len(trade_date[(trade_date <= index_turn) & (trade_date >= fn)])) <= 3 else 'No')
    indus_name = Result_First['industry'].unique()
    Result_Test = pd.DataFrame(Result_First['ts_code'].groupby(Result_First['industry']).count())
    Result_Test.columns = ['First_Num']
    Result_Test = pd.merge(Result_Test, indus_num, how='left', left_index=True, right_index=True)

    # 转折点重合股票占比
    Result_First2 = Result_First.query('Turn_Same=="Yes"')
    testdata = pd.DataFrame(Result_First2['ts_code'].groupby(Result_First2['industry']).count())
    testdata.columns = ['Same_Num']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    Result_Test['ComRatio'] = Result_Test['Same_Num'] / Result_Test['first_num']
    # Result_Test['ComRatio'] = Result_Test.apply(
    #     lambda fn: round(testdata.loc[fn.index]/fn['first_num'] if fn.index in testdata.index.tolist() else 0, 2))
    # 转折点后平均升幅
    testdata = pd.DataFrame(Result_First.dropna(subset=
                                                ['PostTurn_P_AvgRatio'])['PostTurn_P_AvgRatio'].groupby(
        Result_First['industry']).mean())
    testdata.columns = ['PostTurn_P_AvgRatio']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    # 转折点后累计升幅
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['PostTurn_P_SumRatio'])['PostTurn_P_SumRatio'].groupby(Result_First['industry']).max())
    testdata.columns = ['PostTurn_P_SumRatio']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    # 转折点后持续天数
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['PostTurn_P_LastDays'])['PostTurn_P_LastDays'].groupby(Result_First['industry']).mean())
    testdata.columns = ['PostTurn_P_LastDays']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    # 转折点后持续上行天数
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['PostTurn_S_ContiRiseDays'])['PostTurn_S_ContiRiseDays'].groupby(Result_First['industry']).mean())
    testdata.columns = ['PostTurn_S_ContiRiseDays']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    # 转折点后缓步上行天数
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['PostTurn_S_SlowRiseDays'])['PostTurn_S_SlowRiseDays'].groupby(Result_First['industry']).mean())
    testdata.columns = ['PostTurn_S_SlowRiseDays']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    # 上涨天数占比
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['RiseDayRatio'])['RiseDayRatio'].groupby(Result_First['industry']).median())
    testdata.columns = ['RiseDayRatio']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    # 长趋势上行占比
    testdata = pd.DataFrame(Result_First.query(
        'Long_State=="上行"')['ts_code'].groupby(Result_First['industry']).count())
    testdata.columns = ['LongState_Num']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    Result_Test['LongStateRatio'] = Result_Test['LongState_Num'] / Result_Test['first_num']
    # 创N日新高
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['BreakPeak_Days_10'])['BreakPeak_Days_10'].groupby(Result_First['industry']).median())
    testdata.columns = ['BreakPeak_Days_10']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    # 天数差额
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['Days_Gap_10'])['Days_Gap_10'].groupby(Result_First['industry']).mean())
    testdata.columns = ['Days_Gap_10']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)

    # 转折点前平均升幅
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['PreTurn_P_AvgRatio'])['PreTurn_P_AvgRatio'].groupby(Result_First['industry']).mean())
    testdata.columns = ['PreTurn_P_AvgRatio']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    # 转折点前累计升幅
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['PreTurn_P_SumRatio'])['PreTurn_P_SumRatio'].groupby(Result_First['industry']).mean())
    testdata.columns = ['PreTurn_P_SumRatio']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    # 转折点前持续天数
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['PreTurn_P_LastDays'])['PreTurn_P_LastDays'].groupby(Result_First['industry']).mean())
    testdata.columns = ['PreTurn_P_LastDays']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)
    # 转折点前持续上行天数
    testdata = pd.DataFrame(Result_First.dropna(
        subset=['PreTurn_S_ContiDropDays'])['PreTurn_S_ContiDropDays'].groupby(Result_First['industry']).mean())
    testdata.columns = ['PreTurn_S_ContiDropDays']
    Result_Test = pd.merge(Result_Test, testdata, how='left', left_index=True, right_index=True)

    Result_Test.to_csv('/Users/<USER>/PycharmProjects/AI_Stock/res_data/CSV/Result_Test.csv',
                       index=False, encoding='utf-8-sig')
    Result_Test = Result_Test.sort_values('PostTurn_P_SumRatio', ascending=False)

    samnummean = Result_Test['Same_Num'].mean()
    commean = Result_Test['ComRatio'].mean()
    lsratiomean = Result_Test['LongStateRatio'].mean()
    firstnummean = Result_Test['first_num'].mean()
    Result_Test2 = Result_Test.query(
        'first_num>@firstnummean & (Same_Num>@samnummean | ComRatio>@commean | LongStateRatio>@lsratiomean)')

    Result_First = Result_First.sort_values(['industry', 'Days_Gap_10'], ascending=[True, False])

    Result_Test2 = Result_Test2.sort_values('PostTurn_P_SumRatio', ascending=False)
    Result_Test2.index.name = 'industry'
    Result_Test2 = Result_Test2.reset_index()

    indus_list = Result_Test2.index[:5].to_list()
    Result_Temp = Result_First.query('industry in @indus_list & Turn_Same=="Yes" & '
                                     'Rise_S_Num<3 & Days_Gap_20>=300').drop_duplicates(
                                     'ts_code', keep='last').sort_values('Days_Gap_20', ascending=False)
    Result_Temp = Result_Temp.drop(['PreTurn_Period_StartDate_Last', 'PreTurn_Period_EndDate_Last',
                                    'PostTurn_Period_Lastdays', 'PreTurn_Period_Lastdays_Last',
                                    'PreTurn_Period_SlowCountSumDays_Last', 'PostSlow_MaxDrop',
                                    'Recent5_Period_MaxAvgLastdays', 'PostTurn_Section_MaxAvgLastdays',
                                    'PostTurn_HighDailyRatioCount', 'PreTurn_Period_MaxLastdays_UP',
                                    'PreTurn_Period_MaxLastdays_Down'], axis=1)
    Result_Temp = Result_Temp.sort_values('PostTurn_P_SumRatio', ascending=False)

    Result_Second = get_Result_Second()
    stkcode_list = Result_Temp['ts_code'].unique()
    Result_Second = Result_Second.query('ts_code in @stkcode_list')

    # conf_m = config.configModel()
    # engine = create_engine(
    #     'mysql+pymysql://' + conf_m.DC_DB_USER + ':' + conf_m.DC_DB_PASS + '@' + conf_m.DC_DB_URL + ':' + str(
    #         conf_m.DC_DB_PORT) + '/StockSfit')
    # pd.io.sql.to_sql(result_loc, 'ResultsTwo_New', engine, index=False, schema='StockSfit', if_exists='append')
    # engine.dispose()