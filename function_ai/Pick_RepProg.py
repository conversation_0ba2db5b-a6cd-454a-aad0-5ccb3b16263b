"""每日跟踪筛选股票品种，定期更新转折点筛选标准"""
import pdb

import pandas as pd
from datetime import datetime
from function_ai.Func_Base import get_trade_date
from function_ai.StkPick_Func_V7 import stkpick_method_one, get_result_3
from sqlalchemy import create_engine


def daily_pick(Now_Date, Turn_Date='ALL'):
    """定期更新转折点信息，每日运行筛选结果存储为CSV文件"""
    # 转折点 2024-10-24
    # if Turn_Date == 'ALL' or Turn_Date == '2023-10-24':
    #     trade_dates = get_trade_date(start_date='2023-10-20', end_date='2023-10-24').tolist()
    #     result_pick, result_track, result_track1, result_track2 = Get_Result_Pick(
    #         section_startdate=trade_dates,
    #         Now_Date=Now_Date, relative_startdate='2023-08-23'
    #         , Diff_Num=2, storemode=True, pick_mode='NowTurn')
    #     print('转折点 2023-10-24 筛选结果已存储')

    # # 转折点 2023-12-27
    # if Turn_Date == 'ALL' or Turn_Date == '2023-12-27':
    #     trade_dates = get_trade_date(start_date='2023-11-16', end_date='2023-12-27').tolist()
    #     result_pick, result_track, result_track1, result_track2 = Get_Result_Pick(
    #         section_startdate=trade_dates,
    #         Now_Date=Now_Date, relative_startdate='2023-10-23'
    #         , Diff_Num=2, storemode=True, pick_mode='NowTurn')
    #     print('转折点 2023-12-27 筛选结果已存储')

    # 转折点 2024-01-23
    if (Turn_Date == 'ALL' or Turn_Date == '2024-01-23') and Now_Date > '2024-01-23':
        trade_dates = get_trade_date(start_date='2023-12-29', end_date='2024-01-23').tolist()
        result_pick, result_track, result_track1, result_track2 = stkpick_method_one(
            section_startdate=trade_dates,
            Now_Date=Now_Date, relative_startdate='2023-12-20'
            , Diff_Num=2, storemode=True, pick_mode='NowTurn')
        print('转折点 2024-01-23 筛选结果已存储')

    # 转折点 2024-02-05
    if (Turn_Date == 'ALL' or Turn_Date == '2024-02-05') and Now_Date > '2024-02-07':
        trade_dates = get_trade_date(start_date='2024-01-26', end_date='2024-02-07').tolist()
        # industry = ['计算机', '通信', '传媒']
        result_pick, result_track, result_track1, result_track2 = stkpick_method_one(
            section_startdate=trade_dates, now_secdate=None,
            Now_Date=Now_Date, relative_startdate='2024-01-23',
            Diff_Num=2, storemode=True, pick_mode='NowTurn')
        print('转折点 2024-02-05 筛选结果已存储')

    # 转折点 2024-03-27
    if (Turn_Date == 'ALL' or Turn_Date == '2024-03-27') and Now_Date > '2024-03-27':
        trade_dates = get_trade_date(start_date='2024-03-18', end_date='2024-03-27').tolist()
        # industry = ['计算机', '通信', '传媒']
        result_pick, result_track, result_track1, result_track2 = stkpick_method_one(
            section_startdate=trade_dates, now_secdate=None,
            Now_Date=Now_Date, relative_startdate='2024-02-07',
            Diff_Num=2, storemode=True, pick_mode='NowTurn')
        print('转折点 2024-03-27 筛选结果已存储')

    # 转折点 2024-04-16
    if (Turn_Date == 'ALL' or Turn_Date == '2024-04-16') and Now_Date > '2024-04-16':
        trade_dates = get_trade_date(start_date='2024-04-01', end_date='2024-04-16').tolist()
        # industry = ['计算机', '通信', '传媒']
        result_pick, result_track, result_track1, result_track2 = stkpick_method_one(
            section_startdate=trade_dates, now_secdate=None,
            Now_Date=Now_Date, relative_startdate='2024-03-18',
            Diff_Num=2, storemode=True, pick_mode='NowTurn')
        print('转折点 2024-04-16 筛选结果已存储')

    # 转折点 2024-05-24
    if (Turn_Date == 'ALL' or Turn_Date == '2024-05-24') and Now_Date >= '2024-05-24':
        trade_dates = get_trade_date(start_date='2024-05-20', end_date='2024-05-24').tolist()
        # industry = ['计算机', '通信', '传媒']
        result_pick, result_track, result_track1, result_track2 = stkpick_method_one(
            section_startdate=trade_dates, now_secdate=None,
            Now_Date=Now_Date, relative_startdate='2024-04-01',
            Diff_Num=2, storemode=True, pick_mode='NowTurn')
        print('转折点 2024-05-24 筛选结果已存储')

    return


def get_stocktrack_result(Now_Date=None, Turn_Date=None, mode='Valley'):
    """获取stocktrack筛选结果"""
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    sql_name = 'stocksfit.stocktrack_valley' if mode.lower() == 'valley' else 'stocksfit.stocktrack_all'
    if isinstance(Now_Date, str):
        Now_Date = [Now_Date]
    if isinstance(Turn_Date, str):
        Turn_Date = [Turn_Date]
    Now_Date_str = ','.join([f"'{i}'" for i in Now_Date]) if Now_Date is not None else None
    Turn_Date_str = ','.join([f"'{i}'" for i in Turn_Date]) if Turn_Date is not None else None
    if Turn_Date is None and Now_Date is not None:
        sql = f"""select * from {sql_name} where Pick_NowDate in ({Now_Date_str})"""
    elif Turn_Date is not None and Now_Date is None:
        sql = f"""select * from {sql_name} where Pick_TurnDate in ({Turn_Date_str})"""
    elif Turn_Date is not None and Now_Date is not None:
        sql = f"""select * from {sql_name} where Pick_NowDate in ({Now_Date_str}) 
        and Pick_TurnDate in ({Turn_Date_str})"""
    else:
        print('参数错误')
        return
    result = pd.read_sql_query(sql, engine)
    return result


def get_gapvalue_state(stk_code=None, result=None, end_date=None):
    """获取股票间隔值状态"""
    if end_date is None:
        end_date = get_trade_date(loc=-1)
    if result is None:
        result = get_result_3(end_date=end_date)
    if stk_code is not None:
        result = result.query('ts_code == @stk_code')
    print('股票 ', result['name'].iloc[-1], ' 的区间GapValue数值为: ')
    print('Median_PeakGap: ', result['PostSecStart_MedianPeakGapValue'].iloc[-1], '\n',
          'HighQuntl_PeakGap: ', result['PostSecStart_PeakGapValue_HighQuntl'].iloc[-1], '\n',
          'Max_PeakGap: ',  result['PostSecStart_MaxPeakGapValue'].iloc[-1], '\n',
          'Median_ValleyGap: ', result['PostSecStart_MedianValleyGapValue'].iloc[-1], '\n',
          'HighQuntl_ValleyGap: ',  result['PostSecStart_ValleyGapValue_HighQuntl'].iloc[-1], '\n',
          'Max_ValleyGap: ',  result['PostSecStart_MaxValleyGapValue'].iloc[-1], '\n',
          'PostSecStart_MaxDrop_LastDays: ', result['PostSecStart_MaxDrop_LastDays'].iloc[-1], '\n',
          'PostSecPeak2Now_LastDays: ', result['PostSecPeak2Now_LastDays'].iloc[-1], '\n',
          'Now_SecDate: ', result['Now_SecDate'].iloc[-1])
    return result


if __name__ == '__main__':
    # Now_Date = get_trade_date(loc=-1)
    Now_Date = '2024-03-27'
    daily_pick(Now_Date, Turn_Date='ALL')
    print('每日筛选结果已存储')

    day_list = ['2024-03-20', '2024-02-27', '2024-02-28', '2024-03-06', '2024-03-07', '2024-03-13', '2024-03-14']

