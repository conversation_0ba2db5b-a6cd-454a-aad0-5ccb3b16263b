"""构建函数库
    1.完成初步筛选，构建备选池库
    2.跟踪备选池库品种，依据股票走势进行更新调整，剔除回档品种，标注可投品种
"""
import pdb
import time

import numpy as np
import pandas as pd
# import sqlalchemy.exc
from dateutil.relativedelta import relativedelta
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from tqdm import tqdm

import os
import sys

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

import config.config_Ali as config

from function_ai.Func_Base import (cal_maxcontisum,
                                   cal_movingavg_stat,
                                   get_stock_data,
                                   section_stat,
                                   get_index_data,
                                   get_trade_date,
                                   get_stock_info)
from function_ai.swindex_funcs import (get_zzindex_data, cal_prepeak_date,
                                       cal_swindex_state, get_swindex_data)


def sfitprocess_3(Result_Loc=None, method=None):
    """依据计算指标结果进行筛选"""
    new_date = Result_Loc['Cal_Date'].max()
    Result_Loc = Result_Loc.copy()
    Result_Loc['drop2now_days'] = Result_Loc['PostPeak_Recent_Neg4_DropDate'].apply(
        lambda fn: (pd.Timestamp(new_date) - pd.Timestamp(fn)).days
        if pd.notnull(fn) else None)

    # 计算上证指数section_startdate, section_peakdate, 区间天数
    from function_ai.Func_Base import cal_sectionpeak
    end_date = Result_Loc['Cal_Date'].max()
    index_data = get_index_data(end_date=end_date, stk_code='000001.SH').set_index(
        'trade_date', drop=False)
    index_startdate, index_peakdate, index_Pdays, index_Sdays = cal_sectionpeak(
        index_data)
    print('上证指数转折日期:', index_startdate,
          '波峰日期:', index_peakdate,
          '波峰下行天数:', index_Pdays)

    Result_Loc['Sudden_Drop'] = Result_Loc[['Now_DayRatio', 'PostSecStart_DropDayRatio_Quntl',
                                            'PostNowSec_SumRatio', 'PostNowSec_LastDays']].apply(
        lambda fn: 'True' if fn['Now_DayRatio'] < fn['PostSecStart_DropDayRatio_Quntl']
                             and (fn['PostNowSec_SumRatio'] > 0 or fn['PostNowSec_LastDays'] <= 5) else '-', axis=1)

    Result_Loc['PickState'] = np.nan
    threshold = 0.3
    Result_Loc['RiseRatio_BF_MaxContiSum'] = \
        round(((100 + Result_Loc['PostTurn_RiseRatio']) / (
                100 + Result_Loc['PostTurn_MaxContiSum']) - 1) * 100 / threshold, 3)
    Result_Loc['MaxDrop2Rise_Ratio'] = abs(
        Result_Loc['PostTurn_MaxDrop'] / Result_Loc['PostTurn_RiseRatio'])
    Result_Loc['PostBottom_TO_Ratio'] = round(Result_Loc['PostBottom_TO_Over10Num'] / Result_Loc['PostBottom_Lastdays'],
                                              3)

    if method == 1:
        """筛选条件组1:
           针对指数重要转折点，
           与股票Turn_Date相吻合"""
        if len(Result_Loc.query('Peak2Sec_LastDays<=@index_Pdays')) > 0:
            index_days = index_Pdays
            print('上证指数波峰下行天数:', index_Pdays)
        else:
            print('无股票品种满足上证指数下行天数条件')
        result = Result_Loc.query(
            'PostTurn_LastDays<25 & '
            'PostTurn_RiseRatio<50 & '
            'LastDrop_COR_Ratio>1.2 & '
            '(PostBottom_TO_Over10Num>0 | Bottom_Date==Period_TurnDate) & '
            'PreTurn_Period_R2>0.8 & '
            'PreTurn_Period_Sum2Std>2 & '
            'Period_Valley_GapRatio<100'
        ).copy()
        result['PickState'] = '重要转折'
        result = result.sort_values(
            ['Latst2Now_DDRQ_Days', 'PreTurn_Period_Sum2Std'], ascending=[True, False])

    elif method == 2:
        """筛选条件组2:
        针对指数波谷转折点，
        与Section_StartDate相吻合"""
        if len(Result_Loc.query('Peak2Sec_LastDays<=@index_Pdays')) > 0:
            index_days = index_Pdays
            print('上证指数波峰下行天数:', index_Pdays)
        else:
            print('无股票品种满足上证指数下行天数条件')

        num1 = -6
        result = Result_Loc.query(
            'PostTurn_LastDays>=25 & '
            'Period_TurnDate<Section_StartDate & '
            '(Now_Period_SumRatio<0 | Now_Period_Lastdays<5) & '
            'PostBottom_TO_Over10Num>0 & '
            'PostTurn_Over7Num>0 & '
            'PostPeak_Gap2Peak_Ratio<@num1 & '
            'PreSec_Reg_R2>0.8 & '
            'PreSec_Reg_Sum2Std>1.5 & '
            'PostSecStart_LastDays<=3 & '
            'LastDrop_COR_Ratio>1.0 & '
            'PostTurn_Period_RiseNum<=3'
        ).copy()

        result['PickState'] = '波谷转折'
        result = result.sort_values(
            ['Latst2Now_DDRQ_Days', 'LastDrop_COR_Ratio'], ascending=[True, False])

    elif method == 3:
        """筛选条件组3:
        针对指数日间转折点
        与Now_SecDate相吻合"""
        result = Result_Loc.query(
            'Section_StartDate<Now_SecDate & '
            'PostSecStart_RiseSecNum>0 & '
            'SecDiff_Sec_Recov_Position>=0.8 & '
            'PostSecStart_Reg_R2>0.8 & '
            'PostSecStart_Reg_Sum2Std>1.5 & '
            'PostSecStart_LastDays>=8 & '
            'PostBottom_TO_Over10Num>0 & '
            '((PostNowSec_SumRatio>0 & PostNowSec_LastDays>=8) | '
            '(PostNowSec_SumRatio<0 & PostNowSec_LastDays<5)) & '
            'LastDrop_COR_Ratio>1.2 & '
            'PreNow_Sec_Recent_MeanRatio<5 & '
            'PostSecStart_MaxContiSum<20 & '
            'PreNow_Sec_Recent_MaxRatio<5'
        ).copy()

        result['PickState'] = '日间转折'
        result = result.sort_values(['Latst2Now_DDRQ_Days', 'PostSecStart_Reg_Sum2Std'],
                                    ascending=[True, False])

    else:
        print('上证指数转折日期:', index_startdate,
              '波峰日期:', index_peakdate,
              '波峰下行天数:', index_Pdays)
        result = Result_Loc
    # if method != 2:
    # result = result.query('PreTurn_Period_R2>0.8 & PreTurn_Period_Sum2Std>2')
    # result = result.sort_values(['industry', 'PostTurn_TO_Over10Num', 'PreTurn_Period_R2'],
    #                               ascending=[True, False, False])
    return result


def get_result_3(start_date=None, end_date=None, industry=None, 
                 stk_list=None, mode='all'):
    """获取stk_results数据库品种并筛选"""
    # import pymysql
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    result = None
    if end_date is None:
        sql = """
                select Cal_Date from stocksfit.stk_results_common group by Cal_Date
            """
        cal_date = pd.read_sql_query(sql, con=engine)
        max_cal_date = cal_date['Cal_Date'].max()
        end_date = max_cal_date

    if start_date is not None:
        if mode.lower() == "first_half":
            sql = f"""SELECT 
                           *
                    FROM 
                            stocksfit.stk_results_common a
                    WHERE 
                            a.Cal_Date BETWEEN '{start_date}' AND '{end_date}'
                """
        elif mode.lower() == 'second_half':
            sql = f"""SELECT 
                           *
                    FROM 
                            stocksfit.stk_results_common b
                    WHERE 
                            b.Cal_Date BETWEEN '{start_date}' AND '{end_date}'
                """
        else:
            sql = f"""SELECT 
                            *
                    FROM 
                            stocksfit.stk_results_common a
                    JOIN 
                            stocksfit.stk_results_gapvalue b
                    USING 
                            (ts_code, Cal_Date)
                    WHERE 
                            a.Cal_Date BETWEEN '{start_date}' AND '{end_date}'
                """
        for _ in range(3):
            try:
                result = pd.read_sql_query(sql=sql, con=engine)
            except Exception as e:
                time.sleep(3)
            else:
                break
    else:
        if isinstance(end_date, str):
            end_date = [end_date]
        sql_enddate = ','.join(["'%s'" % item for item in end_date])
        if mode.lower() == "first_half":
            sql = f"""SELECT 
                           *
                    FROM 
                            stocksfit.stk_results_common a
                    WHERE 
                            a.Cal_Date in ({sql_enddate})
                """
        elif mode.lower() == 'second_half':
            sql = f"""SELECT 
                           *
                    FROM 
                            stocksfit.stk_results_common b
                    WHERE 
                            b.Cal_Date in ({sql_enddate})
                """
        else:
            sql = f"""SELECT 
                            *
                    FROM 
                            stocksfit.stk_results_common a
                    JOIN 
                            stocksfit.stk_results_gapvalue b
                    USING 
                            (ts_code, Cal_Date)
                    where 
                            a.Cal_Date in ({sql_enddate})"""
        for _ in range(3):
            try:
                result = pd.read_sql_query(sql=sql, con=engine)
            except Exception as e:
                time.sleep(3)
            else:
                break

    if industry is not None:
        if isinstance(industry, str):
            industry = [industry]
        result = result.query('industry in @industry').copy()

    # if now_secdate is not None:
    #     sql = """select trade_date from stocksfit.index_data where ts_code='000001.SH' """
    #     trade_dates = pd.read_sql_query(
    #         sql, con=engine).set_index('trade_date', drop=False)
    #     trade_dates = trade_dates.sort_index(ascending=True)
    #     result = result.dropna(subset=['Now_SecDate']).copy()
    #     result['Sec_Diff'] = result['Now_SecDate'].apply(
    #         lambda fn: len(trade_dates.loc[fn:now_secdate]) if fn <= now_secdate else len(
    #             trade_dates.loc[now_secdate:fn]))
    #     result = result.query('Sec_Diff<=3').copy()
    #     result = result.sort_values('Now_MaxSum', ascending=True)
    #     result = result.drop('Sec_Diff', axis=1)

    # if section_startdate is not None:
    #     sql = """select trade_date from stocksfit.index_data where ts_code='000001.SH' """
    #     trade_dates = pd.read_sql_query(
    #         sql, con=engine).set_index('trade_date', drop=False)
    #     trade_dates = trade_dates.sort_index(ascending=True)
    #     result = result.dropna(subset=['Section_StartDate']).copy()
    #     stock_data = get_stock_data(start_date=result['Section_StartDate'].min(),
    #                                 end_date=max(result['Section_StartDate'].max(), section_startdate))
    #     result['Sec_Diff'] = result.apply(
    #         func=cal_secdiff, args=(section_startdate, stock_data, 'Section_StartDate',), axis=1)
    #     result = result.query('Sec_Diff=="Yes"').copy()
    #     result = result.sort_values(
    #         ['LastDrop_COR', 'LastDrop_ClsOpenRatio_Avg'], ascending=[True, False])
    #     result = result.drop('Sec_Diff', axis=1)

    # if turn_date is not None:
    #     sql = """select trade_date from stocksfit.index_data where ts_code='000001.SH' """
    #     trade_dates = pd.read_sql_query(
    #         sql, con=engine).set_index('trade_date', drop=False)
    #     trade_dates = trade_dates.sort_index(ascending=True)
    #     result = result.dropna(subset=['Period_TurnDate']).copy()
    #     stock_data = get_stock_data(start_date=result['Period_TurnDate'].min(),
    #                                 end_date=max(result['Period_TurnDate'].max(), turn_date))
    #     result['Turn_Diff'] = result.apply(func=cal_secdiff, args=(
    #         turn_date, stock_data, 'Period_TurnDate',), axis=1)
    #     result = result.query('Turn_Diff=="Yes"').copy()
    #     result = result.sort_values('PreTurn_Period_AvgRatio', ascending=False)
    #     result = result.drop('Turn_Diff', axis=1)
    
    if stk_list is not None:
        result = result.query('ts_code in @stk_list').copy()
        if len(result) == 0:
            print('无stk_list对应股票品种')
            return

    # 筛选条件
    # if pickmode is not None:
    #     if len(result) > 0:
    #         result = sfitprocess_3(Result_Loc=result, method=pickmode)
    #     else:
    #         print('无备选品种')
    #     con_list = ['ts_code', 'name', 'industry', 'area', 'Target_Price', 'Target_Ratio',
    #                 'Period_TurnDate',
    #                 'PreTurn_Period_Lastdays',
    #                 'PreTurn_Period_AvgRatio',
    #                 'PreTurn_Period_R2',
    #                 'PreTurn_Period_Sum2Std',
    #                 'PostTurn_LastDays',
    #                 'PostTurn_RiseRatio',
    #                 'PostTurn_RiseAvg',
    #                 'PostTurn_MaxContiSum',
    #                 'PostTurn_Sec_Max_SumRatio',
    #                 'Period_Valley_GapRatio',
    #                 'PostTurn_Reg_R2',
    #                 'PostTurn_Reg_Sum2Std',
    #                 'PostTurn_Over7Num',
    #                 'PostTurn_BMA_Proportion',
    #                 'PostTurn_Peak2Now_Lastdays',
    #                 'TurnDiff_Sec_Recov_Position',
    #                 'Bottom_Date',
    #                 'PostBottom_TO_Over10Num',
    #                 'LongTrend_PeriodNum_Rise',
    #                 'Period_Break_Ratio',
    #                 'Period_Break_Date',
    #                 'Now_DayRatio',
    #                 'Now_HighLow_Ratio',
    #                 'Section_PeakDate',
    #                 'PreSec_StartDate',
    #                 'Section_StartDate',
    #                 'Now_SecDate',
    #                 'PreNow_SecDate',
    #                 'Section_Break_Ratio',
    #                 'SectionStart_Position',
    #                 'PostSecStart_LastDays',
    #                 'PostSecStart_TO_Over10Num',
    #                 'PostSecStart_MaxContiSum',
    #                 'PostSecStart_MaxContiDays',
    #                 'PreSec_LastDays',
    #                 'PreSec_SumRatio',
    #                 'PostSecStart_RiseRatio',
    #                 'PostSecStart_MaxDrop',
    #                 'PostSecStart_RiseSecNum',
    #                 'PostSecStart_Reg_R2',
    #                 'PostSecStart_Reg_Sum2Std',
    #                 'PreSec_Reg_R2',
    #                 'PreSec_Reg_Sum2Std',
    #                 'SecDiff_Sec_Recov_Position',
    #                 'PreSec_Sec_OMA_Prop',
    #                 'PreSec_Sec_OMA_Days',
    #                 'Peak2Sec_LastDays',
    #                 'Peak2Sec_SumRatio',
    #                 'PostNowSec_SumRatio',
    #                 'PostNowSec_LastDays',
    #                 'PrePostNowSec_LastDays',
    #                 'PreNowSec_MaxClsOpenRatio',
    #                 'PreNow_Sec_Recent_MeanRatio',
    #                 'PreNow_Sec_Recent_MaxRatio',
    #                 'PreNow_Sec_MaxContiSum',
    #                 'PreNow_Sec_BMA_Prop',
    #                 'PrePeak_Over4_Date',
    #                 'GapRatio2Sec',
    #                 'PostPeak_Gap2Peak_Ratio',
    #                 'LastDrop_ClsOpenRatio_Avg',
    #                 'LastDrop_COR_Ratio',
    #                 'LastDrop_COR',
    #                 'PickState',
    #                 'MaxDrop2Rise_Ratio',
    #                 'PostBottom_TO_Ratio',
    #                 'Latst2Now_DDRQ_Days',
    #                 'PostTurn_Sec_TrendRatio_Diff',
    #                 'RiseRatio_BF_MaxContiSum',
    #                 'Sudden_Drop'
    #                 ]
    #     result = result[con_list]
    engine.dispose()
    return result


def cal_secdiff(result, check_date, stock_data, datetype='Section_StartDate', calmode=1):
    """计算Period_TurnDate或Section_StartDate 是否与转折点吻合"""
    ts_code = result['ts_code']
    turn_date = result[datetype]
    if pd.notnull(turn_date):
        stk_data = stock_data.query(
            'ts_code==@ts_code').set_index('trade_date', drop=False)
        secdiff = len(stk_data.loc[turn_date:check_date]) \
            if turn_date <= check_date else len(stk_data.loc[check_date:turn_date])
        if calmode == 1:
            dif_num = 4
            dif_num2 = 8
            dif_ratio = 0.05
            if secdiff <= dif_num \
                    or (secdiff <= dif_num2 and 0 < (stk_data.query('trade_date<=@check_date')['low'].iloc[-1] /
                                                     stk_data.query('trade_date<=@turn_date')['low'].iloc[
                                                         -1] - 1) <= dif_ratio):
                return 'Yes'
            else:
                return 'No'
        else:
            dif_num = 3
            if secdiff <= dif_num:
                return 'Yes'
            else:
                return 'No'
    else:
        return 'No'


# def cal_secdiff(result, check_date, stock_data, datetype='Section_StartDate', calmode=1):
#     """计算Section_StartDate 是否与转折点吻合"""
#     ts_code = result['ts_code']
#     turn_date = result[datetype]
#     stk_data = stock_data.query('ts_code==@ts_code').set_index('trade_date', drop=False)
#     secdiff = len(stk_data.loc[turn_date:check_date]) \
#         if turn_date <= check_date else len(stk_data.loc[check_date:turn_date])
#     if calmode == 1:
#         dif_num = 4
#         dif_num2 = 8
#         dif_ratio = 0.05
#         if secdiff <= dif_num \
#                 or (secdiff <= dif_num2 and 0 <= (stk_data.query('trade_date<=@check_date')['close'].iloc[-1] /
#                        stk_data.query('trade_date<=@turn_date')['close'].iloc[-1] - 1) <= dif_ratio):
#             return 'Yes'
#         else:
#             return 'No'
#     else:
#         dif_num = 3
#         if secdiff <= dif_num:
#             return 'Yes'
#         else:
#             return 'No'


def pick_method(Result_Loc=None, method=None):
    """依据既定标准，筛选标的品种
       分为三种情形：Turn转折点，Section_StartDate转折点，及Section_StartDate之前缓步运行
    """
    new_date = Result_Loc['Cal_Date'].max()
    Result_Loc = Result_Loc.copy()
    Result_Loc['drop2now_days'] = Result_Loc['PostPeak_Recent_Neg4_DropDate'].apply(
        lambda fn: (pd.Timestamp(new_date) - pd.Timestamp(fn)).days
        if pd.notnull(fn) else None)

    Result_Loc['Sudden_Drop'] = Result_Loc[['Now_DayRatio', 'PostSecStart_DropDayRatio_Quntl',
                                            'PostNowSec_SumRatio', 'PostNowSec_LastDays']].apply(
        lambda fn: 'True' if fn['Now_DayRatio'] < fn['PostSecStart_DropDayRatio_Quntl']
                             and (fn['PostNowSec_SumRatio'] > 0 or fn['PostNowSec_LastDays'] <= 5) else '-', axis=1)

    Result_Loc['PickState'] = np.nan
    threshold = 0.3
    Result_Loc['RiseRatio_BF_MaxContiSum'] = round(
        ((100 + Result_Loc['PostTurn_RiseRatio']) / (
                100 + Result_Loc['PostTurn_MaxContiSum']) - 1) * 100 / threshold, 3)
    Result_Loc['MaxDrop2Rise_Ratio'] = round(
        abs(Result_Loc['PostTurn_MaxDrop'] / Result_Loc['PostTurn_RiseRatio']), 3)
    Result_Loc['PostBottom_TO_Ratio'] = round(
        Result_Loc['PostBottom_TO_Over10Num'] / Result_Loc['PostBottom_Lastdays'], 3)

    if method == 1:
        """筛选条件组1:
        针对指数重要转折点，
        与股票Turn_Date相吻合"""
        result = Result_Loc.query(
            'Period_TurnDate==Section_StartDate & '
            'PostTurn_RiseRatio<50 & '
            '(PostBottom_TO_Over10Num>0 | Bottom_Date==Period_TurnDate) & '
            'PreTurn_Period_R2>0.8 & '
            'PreTurn_Period_Sum2Std>2 & '
            'Period_Valley_GapRatio<100'
        ).copy()
        #                                 'LastDrop_COR_Ratio>1.2 & '
        result['PickState'] = 'Turn_转折'
        result = result.sort_values(
            ['Latst2Now_DDRQ_Days', 'PreTurn_Period_Sum2Std'], ascending=[True, False])

    elif method == 2:
        """筛选条件组2:
        针对指数波谷转折点，
        与Section_StartDate相吻合"""
        num1 = -6
        num2 = -18
        result = Result_Loc.query(
            'Period_TurnDate<Section_StartDate & '
            'PostBottom_TO_Over10Num>0 & '
            'PostTurn_Over7Num>0 & '
            'PostPeak_Gap2Peak_Ratio<@num1 & '
            'Peak2Sec_SumRatio>=@num2 & '
            'PostTurn_Period_RiseNum<=3'
        ).copy()
        result['PickState'] = 'Section_转折'
        result = result.sort_values(
            ['Latst2Now_DDRQ_Days', 'LastDrop_COR_Ratio'], ascending=[True, False])
    elif method == 3:
        """筛选条件组3：
        适用于Section_StartDate与Turn_Date重合、不重合两种情形，接续用于Shock_Date_Last判定
        """
        result = Result_Loc.query(
            'PreSecWLS_R2>0.5 & '
            'PreSec_Reg_Sum2Std>1.2 & '
            'TurnConcave_LastDays>30 & '
            '(Period_TurnDate==Section_StartDate | PostTurn_RiseAvg<=1.5)'
        ).copy()
        result['PickState'] = 'Shock_Recov'
        result = result.sort_values('PostTurn_TO_Over10Num', ascending=False)
    else:
        result = Result_Loc
    return result


def build_track_pool(start_date=None, end_date=None, industry=None,
                     pickmode=0, stk_list=None,
                     store_mode=False):
    """依据转折点筛选品种，构建跟踪池"""
    # 连接MySQL
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    result = []
    # 如未设定计算日期，设定为最近交易日期
    if end_date is None:
        sql = """
                 select Cal_Date from stocksfit.common group by Cal_Date
              """
        cal_date = pd.read_sql_query(sql, con=engine)
        max_cal_date = cal_date['Cal_Date'].iloc[-1]
        end_date = max_cal_date

    if start_date is None:
        start_date = end_date

    if industry is not None:
        if isinstance(industry, str):
            industry = [industry]
        sql = f"""SELECT 
                            a.*, 
                            b.* 
                    FROM 
                            stocksfit.stk_results_common a
                    JOIN 
                            stocksfit.stk_results_gapvalue b
                    ON 
                            a.ts_code = b.ts_code AND a.Cal_Date = b.Cal_Date
                    WHERE 
                            a.Cal_Date BETWEEN '{start_date}' AND '{end_date}' and a.industry in '{industry}'
                """
        
        for _ in range(3):
            try:
                result = pd.read_sql_query(sql=sql, con=engine)
            except Exception as e:
                time.sleep(3)
            else:
                break
    else:
        sql = f"""SELECT 
                            a.*, 
                            b.* 
                    FROM 
                            stocksfit.stk_results_common a
                    JOIN 
                            stocksfit.stk_results_gapvalue b
                    ON 
                            a.ts_code = b.ts_code AND a.Cal_Date = b.Cal_Date
                    WHERE 
                            a.Cal_Date BETWEEN '{start_date}' AND '{end_date}'
                """
        for _ in range(3):
            try:
                result = pd.read_sql_query(sql=sql, con=engine)
            except Exception as e:
                time.sleep(3)
            else:
                break
    if len(result) == 0:
        print('筛选结果为空！')
        return
    # result['SecFit_Date'] = np.nan

    # result['Sec_Diff'] = None
    # result['SecFit_Date'] = None
    # result['Now_Diff'] = None
    # result['SecStepBack_Diff'] = None
    # result['NowFit_Date'] = None
    # if now_secdate is not None:
    #     sql = """select trade_date from stocksfit.index_data where ts_code='000001.SH' """
    #     trade_dates = pd.read_sql_query(sql, con=engine).set_index('trade_date', drop=False)
    #     trade_dates = trade_dates.sort_index(ascending=True)
    #     # result = result.dropna(subset=['Now_SecDate']).copy()
    #     result['Now_Diff'] = result['Now_SecDate'].apply(
    #         lambda fn: max(len(trade_dates.loc[fn:now_secdate]), len(trade_dates.loc[now_secdate:fn]))
    #         if fn is not None else None)
    #     result['SecStepBack_Diff'] = result['PostSec_StepBackDate'].apply(
    #         lambda fn: max(len(trade_dates.loc[fn:now_secdate]), len(trade_dates.loc[now_secdate:fn])
    #         if fn != '-' else 100))
    #     # result = result.query('Now_Diff<=3 | SecStepBack_Diff<=3').copy()
    #     # result = result.sort_values('Now_MaxSum', ascending=True)
    #     # result = result.drop(columns=['Now_Diff', 'SecStepBack_Diff'])
    #     # result['NowFit_Date'] = now_secdate
    #
    # if section_startdate is not None:
    #     if isinstance(section_startdate, str):
    #         section_startdate = [section_startdate]
    #     sql = """select trade_date from stocksfit.index_data where ts_code='000001.SH' """
    #     trade_dates = pd.read_sql_query(sql, con=engine).set_index('trade_date', drop=False)
    #     trade_dates = trade_dates.sort_index(ascending=True)
    #     result_adj = result.dropna(subset=['Section_StartDate']).copy()
    #     stock_data = get_stock_data(start_date=result_adj['Section_StartDate'].min(),
    #                                 end_date=max(result_adj['Section_StartDate'].max(), max(section_startdate)))
    #     results = pd.DataFrame()
    #     for secstart_date in section_startdate:
    #         result_temp = result.copy()
    #         result_temp['Sec_Diff'] = result_temp.apply(
    #             func=cal_secdiff, args=(secstart_date, stock_data, 'Section_StartDate',), axis=1)
    #         if now_secdate is not None:
    #             result_temp = result_temp.query('Sec_Diff=="Yes" & '
    #                                             '(Now_Diff<=3 & '
    #                                             '(PostSecStart_RiseSecNum<=3 & PostSecStart_MaxDrop<16))').copy()
    #         else:
    #             result_temp = result_temp.query('Sec_Diff=="Yes" & PostSecStart_RiseSecNum==1').copy()
    #         # result_temp = result_temp.drop('Sec_Diff', axis=1)
    #         result_temp['SecFit_Date'] = secstart_date
    #         results = pd.concat([results, result_temp], ignore_index=True)
    #     results = results.drop_duplicates(subset=['ts_code'], keep='first')
    #     result = results.sort_values(['LastDrop_COR', 'LastDrop_ClsOpenRatio_Avg'], ascending=[True, False])
    # else:
    #     result['SecFit_Date'] = None

    # if spring_date is not None:
    #     result['Spring_Diff'] = np.nan
    #     result['SpringFit_Date'] = np.nan
    #     sql = """select trade_date from stocksfit.index_data where ts_code='000001.SH' """
    #     trade_dates = pd.read_sql_query(sql, con=engine).set_index('trade_date', drop=False)
    #     trade_dates = trade_dates.sort_index(ascending=True)
    #     result = result.dropna(subset=['Spring_Date']).copy()
    #     result = result.query('Spring_Date != "-"').copy()
    #     result['Spring_Diff'] = result['Spring_Date'].apply(
    #         lambda fn: len(trade_dates.loc[fn:spring_date]) if fn <= spring_date else len(
    #             trade_dates.loc[spring_date:fn]))
    #     result = result.query('Spring_Diff<=3').copy()
    #     result = result.sort_values('Now_MaxSum', ascending=True)
    #     result = result.drop('Spring_Diff', axis=1)
    #     result['SpringFit_Date'] = spring_date

    # print('缺失secdate参数输入')
    # return

    if stk_list is not None:
        result = result.query('ts_code in @stk_list').copy()
        if len(result) == 0:
            print('无stk_list对应股票品种')
            return

    # 筛选条件
    if pickmode is not None:
        if len(result) > 0:
            result = pick_method(Result_Loc=result, method=pickmode)
        else:
            print('无备选品种')
    # con_list = ['ts_code', 'name', 'industry', 'area', 'Target_Price', 'Target_Ratio',
    #             'Period_TurnDate',
    #             'PreTurn_PeakDate',
    #             'PreTurn_Period_SumRatio',
    #             'PreTurn_Period_Lastdays',
    #             'PreTurn_Period_AvgRatio',
    #             'PreTurn_Turnover2Rise',
    #             'PreTurn_Turnover2Drop',
    #             'PreTurn_Turnover2Drop_Last4Rank',
    #             'Now_Period_SumRatio',
    #             'PostTurn_RiseRatio',
    #             'PostTurn_LastDays',
    #             'PostTurn_RiseAvg',
    #             'Peak2Turn_SumRatio',
    #             'Peak2Turn_LastDays',
    #             'Peak2Sec_SumRatio',
    #             'Peak2Sec_AvgRatio',
    #             'Peak2Sec_LastDays',
    #             'PostSecStart_LastDays',
    #             'PostSecStart_RiseRatio',
    #             'PostSecStart_SumRatio',
    #             'PostSecStart_AvgRatio',
    #             'PostTurn_BreakRatio',
    #             'PostSec_1stSec_BreakRatio',
    #             'PostBottom_RiseRatio',
    #             'Bottom2Now_Ratio',
    #             'PostBottom_Lastdays',
    #             'PreNowSec_SumRatio',
    #             'PreNowSec_LastDays',
    #             'PreNowSec_MaxClsOpenRatio',
    #             'PreNowSec_Adverse2Trend',
    #             'PostNowSec_Adverse2Trend',
    #             'PreSec_Adverse2Trend',
    #             'PostSecStart_Adverse2Trend',
    #             'PostBottom_TO_Over10Num',
    #             'PostTurn_TO_Over10Num',
    #             'PostSecStart_TO_Over10Num',
    #             'PostTurn_MaxContiSum',
    #             'PostTurn_MaxDrop',
    #             'PostTurn_Sec_Max_SumRatio',
    #             'PostBottom_MaxContiSum',
    #             'PostBottom_MaxDrop',
    #             'PostSecStart_MaxDrop',
    #             'PostSecStart_MaxContiSum',
    #             'PostSecStart_MaxDailyRatio',
    #             'Period_Valley_GapRatio',
    #             'PostTurn_Over7Num',
    #             'PostSecStart_Over7Num',
    #             'PostNowSec_Over7Num',
    #             'PostSecMax2Peak_Ratio',
    #             'PostSecStart_RiseSecNum',
    #             'Period_Break_Ratio',
    #             'PostTurn_BMA_Proportion',
    #             'TurnConcave_LastDays',
    #             'Now_DayRatio',
    #             'Bottom_Date',
    #             'Section_PeakDate',
    #             'PreSec_StartDate',
    #             'Section_StartDate',
    #             'Now_SecDate',
    #             'PreNow_SecDate',
    #             'PreSec_Sec_OMA_Prop',
    #             'PreSec_Sec_OMA_Days',
    #             'PreSec_Turnover2Rise',
    #             'PreSec_Turnover2Drop',
    #             'PreSec_Turnover2Drop_Last4Rank',
    #             'PreSec_SumRatio',
    #             'PrePeak_Over4_Date',
    #             'GapRatio2Sec',
    #             'PostPeak_Gap2Peak_Ratio',
    #             'LastDrop_ClsOpenRatio_Avg',
    #             'LastDrop_COR_Ratio',
    #             'LastDrop_COR',
    #             'MaxDrop2Rise_Ratio',
    #             'RiseRatio_BF_MaxContiSum',
    #             'Recent4P_MaxLastDays',
    #             'PickState',
    #             'PreTurn_MaxTurnover',
    #             'PostTurn_MaxTurnover',
    #             'PostBottom_MaxTurnover',
    #             'PreSec_MaxTurnover',
    #             'PreSec_AvgTurnover',
    #             'PostSecStart_MaxTurnover',
    #             'PostSecStart_MaxTurnover_Date',
    #             'Now_Turnover',
    #             'Now_Turnover_Quntl',
    #             'Now_Turnover_Rank',
    #             'Now_Turnover_Signal',
    #             'Now_Turnover_MinGap',
    #             'NowSec_AvgRatio',
    #             'NowSec_LastDays',
    #             'Sudden_Drop',
    #             'Peak2Sec_COR_Mean',
    #             'Peak2Turn_COR_Und2Poxn',
    #             'PostTurn_COR_Und2Poxn',
    #             'Peak2Sec_COR_Und2Poxn',
    #             'PostSecStart_COR_Und2Poxn',
    #             'Turn2Peak_MaxContiDrop',
    #             'Peak2Turn_MaxContiRise',
    #             'Peak2Turn_MaxContiRiseDays',
    #             'Peak2Sec_MaxContiRise',
    #             'Peak2Sec_MaxContiRiseDays',
    #             'PreTurn_Period_R2',
    #             'PreTurn_Period_Sum2Std',
    #             'PostTurn_Reg_R2',
    #             'PostTurn_Reg_Sum2Std',
    #             'Peak2Turn_Reg_R2',
    #             'Peak2Turn_Reg_Sum2Std',
    #             'Peak2Sec_Reg_R2',
    #             'Peak2Sec_Reg_Sum2Std',
    #             'PostSecStart_Reg_R2',
    #             'PostSecStart_Reg_Sum2Std',
    #             'PreSec_Reg_R2',
    #             'PreSec_Reg_Sum2Std',
    #             'PostSecStart_COR_Mean',
    #             'PostSecStart_MaxContiDrop',
    #             'PostSecStart_MaxContiDropDays',
    #             'Peak2SecWLS_Slope',
    #             'Peak2SecWLS_R2',
    #             'Peak2SecWLS_Deviation',
    #             'PreSecWLS_Slope',
    #             'PreSecWLS_R2',
    #             'PreSecWLS_Deviation',
    #             'PostSecWLS_Slope',
    #             'PostSecWLS_R2',
    #             'PostSecWLS_Deviation',
    #             'SecFit_Date',
    #             'Concave_Break_Days2Now',
    #             'PreTurn_ShockDate',
    #             'PostTurn_SOSDate',
    #             'PostTurn_StepBackDate',
    #             'PostTurn_StepBack_GapRatio',
    #             'PostTurn_StepBack_LastDays',
    #             'PostSecPeak_ShockDate',
    #             'PostSecPeak_SOSDate',
    #             'PostSecPeak_StepBackDate',
    #             'PostSecPeak_StepBack_GapRatio',
    #             'PostSecPeak_StepBack_LastDays',
    #             'PreSec_ShockDate',
    #             'PostSec_SOSDate',
    #             'PostSec_StepBackDate',
    #             'PostSec_StepBack_GapRatio',
    #             'PostSec_StepBack_LastDays',
    #             'Shock_Date',
    #             'Shock_Date_Last',
    #             'Spring_Date',
    #             'SOS_Date',
    #             'Shrink_Date',
    #             'Now_Shock_Date',
    #             'Now_Spring_Date',
    #             'Break_PreSec',
    #             'Cal_Date'
    #             ]
    # result = result[con_list]
    # result = pd.concat([result, pd.DataFrame(columns=['Aft_SecFit_Lastdays',
    #                                                   'Aft_SecFit_SumRatio',
    #                                                     'Aft_SecFit_AvgRatio',
    #                                                     'Aft_SecFit_ExtreRatio',
    #                                                     'Aft_SecFit_DropDays',
    #                                                     'Aft_SecFit_DropProp',
    #                                                     'Aft_SecFit_MaxDrop',
    #                                                     'Aft_SecFit_RecoverDays',
    #                                                     'Aft_SecFit_Break',
    #                                                     'Aft_SecFit_Below_Quntl',
    #                                                     'track_signal',
    #                                                     'Adj_Date',
    #                                                     'Break_Shock',
    #                                                     'Break_Shrink_Days',
    #                                                     'SOS_Test',
    #                                                     'Break_LastShock_Days',
    #                                                     'BreakLastShock2Now_Days',
    #                                                     'Peak2Turn_Ratio',
    #                                                     'Turn2Peak_Ratio',
    #                                                     'Peak2Sec_Ratio',
    #                                                     'SecStart_Diff',
    #                                                     'NowSec_Diff',
    #                                                     'PostPeak_Low',
    #                                                     'PostSec_BfNow_Avg',
    #                                                     'Last2PostAvg_Ratio'
    #                                                     ], index=range(0, 1))], axis=1)
    result = result.drop_duplicates(subset=['ts_code'], keep='first')
    if len(result) > 0 and store_mode:
        for _ in range(3):
            try:
                pd.io.sql.to_sql(result, 'stk_track_pool', engine,
                                 index=False, schema='stocksfit', if_exists='append')
            except:
                time.sleep(3)
            else:
                break
    engine.dispose()
    # result = result.sort_values('Aft_SecFit_AvgRatio', ascending=False)
    return result


def adjust_track_pool(end_date=None, index_peakdate=None, index_dropdate=None,
                      SecFit_Date=None, NowFit_Date=None,
                      filter_method=0, track_pool=None):
    """维护track_pool，依据最新行情数据确认是否剔除或标注持仓信号"""
    # 连接mysql
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')

    if end_date is None:
        sql_enddate = """select max(trade_date) as trade_date from stock_data"""
        for _ in range(3):
            try:
                end_date = pd.read_sql_query(sql=sql_enddate,
                                             con=engine)['trade_date'].iloc[-1]
            except:
                time.sleep(3)
            else:
                break

    # 获取与指定SecFit_Date一致的品种列表
    if track_pool is None or len(track_pool) == 0:
        print('备选品种track_pool为空')
        return
    else:
        stk_track_list = track_pool

    # 获取股票行情数据
    start_date = (min(pd.to_datetime(stk_track_list['Bottom_Date'].dropna().min()),
                      pd.to_datetime(stk_track_list['Section_PeakDate'].dropna().min())) -
                  relativedelta(days=200)).strftime('%Y-%m-%d')
    stock_data = get_stock_data(start_date=start_date, end_date=end_date)
    trade_dates = stock_data['trade_date'].unique()
    if index_peakdate is not None and index_dropdate is None:
        index_data = get_index_data(end_date=end_date, stk_code='000906.SH')
        index_data = index_data.sort_values(
            'trade_date').set_index('trade_date', drop=False)
        index_data['ratio'] = round(
            (index_data['close'] / index_data['close'].shift(1) - 1) * 100, 3)
        Index_MaxDrop_Date = index_data.loc[index_peakdate:, 'ratio'].idxmin()
        print('中证800指数自', index_peakdate, '以来最大日跌幅对应日期为：', Index_MaxDrop_Date)
    elif index_dropdate is not None:
        Index_MaxDrop_Date = index_dropdate
    else:
        Index_MaxDrop_Date = None

    stk_track_list['Bottom_TO_Prop'] = stk_track_list['PostBottom_TO_Over10Num'] / \
                                       stk_track_list['PostBottom_Lastdays']
    stk_track_list['Turn_TO_Prop'] = stk_track_list['PostTurn_TO_Over10Num'] / \
                                     stk_track_list['PostTurn_LastDays']
    # 计算SecFit_Date后股价走势特征：持续天数，回档天数及占比、区间涨幅、区间日均涨幅、区间最大涨幅
    # Peak2Turn_LastDays = min(stk_track_list['Peak2Turn_LastDays'].quantile(0.7), 40)
    # Peak2Sec_LastDays = min(stk_track_list['Peak2Sec_LastDays'].quantile(0.7), 40)
    # Turn2Peak_LastDays = min(stk_track_list['PostTurn_LastDays'].quantile(0.7), 40)
    for index in tqdm(stk_track_list.index):
        ts_code = stk_track_list.loc[index, 'ts_code']
        Now_SecDate = stk_track_list.loc[index, 'Now_SecDate']
        Section_StartDate = stk_track_list.loc[index, 'Section_StartDate']
        #     if stk_track_list.loc[index, 'SecFit_Date'] is None \
        #     else stk_track_list.loc[index, 'SecFit_Date']
        stk_data = stock_data.query(
            'ts_code==@ts_code').sort_values('trade_date').set_index('trade_date', drop=False)
        stk_ratio = (stk_data['close'] / stk_data['close'].shift(1) - 1) * 100
        if stk_track_list.loc[index, 'PreSec_StartDate'] is None \
                or Section_StartDate not in stk_data.index:
            continue
        presec_startclose = stk_data.loc[stk_track_list.loc[index,
        'PreSec_StartDate'], 'close']
        # postsec_idxmin = stk_data.loc[Section_StartDate:end_date, 'close'].idxmin()
        postsec_idxmin = Section_StartDate
        stk_track_list.loc[index, 'Aft_SecFit_SumRatio'] = (
            round((stk_data.loc[:end_date, 'close'].iloc[-1] / stk_data.loc[postsec_idxmin, 'close'] - 1) * 100, 3))
        stk_track_list.loc[index, 'Aft_SecFit_Lastdays'] = len(
            stk_data.loc[postsec_idxmin:end_date])
        stk_track_list.loc[index, 'Aft_SecFit_AvgRatio'] = round(
            stk_track_list.loc[index, 'Aft_SecFit_SumRatio'] / stk_track_list.loc[index, 'Aft_SecFit_Lastdays'], 3)
        stk_track_list.loc[index, 'Aft_SecFit_ExtreRatio'] = round(
            stk_ratio.loc[postsec_idxmin:end_date].max(), 3)
        _, _, day_list = section_stat(
            stk_data=stk_data.loc[postsec_idxmin:end_date])
        dropprop, _, dropdays, maxdrop = cal_movingavg_stat(stk_data=stk_data.loc[postsec_idxmin:end_date],
                                                            num=3,
                                                            mode='Rise')
        _, _, drop_last, recover_last = cal_maxcontisum(stk_data=stk_data.loc[postsec_idxmin:end_date],
                                                        day_list=day_list,
                                                        mode='跌')
        stk_track_list.loc[index, 'Aft_SecFit_DropDays'] = dropdays
        stk_track_list.loc[index, 'Aft_SecFit_DropProp'] = dropprop
        stk_track_list.loc[index, 'Aft_SecFit_MaxDrop'] = maxdrop
        stk_track_list.loc[index, 'Aft_SecFit_RecoverDays'] = recover_last
        stk_track_list.loc[index, 'Aft_SecFit_Break'] = len(
            stk_data.loc[postsec_idxmin:end_date].query('high>@presec_startclose'))
        stk_track_list.loc[index, 'Aft_SecFit_Below_Quntl'] = 'True' \
            if stk_ratio.iloc[-1] < stk_ratio.loc[Section_StartDate:end_date].quantile(0.75) else '-'
        if stk_track_list.loc[index, 'Shock_Date'] != '-' \
                and stk_data.loc[stk_track_list.loc[index, 'Section_StartDate']:, 'close'].max() > \
                stk_data.loc[stk_track_list.loc[index, 'Shock_Date'], 'open']:
            stk_track_list.loc[index, 'Break_Shock'] = 'True'
        else:
            stk_track_list.loc[index, 'Break_Shock'] = '-'
        if stk_track_list.loc[index, 'Shrink_Date'] != '-' \
                and stk_data.loc[stk_track_list.loc[index, 'Section_StartDate']:, 'close'].max() > \
                stk_data.loc[stk_track_list.loc[index, 'Shrink_Date'], 'high']:
            shrink_date = stk_track_list.loc[index, 'Shrink_Date']
            break_shrink_date = stk_data.loc[stk_track_list.loc[index, 'Section_StartDate']:][
                stk_data.loc[stk_track_list.loc[index, 'Section_StartDate']:, 'close'] >
                stk_data.loc[shrink_date, 'high']].index[0]
            stk_track_list.loc[index, 'Break_Shrink_Days'] = len(
                stk_data.loc[stk_track_list.loc[index, 'Shrink_Date']:break_shrink_date])
        if stk_track_list.loc[index, 'SOS_Date'] != '-' \
                and stk_track_list.loc[index, 'Shrink_Date'] != '-' \
                and stk_data.loc[stk_track_list.loc[index, 'SOS_Date']:, 'close'].min() < \
                stk_data.loc[stk_track_list.loc[index, 'SOS_Date'], 'close']:
            stk_track_list.loc[index, 'SOS_Test'] = 'True'
        else:
            stk_track_list.loc[index, 'SOS_Test'] = '-'
        stk_track_list.loc[index, 'Break_LastShock_Days'], stk_track_list.loc[index,
        'BreakLastShock2Now_Days'] = 0, 0
        if stk_track_list.loc[index, 'Shock_Date_Last'] != '-' \
                and stk_track_list.loc[index, 'Shock_Date_Last'] is not None:
            shock_date_last = stk_track_list.loc[index, 'Shock_Date_Last']
            shock_date_price = max(
                stk_data.loc[shock_date_last, 'close'], stk_data.loc[shock_date_last, 'open'])
            if stk_data.loc[stk_track_list.loc[index, 'Section_StartDate']:, 'close'].max() > shock_date_price:
                break_lastshock_date = stk_data.loc[stk_track_list.loc[index, 'Section_StartDate']:
                                       ][stk_data.loc[stk_track_list.loc[index, 'Section_StartDate']:, 'close'] >
                                         shock_date_price].index[0]
                stk_track_list.loc[index, 'Break_LastShock_Days'] = len(
                    stk_data.loc[stk_track_list.loc[index, 'Shock_Date_Last']:break_lastshock_date]) - 1
                stk_track_list.loc[index, 'BreakLastShock2Now_Days'] = len(
                    stk_data.loc[break_lastshock_date:]) - 1

        if stk_track_list.loc[index, 'Peak2Turn_LastDays'] > 40 \
                and stk_track_list.loc[index, 'Period_TurnDate'] == stk_track_list.loc[index, 'Section_StartDate'] \
                and stk_track_list.loc[index, 'Peak2Turn_Reg_Sum2Std'] >= 1.5 \
                and stk_track_list.loc[index, 'Peak2Turn_COR_Und2Poxn'] >= 0.6 \
                and stk_track_list.loc[index, 'Peak2Turn_SumRatio'] > -30:
            stk_track_list.loc[index, 'Peak2Turn_Ratio'] = round(
                abs(stk_track_list.loc[index, 'Peak2Turn_MaxContiRise'] /
                    stk_track_list.loc[index, 'Peak2Turn_SumRatio']), 3)
        else:
            stk_track_list.loc[index, 'Peak2Turn_Ratio'] = 1

        if stk_track_list.loc[index, 'PostTurn_LastDays'] > 25 \
                and stk_track_list.loc[index, 'PostTurn_COR_Und2Poxn'] >= 0.6 \
                and 50 > stk_track_list.loc[index, 'PostTurn_RiseRatio'] >= 20:
            stk_track_list.loc[index, 'Turn2Peak_Ratio'] = round(
                abs(stk_track_list.loc[index, 'Turn2Peak_MaxContiDrop'] /
                    stk_track_list.loc[index, 'PostTurn_RiseRatio']), 3)
        else:
            stk_track_list.loc[index, 'Turn2Peak_Ratio'] = 1

        if stk_track_list.loc[index, 'Peak2Sec_LastDays'] > 8 \
                and stk_track_list.loc[index, 'Peak2Sec_SumRatio'] > -35:
            stk_track_list.loc[index, 'Peak2Sec_Ratio'] = round(
                abs(stk_track_list.loc[index, 'Peak2Sec_MaxContiRise'] /
                    stk_track_list.loc[index, 'Peak2Sec_SumRatio']), 3)
        else:
            stk_track_list.loc[index, 'Peak2Sec_Ratio'] = 1

        stk_track_list.loc[index, 'SecStart_Diff'], stk_track_list.loc[index, 'NowSec_Diff'] = 50, 50
        # if 'SecFit_Date' in stk_track_list.columns and pd.notnull(stk_track_list.loc[index, 'SecFit_Date']):
        if SecFit_Date is not None:
            stk_track_list.loc[index, 'SecStart_Diff'] = len(stk_data.loc[Section_StartDate:SecFit_Date]) \
                if Section_StartDate < SecFit_Date \
                else len(stk_data.loc[SecFit_Date:Section_StartDate])

        if NowFit_Date is not None:
            stk_track_list.loc[index, 'NowSec_Diff'] = len(stk_data.loc[Now_SecDate:NowFit_Date]) \
                if Now_SecDate < NowFit_Date \
                else len(stk_data.loc[NowFit_Date:Now_SecDate])

        PostNowSec_MaxTurnover = stk_data.loc[NowFit_Date:, 'turnover'].max()
        stk_track_list.loc[index, 'Max2Avg_Turnover'] = round(
            PostNowSec_MaxTurnover / stk_track_list.loc[index, 'PreSec_AvgTurnover'], 3)
        stk_track_list.loc[index, 'PostSecStart_Max2Avg_Turnover'] = round(
            stk_track_list.loc[index, 'PostSecStart_MaxTurnover'] / stk_track_list.loc[index, 'PreSec_AvgTurnover'], 3)

        PostTurn_MaxTurnover = stk_track_list.loc[index, 'PostTurn_MaxTurnover']
        PostTurn_AvgTurnover = stk_track_list.loc[index, 'PostTurn_AvgTurnover']
        stk_track_list.loc[index, 'PostTurn_Max2Avg_Turnover'] = round(
            PostTurn_MaxTurnover / PostTurn_AvgTurnover, 3) \
            if pd.notnull(PostTurn_MaxTurnover) and pd.notnull(PostTurn_AvgTurnover) and PostTurn_AvgTurnover != 0 \
            else 0

        stk_track_list.loc[index, 'MaxTurnover_Date'] = stk_data.loc[NowFit_Date:, 'turnover'].idxmax() \
            if len(stk_data.loc[NowFit_Date:]) > 0 and len(stk_data.loc[NowFit_Date:, 'turnover'].idxmax()) > 0 \
            else '-'
        stk_track_list.loc[index, 'MaxTurnover'] = PostNowSec_MaxTurnover
        stk_track_list.loc[index, 'PostNow_Max2Avg'] = round(
            (stk_data.loc[NowFit_Date:, 'turnover'].sum() - stk_data.loc[NowFit_Date:, 'turnover'].iloc[0]) /
            (len(stk_data.loc[NowFit_Date:]) - 1), 3) \
            if len(stk_data.loc[NowFit_Date:]) - 1 > 0 else 0
        stk_track_list.loc[index, 'MaxT2Now_LastDays'] = len(
            stk_data.loc[stk_data.loc[NowFit_Date:, 'turnover'].idxmax():]) \
            if len(stk_data.loc[NowFit_Date:]) > 0 else 0

        stk_track_list.loc[index, 'PostPeak_Low'] = 'True' \
            if stk_data.loc[stk_track_list.loc[index, 'Section_PeakDate']:, 'close'].idxmin() == \
               stk_track_list.loc[index, 'Section_StartDate'] \
            else '-'
        Section_StartDate = stk_track_list.loc[index, 'Section_StartDate']
        stk_track_list.loc[index, 'PostSec_BfNow_Avg'] = round(
            stk_ratio.loc[Section_StartDate:].iloc[:-1].mean(), 3)
        stk_track_list.loc[index, 'Last2PostAvg_Ratio'] = round(
            stk_track_list.loc[index, 'Now_DayRatio'] / stk_track_list.loc[index, 'PostSec_BfNow_Avg'], 3) \
            if stk_track_list.loc[index, 'PostSec_BfNow_Avg'] != 0 \
            else 0
        Bottom_Date = stk_track_list.loc[index, 'Bottom_Date']
        if Bottom_Date < Section_StartDate \
                and stk_data.loc[Bottom_Date:Section_StartDate, 'close'].idxmax() == \
                stk_track_list.loc[index, 'Section_PeakDate']:
            stk_track_list.loc[index, 'SecPeak_Match'] = 'PeakMatch'
        elif Bottom_Date == Section_StartDate:
            stk_track_list.loc[index, 'SecPeak_Match'] = 'BottomMatch'
        else:
            stk_track_list.loc[index, 'SecPeak_Match'] = '-'

        stepback_sum = int(stk_track_list.loc[index, 'PostTurn_StepBackDate'] != '-') \
                       + int(stk_track_list.loc[index, 'PostSecPeak_StepBackDate'] != '-') \
                       + int(stk_track_list.loc[index, 'PostSec_StepBackDate'] != '-')
        stk_track_list.loc[index, 'StepBack_Signal'] = stepback_sum \
            if (0 < stk_track_list.loc[index, 'PostTurn_StepBack_LastDays'] < 15
                or 0 < stk_track_list.loc[index, 'PostSecPeak_StepBack_LastDays'] < 15
                or 0 < stk_track_list.loc[index, 'PostSec_StepBack_LastDays'] < 15) \
            else 0
        stk_track_list.loc[index, 'BreakSOS_Signal'] = 'True' \
            if stk_track_list.loc[index, 'PostTurn_SOSDate'] != '-' \
               and stk_track_list.loc[index, 'PostSecPeak_SOSDate'] != '-' \
               and stk_track_list.loc[index, 'PostSec_SOSDate'] != '-' \
               and stk_track_list.loc[index, 'PostTurn_StepBackDate'] == '-' \
               and stk_track_list.loc[index, 'PostSecPeak_StepBackDate'] == '-' \
               and stk_track_list.loc[index, 'PostSec_SOSDate'] == '-' \
            else '-'
        stk_track_list.loc[index, 'PostSecStart_Drop2Sum_State'] = 'True' \
            if stk_track_list.loc[index, 'PostSecStart_SumRatio'] > abs(
            stk_track_list.loc[index, 'PostSecStart_MaxDrop']) \
            else '-'
        stk_track_list.loc[index, 'PreSecWLS_Dev2Sum'] = round(abs(stk_track_list.loc[index, 'PreSec_SumRatio']) /
                                                               stk_track_list.loc[index, 'PreSecWLS_Deviation'], 3) \
            if stk_track_list.loc[index, 'PreSecWLS_Deviation'] != 0 else 0
        stk_track_list.loc[index, 'Peak2SecWLS_Dev2Sum'] = round(abs(stk_track_list.loc[index, 'Peak2Sec_SumRatio']) /
                                                                 stk_track_list.loc[index, 'Peak2SecWLS_Deviation'], 3) \
            if stk_track_list.loc[index, 'Peak2SecWLS_Deviation'] != 0 else 0

        stk_track_list.loc[index, 'Now2SecPeak_Ratio'] = round(
            (stk_data.loc[Now_SecDate:, 'close'].max() /
             stk_data.loc[stk_track_list.loc[index, 'Section_PeakDate'], 'close'] - 1) * 100, 3)

        peak_clsopen_min = min(stk_data.loc[stk_track_list.loc[index, 'Section_PeakDate'], 'close'],
                               stk_data.loc[stk_track_list.loc[index, 'Section_PeakDate'], 'open'])
        breakpeak_list = stk_data.loc[Now_SecDate:].query(
            'close>@peak_clsopen_min')
        if (len(breakpeak_list) > 0
            and sum(stk_ratio.loc[Now_SecDate:] > 7) > 0
            and sum(stk_ratio.loc[breakpeak_list.index] > 7) > 0
            and (stk_ratio.loc[Now_SecDate:][stk_ratio.loc[Now_SecDate:] > 7].index[0] ==
                 stk_ratio.loc[breakpeak_list.index][stk_ratio.loc[breakpeak_list.index] > 7].index[0]
                 or stk_ratio.loc[Now_SecDate:].max() == stk_ratio.loc[breakpeak_list.index].max())) \
                or (stk_track_list.loc[index, 'Shock_Date'] != '-'
                    and stk_data.loc[Now_SecDate:, 'close'].max() >
                    stk_data.loc[stk_track_list.loc[index, 'Shock_Date'], 'open']
                    and 0 < sum(stk_ratio.loc[Now_SecDate:] > 7) <= 2
                    and stk_track_list.loc[index, 'Max2Avg_Turnover'] > 2):
            stk_track_list.loc[index, 'PostNowSec_Over7Break'] = 'True'
        else:
            stk_track_list.loc[index, 'PostNowSec_Over7Break'] = '-'

        # 最近换手率相对Turn_Date至Section_StartDate最低换手率的差额
        turnover_index = stk_ratio.loc[stk_track_list.loc[index, 'Period_TurnDate']:][
            stk_ratio.loc[stk_track_list.loc[index, 'Period_TurnDate']:] <= 9.5].index
        stk_track_list.loc[index, 'Turnover_Diff'] = stk_data['turnover'].iloc[-2:].min() - \
                                                     stk_data.loc[turnover_index, 'turnover'].min()

        stk_track_list.loc[index, 'IndexPeak_Drop'], \
            stk_track_list.loc[index, 'IndexMaxDrop_RecoverDays'], \
            stk_track_list.loc[index, 'IndexMaxDrop_BreakDays'] = None, -1, -1
        if Index_MaxDrop_Date is not None:
            stk_data['ratio'] = (stk_data['close'] /
                                 stk_data['close'].shift(1) - 1) * 100
            MaxDrop_Price = max(stk_data.loc[Index_MaxDrop_Date:, 'open'].iloc[0],
                                stk_data.loc[Index_MaxDrop_Date:, 'close'].iloc[0]) \
                if len(stk_data.loc[Index_MaxDrop_Date:]) > 0 else None
            postmaxdrop_stkdata = stk_data.loc[Index_MaxDrop_Date:].iloc[1:].copy() \
                if len(stk_data.loc[Index_MaxDrop_Date:].iloc[1:]) > 0 else None
            if postmaxdrop_stkdata is not None:
                postmaxdrop_stkdata['Max_CO'] = postmaxdrop_stkdata.apply(
                    lambda fn: max(fn['close'], fn['open']), axis=1)
                postbottomdate = postmaxdrop_stkdata['close'].idxmin()
                if len(postmaxdrop_stkdata.loc[postbottomdate:].query('Max_CO>@MaxDrop_Price')) == 0:
                    postbottomdate = postmaxdrop_stkdata.index[0]
                if MaxDrop_Price is not None \
                        and len(postmaxdrop_stkdata.loc[postbottomdate:].query('Max_CO>@MaxDrop_Price')) > 0:
                    BreakMaxDrop_Date = postmaxdrop_stkdata.loc[
                                        postbottomdate:].query('Max_CO>@MaxDrop_Price').index[0]
                    stk_track_list.loc[index, 'IndexMaxDrop_RecoverDays'] = len(
                        stk_data.loc[Index_MaxDrop_Date:BreakMaxDrop_Date]) - 1
                    stk_track_list.loc[index, 'IndexMaxDrop_BreakDays'] = len(
                        stk_data.loc[BreakMaxDrop_Date:])

        if index_peakdate is not None:
            stk_track_list.loc[index, 'IndexPeak_Drop'] = round(
                (stk_data.loc[index_peakdate:, 'close'].min() /
                 max(stk_data.loc[:index_peakdate, 'close'].iloc[-1],
                     stk_data.loc[:index_peakdate, 'pre_close'].iloc[-1]) - 1) * 100, 3) \
                if len(stk_data.loc[:index_peakdate]) > 0 else 0
            # stk_track_list.loc[index, 'IndexPeak_Drop2SecRise'] = round(
            #     stk_track_list.loc[index, 'IndexPeak_Drop'] /
            #     ((stk_data.loc[Section_StartDate:index_peakdate, 'close'].max() /
            #      stk_data.loc[Section_StartDate, 'close'] - 1) * 100), 3) \
            #     if Section_StartDate < index_peakdate else 0

        if Section_StartDate == Now_SecDate:
            stk_track_list.loc[index, 'PostSec_MaxTurnover'] = stk_track_list.loc[index, 'PostTurn_MaxTurnover']
            stk_track_list.loc[index, 'PostSec_Max2Avg_Turnover'] = stk_track_list.loc[
                index, 'PostTurn_Max2Avg_Turnover']
        else:
            stk_track_list.loc[index, 'PostSec_MaxTurnover'] = stk_track_list.loc[index, 'PostSecStart_MaxTurnover']
            stk_track_list.loc[index, 'PostSec_Max2Avg_Turnover'] = stk_track_list.loc[
                index, 'PostSecStart_Max2Avg_Turnover']

        # if (stk_track_list.loc[index, 'Peak2Turn_Reg_R2'] > 0.6
        #     and stk_track_list.loc[index, 'Peak2Turn_LastDays'] > 60
        #     and stk_track_list.loc[index, 'Peak2Turn_COR_Und2Poxn'] > 0.6
        #     and stk_track_list.loc[index, 'PostTurn_RiseRatio'] < 100):
        #     stk_track_list.loc[index, 'Quntl_Diff'] = stk_track_list.loc[index, 'PostSecStart_COR_Mean'] - \
        #                                                  stk_track_list.loc[index, 'Peak2Turn_COR_Mean']
        if (stk_track_list.loc[index, 'Peak2Sec_Reg_R2'] > 0.6
            and stk_track_list.loc[index, 'Peak2Sec_LastDays'] > 60
            and stk_track_list.loc[index, 'Peak2Sec_COR_Und2Poxn'] > 0.6) \
                and Section_StartDate < Now_SecDate:
            stk_track_list.loc[index, 'Quntl_Diff'] = stk_track_list.loc[index, 'PreNowSec_COR_Mean'] - \
                                                      stk_track_list.loc[index, 'Peak2Sec_COR_Mean']
        elif (stk_track_list.loc[index, 'Peak2Turn_Reg_R2'] > 0.6
              and stk_track_list.loc[index, 'Peak2Turn_LastDays'] > 60
              and stk_track_list.loc[index, 'Peak2Turn_COR_Und2Poxn'] > 0.6) \
                and Section_StartDate == Now_SecDate:
            stk_track_list.loc[index, 'Quntl_Diff'] = stk_track_list.loc[index, 'Peak2Sec_COR_Mean'] - \
                                                      stk_track_list.loc[index, 'Peak2Turn_COR_Mean']
        else:
            stk_track_list.loc[index, 'Quntl_Diff'] = None

        stk_track_list.loc[index, 'PostSecStart_Drop2Rise_Ratio'] = round(
            abs(stk_track_list.loc[index, 'PostSecStart_MaxDrop']) / stk_track_list.loc[
                index, 'PostSecStart_RiseRatio'], 3) \
            if stk_track_list.loc[index, 'PostSecStart_RiseRatio'] > 0 else 0
        stk_track_list.loc[index, 'PostTurn_Drop2Rise_Ratio'] = round(
            abs(stk_track_list.loc[index, 'PostTurn_MaxDrop'] / stk_track_list.loc[index, 'PostTurn_RiseRatio']), 3) \
            if stk_track_list.loc[index, 'PostTurn_RiseRatio'] > 0 else 0

        stk_track_list.loc[index, 'Now_Turnover_MinNowSec'] = 'True' \
            if stk_data.loc[stk_track_list.loc[index, 'PreNow_SecDate']:, 'turnover'].min() == \
               stk_data['turnover'].iloc[-1] \
            else '-'
        stk_track_list.loc[index, 'PreNowSec_NoBreak'] = 'True' \
            if stk_data.loc[stk_track_list.loc[index, 'Now_SecDate']:, 'close'].max() < \
               stk_data.loc[stk_track_list.loc[index, 'PreNow_SecDate'], 'close'] \
            else '-'

    stk_track_list['Adj_Date'] = end_date
    # stk_track_list.loc[stk_track_list.query('Aft_SecFit_Break>0 & Aft_SecFit_Break<3').index, 'track_signal'] = 'True'
    # 设立筛选标准，依据测算指标进行筛选，剔除不符合的品种
    pre_signal = stock_data['trade_date'].unique()[-5]
    if filter_method == 1:
        num_preturn = -1
        num_maxdrop = -5
        num_dropprop = 0.5
        stk_track_list['maxdrop_temp'] = abs(
            stk_track_list['Aft_SecFit_MaxDrop'])
        result = stk_track_list.query('Aft_SecFit_SumRatio<50 & '
                                      'Aft_SecFit_DropProp<@num_dropprop &'
                                      '(PreTurn_Period_AvgRatio>@num_preturn | PreTurn_Period_Lastdays<=10) & '
                                      'Aft_SecFit_MaxDrop>@num_maxdrop & '
                                      'maxdrop_temp < Aft_SecFit_SumRatio & '
                                      'PostBottom_TO_Over10Num<50').copy()
        result = result.drop(columns=['maxdrop_temp'])
        result = result.sort_values('Aft_SecFit_SumRatio', ascending=False)
    elif filter_method == 2:
        num_preturn = -1
        num_dropprop = 0.5
        result = stk_track_list.query('(PreTurn_Period_AvgRatio>@num_preturn | PreTurn_Period_Lastdays<=10) & '
                                      'PostBottom_TO_Over10Num<50 &'
                                      '(Shrink_Date>=@pre_signal | Spring_Date>=@pre_signal)').copy()
        result = result.sort_values('Aft_SecFit_SumRatio', ascending=False)
    elif filter_method == 3:
        stk_track_list['PostSecStart_ContiRatio'] = round(
            stk_track_list['PostSecStart_MaxContiSum'] / stk_track_list['PostSecStart_RiseRatio'], 3)
        result = stk_track_list.query('(PostTurn_RiseRatio>20 | PostSecWLS_R2>0.8) & '
                                      'PostSecWLS_Slope<1 & '
                                      'PostSecWLS_R2>0.5 & '
                                      'PostSecStart_Drop2Rise<0.3 & '
                                      '(Break_LastShock_Days>0 | Break_Shrink_Days>0)').copy()
        result = result.sort_values('PostSecStart_RiseRatio', ascending=False)
    elif filter_method == 4:
        num = -30
        result = stk_track_list.query('PostSecWLS_Slope<1.9 & '
                                      'PostSec_SOSDate!="-" & '
                                      '(Peak2Turn_Ratio<0.6 | Turn2Peak_Ratio<0.6 | Peak2Sec_Ratio<0.6 ) & '
                                      'Concave_Break_Days2Now>100 & '
                                      '(Peak2Turn_SumRatio>@num | Peak2Sec_SumRatio>@num) & '
                                      '(PreTurn_MaxTurnover>3 | PostTurn_MaxTurnover>5 | PostBottom_MaxTurnover>5) & '
                                      'Aft_SecFit_AvgRatio<1.5  '
                                      ).copy()
        # 'PostSecStart_LastDays>=3 & '
        # 'PostSecStart_LastDays<25'
        result = result.sort_values(
            ['BreakLastShock2Now_Days', 'Break_LastShock_Days'], ascending=[True, True])
        result1 = result.query('Peak2Sec_Ratio<0.6').copy().sort_values('Peak2Sec_LastDays', ascending=False)
        result2 = result.query('Peak2Turn_Ratio<0.6').copy(
        ).sort_values('Peak2Turn_LastDays', ascending=False)
        result3 = result.query('Turn2Peak_Ratio<0.6').copy(
        ).sort_values('PostTurn_LastDays', ascending=False)
        result = pd.concat([result1, result2], ignore_index=True)
        result = pd.concat([result, result3], ignore_index=True)
        result = result.drop_duplicates(subset=['ts_code'], keep='first')
    elif filter_method == 5:
        # 筛选逻辑：找慢牛。Section_StartDate之后缓步上行，小幅回档。Now_SecDate与指数回档位吻合
        num = -10
        stk_track_list['threshold'] = stk_track_list.apply(
            lambda fn: 20 * 0.2 if fn['ts_code'][0] == '3' or fn['ts_code'][:3] == '688' else 10 * 0.2, axis=1)
        result = stk_track_list.query('PostSecStart_COR_Quntl<threshold & '
                                      '(PostSecStart_MaxContiDrop>@num | PostSecStart_MaxContiDropDays<=2) & '
                                      'PostSecStart_AvgRatio<2 & '
                                      'PostSecStart_LastDays>20 &'
                                      'PostSec_SOSDate!="-" & '
                                      'Concave_Break_Days2Now>100 & '
                                      'PostBottom_MaxTurnover>5').copy()
        result = result.drop(columns=['threshold'])
        result = result.sort_values(
            ['BreakLastShock2Now_Days', 'Break_LastShock_Days'], ascending=[True, True])
    elif filter_method == 6:
        # 筛选逻辑：找快牛。长周期下行，在当前指数转折点启动快速拉升走势
        num = -0.5
        result = stk_track_list.query('LastDrop_COR_Ratio>1.2 & '
                                      'Section_StartDate==Now_SecDate & '
                                      'Peak2Sec_LastDays>40 &'
                                      'Peak2Sec_AvgRatio>@num & '
                                      'PostPeak_Low=="True" & '
                                      'Bottom_Date<Section_StartDate & '
                                      'PostSecStart_LastDays<10 & '
                                      'Break_LastShock_Days>0').copy()
        result = result.sort_values(
            ['BreakLastShock2Now_Days', 'Break_LastShock_Days'], ascending=[True, True])
    elif filter_method == 7:
        # 筛选快牛品种，已出现拉升走势
        num1 = -10
        num2 = -20
        result = stk_track_list.query('PostPeak_Low=="True" & '
                                      'Break_LastShock_Days>0 & '
                                      '((PostSecStart_RiseSecNum<=3 & PostSecStart_MaxDrop>@num1) | '
                                      '(Period_TurnDate==Section_StartDate & PostSecStart_MaxDrop>@num2)) & '
                                      '(Peak2Sec_LastDays>15 | Peak2Sec_Ratio<=0.2) & '
                                      '((PreSecWLS_R2>0.75 & PreSecWLS_Dev2Sum>2) | '
                                      '(Peak2SecWLS_R2>0.75 & Peak2SecWLS_Dev2Sum>2)) & '
                                      '(PreNowSec_LastDays>=8 | PreNowSec_MaxClsOpenRatio>Peak2Sec_COR_Quntl) & '
                                      '(PreTurn_Turnover2Drop_Last4Rank==1 | PreSec_Turnover2Drop_Last4Rank==1 | '
                                      'PostBottom_RiseRatio>50) & '
                                      'PostSecStart_Drop2Sum_State=="True" & '
                                      'Peak2Sec_Ratio<0.8 & '
                                      '(MaxTurnover>2.5 | Max2Avg_Turnover>2) & '
                                      '(PreSec_Adverse2Trend>0 & PreSec_Adverse2Trend<0.6) & '
                                      '(PostSecStart_RiseSecNum<=2 & PostSecStart_LastDays>22) & '
                                      '(PostSec_1stSec_BreakRatio>0 | PostSec_Max_BreakRatio>0) & '
                                      '(Now_Turnover_Signal<=1 | Now_Turnover_Rank<10) '
                                      ).copy()
        result = result.sort_values(
            by='PreNowSec_Adverse2Trend', ascending=True)
    elif filter_method == 8:
        # 依据成交量萎缩筛选转折点品种
        num = -25
        result = stk_track_list.query('PostPeak_Low=="True" & '
                                      'PostSecStart_MaxDrop>@num & '
                                      '(Peak2Sec_LastDays>15 | Peak2Sec_Ratio<=0.2) & '
                                      '(PreNowSec_LastDays>=8 | PreNowSec_MaxClsOpenRatio>Peak2Sec_COR_Quntl) & '
                                      '(PreSec_Adverse2Trend>0 & PreSec_Adverse2Trend<0.6) & '
                                      '(Section_StartDate==Now_SecDate | '
                                      '(PostSecStart_LastDays>22 & Break_LastShock_Days>0)) & '
                                      '(Now_Turnover_Signal<=1 | Now_Turnover_Rank<10) & '
                                      '(Peak2SecWLS_R2>0.9 | Peak2SecWLS_Dev2Sum>2) & '
                                      '(PostBottom_TO_Over10Num>0 | PostTurn_Over7Num>0) & '
                                      '(Now_Shock_Date!="-" | Now_Spring_Date!="-")'
                                      ).copy()
        # '(PreTurn_Turnover2Drop_Last4Rank==1 | PreSec_Turnover2Drop_Last4Rank==1 | '
        # 'PostBottom_RiseRatio>50) & '
        # 'PostSecStart_Drop2Sum_State=="True" & '
        # '(PostSecStart_MaxTurnover>2.5 | '
        # 'PostSecStart_Max2Avg_Turnover>2 | PostBottom_MaxTurnover>10) & '
        result = result.sort_values(by='Now_Turnover_Signal', ascending=True)
    elif filter_method == 9:
        # 依据首个Section突破PreSec价格作为上行确认指标
        num = -20
        result = stk_track_list.query('PostSecStart_RiseSecNum<=2 & '
                                      '(PostSec_1stSec_BreakRatio>0 | PostSec_Max_BreakRatio>0) & '
                                      'PostSecStart_MaxDrop>@num & '
                                      '(Now_Turnover_Signal<=1 | Now_Turnover_Rank<10) & '
                                      '(Peak2SecWLS_R2>0.8 | Peak2SecWLS_Dev2Sum>2) & '
                                      '(PreSec_Adverse2Trend>=0 & PreSec_Adverse2Trend<0.6)').copy()
        result = result.sort_values(by='Now_Turnover_Signal', ascending=True)
    elif filter_method == 10:
        # 融合7/8/9标准
        num = -20
        result = stk_track_list.query('PostSecStart_RiseSecNum<=3 & '
                                      '(Period_TurnDate<Section_StartDate | PostSecStart_RiseSecNum>=1 | '
                                      'Bottom_Date==Now_SecDate) & '
                                      'PostSecStart_MaxDrop>@num & '
                                      '(PostSecStart_MaxContiAvg<2 | PostSecStart_MaxContiSum<30) & '
                                      '(Now_Turnover_Signal<=1 | Now_Turnover_Rank<15 | '
                                      'IndexMaxDrop_RecoverDays>=0 | NowSec_Diff<=3) & '
                                      '(Peak2Sec_LastDays>=30 | '
                                      'Peak2Turn_LastDays>=30 | '
                                      'PostTurn_Peak2Now_Lastdays>=30) & '
                                      'Peak2SecWLS_R2>0.6 & '
                                      '(PreSec_Adverse2Trend>=0 & PreSec_Adverse2Trend<0.6)').copy()
    else:
        result = stk_track_list
    # '(PostNowSec_Adverse2Trend>=0 & PostNowSec_Adverse2Trend<0.6) & '
    if SecFit_Date is not None:
        result = result.query('SecStart_Diff<=3')
    if NowFit_Date is not None:
        result = result.query('NowSec_Diff<=3')
    return result


def cal_spring_turn(result_loc):
    """统计按行业归属统计Spring_Date转折点"""
    indus_turns = result_loc[['ts_code', 'industry', 'Spring_Date']].groupby(
        ['industry', 'Spring_Date'], as_index=False).count()
    indus_turns = indus_turns.rename(columns={'ts_code': 'spring_num'})
    indus_stknum = result_loc[['ts_code', 'industry']
    ].groupby('industry', as_index=False).count()
    indus_stknum = indus_stknum.rename(columns={'ts_code': 'stk_num'})
    indus_turns = pd.merge(indus_turns, indus_stknum,
                           on='industry', how='left')
    indus_turns['Ratio'] = round(
        indus_turns['spring_num'] * 100 / indus_turns['stk_num'], 3)
    indus_turns = indus_turns.sort_values(
        ['Spring_Date', 'Ratio'], ascending=[False, False])
    return indus_turns


def cal_track_pool(industry=None, start_date=None, end_date=None,
                   Index_PeakDate=None, Index_DropDate=None,
                   Section_StartDate=None, Now_SecDate=None,
                   NowPick_Date=None, filter_mode=0, PickMode='Now'):
    """获取筛选结果"""
    trade_dates = get_trade_date()
    check_dates = trade_dates[(trade_dates >= start_date) & (
            trade_dates <= end_date)].tolist()
    check_dates.reverse()
    track_pool_all = pd.DataFrame()
    if PickMode == 'His' and NowPick_Date is not None:
        for date in check_dates:
            track_pool = build_track_pool(end_date=date,
                                          industry=industry,
                                          pickmode=3)
            track_pool_adj = adjust_track_pool(end_date=date,
                                               index_peakdate=Index_PeakDate,
                                               index_dropdate=Index_DropDate,
                                               SecFit_Date=Section_StartDate,
                                               track_pool=track_pool,
                                               filter_method=filter_mode)
            track_pool_all = pd.concat(
                [track_pool_all, track_pool_adj], ignore_index=True)
            print('finished:', date)
        track_pool_all = track_pool_all.drop_duplicates(
            subset=['ts_code'], keep='last')

        track_resent = get_result_3(end_date=NowPick_Date)
        track_resent = track_resent.rename(columns={'Now_SecDate': 'Recent_NowSecDate',
                                                    'Section_StartDate': 'Recent_SecDate',
                                                    'PostSecStart_MaxDrop': 'Recent_PostSecMaxDrop',
                                                    'PostSecStart_RiseSecNum': 'Recent_PostSec_RiseSecNum',
                                                    'PostSecWLS_R2': 'Recent_PostSecWLS_R2'})
        track_pool_all = pd.merge(track_pool_all, track_resent[['ts_code', 'Recent_SecDate',
                                                                'Recent_NowSecDate', 'Recent_PostSecMaxDrop',
                                                                'Recent_PostSec_RiseSecNum', 'Recent_PostSecWLS_R2']],
                                  on='ts_code', how='left')
        track_pool_all['Recent_Diff'] = track_pool_all['Recent_NowSecDate'].apply(
            lambda fn: len(
                trade_dates[(trade_dates >= fn) & (trade_dates < Now_SecDate)])
            if fn <= Now_SecDate else len(trade_dates[(trade_dates >= Now_SecDate) & (trade_dates < fn)]))
        track_pool_all = track_pool_all.query(
            'Recent_Diff<=3 & Recent_PostSec_RiseSecNum<=3')
    elif PickMode == 'Now':
        for date in check_dates:
            track_pool = build_track_pool(end_date=date,
                                          industry=industry,
                                          pickmode=3)
            track_pool_adj = adjust_track_pool(end_date=date,
                                               index_peakdate=Index_PeakDate,
                                               index_dropdate=Index_DropDate,
                                               SecFit_Date=Section_StartDate,
                                               NowFit_Date=Now_SecDate,
                                               track_pool=track_pool,
                                               filter_method=filter_mode)
            track_pool_all = pd.concat(
                [track_pool_all, track_pool_adj], ignore_index=True)
            print('finished:', date)
            track_pool_all = track_pool_all.drop_duplicates(
                subset=['ts_code'], keep='last')
    return track_pool_all


def pick_from_track_pool(SecPick_Date=None, NowPick_Date=None, Section_StartDate=None,
                         Now_SecDate=None, Index_PeakDate=None, Index_DropDate=None,
                         industry=None, filter_mode=10, storemode=False):
    track_result_His = pd.DataFrame()
    track_result_Now = pd.DataFrame()
    if SecPick_Date is not None and NowPick_Date is not None:
        # 筛选Section_StartDate不吻合的品种
        if isinstance(SecPick_Date, str):
            SecPick_Date = [SecPick_Date]
        if isinstance(NowPick_Date, str):
            NowPick_Date = [NowPick_Date]
        track_pool_adj_His = cal_track_pool(start_date=SecPick_Date[0],
                                            end_date=SecPick_Date[0] if len(SecPick_Date) == 1
                                            else SecPick_Date[1],
                                            Section_StartDate=Section_StartDate,
                                            NowPick_Date=NowPick_Date[-1],
                                            Now_SecDate=Now_SecDate,
                                            filter_mode=filter_mode,
                                            PickMode='His')
        # Section_StartDate与转折点未吻合品种
        track_result_His = track_pool_adj_His[['ts_code', 'name', 'industry', 'Section_StartDate', 'Now_SecDate',
                                               'Bottom2Now_Ratio', 'PreNowSec_Adverse2Trend', 'Peak2Sec_Ratio',
                                               'PostNowSec_Adverse2Trend',
                                               'PostTurn_BreakRatio', 'PostSec_1stSec_BreakRatio',
                                               'PostSecStart_MaxTurnover', 'PostSecStart_Max2Avg_Turnover',
                                               'PostSecStart_MaxDrop', 'PostSecStart_RiseRatio',
                                               'PostSecStart_AvgRatio', 'PostSecStart_MaxContiSum',
                                               'PostTurn_RiseRatio', 'PostTurn_MaxContiSum',
                                               'IndexPeak_Drop', 'IndexMaxDrop_RecoverDays', 'IndexMaxDrop_BreakDays',
                                               'Now2SecPeak_Ratio', 'LastDrop_COR_Ratio', 'PostNowSec_Over7Break',
                                               'Peak2Sec_LastDays', 'Peak2Sec_AvgRatio', 'Peak2Sec_SumRatio',
                                               'PostTurn_LastDays', 'PostTurn_RiseAvg',
                                               'Peak2SecWLS_R2', 'PostSecWLS_R2', 'Recent_PostSecWLS_R2',
                                               'Recent_PostSecMaxDrop', 'Recent_Diff', 'Turnover_Diff', 'Quntl_Diff',
                                               'NowSec_AvgRatio', 'NowSec_LastDays',
                                               'Now_Turnover_Quntl', 'Now_Turnover_Signal', 'Now_Turnover_Rank'
                                               ]].copy()
        track_result_His = track_result_His.query('Recent_Diff<=3')
        track_result_His = track_result_His.sort_values(by=['Now_SecDate', 'PostNowSec_Adverse2Trend'],
                                                        ascending=[True, True])
        if industry is not None:
            track_result_His = track_result_His.query('industry in @industry')
    if NowPick_Date is not None:
        if isinstance(NowPick_Date, str):
            NowPick_Date = [NowPick_Date]
        # 筛选Section_StartDate吻合的品种
        track_pool_adj_Now = cal_track_pool(start_date=NowPick_Date[0],
                                            end_date=NowPick_Date[0] if len(NowPick_Date) == 1
                                            else NowPick_Date[1],
                                            Section_StartDate=Section_StartDate,
                                            Now_SecDate=Now_SecDate,
                                            Index_PeakDate=Index_PeakDate,
                                            Index_DropDate=Index_DropDate,
                                            filter_mode=filter_mode,
                                            PickMode='Now')
        # Section_StartDate吻合品种
        track_result_Now = track_pool_adj_Now[['ts_code', 'name', 'industry',
                                               'Bottom_Date', 'Period_TurnDate',
                                               'Section_StartDate', 'Now_SecDate',
                                               'Bottom2Now_Ratio', 'PostBottom_RiseRatio', 'PostTurn_RiseRatio',
                                               'PostTurn_MaxDrop',
                                               'PreNowSec_Adverse2Trend', 'PreSec_Adverse2Trend', 'Peak2Sec_Ratio',
                                               'PostTurn_Peak2Now_SumRatio',
                                               'PostNowSec_Adverse2Trend', 'PreNowSec_SumRatio',
                                               'PostNowSec_COR_Und2Poxn',
                                               'PreSec_Und2ContiDays', 'PreNowSec_Und2ContiDays',
                                               'NowSec_Und2ContiDays', 'Pre_PreNowSec_Und2ContiDays',
                                               'Section_Break_Ratio',
                                               'PostTurn_BreakRatio', 'PostSec_1stSec_BreakRatio',
                                               'PostSec_MaxTurnover', 'PostSec_Max2Avg_Turnover',
                                               'PostTurn_MaxTurnover', 'PostTurn_Max2Avg_Turnover',
                                               'PostSecStart_MaxDrop', 'PostSecStart_RiseRatio',
                                               'PostSecStart_AvgRatio', 'PostSecStart_LastDays',
                                               'PostSecStart_MaxDailyRatio',
                                               'PostSecStart_Avg_LastDays', 'PostTurn_Peak2Now_Lastdays',
                                               'PostSecStart_Adverse2Trend',
                                               'PostSecStart_MaxContiDrop',
                                               'PostNowSec_ExtreRatio', 'PreNowSec_AvgRatio', 'PreNowSec_LastDays',
                                               'PostSecStart_MaxContiSum', 'PostSecStart_MaxContiAvg',
                                               'PostSecStart_RiseSecNum',
                                               'IndexPeak_Drop',
                                               'IndexMaxDrop_RecoverDays', 'IndexMaxDrop_BreakDays',
                                               'Now2SecPeak_Ratio', 'LastDrop_COR_Ratio', 'PostNowSec_Over7Break',
                                               'Peak2Sec_LastDays', 'Peak2Sec_AvgRatio', 'Peak2Sec_SumRatio',
                                               'Peak2Sec_COR_Und2Poxn', 'PreNowSec_COR_Und2Poxn',
                                               'PostTurn_COR_Und2Poxn', 'PostTurn_COR_Mean',
                                               'Peak2Turn_COR_Und2Poxn', 'Peak2Turn_COR_Mean',
                                               'PostSecStart_COR_Und2Poxn', 'PostSecStart_COR_Mean',
                                               'PostTurn_Sec_RiseNum', 'PostTurn_Sec_DropNum',
                                               'PostTurn_LastDays', 'PostTurn_RiseAvg',
                                               'PostTurn_Sec_Max_SumRatio', 'PostTurn_Sec_Min_SumRatio',
                                               'PostTurn_1stSec_Lastdays',
                                               'Peak2SecWLS_R2', 'PostSecWLS_R2', 'Turnover_Diff', 'Quntl_Diff',
                                               'NowSec_AvgRatio', 'NowSec_LastDays',
                                               'Now_DayRatio', 'Now_HighLow_Ratio',
                                               'PostNowSec_Over7Num', 'PostTurn_Over7Num', 'PostSecStart_Over7Num',
                                               'Now_Turnover_Quntl', 'Now_Turnover_Signal', 'Now_Turnover_Rank',
                                               'Now_Turnover_MinGap', 'Now_Turnover_MinNowSec', 'PreNowSec_NoBreak',
                                               'PostPeak_Recent_Neg4_DropDate', 'PostPeak_Recent_Neg4_RecovDays',
                                               'PreNow_Rise_Recent_MeanRatio', 'PreNow_Rise_MaxContiSum',
                                               'PreSec_Turnover2Drop_Last4Rank', 'PreSec_Turnover2Drop',
                                               'Shock_Date', 'BreakShock_Date', 'Break_PreSec', 'SecValley_GapRatio',
                                               'SecConcave_LastDays', 'SecConcave_BfBottom_LastDays',
                                               'SecConcave_AftBottom_LastDays',
                                               'SecConcave_BfBottom_TO_Sum', 'SecConcave_TO_Sum',
                                               'SecConcave_AftBottom_TO_Sum',
                                               'Break_D100_Ratio', 'PostTurn_Drop2Rise_Ratio',
                                               'PostSecStart_Drop2Rise_Ratio'
                                               ]].copy()
        # track_result_Now = track_result_Now.sort_values(by=['IndexMaxDrop_BreakDays', 'Now_Turnover_Signal'],
        #                                                 ascending=[True, True])
        # track_result_Now = track_result_Now.query('(PostBottom_RiseRatio<100 | PostTurn_RiseRatio<50)')
        # '(PostSec_MaxTurnover>10 | PostSec_Max2Avg_Turnover>2)')
        track_result_Now = track_result_Now.sort_values(
            by='SecConcave_LastDays', ascending=False)
        if Index_DropDate is not None:
            track_result_Now = track_result_Now.query(
                'IndexMaxDrop_BreakDays>0')
        if industry is not None:
            track_result_Now = track_result_Now.query('industry in @industry')
        if storemode:
            import config.config_Ali as config
            conf = config.configModel()
            engine = create_engine(
                'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                    conf.DC_DB_PORT) + '/stocksfit')
            track_result_Now['NowPick_Date'] = NowPick_Date[-1]
            pd.io.sql.to_sql(track_result_Now, 'track_pool', engine, index=False, schema='stocksfit',
                             if_exists='append')
            engine.dispose()
            print(NowPick_Date, '筛选数据已存储！')
    return track_result_His, track_result_Now


def get_track_pool(indexdrop_startdate=None,
                   check_dates=None, check_mode='Update',
                   industry=None, confirm_lag=0):
    """读取数据库，获取筛选结果数据"""
    result_adj_out = None
    result_confirms = None

    if check_dates is not None:
        if isinstance(check_dates, str):
            check_dates = [check_dates]
        result = get_result_3(start_date=min(check_dates),
                              end_date=max(check_dates))
        if len(result) == 0:
            print('缺失日期', check_dates[-1], '数据')
            return
        if industry is not None:
            if isinstance(industry, str):
                industry = [industry]
            result = result.query('industry in @industry')
        # track_list = track_result['ts_code'].values.tolist()
        trade_dates = get_trade_date()
        column_list = ['ts_code', 'name', 'industry', 'Bottom_Date', 'Period_TurnDate',
                       'Section_StartDate', 'Now_SecDate', 'PreNow_SecDate',
                       'Target_Price', 'Target_Ratio',
                       'Turn_Target_Price', 'Turn_Target_Ratio',
                       'Section_Target_Price', 'Section_Target_Ratio',
                       'Peak2Sec_LastDays', 'PostTurn_LastDays', 'PreNowSec_LastDays',
                       'SecConcave_TO_Sum', 'Break_D100_Ratio',
                       'PostSecStart_Und2SumDays', 'Peak2Sec_Und2SumDays', 'PostSecStart_Und2ContiDays',
                       'PostPeak_Recent_Neg4_DropDate', 'PostPeak_Recent_Neg4_RecovDays',
                       'PostSecStart_RiseRatio', 'PostSecStart_MaxContiSum', 'PostSecStart_MaxDrop',
                       'PostSecStart_Sec_Max_SumRatio', 'PostSecStart_MeanClose',
                       'PostNowSec_Over7Num', 'PostSecStart_Over7Num',
                       'Bottom2Now_Ratio', 'PostBottom_RiseRatio', 'PostTurn_RiseRatio',
                       'PreSec_Und2ContiDays', 'PreNowSec_Und2ContiDays',
                       'NowSec_Und2ContiDays', 'Pre_PreNowSec_Und2ContiDays', 'Pre_PreNowSec_Adverse2Trend',
                       'PostTurn_MaxDrop',
                       'PreNowSec_Adverse2Trend', 'PreSec_Adverse2Trend',
                       'PostTurn_Peak2Now_SumRatio',
                       'PostNowSec_Adverse2Trend', 'PreNowSec_SumRatio',
                       'PostNowSec_COR_Und2Poxn', 'Section_Break_Ratio',
                       'PostTurn_BreakRatio', 'PostSec_1stSec_BreakRatio',
                       'PostTurn_MaxTurnover',
                       'PostSecStart_AvgRatio', 'PostSecStart_LastDays',
                       'PostSecStart_MaxDailyRatio', 'PostSecPeak_MinDaily2Now_LastDays',
                       'PostSecStart_Avg_LastDays', 'PostTurn_Peak2Now_Lastdays',
                       'PostSecStart_Adverse2Trend', 'PostSecStart_MaxTurnover',
                       'PostSecStart_MaxContiAvg',
                       'PostSecStart_MaxContiDrop',
                       'PostNowSec_ExtreRatio', 'PostNowSec_AvgRatio',
                       'PreNowSec_AvgRatio',
                       'PostSecStart_RiseSecNum',
                       'LastDrop_COR_Ratio',
                       'Peak2Sec_AvgRatio', 'Peak2Sec_SumRatio',
                       'Peak2Sec_MaxContiRise', 'Peak2Sec_MaxContiRiseDays',
                       'Peak2Sec_COR_Und2Poxn', 'PreNowSec_COR_Und2Poxn', 'PreNowSec_COR_Mean',
                       'PostTurn_COR_Und2Poxn', 'PostTurn_COR_Mean',
                       'Peak2Turn_COR_Und2Poxn', 'Peak2Turn_COR_Mean',
                       'PostSecStart_COR_Und2Poxn', 'PostSecStart_COR_Mean', 'PostSecStart_MinQuntlRatio',
                       'PostNowSec_COR_Mean', 'Recent_Mean_ClsOpenRatio',
                       'PostNowSec_LastDays',
                       'PostTurn_Sec_RiseNum', 'PostTurn_Sec_DropNum',
                       'PostTurn_RiseAvg',
                       'PostTurn_Sec_Max_SumRatio', 'PostTurn_Sec_Min_SumRatio',
                       'PostTurn_1stSec_Lastdays',
                       'Peak2SecWLS_R2', 'PostSecWLS_R2',
                       'NowSec_AvgRatio',
                       'Now_DayRatio', 'Now_HighLow_Ratio', 'Now_Over3Max',
                       'PostTurn_Over7Num',
                       'Now_Turnover_Quntl', 'Now_Turnover_Signal', 'Now_Turnover_Rank',
                       'Now_Turnover_MinGap',
                       'PreNow_Rise_Recent_MeanRatio', 'PreNow_Rise_MaxContiSum', 'PreNowSec_ExtreRatio',
                       'PreSec_Turnover2Drop_Last4Rank', 'PreSec_Turnover2Drop',
                       'PostSecPeak_ShockDate', 'PostSecPeak_SOSDate', 'PostSecPeak_StepBackDate',
                       'Now_StepBack_State',
                       'Break_PreSec', 'SecValley_GapRatio',
                       'SecConcave_LastDays', 'SecConcave_BfBottom_LastDays',
                       'SecConcave_AftBottom_LastDays', 'SecConcave_BfBottom_TO_Sum',
                       'SecConcave_AftBottom_TO_Sum', 'Peak2Sec_MaxPeakGap', 'Peak2Sec_MaxValleyGap',
                       'PostSecStart_MaxPeakGap', 'PostSecStart_MedianPeakGap', 'Now_PeakGap', 'Now_ValleyGap',
                       'PostSecStart_MaxValleyGap', 'PostSecStart_MedianValleyGap', 'PostSecStart_MinValleyGap',
                       'PostSecStart_MinPeakGap', 'PostSecStart_PeakGap_HighQuntl',
                       'PostSecStart_PeakGap_LowQuntl', 'PostSecStart_ValleyGap_HighQuntl',
                       'PostSecStart_ValleyGap_LowQuntl',
                       'PostSecStart_MaxPeakGap2Yesd_Ratio', 'PostSecStart_PeakGapNow2Med_Ratio',
                       'PreNowSec_MinPeakGap', 'PreNowSec_MaxValleyGap',
                       'PostNowSec_MaxPeakGap', 'PostNowSec_MaxValleyGap',
                       'PreNowSec_BelowMinDaily',
                       'Now_PostTurn_SecDropState',
                       'Now_PostSec_SecDropState', 'PostSec_SecDrop_MaxClsDiff',
                       'Now_PostSec_SecRise_ClsDiff', 'Now_PostSec_SecRise_Portion',
                       'Now_PostSec_SecRiseState', 'Now_PostTurn_SecRiseState',
                       'PostSecStart_Drop2Rise_SecMaxDaysDiv', 'PostSecStart_Drop2Rise_SecSumDaysDiv',
                       'PostSecStart_Drop2Rise_SecMaxSumDiv',
                       'PostSecStart_MovAvg_BiasMean', 'PostSecStart_MovAvg_BiasStd',
                       'Cal_Date'
                       ]
        result = result[column_list]
        result['PostSecStart_Drop2Rise_Ratio'] = \
            result['PostSecStart_MaxDrop'] / result['PostSecStart_RiseRatio']
        result['PostSecStart_Und2Conti_Poxn'] = \
            result['PostSecStart_Und2ContiDays'] / \
            result['PostSecStart_LastDays']
        result['PostSec2Pre_PeakGap_MaxComp'] = \
            result['PostSecStart_MaxPeakGap'] / result['Peak2Sec_MaxPeakGap']
        result['PostSec2Pre_ValleyGap_MaxComp'] = \
            result['PostSecStart_MaxValleyGap'] / \
            result['Peak2Sec_MaxValleyGap']
        result['PostSecStart_PeakGap2Close'] = \
            result['PostSecStart_MaxPeakGap'] / \
            result['PostSecStart_MeanClose']
        result['Now_SecDiff'] = result.apply(lambda fn: conf_nowdiff(fn['Now_SecDate'], check_dates, trade_dates),
                                             axis=1)
        if check_mode is not None:
            # result_adj = result.query('ts_code in @track_list').copy()
            result_adj = result.query('((SecConcave_TO_Sum>40 & SecConcave_LastDays>=25) | PostTurn_Over7Num>=3) & '
                                      '((PostSecPeak_StepBackDate==Now_SecDate | Now_SecDiff<=3) | '
                                      '(Now_ValleyGap>=PostSecStart_ValleyGap_HighQuntl & '
                                      'Now_PeakGap<=PostSecStart_PeakGap_LowQuntl) | '
                                      'PostSecPeak_MinDaily2Now_LastDays<=3) & '
                                      'Now_PeakGap<PostSecStart_MaxPeakGap & '
                                      'PostSecStart_MaxContiSum<50 & '
                                      'PostTurn_Peak2Now_Lastdays>=5 & '
                                      '(Peak2Sec_LastDays>=30 | PostTurn_LastDays>=30) & '
                                      '((Turn_Target_Ratio>=15 & Section_Target_Ratio>=15) | '
                                      '(Target_Ratio>30)) & '
                                      '(PreNowSec_Und2ContiDays>=6 | NowSec_Und2ContiDays>=6 | '
                                      'PreSec_Und2ContiDays>=6 | Pre_PreNowSec_Und2ContiDays>=6) & '
                                      '(Now_PostSec_SecRiseState==0 | '
                                      '(Now_PostSec_SecRiseState==2 & Now_PostSec_SecDropState==3) | '
                                      '(Now_PostSec_SecRiseState==2 & Now_PostSec_SecDropState==2 & '
                                      'PostSecStart_Over7Num>2 & PreNowSec_ExtreRatio<(-3)) |'
                                      '(Now_PostSec_SecRiseState==1 & Now_PostSec_SecDropState==1)) & '
                                      'Now_PostSec_SecDropState>0 & '
                                      '((PreSec_Adverse2Trend<=0.1 & PostSecStart_RiseSecNum<=1) | '
                                      'Pre_PreNowSec_Adverse2Trend<=0.15) & '
                                      '(PostSecStart_Adverse2Trend<0.6 | PostSecStart_Drop2Rise_SecMaxSumDiv<0.4)')

            # result_adj_retrace = result_adj.query('(PostSecPeak_StepBackDate == Now_SecDate | Now_SecDiff<=3) & '
            #                                       '((Now_ValleyGap>PostSecStart_MedianValleyGap & '
            #                                       'Now_PeakGap<PostSecStart_MedianPeakGap) | '
            #                                       'PostSecPeak_MinDaily2Now_LastDays<=3)').sort_values(
            #     by=['Now_PostSec_SecDropState',
            #         'Now_PostSec_SecRiseState', 'Break_D100_Ratio'],
            #     ascending=[False, True, False])
            result_adj_retrace = result_adj.sort_values(by=['Now_PostSec_SecDropState',
                                                            'Now_PostSec_SecRiseState', 'Break_D100_Ratio'],
                                                        ascending=[False, True, False])
            result_adj_retrace = result_adj_retrace.drop_duplicates(
                subset='ts_code', keep='last').copy()
            # result_adj_retrace['Pick_Class'] = 'Retrace'
            # result_adj_pull = result_adj.query('(Now_DayRatio>4 & Now_PeakGap==PostSecStart_MaxPeakGap & '
            #                                    'PostSecStart_MaxPeakGap2Yesd_Ratio>=1.3 & '
            #                                    'PostNowSec_Over7Num<=1)').sort_values(
            #     by=['Now_PostSec_SecDropState',
            #         'Now_PostSec_SecRiseState', 'Break_D100_Ratio'],
            #     ascending=[False, True, False])
            # result_adj_pull = result_adj_pull.drop_duplicates(subset='ts_code', keep='last').copy()
            # result_adj_pull['Pick_Class'] = 'Pull_Up'
            # result_adj_out = pd.concat([result_adj_retrace, result_adj_pull], ignore_index=True).copy()
            result_adj_out = result_adj_retrace.copy()
            result_adj_out = result_adj_out.dropna(subset=['name']).copy()
        else:
            result_adj_out = result.query(
                'ts_code in @track_list').drop_duplicates(subset='ts_code', keep='last')
            result_adj_retrace, result_adj_pull = None, None
        result_adj_out['PostSecStart_PreNow2Med_PeakGap'] = result_adj_out['PreNowSec_MinPeakGap'] / \
                                                            result_adj_out['PostSecStart_MedianPeakGap']
        result_adj_out['PostSecStart_Max2Med_PeakGap'] = result_adj_out['PostSecStart_MaxPeakGap'] / \
                                                         result_adj_out['PostSecStart_MedianPeakGap']
        # result_adj_out = result_adj_out.query('PostSecStart_Max2Med_PeakGap<=5').copy()

        if indexdrop_startdate is not None:
            result_adj_out = result_adj_out.query(
                'Now_SecDate<@indexdrop_startdate').copy()

        if confirm_lag >= -1 and check_mode is not None:
            trade_dates = get_trade_date()
            result_confirms = pd.DataFrame()
            retrace_list = result_adj_retrace['ts_code'].values.tolist()
            # pull_list = result_adj_pull['ts_code'].values.tolist()
            pull_list = []
            confirm_dates = trade_dates[trade_dates >= check_dates[-1]]
            if confirm_lag > 0:
                compare_date = confirm_dates[min(
                    confirm_lag, len(confirm_dates) - 1)]
            elif confirm_lag == 0:
                compare_date = confirm_dates[0]
            else:
                compare_date = trade_dates[-1]
            print('二次确认日期为：', compare_date)
            result_confirm = get_result_3(end_date=compare_date)
            if len(result_confirm) > 0:
                if len(retrace_list) > 0:
                    track_retrace = result_adj_out.query(
                        'ts_code in @retrace_list').copy().drop_duplicates(subset=['ts_code'], keep='last')
                    track_retrace = track_retrace.rename(columns={'Now_SecDate': 'Track_SecDate',
                                                                  'SecConcave_TO_Sum': 'Track_SecConcave_Sum',
                                                                  'PostSecStart_Over7Num': 'PreConfirm_Over7Num'})
                    result_confirm_check = result_confirm.query(
                        'ts_code in @retrace_list').copy()
                    result_confirm_check['Now_SecDiff'] = result_confirm_check.apply(
                        lambda fn: conf_nowdiff(
                            fn['Now_SecDate'], [compare_date], trade_dates),
                        axis=1)
                    # result_confirm_vol = result_confirm_check.query('PostNowSec_COR_Quntl>2*PostSecStart_COR_Quntl | '
                    #                                                 'Recent_Mean_ClsOpenRatio>2*PostSecStart_COR_Quntl | '
                    #                                                 'Recent_Mean_ClsOpenRatio>5')
                    result_confirm_check = pd.merge(
                        result_confirm_check, track_retrace[['ts_code', 'Track_SecDate',
                                                             'Track_SecConcave_Sum', 'PreConfirm_Over7Num']],
                        on='ts_code', how='left')
                    result_confirm_check['Now2Extre_RatioGap'] = result_confirm_check['Now_DayRatio'] - \
                                                                 result_confirm_check['PostSecStart_MaxDailyRatio']
                    result_confirm_pull = result_confirm_check.query(
                        'Now_SecDate==Track_SecDate & '
                        'PostSecStart_MaxPeakGap>PostNowSec_MaxPeakGap>=PostSecStart_PeakGap_HighQuntl & '
                        'PostNowSec_Over7Num<=1').copy()
                    result_confirm_pull['Pick_Class'] = 'Pull_Up'
                    result_confirm_retrace1 = result_confirm_check.query(
                        'Now_SecDate==@compare_date & Section_StartDate!=@compare_date & '
                        'PreNowSec_LastDays>3 & '
                        'PostSecStart_Over7Num==PreConfirm_Over7Num & '
                        '(PostSecPeak_MinDaily2Now_LastDays<=2)').copy()
                    result_confirm_retrace1['Pick_Class'] = 'Retrace_MinDaily'
                    # result_confirm_retrace2 = result_confirm_check.query(
                    #     'Now_SecDate==@compare_date & Section_StartDate!=@compare_date & '
                    #     'PreNowSec_LastDays>3 & '
                    #     'PostSecStart_Over7Num==PreConfirm_Over7Num & '
                    #     '(Now_PeakGap==PostSecStart_MinPeakGap)').copy()
                    # result_confirm_retrace2['Pick_Class'] = 'Retrace_MinPeakGap'
                    result_confirm_retrace3 = result_confirm_check.query(
                        'Now_SecDate==@compare_date & Section_StartDate!=@compare_date & '
                        'PreNowSec_LastDays>3 & '
                        'PostSecStart_Over7Num==PreConfirm_Over7Num & '
                        '(PreNowSec_MinPeakGap<=PostSecStart_MedianPeakGap & '
                        'Now_ValleyGap>PostSecStart_ValleyGap_HighQuntl)').copy()
                    result_confirm_retrace3['Pick_Class'] = 'Retrace_ValleyQuntl'
                    # Diff_Num = 0
                    # result_confirm_retrace4 = result_confirm_check.query(
                    #     'Now_SecDiff<=@Diff_Num & '
                    #     '(PostSecStart_Drop2Rise_SecMaxDaysDiv<0.6 | '
                    #     'PostSecStart_Drop2Rise_SecMaxSumDiv<0.4) & '
                    #     'PreNowSec_BelowMinDaily>=1 & '
                    #     '(Peak2Sec_LastDays>=30 | PostTurn_LastDays>=30) & '
                    #     '(Pre_PreNow_Sec_AvgRatio==PostSecStart_Sec_Max_AvgRatio | '
                    #     'Pre_PreNow_Sec_SumRatio==PostSecStart_Sec_Max_SumRatio)')
                    # result_confirm_retrace4['Pick_Class'] = 'Retrace_SecTurn'
                    result_confirms = pd.concat([result_confirm_pull, result_confirm_retrace1],
                                                ignore_index=True)
                    # result_confirms = pd.concat([result_confirms, result_confirm_retrace2],
                    #                             ignore_index=True)
                    result_confirms = pd.concat([result_confirms, result_confirm_retrace3],
                                                ignore_index=True)
                    # result_confirms = result_confirm_retrace4
                    result_confirms = result_confirms.reset_index()
            else:
                print('缺失result数据，日期：', compare_date)
            # result_confirms = pd.concat([result_confirm_retrace, result_confirm_pull], ignore_index=True) \
            #     if len(pull_list) > 0 else result_confirm_retrace
            if len(result_confirms) > 0:
                result_confirms['PostSecStart_Drop2Rise_Ratio'] = \
                    abs(result_confirms['PostSecStart_MaxDrop'] /
                        result_confirms['PostSecStart_RiseRatio'])
                result_confirms['PostSecStart_Und2Conti_Poxn'] = \
                    result_confirms['PostSecStart_Und2ContiDays'] / \
                    result_confirms['PostSecStart_LastDays']
                result_confirms['PostSec2Pre_PeakGap_MaxComp'] = \
                    result_confirms['PostSecStart_MaxPeakGap'] / \
                    result_confirms['Peak2Sec_MaxPeakGap']
                result_confirms['PostSec2Pre_ValleyGap_MaxComp'] = \
                    result_confirms['PostSecStart_MaxValleyGap'] / \
                    result_confirms['Peak2Sec_MaxValleyGap']
                result_confirms['PostSecStart_PeakGap2Close'] = \
                    result_confirms['PostSecStart_MaxPeakGap'] / \
                    result_confirms['PostSecStart_MeanClose']
        else:
            print('com_lag为0，无result_confirm数据')
    return result_adj_out, result_confirms


def conf_nowdiff(secdate, check_dates, trade_dates, ts_code=None, stock_data=None):
    """lambda函数，判定是否为空"""
    if isinstance(check_dates, str):
        check_dates = [check_dates]
    if len(check_dates) == 0:
        nowsec_diff = 100
    if pd.isnull(secdate):
        nowsec_diff = 100
    elif stock_data is not None and ts_code is not None:
        nowsec_diff = 100
        check_dates.sort(reverse=True)
        for check_date in check_dates:
            if secdate <= check_date \
                    and len(trade_dates[(trade_dates >= secdate) & (trade_dates <= check_date)]) < nowsec_diff:
                nowsec_diff = max(len(
                    trade_dates[(trade_dates >= secdate) & (trade_dates <= check_date)]) - 1, 0)
            elif secdate > check_date \
                    and len(trade_dates[(trade_dates >= check_date) & (trade_dates < secdate)]) < nowsec_diff:
                nowsec_diff = len(
                    trade_dates[(trade_dates >= check_date) & (trade_dates < secdate)]) - 1
            maxcls_in = stock_data.query(
                'ts_code==@ts_code & trade_date>=@secdate & trade_date<=@check_date')['close'].max() \
                if secdate < check_date \
                else stock_data.query(
                'ts_code==@ts_code & trade_date<=@secdate & trade_date>=@check_date')['close'].max()
            mincls_in = stock_data.query(
                'ts_code==@ts_code & trade_date>=@secdate & trade_date<=@check_date')['close'].min() \
                if secdate < check_date \
                else stock_data.query(
                'ts_code==@ts_code & trade_date<=@secdate & trade_date>=@check_date')['close'].min()
            if 3 < nowsec_diff <= 8 and \
                    abs(maxcls_in / stock_data.query('ts_code==@ts_code and trade_date<=@check_date'
                                                     )['close'].iloc[-1] - 1) < 0.035 and \
                    abs(mincls_in / stock_data.query('ts_code==@ts_code and trade_date<=@check_date'
                                                     )['close'].iloc[-1] - 1) < 0.035:
                nowsec_diff = -1
    else:
        nowsec_diff = 100
        check_dates.sort(reverse=True)
        for check_date in check_dates:
            if secdate <= check_date \
                    and len(trade_dates[(trade_dates >= secdate) & (trade_dates <= check_date)]) < nowsec_diff:
                nowsec_diff = len(
                    trade_dates[(trade_dates >= secdate) & (trade_dates <= check_date)]) - 1
            elif secdate > check_date \
                    and len(trade_dates[(trade_dates >= check_date) & (trade_dates <= secdate)]) < nowsec_diff:
                nowsec_diff = len(
                    trade_dates[(trade_dates >= check_date) & (trade_dates <= secdate)]) - 1
    return nowsec_diff


def simple_pick(end_date=None, industry=None):
    result = get_result_3(end_date=end_date)
    result['Peak2SecWLS_Dev2Sum'] = result.apply(
        lambda fn: round(abs(fn['Peak2Sec_SumRatio'] /
                             fn['Peak2SecWLS_Deviation']), 3)
        if fn['Peak2SecWLS_Deviation'] != 0 else 0, axis=1)
    result = result.query('(Now_Turnover_Quntl<=0.1 | Now_Turnover_Rank<=5) & '
                          '(Peak2SecWLS_R2>0.9 | Peak2SecWLS_Dev2Sum>2) & '
                          '(PostBottom_TO_Over10Num>0 | PostTurn_Over7Num>0)'
                          )
    result = result.sort_values('Now_Turnover_Signal', ascending=True)
    if industry is not None:
        if isinstance(industry, str):
            industry = [industry]
        result = result.query('industry in @industry')
    result = result[['ts_code', 'name', 'industry', 'Section_StartDate', 'Now_SecDate',
                     'Bottom2Now_Ratio', 'PreNowSec_Adverse2Trend',
                     'PostNowSec_Adverse2Trend',
                     'PostSecStart_MaxTurnover',
                     'PostSecStart_MaxDrop', 'PostSecStart_RiseRatio', 'PostSecStart_AvgRatio',
                     'PostSecStart_MaxContiSum', 'LastDrop_COR_Ratio',
                     'Peak2Sec_LastDays', 'Peak2Sec_AvgRatio',
                     'PostTurn_LastDays', 'PostTurn_RiseAvg',
                     'Peak2SecWLS_R2', 'PostSecWLS_R2',
                     'Now_Turnover', 'Now_Turnover_Quntl', 'Now_Turnover_Signal', 'Now_Turnover_Rank'
                     ]].copy()
    return result


def get_trackpool_adj(start_date=None, end_date=None, industry=None, storemode=False):
    """存储result_adj筛选结果"""
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    if start_date is None:
        start_date = end_date
    sql = f"""select ts_code, name, Section_StartDate, Cal_Date
             from stocksfit.stk_results_Common
             where Cal_Date between '{start_date}' and '{end_date}' and Section_StartDate is not Null"""
    result = pd.read_sql_query(sql=sql, con=engine)
    engine.dispose()
    Sec_Check = result[['ts_code', 'Section_StartDate']].groupby('ts_code')[
        'Section_StartDate'].max()
    Sec_Df = pd.DataFrame(data=Sec_Check).rename(
        columns={'Section_StartDate': 'Max_SecDate'})
    Sec_Df = Sec_Df.reset_index(drop=False)
    result_start = result.query('Cal_Date==@start_date')
    result_check = pd.merge(result_start, Sec_Df, on='ts_code', how='inner')
    result_check = result_check.query('Section_StartDate==Max_SecDate')
    check_list = result_check['ts_code'].tolist()
    # trade_dates = get_trade_date()
    # result['Now_SecDiff'] = result.apply(lambda fn: conf_nowdiff(fn['Now_SecDate'], check_dates[-1], trade_dates),
    #                                      axis=1)
    result_end = get_result_3(end_date=end_date)
    result_end['PostSecStart_Drop2Rise_Ratio'] = \
        abs(result_end['PostSecStart_MaxDrop'] /
            result_end['PostSecStart_RiseRatio'])
    result_end['PostSecStart_Max2Med_PeakGap'] = result_end['PostSecStart_MaxPeakGap'] / \
                                                 result_end['PostSecStart_MedianPeakGap']
    if industry is None:
        print('需要输入行业名称')
        return
    drop_limit = -18
    result_adj = result_end.query('ts_code in @check_list & '
                                  'industry in @industry & '
                                  'PostSecStart_Max2Med_PeakGap<10 & '
                                  'PostSecStart_Sec_Max_SumRatio<30 & '
                                  'PostSecStart_MaxDrop>@drop_limit & '
                                  '(PostPeak_Recent_Neg4_RecovDays>=0 | '
                                  'PreNow_SecDate<PostPeak_Recent_Neg4_DropDate) & '
                                  'PreNowSec_LastDays>3 & '
                                  'PostNowSec_LastDays<5 & '
                                  'PreNowSec_MinPeakGap<PostSecStart_MedianPeakGap*0.8 & '
                                  'PreNowSec_MaxValleyGap == PostSecStart_MaxValleyGap & '
                                  'PostSecStart_MaxPeakGap>1 & '
                                  'Now_PostSec_SecDropState>0 & '
                                  '(PostSecStart_MaxPeakGap < Peak2Sec_MaxPeakGap*2 | '
                                  'PostSecStart_MaxValleyGap < Peak2Sec_MaxValleyGap*2)')
    result_adj = result_adj.sort_values(by=['PostSecStart_RiseSecNum', 'Now_PostSec_SecDropState',
                                            'Now_PostSec_SecRiseState', 'Break_D100_Ratio'],
                                        ascending=[True, False, True, False])
    result_retrace = result_adj.query('Now_PostSec_SecRiseState<2')
    result_pull = result_adj.query('Now_PostSec_SecRiseState==2')
    if storemode:
        import config.config_Ali as config
        conf = config.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')
        result_adj['NowPick_Date'] = end_date
        pd.io.sql.to_sql(result_adj, 'track_pool_adj', engine, index=False, schema='stocksfit',
                         if_exists='append')
        engine.dispose()
        print(end_date, '筛选数据已存储!')
    return result_retrace, result_pull


def get_track_pool_lag(check_dates=None, confirm_lag=0, industry=None, Now_Date=None):
    """获取track_pool筛选结果"""
    trade_dates = get_trade_date()
    # dates = trade_dates[(trade_dates >= start_date) & (trade_dates <= end_date)].tolist()
    if check_dates is None:
        check_dates = [trade_dates[-1]]
    elif isinstance(check_dates, str):
        check_dates = [check_dates]
    if Now_Date is None:
        Now_Date = trade_dates[-1]

    result_adj, result_confirm = \
        get_track_pool(check_dates=check_dates, confirm_lag=confirm_lag,
                       industry=industry)
    # result_adj_adj = result_adj.query('(PreNowSec_Und2ContiDays<=2 | NowSec_Und2ContiDays<=2) & '
    #                                   '((Turn_Target_Ratio<20 & Turn_Target_Ratio>(-20)) | '
    #                                   '(Section_Target_Ratio<20 & Section_Target_Ratio>(-20)))')
    result_adj['PostSecStart_Drop2Rise_Ratio'] = abs(
        result_adj['PostSecStart_Drop2Rise_Ratio'])
    result_adj_adj = result_adj.query('Now_PostSec_SecDropState==3 & '
                                      'Now_PostSec_SecRise_Portion>0.5')

    # 以此为准，无筛选结果就等待
    result_confirm_adj, result_confirm_retrace, result_confirm_pull = None, None, None
    if result_confirm is not None and len(result_confirm) > 0:
        if len(result_confirm.query('Pick_Class=="Pull_Up"')) > 0:
            result_confirm_pull = result_confirm.query(
                'Pick_Class=="Pull_Up"').copy()
            result_confirm_pull = result_confirm_pull.sort_values(
                by=['Now_PostSec_SecDropState', 'Now_PostSec_SecRiseState',
                    'PreNowSec_Adverse2Trend', 'Break_D100_Ratio'],
                ascending=[False, True, True, False])
        if len(result_confirm.query('Pick_Class!="Pull_Up"')) > 0:
            result_confirm_retrace = result_confirm.query(
                'Pick_Class!="Pull_Up"').copy()
            result_confirm_retrace = result_confirm_retrace.sort_values(
                by=['Pick_Class', 'Now_PostSec_SecDropState',
                    'Now_PostSec_SecRiseState', 'Break_D100_Ratio'],
                ascending=[True, False, True, False])
        # result_confirm_retrace = result_confirm_retrace.query('PostPeak_Recent_Neg4_RecovDays>0').sort_values(
        #     by=['PostSecStart_RiseSecNum', 'Now_PostSec_SecDropState',
        #         'Now_PostSec_SecRiseState', 'Break_D100_Ratio'],
        #     ascending=[True, False, True, False])
    result = get_result_3(end_date=Now_Date)
    ts_codes = result_adj['ts_code'].to_list()
    result['Now_SecDiff'] = result.apply(lambda fn: conf_nowdiff(fn['Now_SecDate'],
                                                                 [check_dates[-1]], trade_dates),
                                         axis=1)
    # result_pick = result.query('ts_code in @ts_codes & '
    #                            'Now_SecDiff<3 & '
    #                            'PostSecStart_MaxPeakGap*2>PostNowSec_MaxPeakGap & '
    #                            'PostSecStart_MaxPeakGap!=PostNowSec_MaxPeakGap & '
    #                            'PostNowSec_ExtreRatio>=2').sort_values(
    #     by=['Now_PostSec_SecDropState',
    #         'Now_PostSec_SecRiseState', 'Break_D100_Ratio'],
    #     ascending=[False, True, False])
    Diff_Num = 1
    result_pick = result.query('ts_code in @ts_codes & '
                               'Now_SecDiff<=@Diff_Num & '
                               '(PostSecStart_Drop2Rise_SecMaxDaysDiv<0.6 | '
                               'PostSecStart_Drop2Rise_SecMaxSumDiv<0.5) & '
                               'PreNowSec_BelowMinDaily>=1 & '
                               '(Peak2Sec_LastDays>=30 | PostTurn_LastDays>=30) & '
                               '(Pre_PreNow_Sec_AvgRatio==PostSecStart_Sec_Max_AvgRatio | '
                               'Pre_PreNow_Sec_SumRatio==PostSecStart_Sec_Max_SumRatio) & '
                               '(Recent5D_BfPreNow_Volab_Num>1 | Recent5D_AftPreNow_Volab_Num>1 | '
                               'PreNow2Now_Volab_ProP>Sec2PreNow_Volab_ProP | '
                               '(PreNowSec_LastDays>Pre_PreNow_Sec_LastDays*1.5 & '
                               'Pre_PreNow_Sec_SumRatio+PreNowSec_SumRatio>0)) & '
                               'Now_DayRatio>0').sort_values(
        by=['Recent5D_AftPreNow_Volab_Num', 'PreNow2Now_Volab_ProP'],
        ascending=[False, False])
    result_pick['PostNowSec_PeakGap_Comp'] = round(
        result_pick['PostNowSec_MaxPeakGap'] / result_pick['PostSecStart_PeakGap_HighQuntl'], 3)
    result_pick['PostNowSec_ValleyGap_Comp'] = round(
        result_pick['PostNowSec_MaxValleyGap'] / result_pick['PostSecStart_ValleyGap_HighQuntl'], 3)
    result_pick['ValleyGap_Signal'] = result_pick['PostSecStart_ValleyGap_HighQuntl'] * 1.2
    result_pick['PeakGap_Signal'] = result_pick['PostSecStart_MaxPeakGap'] * 1.5
    # result_pick_track = result_pick.query('PostNowSec_PeakGap_Comp>1.2 & '
    #                                       'PostSecStart_Drop2Rise_SecMaxDaysDiv<0.67').copy().sort_values(
    #     'PostNowSec_PeakGap_Comp', ascending=False)
    result_pick_track = result_pick.query('PostSecStart_LastDays>20 & PreNowSec_LastDays>=3'
                                          )[['ts_code', 'name', 'industry',
                                             'PostSecStart_ValleyGap_HighQuntl', 'ValleyGap_Signal',
                                             'PostNowSec_MaxValleyGap',
                                             'PostSecStart_MaxPeakGap', 'PeakGap_Signal',
                                             'PostNowSec_MaxPeakGap',
                                             'PreNow2Now_Volab_ProP', 'Sec2PreNow_Volab_ProP', 'UndVolab_LastDays',
                                             'Recent5D_BfPreNow_Volab_Num', 'Recent5D_AftPreNow_Volab_Num',
                                             'PostSecStart_Adverse2Trend', 'PostSecPeak_MinDaily2Now_LastDays',
                                             'PostSecStart_Over7Num', 'PostSecStart_RiseRatio',
                                             'PostSecStart_COR_Mean', 'PostSecStart_COR_Std',
                                             'PreNowSec_COR_Mean', 'PreNowSec_COR_Std', 'Now_Over3Max',
                                             'Now_Recent3Mean'
                                             ]]
    return (result_adj, result_adj_adj, result_confirm,
            result_confirm_retrace, result_confirm_pull, result_pick, result_pick_track)


def Cls_Over_Sec(stock_data, ts_code, now_secdate):
    stk_data = stock_data.query('ts_code==@ts_code').set_index('trade_date')
    return 'True' if stk_data.loc[now_secdate:, 'close'].iloc[1:].min() >= \
                     min(stk_data.loc[now_secdate, 'close'], stk_data.loc[now_secdate, 'open']) else 'False'


def stkpick_method_one(section_startdate=None, now_secdate=None, Now_Date=None, Diff_Num=1, industry=None,
                       storemode=False, relative_startdate='2023-12-26', recent_indexpeakdate=None,
                       pick_mode='IndexTurn'):
    """直接依据指定日期指标进行筛选"""
    if isinstance(now_secdate, str):
        now_secdate = [now_secdate]
    if isinstance(section_startdate, str):
        section_startdate = [section_startdate]
    trade_dates = get_trade_date()
    column_list = ['ts_code', 'name', 'industry', 'Total_MV',
                   'PostSecStart_MaxValleyGap', 'PostSecStart_MaxValleyGapValue',
                   'PostSecStart_ValleyGap_HighQuntl', 'PostSecStart_ValleyGapValue_HighQuntl',
                   'PostSecStart_ValleyGap_LowQuntl', 'PostSecStart_ValleyGapValue_LowQuntl',
                   'PostSecStart_MedianValleyGap', 'PostSecStart_MedianValleyGapValue',
                   'PostSecStart_MedianPeakGap', 'PostSecStart_MedianPeakGapValue',
                   'PostNowSec_MaxValleyGap',
                   'PostSecStart_MaxPeakGap', 'PostSecStart_MaxPeakGapValue',
                   'PostSecStart_PeakGap_HighQuntl', 'PostSecStart_PeakGapValue_HighQuntl',
                   'PostSecStart_PeakGap_LowQuntl', 'PostSecStart_PeakGapValue_LowQuntl',
                   'PostNowSec_MaxPeakGap', 'PostNowSec_MedianPeakGap',
                   'Peak2Sec_MaxValleyGap', 'Peak2Sec_MaxPeakGap',
                   'PreNowSec_MaxValleyGap',
                   'PreNowSec_MedianPeakGap', 'PreNowSec_MedianValleyGap',
                   'Now_PeakGap', 'Now_PeakGapValue',
                   'Now_ValleyGap', 'Now_ValleyGapValue',
                   'Recent3Day_MaxPeakGapValue', 'Recent3Day_MaxValleyGapValue',
                   'PreNow2Now_Volab_ProP', 'Sec2PreNow_Volab_ProP', 'UndVolab_LastDays',
                   'Recent5D_BfPreNow_Volab_Num', 'Recent5D_AftPreNow_Volab_Num',
                   'PostSecStart_Adverse2Trend',
                   'PostSecPeak_MinDaily2Now_LastDays',
                   'PostSecStart_Over7Num', 'PostSecStart_RiseRatio', 'PostSecStart_LastDays', 'PostTurn_LastDays',
                   'PostSecStart_COR_Mean', 'PostSecStart_COR_Std', 'PostSecStart_AvgRatio',
                   'PostNowSec_COR_Mean', 'PostNowSec_COR_Std',
                   'Now_PostSec_SecDropState', 'Now_PostSec_SecRiseState', 'Break_D100_Ratio',
                   'PostSecStart_Sec_Max_LastDays',
                   'Period_TurnDate', 'Section_PeakDate', 'Section_StartDate', 'PostSecStart_PeakDate',
                   'Now_SecDate', 'PreNowSec_LastDays', 'PreNowSec_SumRatio', 'PreNowSec_AvgRatio',
                   'Now_Recent3Mean', 'Now_Over3Mean', 'Recent_MaxRatio',
                   'Now_Recent3Min', 'PostSecStart_MaxContiDropDays', 'PostSecStart_MaxDailyRatio',
                   'SecConcave_TO_Sum', 'SecConcave_LastDays', 'SecPeakConcave_CoverDays',
                   'PostNowSec_COR_Und2Poxn', 'PostNowSec_AvgTurnover',
                   'PostNowSec_AvgRatio', 'PostNowSec_Adverse2Trend', 'PostNowSec_LastDays', 'PostNowSec_Over7Num',
                   'Pre_PreNow_Sec_SumRatio', 'Pre_PreNow_Sec_AvgRatio',
                   'PreSecPeak_Sec_SumRatio', 'PreSecPeak_Sec_AvgRatio',
                   'Peak2Sec_LastDays', 'Peak2Sec_Sec_Max_LastDays', 'Peak2Sec_MaxContiRiseDays',
                   'PreSec_Turnover2Drop_State', 'Now_Turnover2Rise_State',
                   'Now_RiseSec_Turnover2Rise', 'Now_RiseSec_Turnover2Drop', 'Now_RiseSec_LastDays',
                   'PreSec_SecDropDays_QuntlRatio', 'PreSec_SecDropAvg_QuntlRatio',
                   'PostPeak_Recent_Neg4_DropDate', 'PostPeak_Recent_Neg4_RecovDays',
                   'PostSecPeak_Over_Btw_Num', 'PostPreNowSec_Over_Btw_Num'
                   ]
    if pick_mode == 'IndexTurn':
        result = get_result_3(start_date=now_secdate[0], end_date=now_secdate[-1])
        result['Now_SecDiff'] = result.apply(lambda fn: conf_nowdiff(fn['Now_SecDate'],
                                                                     now_secdate, trade_dates),
                                             axis=1)
        condition1 = ((result['SecConcave_TO_Sum'] > 40) | (
                       result['SecConcave_LastDays'] >= 25))
        condition2 = result['PostTurn_Over7Num'] >= 3
        condition3 = (result['Now_SecDiff'] <= 3)
        condition4 = (result['Now_ValleyGap'] >= result['PostSecStart_ValleyGap_HighQuntl']) & \
                     (result['Now_PeakGap'] <= result['PostSecStart_PeakGap_LowQuntl'])
        condition5 = result['PostSecPeak_MinDaily2Now_LastDays'] <= 3
        condition6 = result['Now_PeakGap'] <= result['PostSecStart_MaxPeakGap']
        condition7 = result['PostSecStart_MaxContiSum'] < 50
        # condition8 = result['PostTurn_Peak2Now_Lastdays'] >= 5
        condition9 = (result['Peak2Sec_LastDays'] >= 30) | (result['PostTurn_LastDays'] >= 30)
        condition10 = ((result['Turn_Target_Ratio'] >= 15) & (result['Section_Target_Ratio'] >= 15)) | \
                      (result['Target_Ratio'] > 30)
        # condition11 = (result['PreNowSec_Und2ContiDays'] >= 6) | (result['NowSec_Und2ContiDays'] >= 6) | \
        #               (result['PreSec_Und2ContiDays'] >= 6) | (result['Pre_PreNowSec_Und2ContiDays'] >= 6)
        condition12 = (result['Now_PostSec_SecRiseState'] == 0) | \
                      ((result['Now_PostSec_SecRiseState'] == 2) & (result['Now_PostSec_SecDropState'] == 3)) | \
                      ((result['Now_PostSec_SecRiseState'] == 2) & (result['Now_PostSec_SecDropState'] == 2) &
                       (result['PostSecStart_Over7Num'] > 2) & (result['PreNowSec_ExtreRatio'] < -3)) | \
                      ((result['Now_PostSec_SecRiseState'] == 1) & (result['Now_PostSec_SecDropState'] == 1))
        condition13 = result['Now_PostSec_SecDropState'] > 0
        condition14 = (result['PostSecStart_Adverse2Trend'] < 0.6
                       ) | (result['PostSecStart_Drop2Rise_SecMaxSumDiv'] < 0.4
                            ) | (result['PreNowSec_Adverse2Trend'] < 0.6)
        condition15 = ((result['PreSec_Adverse2Trend'] <= 0.3) & (result['PostSecStart_RiseSecNum'] <= 1)) | \
                      (result['Pre_PreNowSec_Adverse2Trend'] <= 0.3)
        condition16 = result['Section_StartDate'] == result['Now_SecDate']
        if recent_indexpeakdate is not None:
            condition17 = result['PreNow_SecDate'] > recent_indexpeakdate
        else:
            condition17 = True

        Pick_Valuation_One = ((condition1 | condition2 | condition16) & (condition3 | condition4 | condition5) &
                              condition6 & condition7 & condition9 & condition10 &
                              ((condition12 & condition13 & condition14) | condition16) &
                              condition15 & condition17)

        condition18 = "Now_SecDiff<=@Diff_Num"
        condition19 = "(PostSecStart_Drop2Rise_SecMaxDaysDiv<0.6 | PostSecStart_Drop2Rise_SecMaxSumDiv<0.6)"
        condition20 = "(PreNowSec_BelowMinDaily>=1 | Now_PostSec_SecDropState==3 | Now_PostSec_SecDropState==1)"
        condition21 = ("(Pre_PreNow_Sec_AvgRatio==PostSecStart_Sec_Max_AvgRatio | "
                       "Pre_PreNow_Sec_SumRatio==PostSecStart_Sec_Max_SumRatio | "
                       "Pre_PreNow_Sec_AvgRatio<1)")
        condition22 = ("(Recent5D_BfPreNow_Volab_Num>1 | Recent5D_AftPreNow_Volab_Num>1 | "
                       "PreNow2Now_Volab_ProP>Sec2PreNow_Volab_ProP | "
                       "(PreNowSec_LastDays>Pre_PreNow_Sec_LastDays*1.5 & "
                       "Pre_PreNow_Sec_SumRatio+PreNowSec_SumRatio>0))")
        condition23 = "(Section_StartDate==Now_SecDate)"
        # condition24 = "Now_DayRatio>0"

        Pick_Valuation_Two = (f"{condition18} & "
                              f"(({condition19} & {condition20} & {condition21} & {condition22}) | {condition23})")

        result_pick = result[Pick_Valuation_One].query(Pick_Valuation_Two).copy().sort_values(
            by=['Recent5D_AftPreNow_Volab_Num', 'PreNow2Now_Volab_ProP'],
            ascending=[False, False])
        if len(result_pick) == 0:
            print('Section_StartDate日期吻合筛选结果为0！')
            return

        # 获取Now_SecDate的指标
        if Now_Date is None:
            Now_Date = now_secdate[-1]
        Now_Date_1 = get_trade_date(end_date=Now_Date, loc=-2)
        result_now_twodate = get_result_3(start_date=Now_Date_1, end_date=Now_Date)
        result_now_1 = result_now_twodate.query(
            'Cal_Date==@Now_Date_1').copy().rename(columns={'Now_Over3Mean': 'Now_Over3Mean_1',
                                                            'Now_ValleyGapValue': 'Now_ValleyGapValue_1',
                                                            'NowSec_SecConcave_TO_Sum': 'NowSec_SecConcave_TO_Sum_1',
                                                            'NowSec_SecConcave_LastDays': 'NowSec_SecConcave_LastDays_1'
                                                            })
        result_now = result_now_twodate.query('Cal_Date==@Now_Date').copy()
        result_now = pd.merge(result_now, result_now_1[['ts_code', 'Now_Over3Mean_1', 'Now_ValleyGapValue_1',
                                                        'NowSec_SecConcave_TO_Sum_1', 'NowSec_SecConcave_LastDays_1']],
                              on='ts_code', how='left')
        result_now['PostSecStart_Drop2Rise_Ratio'] = abs(
            result_now['PostSecStart_MaxDrop'] / result_now['PostSecStart_RiseRatio'])
        result_now['NowSecConCave_TO_Sum_Diff'] = result_now['NowSec_SecConcave_TO_Sum'] - \
                                                  result_now['NowSec_SecConcave_TO_Sum_1']
        result_now['NowSecConCave_LastDays_Diff'] = result_now['NowSec_SecConcave_LastDays'] - result_now[
            'NowSec_SecConcave_LastDays_1']
        result_pick2 = result_pick.rename(
            columns={'Now_SecDate': 'Pick_Now_SecDate'})
        stk_start_date = result_pick2['Pick_Now_SecDate'].min()
        result_now_merge = pd.merge(result_now, result_pick2[[
            'ts_code', 'Pick_Now_SecDate']], on='ts_code', how='inner')
        stock_data = get_stock_data(stk_code=result_now_merge['ts_code'].tolist(),
                                    start_date=stk_start_date, end_date=Now_Date)
        result_now_merge['Cls_OverNowSec'] = result_now_merge.apply(
            lambda fn: Cls_Over_Sec(stock_data, fn['ts_code'], fn['Pick_Now_SecDate']), axis=1)
        result_now_merge['PostSec_FallSecNum'] = result_now_merge.apply(
            lambda fn: count_section_num(stock_data, fn['Pick_Now_SecDate'], fn['ts_code']), axis=1)
        result_now_merge['Pick_NowSec_Min'] = result_now_merge.apply(
            lambda fn: get_daily_min(stock_data, fn['Pick_Now_SecDate'], fn['ts_code']), axis=1)
        condition1 = "(Now_SecDate<=Pick_Now_SecDate | (Now_SecDate>Pick_Now_SecDate & PreNowSec_LastDays<=3))"
        # condition2 = ("(PostNowSec_AvgRatio>Pre_PreNow_Sec_AvgRatio | "
        #               "PostNowSec_MaxPeakGap>=PostSecStart_MaxPeakGap | "
        #               "(PostNowSec_COR_Mean>PostSecStart_COR_Mean & PostNowSec_COR_Mean>1))")
        condition3 = "Cls_OverNowSec=='True'"
        condition4 = "PostSec_FallSecNum<=3"
        # condition5 = "((Now_Over3Mean=='True' | Now_Over3Mean_1=='True') & Now_Over5Min == 'True')"
        condition5 = "Now_Over5Min == 'True'"
        condition6 = "PreSec_SecDropDays_QuntlRatio > 1.2 & PreSec_SecDropAvg_QuntlRatio < 1"
        condition7 = "PreSec_SecDropDays_QuntlRatio == 1 & PreSec_SecDropAvg_QuntlRatio == 1"
        result_track1 = result_now_merge.query(f"{condition1} & "
                                               f"{condition3} & {condition4} & "
                                               f"{condition5} & ({condition6} | {condition7})").copy().sort_values(
            by=['Now_PostSec_SecDropState',
                'Now_PostSec_SecRiseState', 'PreSec_SecDropDays_QuntlRatio'],
            ascending=[False, True, False])
        condition_last = ("(PostNowSec_MaxPeakGap>=PostSecStart_MaxPeakGap | "
                          "PostNowSec_MaxValleyGap>=PostSecStart_MaxValleyGap)")
        # condition7 = ("Now_ValleyGap>=PostSecStart_MaxValleyGap & "
        #               "Now_ValleyGap>=PostNowSec_MaxValleyGap & "
        #               "Now_ValleyGapValue_1<PostSecStart_MaxValleyGapValue & "
        #               "PostNowSec_MaxPeakGap<PostSecStart_MaxPeakGap*2 &"
        #               "PostNowSec_Adverse2Trend<0.4")
        # condition8 = "PostNowSec_MaxValleyGap>=PostSecStart_MaxValleyGap"
        result_track2 = result_now_merge.query(f"{condition1} & {condition3} & {condition4} &"
                                               f"{condition5} & ({condition6} | {condition7}) & {condition_last}"
                                               ).copy().sort_values(
            by=['Now_PostSec_SecDropState',
                'Now_PostSec_SecRiseState', 'PreSec_SecDropDays_QuntlRatio'],
            ascending=[False, True, False])
        column_list.extend(['Pick_NowSec_Min', 'Pick_Now_SecDate',
                            'NowSecConCave_TO_Sum_Diff', 'NowSecConCave_LastDays_Diff', 'PostSecStart_Drop2Rise_Ratio'])
        result_track1 = result_track1[column_list].drop_duplicates(subset='ts_code', keep='first')
        result_track2 = result_track2[column_list].drop_duplicates(subset='ts_code', keep='first')
        result_now_merge = result_now_merge.drop_duplicates(subset='ts_code', keep='last').sort_values(
            by=['Now_PostSec_SecDropState',
                'Now_PostSec_SecRiseState', 'PreSec_SecDropDays_QuntlRatio'],
            ascending=[False, True, False])
        from function_ai.Relative_Func import collect_relative_comp2mean, collect_relative_state
        result_track1 = collect_relative_comp2mean(result_track1, start_date=relative_startdate, end_date=Now_Date)
        result_track1 = collect_relative_state(result_track1, start_date=relative_startdate, end_date=Now_Date)
        # result_track1 = result_track1.sort_values(by='relative2Index_sumratio', ascending=False)
        result_track2 = pd.merge(result_track1, result_track2[['ts_code']], on='ts_code', how='inner')
        if industry is not None:
            result_track1 = result_track1.query('industry in @industry')
            result_track2 = result_track2.query('industry in @industry')
        result_track1 = result_track1.groupby(['Section_StartDate']).head(20)
        # result_track1 = result_track1.query('fall_relative_comp2mean>0 & fall_relative_count>0.4').copy().sort_values(
        #     by='fall_relative_comp2mean', ascending=False)
        if storemode and len(result_track1) > 0:
            start_date = pd.to_datetime(now_secdate[-1]).strftime('%Y%m%d')[-4:]
            end_date = pd.to_datetime(Now_Date).strftime('%Y%m%d')[-4:]
            result_track1.to_csv('/Users/<USER>/PycharmProjects/AI_Stock/stock_track/tracksec_' +
                                 start_date + '_' + end_date + '.csv',
                                 index=False, encoding='utf-8-sig')
            print('筛选结果已存储。')
        # 对于result_track1，按照Now_PostSec_SecDropState, Now_PostSec_SecRiseState进行groupby处理后分别取排序前5位的品种，
        # 如groupby后的品种数量不足5位，则取全部
        # result_track1 = result_track1.sort_values(
        #         by=['Now_PostSec_SecDropState',
        #             'Now_PostSec_SecRiseState', 'Break_D100_Ratio'],
        #         ascending=[False, True, False])
        return result_pick, result_now_merge, result_track1, result_track2
    elif pick_mode == 'NowTurn':
        if section_startdate is None:
            print('需要输入Section_StartDate！')
            return
        Now_Date_1 = get_trade_date(end_date=Now_Date, loc=-2)
        result_now_threedate = get_result_3(start_date=Now_Date_1, end_date=Now_Date)
        result_3day = result_now_threedate.query('Cal_Date==@Now_Date_1').copy().rename(
            columns={'Now_ValleyGapValue': 'Now_ValleyGapValue_1',
                     'Now_PeakGapValue': 'Now_PeakGapValue_1',
                     'PostSecStart_MaxPeakGapValue': 'PostSecStart_MaxPeakGapValue_1',
                     'PostSecStart_ValleyGapValue_HighQuntl': 'PostSecStart_ValleyGapValue_HighQuntl_1'})
        if len(result_3day) == 0:
            print('缺失Now_Date前一日指标数据！')
        result_now = result_now_threedate.query('Cal_Date==@Now_Date').copy()
        result_now = pd.merge(result_now, result_3day[['ts_code', 'Now_ValleyGapValue_1',
                                                       'Now_PeakGapValue_1', 'PostSecStart_MaxPeakGapValue_1',
                                                       'PostSecStart_ValleyGapValue_HighQuntl_1']],
                              on='ts_code', how='left')
        result_now['Now_SecDiff'] = result_now.apply(lambda fn: conf_nowdiff(fn['Now_SecDate'],
                                                     Now_Date, trade_dates), axis=1)
        result_now['PeakDate_Diff'] = result_now.apply(lambda fn: conf_nowdiff(fn['PostSecStart_PeakDate'],
                                                       Now_Date, trade_dates), axis=1)

        if recent_indexpeakdate is not None:
            condition1 = result_now['PreNow_SecDate'] > recent_indexpeakdate
        else:
            condition1 = True
        # 筛选条件：condition2:SectionStartDate在pick_date中
        condition2 = result_now['Section_StartDate'].isin(section_startdate)
        # condition3 = result_now['PreSec_Turnover2Drop_State'] == 2
        condition4 = (result_now['PreSec_SecDropDays_QuntlRatio'] >= 1) & (
                result_now['PreSec_SecDropAvg_QuntlRatio'] <= 1)
        # condition5 = (result_now['PreSec_SecDropDays_QuntlRatio'] == 1) & (
        #         result_now['PreSec_SecDropAvg_QuntlRatio'] == 1)
        condition6 = (result_now['PreSec_SecDropDays_QuntlRatio'] < 1) & (
                result_now['PreSec_SecDropAvg_QuntlRatio'] > 1.2)
        if now_secdate is not None:
            condition7 = (result_now['Now_SecDate'].isin(now_secdate)) & \
                         (result_now['PostSecStart_Sec_Max_SumRatio'] < 50)
        else:
            condition7 = True
        condition8 = (result_now['Now_PeakGapValue_1'] < result_now['PostSecStart_MaxPeakGapValue']) | (
                result_now['Now_PeakGapValue'] < result_now['PostSecStart_MaxPeakGapValue'])
        condition9 = (result_now['SecPeakConcave_CoverDays'] >= result_now['Peak2Sec_LastDays'])
        Pick_Valuation_Three = (condition1 & condition2 & (condition4 | condition6) &
                                condition7 & condition8 & condition9)
        result_pick = result_now[Pick_Valuation_Three].copy().sort_values(
            by=['Section_StartDate', 'PreSec_SecDropDays_QuntlRatio'],
            ascending=[False, False])
        result_pick = result_pick.drop_duplicates(subset='ts_code', keep='last')
        if len(result_pick) == 0:
            print('Section_StartDate日期吻合筛选结果为0！')
            return None, None, None, None
        stock_data = get_stock_data(stk_code=result_pick['ts_code'].tolist(),
                                    start_date=result_pick['Section_StartDate'].min(), end_date=Now_Date)
        result_pick['Cls_OverSec'] = result_pick.apply(
            lambda fn: Cls_Over_Sec(stock_data, fn['ts_code'], fn['Section_StartDate']), axis=1)
        result_pick['PostSecStart_Drop2Rise_Ratio'] = abs(
            result_pick['PostSecStart_MaxDrop'] / result_pick['PostSecStart_RiseRatio'])
        result_pick['PostSecStart_FallSecNum'] = result_pick.apply(
            lambda fn: count_section_num(stock_data, fn['Section_StartDate'], fn['ts_code']), axis=1)
        result_pick['PostSecStart_Max2Med_PeakGapRatio'] = round(result_pick['PostSecStart_MaxPeakGap'] /
            result_pick['PostSecStart_MedianPeakGap'], 4)
        condition_last = result_pick['Cls_OverSec'] == "True"
        result_pick = result_pick[condition_last].copy().reset_index()
        # result_pick['Pick_NowSec_Min'] = result_pick.apply(
        #     lambda fn: get_daily_min(stock_data, fn['Now_SecDate'], fn['ts_code']), axis=1)
        column_list.extend(['Cls_OverSec', 'PostSecStart_Drop2Rise_Ratio',
                            'PostSecStart_FallSecNum', 'Now_ValleyGapValue_1', 'Now_PeakGapValue_1',
                            'PostSecStart_ValleyGapValue_HighQuntl_1',
                            'PostSecStart_Max2Med_PeakGapRatio', 'Now_SecDiff', 'PeakDate_Diff'])
        result_pick = result_pick[column_list].copy()
        condition_20 = ((result_pick['PostNowSec_MedianPeakGap'] > 1.2) |
                        (result_pick['PostSecStart_MedianPeakGap'] > 1.2))
        condition_21 = (result_pick['Now_ValleyGapValue'] >= result_pick['PostSecStart_ValleyGapValue_HighQuntl']) & \
                       (result_pick['Now_ValleyGapValue'] == result_pick['Recent3Day_MaxValleyGapValue']) & \
                       (result_pick['Recent3Day_MaxPeakGapValue'] < result_pick['PostSecStart_MaxPeakGapValue'])
        condition_22 = (result_pick['Now_ValleyGapValue'] == result_pick['PostSecStart_MaxValleyGapValue']) & \
                       (result_pick['Recent3Day_MaxPeakGapValue'] < result_pick['PostSecStart_MaxPeakGapValue'])
        condition_23 = (result_pick['Now_PeakGapValue'] <= result_pick['PostSecStart_MedianPeakGapValue']) & \
                       (result_pick['Now_SecDiff'] <= 1) & \
                       (result_pick['PreNowSec_LastDays'] >= 2) & \
                       (result_pick['PreNowSec_MaxValleyGap'] > result_pick['PostSecStart_MedianValleyGap'])
        # condition_23 = (result_pick['Peak3Day_MaxPeakGapValue'] < result_pick['PostSecStart_MaxPeakGapValue']) & \
        #                (result_pick['Now_SecDiff'] <= 1) & \
        #                (result_pick['PreNowSec_LastDays'] >= 2) & \
        #                (result_pick['PreNowSec_MaxValleyGap'] > result_pick['PostSecStart_ValleyGap_HighQuntl'])
        condition_24 = ((result_pick['Now_PeakGapValue'] < result_pick['PostSecStart_MedianPeakGapValue']) |
                        (result_pick['Now_PeakGapValue_1'] < result_pick['PostSecStart_MedianPeakGapValue'])) & \
                       (result_pick['PostNowSec_LastDays'] >= 2) & (result_pick['PostNowSec_LastDays'] < 20) & \
                       (result_pick['PostNowSec_Over7Num'] <= 3)
        # condition_24 = (result_pick['Peak3Day_MaxPeakGapValue'] < result_pick['PostSecStart_MaxPeakGapValue']) & \
        #                (result_pick['PostNowSec_LastDays'] >= 2) & (result_pick['PostNowSec_LastDays'] < 20) & \
        #                (result_pick['PostNowSec_Over7Num'] <= 3) & (result_pick['Now_SecDiff'] > 1)
        result_pick21 = result_pick[(condition_21 & condition_20)].copy().sort_values(
            by=['Now_SecDate', 'PreNowSec_LastDays'], ascending=[False, False])
        result_pick21['Pick_Mode'] = '突破HighQuntl'
        result_pick22 = result_pick[(condition_22 & condition_20)].copy().sort_values(
            by=['Now_SecDate', 'PreNowSec_AvgRatio'], ascending=[False, True])
        result_pick22['Pick_Mode'] = '突破Max'
        result_pick23 = result_pick[(condition_23 & condition_20)].copy().sort_values(
            by=['Now_SecDate', 'PreNowSec_AvgRatio'], ascending=[False, True])
        result_pick23['Pick_Mode'] = 'Sec下行'
        result_pick24 = result_pick[(condition_24 & condition_20)].copy().sort_values(
            by='PostNowSec_AvgRatio', ascending=False)
        result_pick24['Pick_Mode'] = 'Sec上行'
        result_pick2 = pd.concat([result_pick21, result_pick22], axis=0, ignore_index=True)
        result_pick2 = pd.concat([result_pick2, result_pick23], axis=0, ignore_index=True)
        result_pick2 = pd.concat([result_pick2, result_pick24], axis=0, ignore_index=True)
        result_pick2 = result_pick2.drop_duplicates(subset=['ts_code', 'Pick_Mode'], keep='last')
        if industry is not None:
            result_pick = result_pick.query('industry in @industry').copy()

        if storemode and len(result_pick) > 0:
            import config.config_Ali as config
            conf = config.configModel()
            engine = create_engine(
                'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                    conf.DC_DB_PORT) + '/stocksfit')
            Session = sessionmaker(bind=engine)
            session = Session()
            start_date = section_startdate[-1]
            end_date = Now_Date
            if len(result_pick) > 0:
                result_pick['Pick_TurnDate'] = start_date
                result_pick['Pick_NowDate'] = end_date
                check_sql = f"""select * from stocksfit.stocktrack_all
                                where Pick_TurnDate='{start_date}' and Pick_NowDate='{end_date}'"""
                check_result = pd.read_sql(check_sql, engine)
                if len(check_result) > 0:
                    try:
                        delete_sql = text("delete from stocksfit.stocktrack_all "
                                          "where Pick_TurnDate=:start_date and Pick_NowDate=:end_date")
                        with session.begin():
                            session.execute(delete_sql, {"start_date": start_date, "end_date": end_date})
                        session.commit()
                    except Exception as e:
                        session.rollback()
                        print('An error occured:', e)
                result_pick = result_pick.replace(np.inf, 0, inplace=False)
                pd.io.sql.to_sql(result_pick, 'stocktrack_all', con=engine, if_exists='append', index=False)
            else:
                print('NowTurn筛选结果为0！未存储')
            if len(result_pick2) > 0:
                result_pick2['Pick_TurnDate'] = start_date
                result_pick2['Pick_NowDate'] = end_date
                check_sql = f"""select * from stocksfit.stocktrack_valley
                                where Pick_TurnDate='{start_date}' and Pick_NowDate='{end_date}'"""
                check_result = pd.read_sql(check_sql, engine)
                if len(check_result) > 0:
                    try:
                        delete_sql = text("delete from stocksfit.stocktrack_valley "
                                          "where Pick_TurnDate=:start_date and Pick_NowDate=:end_date")
                        with session.begin():
                            session.execute(delete_sql, {"start_date": start_date, "end_date": end_date})
                        session.commit()
                    except Exception as e:
                        session.rollback()
                        print('An error occured:', e)
                result_pick2 = result_pick2.replace(np.inf, 0, inplace=False)
                pd.io.sql.to_sql(result_pick2, 'stocktrack_valley', con=engine, if_exists='append', index=False)
            else:
                print('NowTurn_Valley筛选结果为0！未存储')
            session.close()
            engine.dispose()
        return None, None, result_pick, result_pick2
    else:
        print('pick_mode输入错误！备选："IndexTurn"或"NowTurn"')
        return


def count_section_num(stock_data=None, start_date=None, stk_code=None):
    """统计指定日期区间内，下行Section数量"""
    stk_data = stock_data.query('ts_code==@stk_code').copy().set_index('trade_date')
    stk_data = stk_data.loc[start_date:]
    _, section_drop, _ = section_stat(stk_data=stk_data)
    section_drop_num = len(section_drop)
    return section_drop_num


def get_daily_min(stock_data=None, cal_date=None, stk_code=None):
    """获取指定日期区间内，最低价"""
    stk_data = stock_data.query('ts_code==@stk_code').copy().set_index('trade_date')
    return min(stk_data.loc[cal_date, 'close'], stk_data.loc[cal_date, 'open'])


def get_result_for_dailytrack(Now_SecDate=None, pick_date=None,
                              trade_dates=None, result_track=None, track=True, NowDate=None):
    """筛选跟踪品种
       Now_SecDate: 市场指数Now_Sec转折日期
       pick_date: dailytrack筛选日期
       trade_dates: 交易日序列
       result_track: 备选跟踪品种
    """
    # result_track = result_track.query('Pick_Mode!="Sec上行"').copy()
    track_signal = 0
    if trade_dates is None:
        trade_dates = get_trade_date()
    if pick_date != '':
        if track:
            last_tradedate = trade_dates[-1]
            last2_tradedate = trade_dates[-2]
        else:
            if NowDate is None:
                last_tradedate = input('输入跟踪计算结束日期（直接回车选择最新日期）：')
            else:
                last_tradedate = NowDate
            if last_tradedate == '':
                last_tradedate = trade_dates[-1]
            else:
                last_tradedate = last_tradedate.replace("'", "")
                last_tradedate = last_tradedate.replace("[", "")
                last_tradedate = last_tradedate.replace("]", "")
                last_tradedate = last_tradedate.replace(" ", "")
            last2_tradedate = trade_dates[trade_dates < last_tradedate][-1]
        result_now = get_result_3(start_date=last2_tradedate, end_date=last_tradedate)
        result_pick = result_now.query('Cal_Date==@last_tradedate')
        # result_last2day = result_now.query('Cal_Date==@last2_tradedate')
        # result_last2day = result_last2day.rename(columns={'Now_PeakGapValue': 'Now_PeakGapValue_1'})
        result_track = result_track.rename(columns={'Section_StartDate': 'Section_StartDate_Turn',
                                                    'Now_SecDate': 'Now_SecDate_Turn'})
        if 'Pick_Mode' in result_track.columns:
            result_pick = pd.merge(result_pick, result_track[['ts_code', 'Pick_Mode',
                                                              'Section_StartDate_Turn', 'Now_SecDate_Turn']],
                                   on=['ts_code'], how='inner')
        else:
            result_pick = pd.merge(result_pick, result_track[['ts_code',
                                                              'Section_StartDate_Turn', 'Now_SecDate_Turn']],
                                   on=['ts_code'], how='inner')
        # result_pick = pd.merge(result_pick, result_last2day[['ts_code', 'Now_PeakGapValue_1']],
        #                        on=['ts_code'], how='left')
        result_track1 = stkpick_from_simplesfit(result_pick, Now_SecDate, trade_dates)
        track_signal = 2
    else:
        last_tradedate = trade_dates[-1]
        result_pick = get_result_3(end_date=last_tradedate)
        if 'Pick_Mode' in result_track.columns:
            result_track = pd.merge(result_pick, result_track[['ts_code', 'Pick_Mode']],
                                    on=['ts_code'], how='inner')
        else:
            result_track = pd.merge(result_pick, result_track[['ts_code']],
                                    on=['ts_code'], how='inner')
        result_track['Now_SecDiff'] = result_track.apply(
            lambda fn: conf_nowdiff(fn['Now_SecDate'], Now_SecDate, trade_dates), axis=1)
        result_track1 = result_track.query('Now_SecDiff<=1')
        if len(result_track1) == 0:
            print('筛选日期为昨日，Now_SecDate筛选清单为空！')
    return result_track1, track_signal


def stkpick_from_simplesfit(result_pick, Now_SecDate, trade_dates=None):
    if trade_dates is None:
        trade_dates = get_trade_date()
    if result_pick is None or len(result_pick) == 0:
        print('result_pick为空！')
        return None
    stock_data = get_stock_data(stk_code=result_pick['ts_code'].to_list(),
                                start_date=result_pick['Section_StartDate'].dropna().min(),
                                end_date=result_pick['Cal_Date'].max())
    result_pick['Now_SecDiff'] = result_pick.apply(
        lambda fn: conf_nowdiff(fn['Now_SecDate'], Now_SecDate, trade_dates,
                                fn['ts_code'], stock_data=stock_data), axis=1)
    max_date = result_pick['Cal_Date'].max()
    result_pick_adj = result_pick.query('Cal_Date==@max_date')
    if len(result_pick_adj) > 0 \
            and (pd.isnull(result_pick_adj['PostSecStart_MedianPeakGap'])).all():
        print('PeakGap数据为空')
        return None
    indus_medianpeakgap = result_pick_adj[
        ['industry', 'PostSecStart_MedianPeakGap']].groupby('industry')['PostSecStart_MedianPeakGap'].quantile(0.4)
    result_pick['Indus_MedianPeakGap'] = result_pick['industry'].map(indus_medianpeakgap)
    result_pick['AdjPeak2Now_LastDays'] = result_pick.apply(
        lambda fn: fn['PostSecPeak2Now_LastDays']
        if pd.notnull(fn['Section_StartDate']) and pd.notnull(fn['Period_TurnDate']) and
        fn['Section_StartDate'] > fn['Period_TurnDate'] else fn['Peak2Turn_LastDays'], axis=1)

    # condition_1 = ((result_pick['Now_PeakGapValue'] <= result_pick['PostSecStart_MedianPeakGapValue']*1.02) |
    #                (result_pick['Now_PeakGapValue_1'] <= result_pick['PostSecStart_MedianPeakGapValue']*1.02)
    #                ) & (result_pick['PostNowSec_Over7Num'] <= 3)
    # condition_2 = ((result_pick['Now_SecDiff'] <= 2) |
    #                (result_pick['Now_SecDate'] <= result_pick['Now_SecDate_Turn'])) & \
    #               (result_pick['Section_StartDate'] == result_pick['Section_StartDate_Turn'])
    condition_2 = (result_pick['Now_SecDiff'] <= 2)
    # condition_3 = (result_pick['PostSecPeak_MaxValleyGapValue'] >=
    #                (result_pick['PostSecStart_ValleyGapValue_HighQuntl'] +
    #                 result_pick['PostSecStart_MedianValleyGapValue']) / 2) & \
    #               ((result_pick['Now_PeakGapValue'] <= result_pick['PostSecPeak_MinPeakGapValue']) |
    #                (result_pick['Now_PeakGapValue'] <= result_pick['Recent3Day_MinPeakGapValue']) |
    #                (result_pick['Now_PeakGapValue_1'] <= result_pick['PostSecPeak_MinPeakGapValue']) |
    #                (result_pick['Now_PeakGapValue_1'] <= result_pick['Recent3Day_MinPeakGapValue'])
    #                )
    condition_331 = ((result_pick['PostSecPeak_MaxValleyGapValue'] >=
                      result_pick['PostSecStart_ValleyGapValue_HighQuntl']))
                    #  (result_pick['PostSecPeak_Over_HighQuntl_Num'] >= 2))
    condition_332 = ((result_pick['PreNowSec_MaxValleyGapValue'] >=
                      result_pick['PostSecStart_ValleyGapValue_HighQuntl']))
                    #  (result_pick['PostPreNowSec_Over_HighQuntl_Num'] >= 1))
    condition_333 = ((result_pick['Now_ValleyGapValue'] >= result_pick['PostSecStart_ValleyGapValue_HighQuntl']) &
                     (result_pick['PostSecStart_MedianPeakGap'] > result_pick['Indus_MedianPeakGap']))
    condition_44 = ((result_pick['Now_SecDate'] == result_pick['Section_StartDate']) &
                    (result_pick['Period_TurnDate'] == result_pick['Section_StartDate']))
    # condition_4 = (result_pick['PostSecStart_PeakGapValue_TrackSignal'] <= 1) & \
    #               (result_pick['PostSecPeak2Now_LastDays'] < 10) & \
    #               (result_pick['PostSecStart_MaxDrop'] > (-15)) & \
    #               (result_pick['PreSecPeak_Sec_SumRatio'] > result_pick['PostSecStart_MaxDrop'].abs()) & \
    #               (result_pick['PreSecPeak_Sec_LastDays'] > result_pick['PostSecStart_MaxDrop_LastDays'])
    condition_55 = (result_pick['PostSecStart_PeakGapValue_TrackSignal'] <= 1)
                   #  & \
                   # (result_pick['PreSecPeak_Sec_SumRatio'] > result_pick['PostSecStart_MaxDrop'].abs()))
    # condition_5 = ((result_pick['Peak3Day_MaxPeakGapValue'] < result_pick['PostSecStart_MaxPeakGapValue']) |
    #                (result_pick['PreSecPeak_Sec_SumRatio'] < 15)) & (result_pick['PostNowSec_LastDays'] <= 3)
    condition_66 = (result_pick['PreNowSec_LastDays'] > 2) & (result_pick['Total_MV'] > 10)
    print('备选总数: ', len(result_pick),
          '\ncondition2数量:', sum(condition_2),
          '\ncondition331数量:', sum(condition_331),
          '\ncondition332数量:', sum(condition_332),
          '\ncondition333数量:', sum(condition_333),
          '\ncondition44数量:', sum(condition_44),
          '\ncondition55数量:', sum(condition_55),
          '\ncondition66数量:', sum(condition_66))
    result_track1 = result_pick[(condition_2 & ((condition_332 & condition_333) | condition_44) &
                                 condition_55 & condition_66)].copy()
    if len(result_track1) == 0:
        print('Now_SecDates筛选清单为空！')
    # else:
    #     result_track1 = result_track1.sort_values(
    #         by=['PostSecPeak2Now_LastDays', 'PostSecStart_MaxDrop_LastDays', 'PostSecStart_MaxDrop'],
    #         ascending=[True, True, False])
    return result_track1


def stkpick_indus_sort_totalmv(start_date=None, end_date=None, industry=None, mode=None):
    """获取指定日期的resultpick数据，筛序指定industry的股票，按照totalmv排序，并按照get_result_from_simplesfit进行筛选"""
    if start_date is None:
        start_date = end_date
    if isinstance(industry, str):
        industry = [industry]
    result = get_result_3(start_date=start_date, end_date=end_date).query('industry==@industry')
    now_secdate = result['Cal_Date'].unique().tolist()
    if any(result['Total_MV'].isnull()):
        result = result.drop(columns=['Total_MV'])
        import config.config_Ali as config
        conf = config.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')
        sql_industry = ','.join(["'%s'" % item for item in industry])
        total_mv_sql = f"""select sd.ts_code, si.name, si.industry, sd.total_mv, sd.trade_date
                           from stock_data sd 
                           inner join (select ts_code,name, industry 
                           from stockinfo where industry in ({sql_industry})) si 
                           on sd.ts_code =si.ts_code 
                           where sd.trade_date = '{end_date}' 
                           order by sd.ts_code desc"""
        stock_mvs = pd.read_sql_query(sql=total_mv_sql, con=engine)
        stock_mvs = stock_mvs.rename(columns={'total_mv': 'Total_MV'})
        result = pd.merge(result, stock_mvs[['ts_code', 'Total_MV']], on='ts_code', how='inner')
    result['Total_MV'] = round(result['Total_MV'] / 10000, 2)
    result = result.sort_values(by=['industry', 'Total_MV'], ascending=[True, False])
    result = result.drop_duplicates(subset='ts_code', keep='last')
    if mode is not None:
        result_pick = stkpick_from_simplesfit(result, now_secdate)
        # result_pick = result_pick.sort_values(by=['Now_Turnover_Rank', 'Total_MV'], ascending=[True, False])
        # result_pick = result_pick.sort_values(by=['PreNowPeak2Now_LastDays', 'Total_MV'], ascending=[False, False])
        if result_pick is not None and len(result_pick) > 0:
            result_pick = result_pick.drop_duplicates(subset='ts_code', keep='last').sort_values(
                by=['Pre_PreNow_Sec_SumRatio', 'Total_MV'], ascending=[True, False])
    else:
        result_pick = None
    return result, result_pick


def pickstk_method_recent_indus(test_startdate=None, test_enddate=None, pick_startdate=None, pick_enddate=None,
                                index_peakdate=None, now_secdate=None, num=6, industry=None):
    """统计start_date至end_date区间强势股票行业，据此筛选pick_date的股票品种"""
    result = get_result_3(start_date=test_startdate, end_date=test_enddate)
    if pick_startdate is None:
        pick_startdate = pick_enddate
    if index_peakdate is None:
        index_peakdate = '2024-05-20'
    print('市场指数近期波峰日期：', index_peakdate, '\n')

    def get_condition_1(result, index_peakdate, now_secdate):
        condition_1 = (result['Recent3DValley_Over_HighQuntl_Num'] >= 1
                       ) & (result['Recent3DPeak_Over_HighQuntl_Num'] >= 1
                            ) & ((result['Now_ValleyGapValue'] == result['PreNowSec_MaxValleyGapValue']) |
                                 (result['PostPreNowSec_Over_HighQuntl_Num'] >= 2))
        condition_2 = (result['PostSecPeak_Over_StartCls_State'] == "True"
                       ) & (result['PostSecStart_Sec_Max_AvgRatio'] > result['Pre_PreNow_Sec_AvgRatio']) & (
                result['PostSecStart_MaxPeakGapValue']*0.9 > result['PreSecPeak_Sec_MaxPeakGapValue'])
        condition_6 = ((result['PostSecStart_Sec_Max_AvgRatio'] != result['Pre_PreNow_Sec_AvgRatio']) |
                       result['PostSecStart_Sec_Max_AvgRatio'] < 2)
        condition_3 = (result['Section_StartDate'] == result['Now_SecDate'])
        condition_4 = (result['PreNow_SecDate'] < index_peakdate)
        if now_secdate is None:
            condition_5 = (result['Now_SecDate'] == result['Cal_Date'])
        else:
            condition_5 = (result['Now_SecDate'] == now_secdate)
        # condition_4 = (result['Now_ValleySignal'] == "True") & (result['PostNowSec_Over_HighQuntl_Num']<=1)
        return result[(condition_1 & (condition_2 | condition_3) & condition_4 & condition_5 & condition_6)].copy()

    def cal_stk_meanperf(ts_code, cal_date, num, stock_data):
        stk_data = stock_data.query(
            'ts_code==@ts_code & trade_date>=@cal_date').sort_values(
            by='trade_date', ascending=True).set_index('trade_date', drop=True)
        select_num = min(num, len(stk_data))
        return round((stk_data['close'].iloc[1:select_num].mean() / stk_data['close'].iloc[0] - 1) * 100, 2)

    if industry is None:
        result_select = get_condition_1(result, index_peakdate, now_secdate
                                        ).drop_duplicates(subset='ts_code', keep='last')
        if len(result_select) == 0:
            print('test周期区间筛选结果为0，程序结束！')
            return None, None, None
        stock_data = get_stock_data(start_date=test_startdate, stk_code=result_select['ts_code'].unique().tolist())
        result_select['post5D_avgratio'] = result_select.apply(
            lambda fn: cal_stk_meanperf(fn['ts_code'], fn['Now_SecDate'], num, stock_data), axis=1)
        indus_stat = result_select[['industry', 'post5D_avgratio']].groupby('industry').mean()
        indus_stat = indus_stat.sort_values(by='post5D_avgratio', ascending=False)
        indus_count = result_select[['industry', 'post5D_avgratio']].groupby('industry').count()
        indus_count = indus_count.rename(columns={'post5D_avgratio': 'count'})
        # 获取result_select按照industry进行groupby后post5D_avgratio大于0的比例
        indus_over0 = result_select.query('post5D_avgratio>0')[['industry', 'post5D_avgratio']].groupby('industry').count()
        indus_over0 = indus_over0.rename(columns={'post5D_avgratio': 'over0_count'})
        indus_all = result.drop_duplicates(subset='ts_code', keep='last'
                                           )[['ts_code', 'industry']].groupby('industry').count()
        indus_all = indus_all.rename(columns={'ts_code': 'all_count'})
        indus_stat = pd.merge(indus_stat, indus_count, on='industry', how='left')
        indus_stat = pd.merge(indus_stat, indus_over0, on='industry', how='left')
        indus_stat = pd.merge(indus_stat, indus_all, on='industry', how='left')
        indus_stat['over0_ratio'] = round(indus_stat['over0_count'] / indus_stat['count'], 2)
        indus_stat['count_ratio'] = round(indus_stat['count'] * 100 / indus_stat['all_count'], 2)
        median_count = min(indus_stat['over0_count'].median(), 3)
        industry_adj = indus_stat.query('post5D_avgratio>0 & over0_count>=@median_count').sort_values(
            by=['over0_count', 'post5D_avgratio'], ascending=[False, False])
        industry_adj = industry_adj.iloc[:10].sort_values(by='post5D_avgratio', ascending=False)
        industry_list = industry_adj.index[:5].tolist()
        with pd.option_context('expand_frame_repr', False):
            print('近期强势行业筛选结果：\n', industry_adj[['post5D_avgratio', 'over0_count']].iloc[:5])
    else:
        industry_list = industry
        indus_stat = None
        result_select = None
    if len(industry_list) > 0 and pick_enddate is not None:
        if pick_startdate is None:
            pick_startdate = pick_enddate
        result = get_result_3(start_date=pick_startdate, end_date=pick_enddate)
        result_pick = get_condition_1(result, index_peakdate, now_secdate).sort_values(
            by=['industry', 'Total_MV'], ascending=[True, False])
        result_indus = result_pick.query('industry in @industry_list').drop_duplicates(
            subset='ts_code', keep='last').sort_values(by=['industry', 'Total_MV'], ascending=[True, False])
        result_dispute = result_pick[['ts_code', 'industry']].groupby('industry').count()
        result_dispute = result_dispute.rename(columns={'ts_code': 'count'}).sort_values(by='count', ascending=False)
        print('\n备选股票行业分布：\n', result_dispute.iloc[:len(result_dispute)])
    elif industry is None:
        result_pick, result_indus = result_select.query('post5D_avgratio>0').sort_values(
            by=['industry', 'post5D_avgratio'], ascending=[True, False]), None
    else:
        result_indus, result_pick = None, None
    return result_indus, result_pick, indus_stat


def stk2idx_compare(stk_list=None, start_date=None, end_date=None, now_secdate=None, industry=None):
    """获取指定起止日期区间内，stk_list中股票相对中证800指数的涨跌幅状态"""
    trade_dates = get_trade_date()
    if end_date is None:
        end_date = trade_dates.iloc[-1]
    result_data = get_result_3(start_date=start_date, end_date=end_date)
    if stk_list is None:
        if industry is None:
            print('需要stk_list或industry参数！')
            return None
        num = 30
        result_list = result_data.query('Cal_Date==@start_date & industry in @industry'
                                        ).sort_values(by=['industry', 'Total_MV'], ascending=[True, False])
        stk_list = result_list.groupby('industry').head(num)['ts_code'].tolist()
    elif isinstance(stk_list, str):
        stk_list = [stk_list]
    if now_secdate is None:
        now_secdate = start_date
    stock_data = get_stock_data(stk_code=stk_list, start_date=start_date, end_date=end_date)
    index_data = get_index_data(stk_code='000906.SH', start_date=start_date, end_date=end_date
                                ).sort_values(by='trade_date').set_index('trade_date')
    stock_info = get_stock_info()
    index_data['idx_sumratio'] = round((index_data['close'] / index_data['close'].iloc[0] - 1) * 100, 2)
    index_data['idx_dayratio'] = index_data['close'].pct_change() * 100
    compare_result = pd.DataFrame()
    for stk_code in stk_list:
        stk_data = stock_data.query('ts_code==@stk_code').sort_values(by='trade_date', ascending=True
                                                                      ).set_index('trade_date')
        if len(stk_data) == 0:
            continue
        stk_data['stk_sumratio'] = round((stk_data['close'] / stk_data['close'].iloc[0] - 1) * 100, 3)
        stk_data['stk_dayratio'] = stk_data['close'].pct_change() * 100
        stk_data = pd.merge(stk_data, index_data[['idx_sumratio', 'idx_dayratio']],
                            left_index=True, right_index=True, how='inner')
        stk_data['stk2idx_sumratio'] = stk_data['stk_sumratio'] - stk_data['idx_sumratio']
        stk_data['stk2idx_dayratio'] = stk_data['stk_dayratio'] - stk_data['idx_dayratio']
        stk_data['stk_rollmin_shift'] = stk_data['close'].rolling(window=2, closed='left').min().shift(1)
        temp = pd.DataFrame({'ts_code': stk_code,
                             'name': stock_info.query('ts_code==@stk_code')['name'].iloc[-1]
                             if len(stock_info.query('ts_code==@stk_code')) > 0 else '-',
                             'industry': stock_info.query('ts_code==@stk_code')['industry'].iloc[-1] if
                             len(stock_info.query('ts_code==@stk_code')) > 0 else '-',
                             'stk2idx_sum': round(stk_data['stk2idx_sumratio'].iloc[-1], 3),
                             'stk2idx_sumavg': round(stk_data['stk2idx_sumratio'].mean(), 3),
                             'stk2idx_sumstd': round(stk_data['stk2idx_sumratio'].std(), 3),
                             'dropdate_stk2idx_dayavg': stk_data.query('idx_dayratio<0')['stk2idx_dayratio'].mean(),
                             'dropdate_stk2idx_daymax': stk_data.query('idx_dayratio<0')['stk2idx_dayratio'].min(),
                             'surpass_ratio': round(sum(stk_data['stk2idx_dayratio'] > 0) / len(stk_data), 3),
                             'adverse2trend_ratio': round(len(stk_data.query('close<stk_rollmin_shift')) /
                                                          len(stk_data), 3),
                             'stk_avg': round(stk_data['stk_dayratio'].mean(), 3)},
                            index=range(1))
        compare_result = pd.concat([compare_result, temp], ignore_index=True)
    if now_secdate is not None:
        if stk_list is None:
            result = result_data.query('Cal_Date==@end_date & industry in @industry').copy()
        else:
            result = result_data.query('Cal_Date==@end_date & ts_code in @stk_list').copy()
        result_turn = result_data.query('Cal_Date==@start_date').copy()
        result['Now_SecDiff'] = result.apply(lambda fn: conf_nowdiff(fn['Now_SecDate'], now_secdate, trade_dates),
                                             axis=1)
        compare_result = pd.merge(compare_result, result[['ts_code', 'Total_MV', 'Now_SecDate',
                                                          'Now_SecDiff', 'PostTurn_Over7Num']],
                                  on='ts_code', how='left')
        compare_result = pd.merge(compare_result,
                                  result_turn[['ts_code', 'Recent3DValley_Over_HighQuntl_Num',
                                               'Recent3DPeak_Over_HighQuntl_Num']],
                                  on='ts_code', how='left')
    # std_mean = compare_result['stk2idx_sumstd'].median()
    compare_result = compare_result.sort_values(by=['industry', 'stk2idx_sum'], ascending=[True, False])
    compare_result_adj = compare_result.query('stk2idx_sum>0 & stk_avg>0 & '
                                              'Recent3DValley_Over_HighQuntl_Num>0 & Now_SecDiff<=2'
                                              ).sort_values(by=['industry', 'stk2idx_sum'], ascending=[True, False])
    secdiff = compare_result[['industry', 'Now_SecDiff']].groupby('industry')['Now_SecDiff'].mean().sort_values()
    print('行业Now_SecDate吻合度排序：\n', secdiff)
    return compare_result, compare_result_adj


def cal_indus_secstate(end_date=None, now_secdate=None, industry=None):
    """统计行业列表中，Now_SecDate吻合，且近三天存在超HighQuntl现象占比"""
    num = 30
    if isinstance(industry, str):
        industry = [industry]
    if now_secdate is None:
        now_secdate = end_date
    result_data = get_result_3(start_date=now_secdate, end_date=end_date).query('industry in @industry').copy()
    trade_dates = get_trade_date()
    result = result_data.query('Cal_Date==@end_date').copy()
    result_sec = result_data.query('Cal_Date==@now_secdate').copy()
    result_sec = result_sec.rename(columns={'Recent3DValley_Over_HighQuntl_Num': 'Sec_Recent3DValley_Over_HighQuntl_Num'})
    result['Now_SecDiff'] = result.apply(lambda fn: conf_nowdiff(fn['Now_SecDate'], now_secdate, trade_dates),
                                         axis=1)
    result = pd.merge(result, result_sec[['ts_code', 'Sec_Recent3DValley_Over_HighQuntl_Num']],
                      on='ts_code', how='left')
    result_adj = result.groupby('industry').head(num)
    result_state = dict()
    for indus in industry:
        indus_count = len(
            result_adj.query('industry==@indus & Now_SecDiff<=2 & (Sec_Recent3DValley_Over_HighQuntl_Num>0 | '
                             'Recent3DPeak_Over_HighQuntl_Num>0)')) / len(result_adj.query('industry==@indus'))
        result_state[indus] = round(indus_count, 3)
    result_indus = result_adj.query(
        'Now_SecDiff<2 & (Sec_Recent3DValley_Over_HighQuntl_Num>0 | Recent3DPeak_Over_HighQuntl_Num>0)'
    ).sort_values(by=['industry', 'Total_MV'], ascending=[True, False])
    return sorted(result_state.items(), key=lambda x: x[1], reverse=True), result_indus


def pickstk_method_indus_head_old(end_date=None, now_secdate=None, industry=None, check_lagdate=None):
    """获取指定日期、指定行业的指标数据，筛选标的"""
    if now_secdate is None:
        now_secdate = end_date
    trade_dates = get_trade_date()
    pre_nowsec = trade_dates[trade_dates < now_secdate][-1]
    pre_enddate = trade_dates[trade_dates < end_date][-1]
    result_origin = get_result_3(start_date=pre_nowsec, end_date=end_date)
    # result_data = result_origin.query('industry in @industry').copy()
    result_data = result_origin
    result = result_data.query('Cal_Date==@end_date').copy()
    result_sec = result_data.query('Cal_Date==@now_secdate').copy()
    result_preend = result_data.query('Cal_Date==@pre_enddate').copy()
    result_preend = result_preend.rename(columns={'Now_ValleyGapValue': 'PreNow_ValleyGapValue'})
    result = pd.merge(result, result_preend[['ts_code', 'PreNow_ValleyGapValue']], on='ts_code', how='left')
    result_sec = result_sec.rename(columns={
        'Recent3DValley_Over_HighQuntl_Num': 'Sec_Recent3DValley_Over_HighQuntl_Num'})
    result['Now_SecDiff'] = result.apply(lambda fn: conf_nowdiff(fn['Now_SecDate'], now_secdate, trade_dates),
                                         axis=1)
    result['Turnover_Ratio'] = round(result['PostNowSec_AvgTurnover'] / result['PreSec_AvgTurnover'], 3)
    result = pd.merge(result, result_sec[['ts_code', 'Sec_Recent3DValley_Over_HighQuntl_Num']],
                      on='ts_code', how='left')
    result = result.sort_values(by=['industry', 'Total_MV'], ascending=[True, False])
    if now_secdate == end_date:
        result_temp = result.query(
            'Now_SecDiff<2 & '
            # '((PostSecStart_Sec_Max_AvgRatio!=Pre_PreNow_Sec_AvgRatio) | PostSecStart_Sec_Max_AvgRatio<2) & '
            'PostPreNowSec_Over_HighQuntl_Num>=1 & '
            '(Sec_Recent3DValley_Over_HighQuntl_Num>=1 | NowSec_Recent3DValley_Over_HighQuntl_Num>=1) & '
            # 'PreTurnPeak_Sec_MaxPeakGapValue<PostTurn_MaxPeakGapValue & '
            'PostTurn_Over7Num>0 & '
            'PostSecStart_MedianPeakGap>=2 & '
            'PostSecStart_Sec_Max_AvgRatio>2')
    else:
        result_temp = result.query(
            '(Now_SecDiff<2 | Now_SecDate<@now_secdate) & '
            # '((PostSecStart_Sec_Max_AvgRatio!=Pre_PreNow_Sec_AvgRatio) | PostSecStart_Sec_Max_AvgRatio<2) & '
            'PostNowSec_Over_HighQuntl_Num>=1 & '
            # 'Recent3Day_MaxPeakGapValue<PostSecStart_PeakGapValue_HighQuntl & '
            '(Sec_Recent3DValley_Over_HighQuntl_Num>=1 | NowSec_Recent3DValley_Over_HighQuntl_Num>=1) & '
            'PostNowSec_MaxPeakGapValue<PostTurn_MaxPeakGapValue & '
            # 'PreTurnPeak_Sec_MaxPeakGapValue<PostTurn_MaxPeakGapValue & '
            'PostTurn_Over7Num>0 & '
            'PostSecStart_MedianPeakGap>=2 & '
            'PostSecStart_Sec_Max_AvgRatio>2')
    result_to = result_temp[['industry', 'ts_code', 'name', 'Turnover_Ratio', 'PostSecStart_Over7Num',
                             'PostTurn_Over7Num', 'Now_SecDate', 'PreNowPeak2Now_LastDays',
                             'PostNowSec_Over_HighQuntl_Num', 'Total_MV'
                             ]].sort_values(by=['industry', 'Total_MV'], ascending=[True, False]).iloc[:20]

    # result_mv = result.groupby('industry').head(20)
    mv_column_list = ['industry', 'ts_code', 'name', 'Turnover_Ratio',
                      'PostSecStart_Over7Num', 'PostTurn_Over7Num', 'Now_SecDate',
                      'PreNowPeak2Now_LastDays', 'PostNowSec_LastDays', 'PostSecStart_LastDays',
                      'PostNowSec_AvgRatio', 'PostPreNowSec_Over_HighQuntl_Num',
                      'PostNowSec_Over_HighQuntl_Num',
                      'PostNowSec_Adverse2Trend', 'PostSecStart_MedianPeakGap',
                      'Recent3DValley_Over_HighQuntl_Num',
                      'Recent3DPeak_Over_HighQuntl_Num',
                      'NowSec_Recent3DValley_Over_HighQuntl_Num', 'Now_Und3Mean',
                      'BreakPreNowSec_Ratio', 'NowSec_MaxValley',
                      'Total_MV']
    prenowpeak2now_avglastdays = result[['industry', 'PreNowPeak2Now_LastDays']].groupby('industry').mean()
    prenowpeak2now_avglastdays = prenowpeak2now_avglastdays.rename(
        columns={'PreNowPeak2Now_LastDays': 'PreNowPeak2Now_AvgLastDays'})
    result['PreNowPeak2Now_AvgLastDays'] = result['industry'].map(
        prenowpeak2now_avglastdays['PreNowPeak2Now_AvgLastDays'])
    postnow_avglastdays = result[['industry', 'PostNowSec_LastDays']].groupby('industry').mean()
    postnow_avglastdays = postnow_avglastdays.rename(columns={'PostNowSec_LastDays': 'PostNowSec_AvgLastDays'})
    result['PostNowSec_AvgLastDays'] = result['industry'].map(postnow_avglastdays['PostNowSec_AvgLastDays'])
    postsec_avglastdays = result[['industry', 'PostSecStart_LastDays']].groupby('industry').mean()
    postsec_avglastdays = postsec_avglastdays.rename(columns={'PostSecStart_LastDays': 'PostSecStart_AvgLastDays'})
    result['PostSecStart_AvgLastDays'] = result['industry'].map(postsec_avglastdays['PostSecStart_AvgLastDays'])
    result['NowSec_MaxValley'] = result.apply(
        lambda fn: 'True' if fn['NowSec_Recent3D_MaxValleyGapValue'] == fn['Peak2Sec_MaxValleyGapValue'] or
                             fn['NowSec_Recent3D_MaxValleyGapValue'] == fn['PostSecPeak_MaxValleyGapValue']
        else '-', axis=1)

    result_head = result.groupby('industry').head(30).copy()
    # result_mv_sec = result_head.query('Now_SecDate>=@pre_nowsec & (NowSec_Recent3DValley_Over_HighQuntl_Num>=1 |'
    #                                   'PostPreNowSec_Over_HighQuntl_Num>=1) & '
    #                                   '(PreNowPeak2Now_LastDays>=PreNowPeak2Now_AvgLastDays | '
    #                                   'PostSecStart_LastDays<=PostSecStart_AvgLastDays) & '
    #                                   'PostSecStart_MedianPeakGap>=2 & '
    #                                   'Period_TurnDate<Section_StartDate & '
    #                                   'Now_ValleyGapValue>PreNow_ValleyGapValue & '
    #                                   'Now_ValleyGapValue>PostSecStart_MedianValleyGapValue & '
    #                                   'Now_PostSecPeak_VGV_Desc_Rank<=3 & '
    #                                   'PreSec_SecDropDays_QuntlRatio>1 & PreSec_SecDropAvg_QuntlRatio<1'
    #                                   ).copy().sort_values(
    #     by=['industry', 'Total_MV'], ascending=[True, False])[mv_column_list]
    result_head_sec = result_head.query('Now_SecDate>=@pre_nowsec & (NowSec_Recent3DValley_Over_HighQuntl_Num>=1 |'
                                        'PostPreNowSec_Over_HighQuntl_Num>=1) & '
                                        'Period_TurnDate<Section_StartDate & '
                                        'Now_PostSecPeak_VGV_Desc_Rank<=4 & '
                                        '(Now_PostSecPeak_VGV_Desc_Rank>1 | PreNowSec_LastDays>=5) & '
                                        'Now_PostSecPeak_VGV_MaxCoverDays>=2 & '
                                        'SecConcave_TO_Sum>40'
                                        ).copy().sort_values(
        by=['industry', 'Total_MV'], ascending=[True, False])[mv_column_list]
    # 'PreNowPeak_BottomDate<Now_SecDate'
    # result_mv_stand = result_head.query('(PostNowSec_LastDays>=PostNowSec_AvgLastDays | '
    #                                     'PostNowSec_Adverse2Trend<(0.4)) & '
    #                                     'PostNowSec_Adverse2Trend<(0.6) & '
    #                                     'PostNowSec_Over_HighQuntl_Num>0 & Now_DayRatio<0 & '
    #                                     'Recent3Day_MaxValleyGapValue<PostSecStart_ValleyGapValue_HighQuntl & '
    #                                     'PostNowSec_MaxPeakGapValue<PostTurn_MaxPeakGapValue'
    #                                     ).copy().sort_values(
    #     by=['industry', 'PostSecStart_LastDays'], ascending=[True, False])[mv_column_list]
    result_stand = result_origin.query('Cal_Date==@end_date & '
                                       'PostNowSec_LastDays<=5 & Now_SecDate<=@pre_nowsec & '
                                       'PostNowSec_Adverse2Trend<(0.4) & '
                                       'PostNowSec_Over7Num>=1 & '
                                       'Recent3Day_MaxValleyGapValue>PostSecStart_ValleyGapValue_HighQuntl & '
                                       'PostSecStart_MedianPeakGap>=2'
                                       ).copy().sort_values(
            by=['industry', 'PostSecStart_MedianPeakGap'], ascending=[True, False])
    if industry is not None:
        if isinstance(industry, str):
            industry = [industry]
        result_to = result_to.query('industry in @industry').copy()
        result_head = result_head.query('industry in @industry').copy()
        result_head_sec = result_head_sec.query('industry in @industry').copy()
    # mv_stk_list = result_mv['ts_code'].values.tolist()
    # result_indus['in_head'] = result_indus.apply(
    #     lambda fn: 'True' if fn['ts_code'] in mv_stk_list else '-', axis=1)
    if check_lagdate is not None and len(result_head_sec) > 0:
        stock_data = get_stock_data(stk_code=result_head_sec['ts_code'].values.tolist(),
                                    start_date=end_date, end_date=check_lagdate)
        result_head_sec['Lag_Ratio'] = result_head_sec['ts_code'].apply(
            lambda fn: round((stock_data.query('ts_code==@fn')['close'].iloc[-1] /
                              stock_data.query('ts_code==@fn')['close'].iloc[0] - 1)*100, 3))
        result_head_sec = result_head_sec.sort_values(by='Lag_Ratio', ascending=False)
        head_list = result_head['ts_code'].values.tolist()
        result_lag = get_result_3(end_date=check_lagdate)
        result_lag = result_lag.query('ts_code in @head_list').copy()
        result_lag = result_lag.rename(columns={'Now_SecDate': 'Lag_Now_SecDate'})
        result_head = pd.merge(result_head, result_lag[['ts_code', 'Lag_Now_SecDate']], on='ts_code', how='left')
        result_head['NowSec_Check'] = result_head.apply(
            lambda fn: True if fn['Now_SecDate'] >= fn['Lag_Now_SecDate'] else False, axis=1)
    return result_to, result_head, result_stand, result_head_sec


def compare_indus2idx_avg(start_date=None, end_date=None, index_code='000906.SH', industry=None):
    """计算市值居前股票在指定日期区间内相对指数涨跌幅的均值及战胜指数的胜率"""
    num = 20
    stock_data = get_stock_data(start_date=start_date, end_date=end_date)
    def cal_ratio(stk_data):
        return round((stk_data['close'].iloc[-1] / stk_data['close'].iloc[0] - 1) * 100, 2)
    stk_ratio = stock_data.groupby('ts_code', as_index=False).apply(cal_ratio)
    stk_ratio.columns=['ts_code', 'stk_ratio']
    stock_info = get_stock_info()
    stk_ratio = pd.merge(stk_ratio, stock_info[['ts_code', 'name', 'industry']], on='ts_code', how='left')
    stk_ratio = pd.merge(stk_ratio, stock_data.query('trade_date==@end_date')[['ts_code', 'total_mv']],
                         on='ts_code', how='left')
    index_data = get_index_data(stk_code=index_code, start_date=start_date, end_date=end_date)
    index_data['idx_ratio'] = round((index_data['close'].iloc[-1] / index_data['close'].iloc[0] - 1) * 100, 2)
    stk_ratio['idx_ratio'] = index_data['idx_ratio'].iloc[-1]
    stk_ratio['stk2idx_ratio'] = stk_ratio['stk_ratio'] - stk_ratio['idx_ratio']
    if industry is not None:
        stk_ratio = stk_ratio.query('industry in @industry').copy()
    stk_ratio_mv = stk_ratio.sort_values(by=['industry', 'total_mv'], ascending=[True, False]
                                         ).groupby('industry').head(num)
    indus_dispute = stk_ratio_mv.groupby('industry')['stk2idx_ratio'].mean()
    indus_dispute = indus_dispute.round(4)
    indus_dispute = indus_dispute.sort_values(ascending=False)
    return indus_dispute


def check_stk_rules(end_date=None, stk_list=None, result=None, check_mode='store',
                    check_date=None, check_column=None):
    """获取result结果中指定股票品种的指定指标数据，统计对比"""
    if end_date is not None and stk_list is not None:
        result = get_result_3(end_date=end_date, stk_list=stk_list)
    elif result is None:
        print('需要end_date和stk_list或result参数！')
        return None
    Columns = result.columns
    Column_Base = ['ts_code', 'name', 'industry', 'Total_MV',
                   'Period_TurnDate', 'Section_StartDate', 'Now_SecDate']
    column_str, Column_List = [], []
    for column in Columns:
        try:
            if isinstance(result[column].iloc[:10].max(skipna=True), str):
                column_str.append(column)
            elif isinstance(result[column].iloc[:10].max(skipna=True), float):
                Column_List.append(column)
        except TypeError:
            continue
    if isinstance(check_column, str):
        Column_List = Column_List[Column_List.get_loc(check_column):]
    #
    # Column_List_1 = ['Turn2Peak_MaxContiDrop', 'PostTurn_Period_RiseNum',
    #                  'PostTurn_Over7Num', 'PostSecPeak_MinDaily2Now_LastDays', 'PostSecPeak2Now_LastDays',
    #                  'PostSecStart_Drop2Rise_SecMaxDaysDiv', 'PostSecStart_Drop2Rise_SecSumDaysDiv',
    #                  'PostSecStart_Drop2Rise_SecMaxSumDiv',
    #                  'Bf_SecStart_5D_MinRatio', 'Recent4P_MaxLastDays', 'LastDrop_COR_Ratio',
    #                  'PreNowSec_Und2ContiDays', 'Pre_PreNowSec_Und2ContiDays',
    #                  'PreNowSec_SecDropDays_QuntlRatio', 'PreNowSec_SecDropAvg_QuntlRatio',
    #                  'PreNow_Rise_MaxContiAvg',
    #                  'SecConcave_LastDays', 'SecConcave_TO_Sum', 'Peak_Pres_Lastdays', 'PostSec_1stSec_BreakRatio',
    #                  'Break_D100_Ratio'
    #                  ]
    # Column_List_2 = ['PostTurn_TO_Over10Num', 'Now_Turnover_Signal', 'Now_Turnover_Quntl',
    #                  'PostSecStart_TO_Over10Num', 'PreTurn_Turnover2Drop_Last4Rank',
    #                  'PreSec_Turnover2Drop_Last4Rank', 'PreSec_Turnover2Drop_State',
    #                  'Now_Turnover2Rise_State', 'SecStart_1stTODate2Now_LastDays',
    #                  'PreNowSec_Adverse2Trend', 'Latst2Now_DDRQ_Days',
    #                  'PostSecStart_MovAvg_BiasMean', 'PostSecStart_MovAvg_BiasStd',
    #                  'PreNow2Now_Volab_ProP', 'UndVolab_LastDays', 'Recent5D_BfPreNow_Volab_Num',
    #                  'PostPeak_Recent_Neg4_RecovDays',
    #                  'TurnDiff_Recoved_Sec_Num', 'PostSec_Max2PreShock_Ratio',
    #                  'Now_PostSec_SecDropState', 'Now_PostSec_SecRiseState'
    #                  ]
    # Column_List_3 = ['PostSecStart_MedianPeakGap', 'PostSecStart_PeakGapValue_HighQuntl',
    #                  'PostSecStart_ValleyGapValue_HighQuntl', 'PostSecStart_MaxPeakGap2Yesd_Ratio',
    #                  'PostSecStart_PeakGapNow2Med_Ratio', 'PostSecStart_PeakGapValue_TrackSignal',
    #                  'PostPreNowSec_Over_HighQuntl_Num',
    #                  'PostNowSec_Over_HighQuntl_Num', 'Recent3DValley_Over_HighQuntl_Num',
    #                  'Recent3DPeak_Over_HighQuntl_Num'
    #                  ]
    # Column_List_4 = ['ts_code', 'name', 'industry', 'Total_MV',
    #                  'Period_TurnDate', 'Section_StartDate', 'Now_SecDate',
    #                  'Period_Trend', 'Break_PreSec', 'Break_TurnShock', 'PostPreNowPeak_CumSum2IdxRatio_MinDate',
    #                  'Convex_Break_DropDate', 'Now_Shock_Date', 'PostSecPeak_PeakGapUndHighQuntl_Signal']
    # Column_List = Column_List_1 + Column_List_2 + Column_List_3
    if check_mode.lower() != "compare":
        result_adj = result[Column_List]
        result_mean = result_adj[Column_List].mean(skipna=True)
        result_std = result_adj[Column_List].std(skipna=True)
        result_max = result_adj[Column_List].max(skipna=True)
        result_min = result_adj[Column_List].min(skipna=True)
        output = pd.DataFrame({'mean': result_mean, 'std': result_std, 'max': result_max, 'min': result_min})
        output['upper_line'] = output['mean'] + output['std'] / 4
        output['lower_line'] = output['mean'] - output['std'] / 4
        result_adj = result[Column_Base + Column_List]
        output = output.reset_index()
        if end_date is None:
            end_date = result['Cal_Date'].max()
        if check_mode.lower() != "store":
            if check_column is None:
                path = '/Users/<USER>/PycharmProjects/AI_Stock/check_data/check_stat_' + \
                       pd.to_datetime(end_date).strftime('%Y%m%d') + '.csv'
            else:
                path = '/Users/<USER>/PycharmProjects/AI_Stock/check_data/check_stat_column_' + \
                       pd.to_datetime(end_date).strftime('%Y%m%d') + '.csv'
            output.to_csv(path, index=False, encoding='utf-8-sig')
    elif check_mode.lower() == 'compare' and check_date is not None:
        if check_column is None:
            path = '/Users/<USER>/PycharmProjects/AI_Stock/check_data/check_stat_' + \
                   pd.to_datetime(check_date).strftime('%Y%m%d') + '.csv'
        else:
            path = '/Users/<USER>/PycharmProjects/AI_Stock/check_data/check_stat_column_' + \
                   pd.to_datetime(check_date).strftime('%Y%m%d') + '.csv'
        check_standard = pd.read_csv(path)
        check_standard = check_standard.set_index('index')
        # check_stardard是以result_adj的columns为index的DataFrame，现需要比较result_adj中每个columns的值是否在
        # check_standard的upper_line和lower_line的范围内
        check_result = pd.DataFrame(index=result.index, columns=Column_List)
        for column in Column_List:
            min_val = check_standard.loc[column, 'lower_line']
            max_val = check_standard.loc[column, 'upper_line']
            check_result[column] = result[column].between(min_val, max_val)
        check_result['check_sum'] = check_result.sum(axis=1)
        columns = check_result.columns
        column_loc = columns.get_loc('Now_PostSecPeak_VGV_MaxCoverDays') - len(columns)
        check_result['gvcheck_sum'] = check_result.iloc[:, column_loc:-1].sum(axis=1)
        check_result = pd.merge(result[['ts_code', 'name', 'industry', 'Total_MV', 'Now_SecDate', 'Now_DayRatio']],
                                check_result,
                                left_index=True, right_index=True, how='left')
        # for index in result.index:
        #     temp = pd.DataFrame()
        #     for column in Column_List:
        #         if len(temp) == 0:
        #             temp = pd.DataFrame({'ts_code': result.loc[index, 'ts_code'],
        #                                  'name': result.loc[index, 'name'],
        #                                  'industry': result.loc[index, 'industry'],
        #                                  'Total_MV': result.loc[index, 'Total_MV'],
        #                                  'Now_SecDate': result.loc[index, 'Now_SecDate'],
        #                                  column: True
        #                                  if pd.notnull(result.loc[index, column])
        #                                  and check_standard.loc[column, 'upper_line'] >=
        #                                  result.loc[index, column] >=
        #                                  check_standard.loc[column, 'lower_line']
        #                                  else False
        #                                  }, index=range(1))
        #         else:
        #             new_columndata = True \
        #                 if (pd.notnull(result.loc[index, column]) and
        #                     check_standard.loc[column, 'upper_line'] >= result.loc[index, column] >=
        #                     check_standard.loc[column, 'lower_line']) else False
        #             temp = pd.concat([temp, pd.DataFrame({column: new_columndata}, index=range(1))], axis=1)
        #             # temp.loc[:, column] = new_columndata
        #     check_result = pd.concat([check_result, temp], ignore_index=True, axis=1)

        check_result = check_result.sort_values(by=['check_sum', 'Now_SecDate', 'Total_MV_x'],
                                                ascending=[False, False, False])
        output = check_result
        result_adj = result[Column_Base + Column_List]
    else:
        print('需要指定check_mode和check_date日期！')
        output, result_adj = None, None
    return output, result_adj


def pickstk_method_stand_sec(start_date=None, pick_date=None, section_limitdate=None,
                             industry=None, lag_checkdate=None):
    """筛选start_date至pick_date期间出现上行强势的品种，再次筛选pick_date处于Now_SecDate的品种"""
    result_origin = get_result_3(start_date=start_date, end_date=pick_date)
    result_period = result_origin.query('Cal_Date<@pick_date').copy()
    trade_dates = get_trade_date()
    eval_num = -2
    result_stand = result_period.query('PostNowSec_LastDays<=15 & Now_SecDate<Cal_Date & '
                                       'PostNowSec_Adverse2Trend<(0.4) & '
                                       '(PostNowSec_HighOver7Num>=1 | PostNowSec_Over7Num>=1)& '
                                       '(Recent3Day_MaxValleyGapValue>PostSecStart_ValleyGapValue_HighQuntl | '
                                       '(Recent3Day_MaxPeakGapValue>PostSecStart_PeakGapValue_HighQuntl & '
                                       'Now_DayRatio<=@eval_num)) & '
                                       'PostSecStart_MedianPeakGap>=1.4 '
                                       ).drop_duplicates(subset='ts_code', keep='last').copy().sort_values(
        by=['industry', 'PostSecStart_MedianPeakGap'], ascending=[True, False])
    stand_list = result_stand['ts_code'].values.tolist()
    pick_startdate = trade_dates[(trade_dates < pick_date)][-2]
    result_pick = result_origin.query('Cal_Date>=@pick_startdate & ts_code in @stand_list').copy()
    result_stand2 = result_stand.copy().rename(columns={'Now_SecDate': 'Pick_Now_SecDate'})
    result_pick = pd.merge(result_pick, result_stand2[['ts_code', 'Pick_Now_SecDate']], on='ts_code', how='left')
    stock_data = get_stock_data(start_date=result_stand2['Pick_Now_SecDate'].min(), stk_code=stand_list)

    def cal_sec_clsdiff(ts_code, now_secdate, pick_now_secdate, stock_data):
        stk_data = stock_data.query('ts_code==@ts_code').sort_values(by='trade_date').set_index('trade_date')
        return round((stk_data.loc[now_secdate, 'close'] / stk_data.loc[pick_now_secdate, 'close'] - 1) * 100, 3)

    result_pick['Sec_ClsDiff'] = result_pick.apply(
        lambda fn: cal_sec_clsdiff(fn['ts_code'], fn['Now_SecDate'], fn['Pick_Now_SecDate'], stock_data), axis=1)
    result_now = result_pick.query('Cal_Date==@pick_date').copy()
    # result_pick['Now_SecDiff'] = result_pick.apply(
    #     lambda fn: conf_nowdiff(fn['Now_SecDate'], fn['Cal_Date'], trade_dates), axis=1)
    result_stand_now = pd.DataFrame()
    if section_limitdate is not None:
        result_stand_now = result_pick.query(
            '(Now_PostSecPeak_PGV_RollAvg_Asc_Rank==1 | '
            'Now_PostSecPeak_VGV_MaxCoverDays>10) & '
            'PostSecPeak_PGV_MinRollAvg2MaxRatio<=0.3 & '
            'Section_StartDate>=@section_limitdate & '
            'PreNowSec_SecDropDays_QuntlRatio<1.2 & '
            'PreNowSec_SecDropAvg_QuntlRatio<1.2 & '
            'Sec_ClsDiff>=0 & '
            'PreNowSec_LastDays>3'
            ).drop_duplicates(subset='ts_code', keep='last'
                              ).sort_values(by=['industry', 'PreNowPeak2Now_AvgTurnover'],
                                            ascending=[True, False])
        if len(result_stand_now) == 0:
            print('指定section_startdate筛选结果为空')
    if len(result_stand_now) == 0:
        result_stand_now = result_pick.query(
            '(Now_PostSecPeak_PGV_RollAvg_Asc_Rank==1 | '
            'Now_PostSecPeak_PGV_RollAvg_MinCoverDays>10) & '
            'PostSecPeak_PGV_MinRollAvg2MaxRatio<=0.3 & '
            'SecConcave_TO_Sum>40 & '
            'PreNowSec_SecDropDays_QuntlRatio<1.2 & '
            'PreNowSec_SecDropAvg_QuntlRatio<1.2 & '
            'Sec_ClsDiff>=0 & '
            'PreNowSec_LastDays>3'
            ).drop_duplicates(subset='ts_code', keep='last'
                              ).sort_values(by=['industry', 'PreNowPeak2Now_AvgTurnover'],
                                            ascending=[True, False])
    now_list = result_stand_now['ts_code'].values.tolist()
    result_stand_now_adj = result_now.query(
        'ts_code in @now_list & Now_SecDate==Cal_Date').sort_values(
        by=['industry', 'PreNowPeak2Now_AvgTurnover'],
        ascending=[True, False])
    result_stand_drop = result_now.query('Cal_Date==Now_SecDate &'
                                         'Now_DayRatio<0 & '
                                         '(Now_PostSecPeak_PGV_RollAvg_Asc_Rank==1 | '
                                         'Now_PostSecPeak_VGV_MaxCoverDays>=10) & '
                                         'PostSecPeak_PGV_MinRollAvg2MaxRatio<=0.3 & '
                                         'Section_StartDate<Now_SecDate & '
                                         'PreNowSec_SecDropDays_QuntlRatio<1.2 & '
                                         'PreNowSec_SecDropAvg_QuntlRatio<1.2 & '
                                         'Sec_ClsDiff>=0 & '
                                         'PreNowSec_LastDays>=3').sort_values(
        by=['industry', 'PreNowPeak2Now_AvgTurnover'], ascending=[True, False])

    stand_now_check = None
    if lag_checkdate is not None and len(result_stand_now_adj) > 0:
        result_lag = get_result_3(end_date=lag_checkdate)
        if len(result_lag) > 0:
            result_stand_now2 = result_stand_now_adj.copy()
            result_stand_now2 = result_stand_now2.rename(columns={'Now_SecDate': 'Confirm_Now_SecDate'})
            result_lag = pd.merge(result_lag, result_stand_now2[['ts_code', 'Confirm_Now_SecDate']],
                                  on='ts_code', how='inner')
            # result_lag['Sec_ClsDiff'] = result_lag.apply(
            #     lambda fn: cal_sec_clsdiff(fn['ts_code'], fn['Now_SecDate'],
            #                                fn['Confirm_Now_SecDate'], stock_data), axis=1)
            break_ratio = -1
            stand_now_check = result_lag.query(
                'Now_SecDate<=Confirm_Now_SecDate & PostNowSec_Adverse2Trend<0.4 & '
                'Now_PostNowSec_PGV_Desc_Rank>2 & BreakPreNowSec_Ratio>@break_ratio'
            ).sort_values(by=['industry', 'PostNowSec_AvgTurnover'],
                          ascending=[True, False])

    if industry is not None:
        if isinstance(industry, str):
            industry = [industry]
        result_stand_now_adj = result_stand_now_adj.query('industry in @industry')
        result_stand_drop = result_stand_drop.query('industry in @industry')
    column_list = ['industry', 'ts_code', 'name', 'PreNow_SecDate', 'PostSecStart_MedianValleyGapValue',
                   'PostSecStart_ValleyGapValue_HighQuntl', 'PostSecStart_MaxValleyGapValue']
    # result_stand_now = result_stand_now[column_list]
    return result_stand_now_adj, result_stand_drop, result_stand, stand_now_check


def induspick_turn_state(end_date=None, section_startdate=None, now_secdate=None, limit_num=30):
    """获取指定日期end_date的股票数据，选择行业前30，统计获取可能在该日期出现强弱转换的行业
    
    参数:
        end_date: 结束日期
        section_startdate: 区间起始日期
        now_secdate: 当前区间日期
        lag_checkdate: 滞后检查日期
        limit_num: 每个行业选择的股票数量限制,默认30
        
    返回:
        indus_count: 行业统计结果,包含Score等排序指标
        plate_indus_count: 板块行业统计结果
    """
    if section_startdate is None and now_secdate is None:
        print('须指定section_startdate或now_secdate参数！')
        return
    zzindex_daily_df = get_zzindex_data()
    prepeak_date = cal_prepeak_date(zzindex_daily_df, end_date)
    PreDrop_StartDate = prepeak_date
    Bottom_List, _, _ = cal_swindex_state(end_date=end_date, index_preturn_date=PreDrop_StartDate)
    indus_list = Bottom_List['indus'].values.tolist()
    Bottom_List = Bottom_List.set_index('indus')
    trade_dates = get_trade_date()
    cal_secdate = section_startdate if section_startdate is not None else now_secdate
    result = get_result_3(end_date=end_date, industry=indus_list)
    if len(result) > 0:
        result_head = result.sort_values(
            by=['industry', 'Total_MV'], ascending=[True, False]).groupby('industry').head(limit_num).copy()
        ts_list = result_head['ts_code'].values.tolist()
        stock_data = get_stock_data(stk_code=ts_list, start_date=cal_secdate, end_date=end_date)
    else:
        stk_info = get_stock_info()
        result = stk_info.query('industry in @indus_list').copy()
        ts_list = result['ts_code'].values.tolist()
        stock_data = get_stock_data(stk_code=ts_list, start_date=cal_secdate, end_date=end_date)
        
        # 获取每个股票最新日期的total_mv字段
        latest_mv_data = stock_data.sort_values(
            'trade_date', ascending=False).drop_duplicates('ts_code', keep='first')[['ts_code', 'total_mv']]
        # 添加到result中作为Total_MV字段
        result = pd.merge(result, latest_mv_data, on='ts_code', how='left')
        result = result.rename(columns={'total_mv': 'Total_MV'}, inplace=True)
        result_head = result.sort_values(by=['industry', 'Total_MV'], ascending=[True, False]).groupby('industry').head(limit_num).copy()
        
    if (section_startdate is not None and section_startdate < end_date) or \
       (now_secdate is not None and now_secdate < end_date):
        result_head['PostSec_Ratio'] = 0
        for index in result_head.index:
            stk_code = result_head.loc[index, 'ts_code']
            stk_data = stock_data.query('ts_code==@stk_code').copy()
            if len(stk_data) > 0:
                stk_data = stk_data.sort_values(by='trade_date', ascending=True).set_index('trade_date')
                result_head.loc[index, 'PostSec_Ratio'] = round(
                    (stk_data.loc[:end_date, 'close'].iloc[-1] / stk_data.loc[cal_secdate:, 'close'].iloc[0] - 1) * 100, 3)
        indus_SecRatio = result_head[['PostSec_Ratio', 'industry']].groupby(
            'industry')['PostSec_Ratio'].mean().sort_values(ascending=False)
        indus_SecRatio = pd.DataFrame(indus_SecRatio).dropna()
        indus_SecRatio['SecRatio_Rank'] = indus_SecRatio['PostSec_Ratio'].rank(ascending=False)
    else:
        indus_SecRatio = None
    if section_startdate is not None:
        result_head['Sec_DiffNum'] = result_head.apply(
            lambda fn: conf_nowdiff(fn['Section_StartDate'], section_startdate, trade_dates), axis=1)
        result_head['Sec2Peak_PRV_Diff'] = round(
            result_head['SectionStart_PRV_Top3Mean'] / result_head['SectionPeak_PRV_Top3Mean'], 3)
    elif now_secdate is not None:
        result_head['Sec_DiffNum'] = result_head.apply(
            lambda fn: conf_nowdiff(fn['Now_SecDate'], now_secdate, trade_dates), axis=1)
        result_head['Sec2Peak_PRV_Diff'] = round(
            result_head['NowSec_PRV_Top3Mean'] / result_head['PreNowPeak_PRV_Top3Mean'], 3)
    indus_SecDiff = result_head[['Sec_DiffNum', 'industry']].groupby(
        'industry')['Sec_DiffNum'].mean().sort_values(ascending=True)
    indus_SecDiff = pd.DataFrame(indus_SecDiff).dropna()
    indus_SecDiff['SecDiff_Rank'] = indus_SecDiff['Sec_DiffNum'].rank(ascending=True)
    if section_startdate is not None:
        indus_Peak2Now = result_head[['Peak2Sec_LastDays', 'industry']].groupby(
            'industry')['Peak2Sec_LastDays'].mean().sort_values(ascending=False)
        indus_Peak2Now = pd.DataFrame(indus_Peak2Now).dropna()
        indus_Peak2Now['Peak2Now_Rank'] = indus_Peak2Now['Peak2Sec_LastDays'].rank(ascending=False)
    elif now_secdate is not None:
        indus_Peak2Now = result_head[['PreNowPeak2Now_LastDays', 'industry']].groupby(
            'industry')['PreNowPeak2Now_LastDays'].mean().sort_values(ascending=False)
        indus_Peak2Now = pd.DataFrame(indus_Peak2Now).dropna()
        indus_Peak2Now['Peak2Now_Rank'] = indus_Peak2Now['PreNowPeak2Now_LastDays'].rank(ascending=False)
    indus_PGVMinRollAvg = result_head[['PostSecPeak_PGV_MinRollAvg2Now_LastDays', 'industry']].groupby(
        'industry')['PostSecPeak_PGV_MinRollAvg2Now_LastDays'].mean().sort_values(ascending=True)
    indus_PGVMinRollAvg = pd.DataFrame(indus_PGVMinRollAvg).dropna()
    indus_PGVMinRollAvg['MinRollAvg_Rank'] = indus_PGVMinRollAvg[
        'PostSecPeak_PGV_MinRollAvg2Now_LastDays'].rank(ascending=True)
    indus_Turnover2Change = result_head[['PreNowSec_Turnover2Change', 'industry']].groupby(
        'industry')['PreNowSec_Turnover2Change'].mean().sort_values(ascending=True)
    indus_Turnover2Change = pd.DataFrame(indus_Turnover2Change).dropna()
    indus_Turnover2Change['Turnover2Change_Rank'] = indus_Turnover2Change[
        'PreNowSec_Turnover2Change'].rank(ascending=True)
    indus_Sec2Peak_Diff = result_head[['Sec2Peak_PRV_Diff', 'industry']].groupby(
        'industry')['Sec2Peak_PRV_Diff'].mean().sort_values(ascending=True)
    indus_Sec2Peak_Diff = pd.DataFrame(indus_Sec2Peak_Diff).dropna()
    indus_Sec2Peak_Diff['Sec2Peak_Diff_Rank'] = indus_Sec2Peak_Diff[
        'Sec2Peak_PRV_Diff'].rank(ascending=True)
    indus_count = pd.merge(Bottom_List[['Indus_MV']], indus_SecDiff,
                           left_index=True, right_index=True, how='left')
    indus_count = pd.merge(indus_count, indus_Peak2Now,
                           left_index=True, right_index=True, how='left')
    indus_count = pd.merge(indus_count, indus_PGVMinRollAvg,
                           left_index=True, right_index=True, how='left')
    indus_count = pd.merge(indus_count, indus_Turnover2Change,
                           left_index=True, right_index=True, how='left')
    indus_count = pd.merge(indus_count, indus_Sec2Peak_Diff,
                           left_index=True, right_index=True, how='left')
    if indus_SecRatio is not None:
        indus_count = pd.merge(indus_count, indus_SecRatio,
                               left_index=True, right_index=True, how='left')
        indus_count['Score'] = indus_count['SecDiff_Rank'] + indus_count['Peak2Now_Rank'] + \
            indus_count['Sec2Peak_Diff_Rank'] +indus_count['SecRatio_Rank']
    else:
        indus_count['Score'] = indus_count['SecDiff_Rank'] + indus_count['Peak2Now_Rank'] + indus_count[
            'Sec2Peak_Diff_Rank']
    Bottom_List = pd.merge(Bottom_List, indus_count[['Score']], left_index=True, right_index=True, how='left')
    columns = list(Bottom_List)
    columns.insert(0, columns.pop(columns.index('Score')))
    Bottom_List = Bottom_List.loc[:, columns].sort_values(by='Score', ascending=True)
    # if lag_checkdate is not None:
    #     sw_data = get_swindex_data(start_date=end_date, end_date=lag_checkdate)
    #     sw_ratio = round((sw_data.loc[lag_checkdate, :] / sw_data.loc[end_date, :] - 1) * 100, 3)
    #     sw_ratio.name = 'sw_ratio'
    #     indus_count = pd.merge(indus_count, sw_ratio, left_index=True, right_index=True, how='left')
    #     indus_count['ratio_rank'] = indus_count['sw_ratio'].rank(ascending=False)
    result_indus_pick = result.query('SecConcave_LastDays>50 & '
                                     'SecConcave_RatioBand<30 & '
                                     'SecConcave_TO_Sum>90 & '
                                     'PreNowSec_COR_Und2Poxn>0.9').copy()
    plate_indus_count = result_indus_pick[['ts_code', 'industry']].groupby('industry')['ts_code'].count()
    plate_indus_count = plate_indus_count.sort_values(ascending=False)
    return indus_count.sort_values(by='Score', ascending=True), plate_indus_count


def pickstk_method_indus_head(end_date=None, now_secdate=None, industry=None, limit_num=30, sorted='totalmv'):
    """获取指定日期、指定行业的指标数据，依据MinRollAvg规则筛选品种"""
    if now_secdate is None:
        now_secdate = end_date
    if isinstance(industry, str):
        industry = [industry]
    trade_dates = get_trade_date()
    result_origin = get_result_3(end_date=end_date)
    if len(result_origin) == 0:
        print('股票数量为零，退出')
        return
    if industry is not None:
        result_data = result_origin.query('industry in @industry').copy().sort_values(
            by=['industry', 'Total_MV'], ascending=[True, False])
    else:
        result_data = result_origin.copy().sort_values(
            by=['industry', 'Total_MV'], ascending=[True, False])
    stock_data = get_stock_data(stk_code=result_data['ts_code'].to_list(),
                                start_date=result_data['Section_StartDate'].dropna().min(),
                                end_date=result_data['Cal_Date'].max())
    result_data['Now_SecDiff'] = result_data.apply(
        lambda fn: conf_nowdiff(fn['Now_SecDate'], now_secdate, trade_dates,
                                fn['ts_code'], stock_data=stock_data), axis=1)
    result_data['MV_Rank'] = result_data.groupby('industry')['Total_MV'].rank(ascending=False)
    # result_for_pick = result_data.groupby('industry').head(limit_num).copy()
    result_for_pick = result_data.copy()
    condition_1 = (result_for_pick['Now_SecDiff']<=2)
    condition_2 = (result_for_pick['PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio']<0.5)
    condition_3 = (result_for_pick['PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays']<10)
    condition_4 = (result_for_pick['PostSecPeak_PGV_MinRollAvg2MaxRatio']<0.5)
    condition_5 = (result_for_pick['PostSecPeak_PGV_MinRollAvg2Now_LastDays']<10)
    result_pick = result_for_pick[condition_1 & ((condition_2 & condition_3) | (condition_4 & condition_5))]
    result_pick = result_pick.groupby('industry').head(limit_num).copy()

    predict_data = get_predictdata_from_sql(start_date=end_date, end_date=end_date, industry=industry, limit_num=0)
    if len(predict_data) > 0:
        result_pick = pd.merge(result_pick, predict_data[['ts_code', 'Prop_Rank']], on='ts_code', how='left')
        if sorted.lower() == 'predict':
            result_pick = result_pick.query('Prop_Rank<500').sort_values(
                by=['industry', 'Prop_Rank'], ascending=[True, True])
    return result_pick, result_for_pick


def get_predictdata_from_sql(start_date=None, end_date=None, industry=None, limit_num=100):
    """从modelrf数据库获取指定日期区间的数据"""
    if start_date is None:
        trade_dates = get_trade_date()
        start_date = trade_dates[trade_dates<end_date][-10]
    if start_date < end_date:
        print('modelrf数据库取值范围：', start_date, '~', end_date)
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    if industry is None or len(industry) == 0:
        model_sql = f"""select * from stocksfit.stk_results_modelrf 
                        where Cal_Date between '{start_date}' and '{end_date}'"""
    else:
        if isinstance(industry, str):
            industry = [industry]
        sql_indus = ','.join(["'%s'" % item for item in industry])
        model_sql = f"""select * from stocksfit.stk_results_modelrf 
                        where Cal_Date between '{start_date}' and '{end_date}' and industry in ({sql_indus})"""
    model_data = pd.read_sql_query(model_sql, con=engine)
    if len(model_data) > 0:
        model_data = model_data[~model_data['name'].str.contains('ST') & ~model_data['Target_Price'].isnull()
                                ].copy().sort_values(
                        by=['Cal_Date', 'Prop_Rank'],
            ascending=[True, True])
        if limit_num > 0:
            if industry is None:
                model_data = model_data.groupby('Cal_Date').head(limit_num)
            else:
                model_data = model_data.groupby(['Cal_Date', 'industry']).head(limit_num)
        print('modelrf数据库提取品种数量：', len(model_data['ts_code'].unique()))
    else:
        print('modelrf数据库检索数据为空')
    return model_data


def get_gapvalue(stk_list=None, result=None, end_date=None):
    """获取gapvalue指标数据"""
    if isinstance(stk_list, str):
        stk_list = [stk_list]
    if result is None:
        result = get_result_3(end_date=end_date, stk_list=stk_list)
    else:
        result = result.query('ts_code in @stk_list')
    result_adj = result[['ts_code', 'name', 'industry',
                         'PostTurn_COR_Mean', 'PostTurn_COR_Und2Poxn',
                         'PostSecStart_MedianValleyGapValue',
                         'PostSecStart_ValleyGapValue_HighQuntl',
                         'PostSecStart_MaxValleyGapValue',
                         'PostSecMaxRollAvg_PGV_MinRollAvg',
                         'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays',
                         'PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio',
                         'PostSecStart_MedianPeakGapValue',
                         'PostSecStart_PeakGapValue_HighQuntl',
                         'PostSecStart_MaxPeakGapValue',
                         'PostSecStart_PGV_MaxRollAvg',
                         'Recent3Day_MaxValleyGapValue',
                         'Recent3Day_MaxPeakGapValue']]
    return result_adj.sort_values(by='PostTurn_COR_Und2Poxn', ascending=False)


def get_result_droprecov(end_date):
    """从数据库获取指定日期的数据"""
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    if isinstance(end_date, str):
        end_date = [end_date]
    sql_enddate = ','.join(["'%s'" % item for item in end_date])
    sql = f"""select * from stocksfit.stksfit_droprecov where Pick_Date in ({sql_enddate})"""
    result = pd.read_sql_query(sql, con=engine)
    return result


def check_indus_peak_state(end_date=None, indus_list=None, index_preturn_date=None):
    """检查指定行业是否出现上行波峰，并返回行业列表和相应指标"""
    if isinstance(indus_list, str):
        indus_list = [indus_list]
    _, Cumret_List, Rise_List = cal_swindex_state(end_date=end_date, index_preturn_date=index_preturn_date)
    Rise_List_Indus = Rise_List.query('indus in @indus_list').copy()
    Cumret_List_Indus = Cumret_List.query('indus in @indus_list').copy()
    start_date = Cumret_List_Indus['MinCumRet_Date'].min()
    stock_info = get_stock_info()
    stk_info = stock_info.query('industry in @indus_list')
    all_stockdata = get_stock_data(stk_code=stk_info['ts_code'].tolist(), 
                                   start_date=start_date, end_date=end_date)
    
    # 计算每个行业内所有股票由MinCumRet_Date至end_date的最大涨幅和最高收盘价日期
    industry_max_rise = []
    
    for industry in indus_list:
        industry_stocks = stk_info.query('industry == @industry')['ts_code'].tolist()
        industry_bottom_date = Cumret_List_Indus.query('indus == @industry')['MinCumRet_Date'].iloc[0]
        
        for stock_code in industry_stocks:
            stock_data = all_stockdata.query('ts_code == @stock_code').set_index('trade_date')
            if len(stock_data) == 0:
                continue
                
            # 获取从底部日期到end_date的数据
            stock_data_since_bottom = stock_data.loc[industry_bottom_date:].copy()
            if len(stock_data_since_bottom) <= 1:
                continue
                
            # 计算基准价格（底部日期的收盘价）
            base_price = stock_data_since_bottom.iloc[0]['close']
            
            # 计算每日涨幅
            stock_data_since_bottom['rise_ratio'] = (stock_data_since_bottom['close'] / base_price - 1) * 100
            
            # 获取最大涨幅和对应日期
            max_rise_date = stock_data_since_bottom['rise_ratio'].idxmax()
            max_rise = stock_data_since_bottom.loc[max_rise_date, 'rise_ratio']
            
            # 计算最高价日期距离end_date的天数
            days_from_peak = len(stock_data_since_bottom.loc[max_rise_date:]) - 1
            
            industry_max_rise.append({
                'industry': industry,
                'ts_code': stock_code,
                'stock_name': stock_info.query('ts_code == @stock_code')['name'].iloc[0],
                'total_mv': stock_info.query('ts_code == @stock_code')['total_mv'].iloc[0],
                'max_rise': round(max_rise, 2),
                'max_rise_date': max_rise_date,
                'days_from_peak': days_from_peak
            })
    
    # 创建DataFrame并按行业和最大涨幅排序
    industry_max_rise_df = pd.DataFrame(industry_max_rise)
    
    # 获取每个行业最大涨幅最大的5个股票
    risetop5_stocks_by_industry = industry_max_rise_df.sort_values(['industry', 'max_rise'], ascending=[True, False]) \
                                                 .groupby('industry').head(5)
    mvtop5_stocks_by_industry = top5_stocks_by_industry.sort_values(['industry', 'total_mv'], ascending=[True, False]) \
                                                 .groupby('industry').head(5)
    
    Rise_List_Indus = Rise_List_Indus.rename(columns={'start_date':'Rise_StartDate'})
    
    SW_Data = pd.merge(Cumret_List_Indus[['indus', 'End2Max_MaxDate', 'MinCumRet_Date', 'MinCumRet_SumRatio', 
                                          'Ret2Now_Ratio', 'MinCumRet_Seg_MaxRise_EndDate', 
                                          'MinCumRet_Seg_MaxRise_Sum', 'MinCumRet_Seg_MaxRise_Avg', 
                                          'MinCumRet_Seg_MaxRise_LastDays', 
                                          'MinCumRet_Seg_MaxRise_End_To_Now_Days']], 
                       Rise_List_Indus[['indus', 'Rise_StartDate', 'Rise_Lastdays',  
                                        'Rise_Seg_MaxRise_EndDate', 'Rise_Seg_MaxRise_Sum', 
                                        'Rise_Seg_MaxRise_Avg', 
                                        'Rise_Seg_MaxRise_LastDays', ]], 
                       on='indus', how='left')

    SW_Data = SW_Data.sort_values(by=['MinCumRet_Date', 'Ret2Now_Ratio'], ascending=[False, False])
    
    # 计算每个行业股票days_from_peak的均值
    industry_rise_avg_days_from_peak = risetop5_stocks_by_industry.groupby('industry')['days_from_peak'].mean().reset_index()
    industry_rise_avg_days_from_peak.rename(columns={'days_from_peak': 'Rise_Avg_Days_From_Peak'}, inplace=True)
    
    industry_mv_avg_days_from_peak = mvtop5_stocks_by_industry.groupby('industry')['days_from_peak'].mean().reset_index()
    industry_mv_avg_days_from_peak.rename(columns={'days_from_peak': 'MV_Avg_Days_From_Peak'}, inplace=True)
    # 将行业名称列从'industry'改为'indus'以便与SW_Data合并
    industry_rise_avg_days_from_peak.rename(columns={'industry': 'indus'}, inplace=True)
    industry_mv_avg_days_from_peak.rename(columns={'industry': 'indus'}, inplace=True)
    
    # 将计算结果添加到SW_Data
    SW_Data = pd.merge(SW_Data, industry_rise_avg_days_from_peak[['indus', 'Rise_Avg_Days_From_Peak']], 
                      on='indus', how='left')
    SW_Data = pd.merge(SW_Data, industry_mv_avg_days_from_peak[['indus', 'MV_Avg_Days_From_Peak']], 
                      on='indus', how='left')
    
    # 四舍五入保留两位小数
    if 'Rise_Avg_Days_From_Peak' in SW_Data.columns:
        SW_Data['Rise_Avg_Days_From_Peak'] = SW_Data['Rise_Avg_Days_From_Peak'].round(2)
    if 'MV_Avg_Days_From_Peak' in SW_Data.columns:
        SW_Data['MV_Avg_Days_From_Peak'] = SW_Data['MV_Avg_Days_From_Peak'].round(2)
    # 返回结果
    return SW_Data, mvtop5_stocks_by_industry
    
    
def check_indus_valley_state(end_date=None, indus_list=None, index_preturn_date=None):
    """检查指定行业是否出现下行波谷，并返回行业列表和相应指标"""
    if isinstance(indus_list, str):
        indus_list = [indus_list]
    _, Cumret_List, _ = cal_swindex_state(end_date=end_date, index_preturn_date=index_preturn_date)
    
    # 计算每个行业市值排名前10的股票
    result_store = get_result_3(end_date=end_date, industry=indus_list)
    if len(result_store) > 0:
        result_store = result_store.sort_values(by=['industry', 'Total_MV'], ascending=[True, False]).groupby('industry').head(10)
    
        Cumret_List = Cumret_List.rename(columns={'indus':'industry'})
        result_store = pd.merge(result_store, Cumret_List[['industry', 'MinCumRet_Date']], on='industry', how='left')
        
        stk_list = result_store['ts_code'].tolist()
        all_stockdata = get_stock_data(stk_code=stk_list, start_date=result_store['Section_StartDate'].min(), end_date=end_date)
        trade_dates = get_trade_date()
        
        result_store['CumRet_NowSecDiff'] = result_store.apply(
                lambda fn: conf_nowdiff(fn['Now_SecDate'], fn['MinCumRet_Date'], trade_dates),
                axis=1)
        result_store['CumRet_SecDiff'] = result_store.apply(
                lambda fn: conf_nowdiff(fn['Section_StartDate'], fn['MinCumRet_Date'], trade_dates),
                axis=1)
        result_store['PostCumRet_Ratio'] = None
        for index in result_store.index:
                stk_code = result_store.loc[index, 'ts_code']
                stk_data = all_stockdata.query('ts_code==@stk_code').copy()
                mincumret_date = result_store.loc[index, 'MinCumRet_Date']
                if len(stk_data) > 0 and pd.notnull(mincumret_date) and mincumret_date <= end_date:
                    stk_data = stk_data.sort_values(by='trade_date', ascending=True).set_index('trade_date')
                    result_store.loc[index, 'PostCumRet_Ratio'] = round(
                        (stk_data.loc[:end_date, 'close'].iloc[-1] / stk_data.loc[mincumret_date:end_date, 'close'].iloc[0] - 1) * 100, 3)
        result_store = result_store[['ts_code', 'name', 'industry', 'Total_MV', 
                                     'Section_StartDate', 'Now_SecDate', 'MinCumRet_Date',
                                    'CumRet_SecDiff', 'CumRet_NowSecDiff', 
                                    'PostCumRet_Ratio', 'Peak2Sec_LastDays', 
                                    'Peak2Sec_SumRatio', 'PreNowPeak2Now_LastDays', 
                                    'PreNowPeak2Now_SumRatio']]
    else:
        print(end_date, '股票指标数据为空')
        result_store = pd.DataFrame()
    return result_store, Cumret_List


def get_data_from_pullstart(end_date=None, class_type=None, industry_list=None):
    """从pullstart数据库获取指定日期区间的数据"""
    trade_dates = get_trade_date()
    start_date = trade_dates[trade_dates<end_date][-1]
    if class_type is None:
        class_type = ['PullStart', 'TurnBreak']
    if isinstance(class_type, str):
        class_type = [class_type]
    if isinstance(industry_list, str):
        industry_list = [industry_list]
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    class_type_sql = ','.join(["'%s'" % item for item in class_type])
    sql = f"""select * from stocksfit.stkpick_pullstart 
             where Check_Date between '{start_date}' and '{end_date}' and 
             Class_Type in ({class_type_sql})"""
    result = pd.read_sql_query(sql, con=engine)
    if industry_list is not None:
        result = result.query('industry in @industry_list')
    result_latest = result.query('Check_Date==@end_date')
    result_earlier = result.query('Check_Date==@start_date')
    if len(result_earlier) > 0:
        result_latest = pd.merge(result_latest, result_earlier[['ts_code', 'sort_position']], 
                                 on='ts_code', how='left', suffixes=('', '_earlier'))
        result_latest['sort_position_diff'] = result_latest['sort_position_earlier'] - result_latest['sort_position']
        result_latest = result_latest.sort_values(by=['sort_position_diff', 'sort_position'], ascending=[False, True])
        col = ['sort_position_diff', 'sort_position']
        cols = col + [i for i in result_latest.columns if i not in [col, 'sort_position_earlier']]
        result_latest = result_latest[cols]
    return result_latest
    
    
    

if __name__ == '__main__':
    # 筛选逻辑
    # indus_count, Bottom_List = induspick_turn_state(end_date='2024-08-28', now_secdate='2024-08-28')
    
    # SW_Data, top5_stocks_by_industry = check_indus_peak_state(end_date='2025-02-26', indus_list=['电子','机械设备','计算机','传媒'], index_preturn_date='2025-02-05')
    
    result_store = get_result_3(end_date='2025-06-20', mode='all', stk_list=['600104.SH', '600036.SH'])
    
    # indus_count, indus_result = induspick_turn_state(end_date='2025-02-06', now_secdate='2025-01-24', limit_num=30)

    # result_pick, result_head = pickstk_method_indus_head(end_date='2024-09-19', industry=['农林牧渔', '食品饮料'])

    # result = get_result_3(end_date='2024-01-29')
    # result_temp = result.query(
    #     'Section_StartDate=="2024-01-26" & PreSec_Turnover2Drop_State>0 & Section_StartDate==Now_SecDate & '
    #     'Section_StartDate>Period_TurnDate & Now_DayRatio>0')

    # pick_date = '2024-05-24'
    # result = get_result_3(end_date=pick_date)
    # result_adj = result.query(
    #     'PreNowSec_MaxValleyGapValue>PostSecStart_ValleyGapValue_HighQuntl & '
    #     'Now_ValleyGapValue>PostSecStart_MedianValleyGapValue & '
    #     'Now_SecDate==@pick_date & PreNowSec_LastDays>5 & PostPreNowSec_Over_HighQuntl_Num>=2')
    # result_adj = result_adj.sort_values(by='PreSecPeak_Sec_SumRatio', ascending=True)
    # result_adj2 = result_adj[(result_adj['PreSecPeak_Sec_SumRatio'] > result_adj['PostSecStart_MaxDrop'].abs())]
    # industry = ['钢铁', '农林牧渔']
    # result_adj2_indus = result_adj2.query('industry in @industry')
    #
    # pick_date = '2024-05-08'
    # industry2 = '商贸零售'
    # result = get_result_3(end_date=pick_date)
    # result_adj_indus = result.query(
    #     'industry==@industry2 & PostSecStart_MedianPeakGap>2').sort_values(by='Total_MV', ascending=False)
    # result_adj_indus_dropavg = result_adj_indus.sort_values(by='PreNowSec_AvgRatio', ascending=True)

    # import pinyin as py
    # now_secdate, end_date = '2024-06-25', '2024-07-04'
    # industry = ['医药生物']
    # output = pickstk_method_indus_head(end_date=end_date, now_secdate=now_secdate, industry=industry)
    #
    # # 强势行业确认，转折点股票筛选
    # industry = ['通信', '电力设备', '机械设备', '汽车', '国防军工', '传媒', '计算机',
    #             '综合', '纺织服饰', '商贸零售', '建筑装饰']
    # start_date, end_date = '2024-06-07', '2024-06-14'
    # num = 30
    # # result, result_pick = get_indusresult_sort_totalmv(start_date=start_date, end_date=start_date,
    # #                                                    industry=industry)
    # result = get_result_3(end_date=start_date, industry=industry
    #                       ).sort_values(by=['industry', 'Total_MV'], ascending=[True, False])
    # result_adj = result.groupby('industry').head(num)
    # ts_codes = result.groupby('industry').head(num)['ts_code'].tolist()
    # result_compare, result_compare_adj = stk2idx_compare(stk_list=ts_codes, start_date=start_date,
    #                                                      end_date=end_date)
    #
    # # Now_SecDate转折点筛选
    # result_adj = result.query(
    #     'Recent3DValley_Over_HighQuntl_Num>=1 & Now_SecDate=="2024-07-17" & '
    #     'Recent3Day_MaxValleyGapValue==PostSecPeak_MaxValleyGapValue & '
    #     'PostSecPeak_Over_HighQuntl_Num>1 & PreNowSec_LastDays>3')
    # result_adj_indus = result_adj.query('industry in ["电子", "通信", "汽车", "国防军工", "医药生物", "食品饮料"]')
    # result_adj_indus = result_adj_indus.sort_values(by=['industry', 'PostTurn_Over7Num'], ascending=[True, False])
    #
    # # 筛选近期强势品种
    # end_date = '2024-04-26'
    # result = get_result_3(end_date=end_date)
    # result_adj = result.query('PostNowSec_LastDays<=10 & PostNowSec_Over7Num>=2'
    #                           )[['ts_code', 'name', 'industry', 'Now_SecDate',
    #                              'PostNowSec_Adverse2Trend', 'PostSecStart_MedianPeakGap']]
    #
    # # 筛选行业内强势品种
    # end_date = '2024-08-05'
    # result = get_result_3(end_date=end_date)
    # result_indus = result.query('industry in ["石油石化", "煤炭"]')
    # result_indus_pick = result_indus.query(
    #     'NNow_PostSecPeak_PGV_RollAvg_MinCoverDays<Now_PostSecPeak_VGV_MaxCoverDays & Now_SecDate==@end_date')
    # result_indus_pick = result_indus_pick.sort_values(by='PostSecPeak_VGV_MinRollAvg2MaxRatio', ascending=True)



