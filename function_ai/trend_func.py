# 趋势识别函数汇总
# peak_valley_turn  股价波峰波谷识别函数
# trend_judge 依据波峰波谷序列，识别市场趋势区间并测算区间目标价格
# period_stat 波峰波谷区间走势特征识别函数
# import numba
import numpy as np
import pandas as pd
import pdb
# from numba import jit


def peak_valley_turn_cal(stk_close, trade_date, trig_ratio):
    """ 函数peak_valley_turn  判定价格运行的波峰波谷点，并输出按照日期序列排列的数据识别信号
         波谷位置为Thr，波峰位置为Cr
    输入参数stk_close为股票收盘价，trade_date为对应日期序列，stk_code为股票代码，trig_ratio为股价变化识别阀值
    输入参数stk_close和trade_date数据格式均为np.ndarray"""
    # 设定初始波峰波谷价格及波峰波谷记录
    peak_record_date = trade_date[0]
    peak_record_close = stk_close[0]
    valley_record_date = trade_date[0]
    valley_record_close = stk_close[0]
    pv_turn = np.array([np.nan, np.nan])
    pv_date = np.array([trade_date[0]])
    # 记录最新波峰波谷日期及数值，第二列为波谷记录，第三列为波峰记录
    # 按照stk_close循环，记录波峰波谷位置pv_turn
    for k in range(3, len(stk_close)):
        if stk_close[k] > max(stk_close[k - 3:k - 1]):
            if stk_close[k] > peak_record_close:
                peak_record_date = trade_date[k]
                peak_record_close = stk_close[k]
            if stk_close[k] / valley_record_close - 1 >= trig_ratio and (
                    pd.isnull(pv_turn).all() or pd.isnull(pv_turn[-2])):
                pv_turn = np.append(pv_turn, [valley_record_close, np.nan])
                pv_date = np.append(pv_date, valley_record_date)
                valley_record_date = trade_date[k]
                valley_record_close = stk_close[k]
                peak_record_date = trade_date[k]
                peak_record_close = stk_close[k]
        else:
            if stk_close[k] < valley_record_close:
                valley_record_date = trade_date[k]
                valley_record_close = stk_close[k]
            if -(stk_close[k] / peak_record_close - 1) >= trig_ratio and (
                    pd.isnull(pv_turn).all() or pd.isnull(pv_turn[-1])):
                pv_turn = np.append(pv_turn, [np.nan, peak_record_close])
                pv_date = np.append(pv_date, peak_record_date)
                valley_record_date = trade_date[k]
                valley_record_close = stk_close[k]
                peak_record_date = trade_date[k]
                peak_record_close = stk_close[k]
    # 设定波峰波谷序列结尾状态
    if pd.notnull(pv_turn[-2:]).any() and pd.isnull(pv_turn[-2]) and valley_record_date > pv_date[-1]:
        pv_turn = np.append(pv_turn, [valley_record_close, np.nan])
        pv_date = np.append(pv_date, valley_record_date)
        if valley_record_date < peak_record_date:
            pv_turn = np.append(pv_turn, [np.nan, peak_record_close])
            pv_date = np.append(pv_date, peak_record_date)
            if trade_date[-1] > peak_record_date:
                pv_turn = np.append(pv_turn, [stk_close[-1], np.nan])
                pv_date = np.append(pv_date, trade_date[-1])
        elif trade_date[-1] > valley_record_date:
            if stk_close[-1] > pv_turn[-2]:
                pv_turn[-2] = stk_close[-1]
                pv_date[-1] = trade_date[-1]
            else:
                pv_turn = np.append(pv_turn, [np.nan, stk_close[-1]])
                pv_date = np.append(pv_date, trade_date[-1])
    elif pd.notnull(pv_turn[-2:]).any() and pd.isnull(pv_turn[-1]) and peak_record_date > pv_date[-1]:
        pv_turn = np.append(pv_turn, [np.nan, peak_record_close])
        pv_date = np.append(pv_date, peak_record_date)
        if valley_record_date > peak_record_date:
            pv_turn = np.append(pv_turn, [valley_record_close, np.nan])
            pv_date = np.append(pv_date, valley_record_date)
            if trade_date[-1] > valley_record_date:
                pv_turn = np.append(pv_turn, [np.nan, stk_close[-1]])
                pv_date = np.append(pv_date, trade_date[-1])
        elif trade_date[-1] > peak_record_date:
            if stk_close[-1] < pv_turn[-1]:
                pv_turn[-1] = stk_close[-1]
                pv_date[-1] = trade_date[-1]
            else:
                pv_turn = np.append(pv_turn, [stk_close[-1], np.nan])
                pv_date = np.append(pv_date, trade_date[-1])
    return pv_turn, pv_date


def peak_valley_turn(stk_close, trade_date, mode='Stock'):
    """调用jit计算程序完成波峰波谷计算"""
    if isinstance(stk_close, pd.Series):
        stk_close = stk_close.values
    if len(stk_close) < 50:
        # print(str(stk_code) + "价格数据不足50天，无法完成波峰波谷测算")
        return []
    if mode == 'Stock':
        trig_ratio = 0.08
    elif mode == 'Index':
        trig_ratio = 0.02
    else:
        return []
    pv_turn, pv_date = peak_valley_turn_cal(stk_close, trade_date, trig_ratio)
    # 剔除无波峰波谷记录的行
    pv_turn = pd.DataFrame({'date': pv_date, 'valley': pv_turn[::2], 'peak': pv_turn[1::2]},
                           index=range(0, len(pv_date)))
    pv_turn = pv_turn.drop(pv_turn.index[0])
    return pv_turn


def cal_trend_target(turn_value, crthr_value, right_stand, state_style):
    """ 函数cal_trend_target 用于计算上行或下行趋势目标价格
        依据转折点左侧压力价位与转折点价格的价差，进行测算
        输入参数：turn_value转折下轨价格，crthr_value左侧波峰波谷价格,right_stand转折点右侧最高/最低收盘价
                state_style为设定参数，'上行'表示测算上行目标价格，'下行表示测算下行目标价格'"""
    # if isinstance(crthr_value, pd.Series):
    #    crthr_value = crthr_value.values
    if state_style == '上行':
        crthr_value = np.sort(crthr_value)
        if right_stand * 0.9 < crthr_value[0] * 2 - turn_value:
            return crthr_value[0] * 2 - turn_value
        elif right_stand * 0.9 < crthr_value[0] * 3 - turn_value * 2:
            return crthr_value[0] * 3 - turn_value * 2
        else:
            subcount = 0
            while subcount < len(crthr_value) and right_stand * 0.9 > crthr_value[
                subcount] * 2 - turn_value:
                subcount += 1
            if subcount != len(crthr_value):
                return crthr_value[subcount - 1] * 2 - turn_value
            else:
                return max(crthr_value) * 3 - turn_value * 2
    elif state_style == '下行':
        crthr_value = np.sort(crthr_value)[::-1]
        multiple_value = [0.5, 1, 2]
        tagt_price = crthr_value[0] - np.dot(multiple_value, turn_value - crthr_value[0])
        if len(tagt_price[(tagt_price > 0) & (tagt_price < right_stand * 1.1)]) != 0:
            return tagt_price[(tagt_price > 0) & (tagt_price < right_stand * 1.1)][0]
        else:
            subcount = 0
            while subcount < len(crthr_value) \
                    and all(crthr_value[subcount] -
                            np.dot(multiple_value, turn_value - crthr_value[subcount]) > right_stand * 1.1):
                subcount += 1
            tagt_price = crthr_value[subcount - 1] - np.dot(multiple_value, turn_value - crthr_value[subcount - 1])
            tagt_price = tagt_price[(tagt_price <= right_stand * 1.1) & (tagt_price > 0)]
            if len(tagt_price) != 0:
                return tagt_price[0]
            else:
                return crthr_value.min() * 2 - turn_value
    else:
        print('趋势状态输入错误，需设定上行/下行')
        return


def process_rise(trend_state, crthr_turn, stk_close, peak_value, loop_date):
    """上行测算目标价格，数据预处理"""
    """输入参数trend_state趋势状态，crthr_turn波峰波谷序列， stk_close收盘价序列， 
    peak_value波峰值， loop_date循环日期"""
    trend_ls_num = trend_state['long_state'].apply(lambda x: 1 if x == '下行' else 3)
    trend_turn = trend_state[trend_ls_num.diff() == -2]  # 定位长趋势由上行转为下行的转折点
    if len(trend_turn) != 0:
        count = 1
        while count < len(trend_turn) \
                and stk_close[trend_turn.loc[trend_turn.index[-count], 'start_date']] < peak_value[1]:
            count += 1
        if count != len(trend_turn):
            turn_date = trend_turn.loc[trend_turn.index[-count], 'start_date']
        else:
            max_loc = np.argmax(stk_close[trend_turn['start_date']].values)
            turn_date = trend_turn['start_date'].iloc[max_loc]
    else:
        trend_turn = trend_state.query("long_state == '下行'")
        if len(trend_turn) != 0:
            turn_date = trend_turn.loc[trend_turn.index[-1], 'ls_start_date']
        else:
            max_loc = np.argmax(stk_close[trend_state['start_date']].values)
            turn_date = trend_state['start_date'].iloc[max_loc]
    valley_list = crthr_turn.loc[turn_date: loop_date, 'valley'].dropna(how='all')
    if len(valley_list) != 0:
        valley_rail_value = np.min(valley_list.values)
        rise_start_date = valley_list.index[np.argmin(valley_list.values)]
    else:
        valley_rail_value = np.min(crthr_turn.loc[:loop_date, 'valley'].values)
        min_loc = np.argmin(crthr_turn.loc[:loop_date, 'valley'].values)
        rise_start_date = crthr_turn.index[min_loc]
    peak_rail_value = peak_value
    """ 计算上行起始日期左侧最低波谷值min_valley_left_value，及左侧波峰序列收盘价peak_left_close """
    trend_state_left = trend_state.query("start_date >= @turn_date and end_date <= @rise_start_date")
    if len(trend_state_left) > 1:
        peak_left_close = \
            crthr_turn.loc[trend_state_left.query("state != '上行'")
                           ['start_date'].values, 'peak'].dropna(how='all').values
        valley_left_close = stk_close[trend_state_left.query("state != '上行'")['end_date'].values].values
        if len(peak_left_close) == 0:
            peak_left_close = crthr_turn.loc[turn_date: rise_start_date, 'peak'].dropna(how='all').values
        if len(valley_left_close) != 0:
            min_valley_left_value = np.min(valley_left_close)
        else:
            min_valley_left_value = np.min(crthr_turn.loc[turn_date: rise_start_date, 'valley'].values)
    else:
        peak_left_close = crthr_turn.loc[turn_date: rise_start_date, 'peak'].dropna(how='all').values
        valley_left_close = crthr_turn.loc[turn_date: rise_start_date, 'valley'].dropna(how='all').values
        if len(valley_left_close) != 0:
            min_valley_left_value = np.min(valley_left_close)
        else:
            min_valley_left_value = np.min(stk_close[turn_date:rise_start_date].values)
    return {'valley_rail_value': valley_rail_value, 'peak_left_close': peak_left_close,
            'min_valley_left_value': min_valley_left_value, 'rise_start_date': rise_start_date}


def process_drop(trend_state, crthr_turn, stk_close, loop_date):
    """下行趋势，测算目标价位数据处理"""
    """输入参数trend_state趋势状态, crthr_turn波峰波谷状态, stk_close收盘价格, loop_date循环日期"""
    drop_start_date = trend_state.loc[trend_state.index[-1], 'ls_start_date']
    peak_rail_value = stk_close[drop_start_date]
    turn_dates = trend_state.query("ls_start_date != @drop_start_date")['ls_start_date'].values
    if len(turn_dates) != 0:
        turn_date = turn_dates[-1]
    else:
        turn_date = trend_state.loc[trend_state.index[-1], 'ls_start_date']
    if turn_date != drop_start_date:
        valley_left_close = crthr_turn.loc[turn_date: drop_start_date, 'valley'].dropna(how='all').values
    else:
        valley_left_close = crthr_turn.loc[:loop_date, 'valley'].dropna(how='all').values
    return {'valley_left_close': valley_left_close, 'peak_rail_value': peak_rail_value,
            'drop_start_date': drop_start_date}


def trend_judge(stk_close, crthr_turn):
    """函数 trend_judge 长趋势判定及目标价测算
       依据波峰波谷序列数据，判断价格所处趋势状态，并计算每个趋势区间的目标价格，输出测算结果
       输入参数：stk_close收盘价格，trade_date交易日期序列，crthr_turn波峰波谷数据序列
       输出trend_state列表项包括：【判定日期、阶段趋势状态、阶段起始日期、阶段结束日期、阶段目标价格、长趋势状态、长趋势起始日期】
       """
    if len(crthr_turn) < 2:
        # print(stock_code + ': 数据日期' + trade_date[0] + '~' + trade_date[-1] + ', 波峰波谷序列数据不足')
        return []
    trade_date = stk_close.index
    if crthr_turn.index[0] == 0 or crthr_turn.index[0] == 1:
        crthr_turn = crthr_turn.set_index(['date'])
    """ 初始化趋势判定序列dataframe，新建首条趋势状态 """
    if pd.notnull(crthr_turn.iloc[0, 1]):
        trend_state = pd.DataFrame({'judge_date': crthr_turn.index[1],
                                    'state': '下行',
                                    'start_date': crthr_turn.index[0],
                                    'end_date': crthr_turn.index[1],
                                    'target_price': crthr_turn.loc[crthr_turn.index[1], 'valley'],
                                    'long_state': '下行',
                                    'ls_start_date': crthr_turn.index[0],
                                    }, index=[0])
        peak_value = [crthr_turn.index[0], crthr_turn.iloc[:2]['peak'].max()]  # 设定初始波峰值
        valley_value = [crthr_turn.index[1], crthr_turn.iloc[:2]['valley'].max()]  # 设定初始波谷值
    else:
        trend_state = pd.DataFrame({'judge_date': crthr_turn.index[1],
                                    'state': '上行',
                                    'start_date': crthr_turn.index[0],
                                    'end_date': crthr_turn.index[1],
                                    'target_price': crthr_turn.loc[crthr_turn.index[1], 'peak'],
                                    'long_state': '上行',
                                    'ls_start_date': crthr_turn.index[0],
                                    }, index=[0])
        peak_value = [crthr_turn.index[1], crthr_turn.iloc[:2]['peak'].max()]  # 设定初始波峰值
        valley_value = [crthr_turn.index[0], crthr_turn.iloc[:2]['valley'].max()]  # 设定初始波谷值
    # 计算横盘上下轨价格horizon_band
    horizon_band = [valley_value[1], peak_value[1]]  # 设定横盘区间上下轨

    """ 启动循环，按照波峰波谷序列，逐个转折点的相对关系判断趋势状态 """
    for loop_date in crthr_turn.index[2:]:
        """ 以下if-else语句，目的在于定位当前上行/下行趋势起始日期，以及趋势转折点日期"""
        """ 如now当前趋势状态为下行，或者，now趋势状态为横盘且now-1趋势为下行"""
        if trend_state.loc[trend_state.index[-1], 'state'] == '下行' \
                or (len(trend_state) >= 2
                    and trend_state.loc[trend_state.index[-1], 'state'] == '横盘'
                    and trend_state.loc[trend_state.index[-2], 'state'] == '下行'):
            # 设定长趋势下行为1，上行为3，计算上行转为下行的转折点
            dataprocess = process_rise(trend_state, crthr_turn, stk_close, peak_value, loop_date)
            peak_left_close = dataprocess['peak_left_close']
            valley_rail_value = dataprocess['valley_rail_value']
            min_valley_left_value = dataprocess['min_valley_left_value']
            rise_start_date = dataprocess['rise_start_date']

            """ now如当前趋势为上行，或now-1趋势为上行且now趋势为横盘 """
        elif trend_state.loc[trend_state.index[-1], 'state'] == '上行' \
                or (len(trend_state) >= 2
                    and trend_state.loc[trend_state.index[-1], 'state'] == '横盘'
                    and trend_state.loc[trend_state.index[-2], 'state'] == '上行'):
            """ 如当前长趋势为上行，则长趋势起始日期作为当前趋势起始日期turn_date，
                否则，使用最近长上行趋势起始日期（如有）或当前趋势起始日期turn_date"""
            turn_dates = trend_state.query("long_state == '上行'")['ls_start_date'].values
            if len(turn_dates) != 0:
                turn_date = turn_dates[-1]
            else:
                turn_date = trend_state.loc[trend_state.index[0], 'start_date']
            """ 测算获得波峰上轨值peak_rail_value，及转折下行趋势起始日期drop_start_date。
                下行转折日期左侧的波峰价格序列peak_left_close,左侧波谷价格序列valley_left_close和最大波峰值max_peak_left_value """
            peak_rail_value = np.max(crthr_turn.loc[turn_date: loop_date, 'peak'].values)
            valley_rail_value = valley_value[1]
            arr1 = crthr_turn.loc[turn_date:loop_date, 'peak'].tolist()
            drop_start_date = crthr_turn[turn_date:loop_date].index[np.argmax(arr1)]
            rise_start_date = drop_start_date
            peak_left_close = crthr_turn.loc[turn_date:drop_start_date, 'peak'].dropna(how='all').values
            if turn_date != drop_start_date:
                valley_left_close = crthr_turn.loc[turn_date:drop_start_date, 'valley'].dropna(how='all').values
            else:
                valley_left_close = crthr_turn.loc[:loop_date, 'valley'].dropna(how='all').values
            min_valley_left_value = min(valley_left_close)
        # else:
        #    print('出现预期意外判定结果')
        #    continue

        # if loop_date=='2020-12-30':
        #     pdb.set_trace()

        """ !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!启动趋势判定!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"""
        # 上下轨区间价格差horizon_width，波峰波谷值均值peak_valley_mean
        horizon_half_width = (horizon_band[1] - horizon_band[0]) / 2
        peak_valley_mean = (peak_value[1] + valley_value[1]) / 2
        """~~~~~~~~~~~~~~~~~~~~~~~~新增上行趋势阶段，计算目标价格~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"""
        if (trend_state.loc[trend_state.index[-1], 'state'] == '横盘'
            and trend_state.loc[trend_state.index[-2], 'state'] == '下行'
            and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                  and crthr_turn.loc[loop_date, 'peak'] > horizon_band[1] + horizon_half_width
                 ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                       and crthr_turn.loc[loop_date, 'valley'] > horizon_band[1]))) \
                or (trend_state.loc[trend_state.index[-1], 'state'] == '下行'
                    and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                          and crthr_turn.loc[loop_date, 'peak'] > max(peak_value[1], min_valley_left_value)
                         ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                               and crthr_turn.loc[loop_date, 'valley'] > peak_valley_mean))):
            """判定条件：（1）now趋势为横盘，now-1趋势为下行，
                          当前为波峰，波峰值高于横盘上轨价格+横盘宽度
                          或者 当前为波谷，波谷值高于横盘上轨价格
                       （2）now趋势为下行，
                          当前为波峰，波峰值高于最小波谷值与波峰值的较大值
                          或者，当前为波谷，波谷值高于波峰波谷值均值"""
            # 更新趋势序列trend_state最新趋势结束日期
            if valley_value[0] > trend_state.loc[trend_state.index[-1], 'start_date']:
                trend_state.loc[trend_state.index[-1], 'end_date'] = valley_value[0]
            else:
                trend_state.loc[trend_state.index[-1], 'end_date'] = loop_date
            # 更新波峰和波谷值
            if pd.notnull(crthr_turn.loc[loop_date, 'peak']):
                peak_value = [loop_date, crthr_turn.loc[loop_date, 'peak']]
            else:
                valley_value = [loop_date, crthr_turn.loc[loop_date, 'valley']]

            """ 调用子函数cal_trend_target 计算目标价位"""
            if len(peak_left_close) == 0:
                peak_left_close = crthr_turn.loc[turn_date: loop_date, 'peak'].dropna(how='all').values
                if len(peak_left_close) == 0:
                    continue
            # pdb.set_trace()
            right_max_close = np.max(stk_close[rise_start_date:loop_date].values)
            target_price = cal_trend_target(valley_rail_value, peak_left_close, right_max_close, '上行')

            """ 新增一条趋势判定数据trend_state"""
            trend_state.loc[len(trend_state)] = {'judge_date': loop_date,
                                                 'state': '上行',
                                                 'start_date': trend_state.loc[trend_state.index[-1], 'end_date'],
                                                 'end_date': loop_date,
                                                 'target_price': target_price,
                                                 'long_state': trend_state.loc[trend_state.index[-1], 'long_state'],
                                                 'ls_start_date': trend_state.loc[
                                                     trend_state.index[-1], 'ls_start_date']}
            # trend_state = trend_state.append(trend_update, ignore_index=True)

            """ 回溯调整：如当前趋势结束价格超过前一下行趋势起始价格，且长趋势状态为下行
                         则需调整长趋势状态为上行，回溯定位波谷最低对应日期作为长趋势上行状态的起始日期 """
            if trend_state.loc[trend_state.index[-1], 'long_state'] == '下行' \
                    and peak_value[1] > np.max(stk_close[trend_state.loc[
                                                         trend_state.index[-2]:, 'start_date'].values].values):
                trend_ls_sdate = trend_state.loc[trend_state.index[-1], 'ls_start_date']
                valley_temp = crthr_turn.query("index >= @trend_ls_sdate and index <= @loop_date")[
                    'valley'].dropna(how='all')
                new_turn_date = valley_temp.idxmin()
                trend_need_change = trend_state.query("start_date >= @new_turn_date").index
                if len(trend_need_change) != 0:
                    trend_state.loc[trend_need_change, 'long_state'] = '上行'
                    trend_state.loc[trend_need_change, 'ls_start_date'] = new_turn_date
            """ 当前长趋势为上行，如当前趋势满足相关条件，则抹去前一下行长趋势
                定位当前上行趋势now的起始点rise_trend，回溯前一下行趋势now-1起始点pre_drop_trend，再前溯至now-2上行趋势的起点
                pre_pre_rise_start_close
                判定条件：（1）如now-2上行起始价格低于now-1下行结束价格，
                        （2）now-1下行起始价格低于当前波峰值，
                        （3）now-1下行趋势持续周期低于80天，
                        （4）now-1下行趋势跌幅低于40%
                符合条件，则将now-1长趋势状态改为上行，且起始日期更改至now-2上行的起始日期"""
            trend_ls_sdate = trend_state.loc[trend_state.index[-1], 'ls_start_date']
            rise_trend = trend_state.query("start_date == @trend_ls_sdate and state != '下行'")
            if len(rise_trend) != 0 \
                    and len(trend_state) != 0 \
                    and trend_state.loc[trend_state.index[-1], 'long_state'] == '上行':
                # judge_start_date = rise_trend['start_date'].iloc[0]
                pre_drop_trend = trend_state.query('start_date < @trend_ls_sdate & long_state=="下行"')
                if len(pre_drop_trend) != 0:
                    pre_drop_endlsdate = pre_drop_trend['ls_start_date'].iloc[-1]
                    pre_drop_trend = pre_drop_trend.query("start_date == @pre_drop_endlsdate and state == '下行'")
                    if len(pre_drop_trend) != 0 and pre_drop_trend.index[0] > 0 and \
                            (pd.to_datetime(stk_close[pre_drop_endlsdate:trend_state['end_date'].iloc[-1]].idxmin()) -
                             pd.to_datetime(pre_drop_endlsdate)).days < 80 and \
                            abs(stk_close[pre_drop_endlsdate] /
                                stk_close[pre_drop_endlsdate:trend_state['end_date'].iloc[-1]].min() - 1) < 0.4:
                        # abs(stk_close[pre_drop_endlsdate] / stk_close[rise_trend['start_date'].iloc[0]] - 1) < 0.4:
                        pre_pre_rise_start_close = stk_close[trend_state.loc[pre_drop_trend.index[0] - 1,
                                                                            'ls_start_date']]
                        pre_drop_start_close = stk_close[pre_drop_trend.loc[
                            pre_drop_trend.index[0], 'ls_start_date']]
                        pre_drop_end_close = stk_close[trend_state.loc[trend_state.index[-1], 'ls_start_date']]
                        if pre_pre_rise_start_close < pre_drop_end_close and pre_drop_start_close < peak_value[1]:
                            trend_state.loc[pre_drop_trend.index[0]:, 'long_state'] = '上行'
                            trend_state.loc[pre_drop_trend.index[0]:, 'ls_start_date'] = \
                                trend_state.loc[pre_drop_trend.index[0] - 1, 'ls_start_date']

            """~~~~~~~~~~~~~~~~~~~~~~~~~接续上行趋势阶段，如突破目标价，则更新目标价格~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"""
        elif trend_state.loc[trend_state.index[-1], 'state'] == '横盘' \
                and trend_state.loc[trend_state.index[-2], 'state'] == '上行' \
                and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                      and crthr_turn.loc[loop_date, 'peak'] > horizon_band[1] + horizon_half_width
                     ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                           and crthr_turn.loc[loop_date, 'valley'] > horizon_band[1])):
            """判定条件：（1）now趋势为横盘，now-1趋势为上行，
                      （2）当前为波峰，波峰值高于横盘上轨价格+横盘宽度
                         或者 当前为波谷，波谷值高于横盘上轨价格  """
            """ 更新波峰波谷值，如当前T为波峰，则更改前一横盘趋势结束日期为T-1波谷对应日期，如当前T为波谷，则更改前一横盘趋势结束日期为
                T-2波谷对应日期 """
            if pd.notnull(crthr_turn.loc[loop_date, 'peak']):
                peak_value = [loop_date, crthr_turn.loc[loop_date, 'peak']]
                trend_state.loc[trend_state.index[-1], 'end_date'] = crthr_turn[:loop_date].index[-2]
            else:
                valley_value = [loop_date, crthr_turn.loc[loop_date, 'valley']]
                trend_state.loc[trend_state.index[-1], 'end_date'] = crthr_turn[:loop_date].index[-3]
            """ 如当前长趋势为下行，则更改为上行，起始日期更改为now-1趋势"""
            if trend_state.loc[trend_state.index[-1], 'long_state'] != '上行':
                trend_state.loc[trend_state.index[-2]:, 'long_state'] = '上行'
                trend_state.loc[trend_state.index[-2]:, 'ls_start_date'] = \
                    trend_state.loc[trend_state.index[-2], 'start_date']
            """ 更新趋势条目"""
            trend_state.loc[len(trend_state)] = {'judge_date': loop_date,
                                                 'state': '上行',
                                                 'start_date': trend_state.loc[trend_state.index[-1], 'end_date'],
                                                 'end_date': loop_date,
                                                 'target_price': trend_state.loc[trend_state.index[-2], 'target_price'],
                                                 'long_state': trend_state.loc[trend_state.index[-2], 'long_state'],
                                                 'ls_start_date': trend_state.loc[
                                                     trend_state.index[-2], 'ls_start_date']}
            # trend_state = trend_state.append(trend_update, ignore_index=True)
            if peak_value[1] * 0.9 > trend_state.loc[trend_state.index[-1], 'target_price']:
                """ 更新目标价：
                    定位长趋势上行转下行转折点集合trend_turn，据此定位上行转下行的转折点turn_date
                    """
                dataprocess = process_rise(trend_state, crthr_turn, stk_close, peak_value, loop_date)
                peak_left_close = dataprocess['peak_left_close']
                valley_rail_value = dataprocess['valley_rail_value']
                rise_start_date = dataprocess['rise_start_date']
                if len(peak_left_close) != 0:
                    right_max_close = np.max(stk_close[rise_start_date:loop_date].values)
                    target_price = cal_trend_target(valley_rail_value, peak_left_close, right_max_close, '上行')
                else:
                    continue
                trend_state.loc[trend_state.index[-1], 'target_price'] = target_price

            """ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~添加横盘趋势~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"""
        elif (trend_state.loc[trend_state.index[-1], 'state'] == '上行'
              and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                    and peak_value[1] >= crthr_turn.loc[loop_date, 'peak'] > peak_valley_mean
                   ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                         and valley_value[1] >= crthr_turn.loc[loop_date, 'valley'] >=
                         valley_value[1] - (peak_value[1] - valley_value[1]) / 2))
        ) or (trend_state.loc[trend_state.index[-1], 'state'] == '下行'
              and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                    and peak_value[1] + (peak_value[1] - valley_value[1]) / 2 >
                    crthr_turn.loc[loop_date, 'peak'] > peak_value[1]
                   ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                         and peak_valley_mean > crthr_turn.loc[loop_date, 'valley'] > valley_value[1]))):
            """判定条件：（1）now趋势为上行，
                                当前拐点为波峰，价格高于波峰波谷均值，小于波峰值
                                或者 当前拐点为波谷，价格低于波谷值，高于波谷值-1/2波峰波谷区间
                        (2）now趋势为下行，
                                当前拐点为波峰，价格高于波峰值，小于波峰值+1/2波峰波谷区间
                                或者，当前拐点为波谷，价格高于波谷值，低于波峰波谷均值 """
            if trend_state.loc[trend_state.index[-1], 'state'] == '上行':
                if peak_value[0] > trend_state.loc[trend_state.index[-1], 'start_date']:
                    trend_state.loc[trend_state.index[-1], 'end_date'] = peak_value[0]
                else:
                    trend_state.loc[trend_state.index[-1], 'end_date'] = loop_date
            elif trend_state.loc[trend_state.index[-1], 'state'] == '下行':
                if valley_value[0] > trend_state.loc[trend_state.index[-1], 'start_date']:
                    trend_state.loc[trend_state.index[-1], 'end_date'] = valley_value[0]
                else:
                    trend_state.loc[trend_state.index[-1], 'end_date'] = loop_date
            horizon_band = [valley_value[1], peak_value[1]]
            if pd.notnull(crthr_turn.loc[loop_date, 'valley']):
                valley_value = [loop_date, crthr_turn.loc[loop_date, 'valley']]
            else:
                peak_value = [loop_date, crthr_turn.loc[loop_date, 'peak']]
            """ 更新趋势条目"""
            trend_state.loc[len(trend_state)] = {'judge_date': loop_date,
                                                 'state': '横盘',
                                                 'start_date': trend_state.loc[trend_state.index[-1], 'end_date'],
                                                 'end_date': loop_date,
                                                 'target_price': 0,
                                                 'long_state': trend_state.loc[trend_state.index[-1], 'long_state'],
                                                 'ls_start_date': trend_state.loc[
                                                     trend_state.index[-1], 'ls_start_date']}
            # trend_state = trend_state.append(trend_update, ignore_index=True)

            """~~~~~~~~~~~~~~~~~新增下行趋势条目，计算下行目标价~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"""
        elif (trend_state.loc[trend_state.index[-1], 'state'] == '上行'
              and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                    and crthr_turn.loc[loop_date, 'peak'] < peak_valley_mean
                   ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                         and crthr_turn.loc[loop_date, 'valley'] < valley_value[1] - (
                                 peak_value[1] - valley_value[1]) / 2))
        ) or (trend_state.loc[trend_state.index[-1], 'state'] == '横盘'
              and trend_state.loc[trend_state.index[-2], 'state'] == '上行'
              and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                    and crthr_turn.loc[loop_date, 'peak'] < horizon_band[0]
                   ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                         and crthr_turn.loc[loop_date, 'valley'] < horizon_band[0] - horizon_half_width))):
            """下行趋势判定条件：（1）now趋势为上行，
                                当前拐点为波峰，价格低于波峰波谷均值
                                或者 当前拐点为波谷，价格低于波谷值-1/2波峰波谷区间
                        (2）now趋势为横盘，now-1趋势为上行
                                当前拐点为波峰，价格低于横盘区间下轨
                                或者，当前拐点为波谷，价格低于横盘区间下轨-1/2横盘区间宽度 """
            # 更新前一趋势结束日期
            if peak_value[0] > trend_state.loc[trend_state.index[-1], 'start_date']:
                trend_state.loc[trend_state.index[-1], 'end_date'] = peak_value[0]
            else:
                trend_state.loc[trend_state.index[-1], 'end_date'] = loop_date
            if pd.notnull(crthr_turn.loc[loop_date, 'peak']):
                peak_value = [loop_date, crthr_turn.loc[loop_date, 'peak']]
            else:
                valley_value = [loop_date, crthr_turn.loc[loop_date, 'valley']]
            """ 计算下行趋势目标价格 """
            if len(valley_left_close) != 0:
                right_min_close = np.min(stk_close[drop_start_date:loop_date].values)
                target_price = cal_trend_target(peak_rail_value, valley_left_close, right_min_close, '下行')
            else:
                continue
            """ 更新趋势条目"""
            trend_state.loc[len(trend_state)] = {'judge_date': loop_date,
                                                 'state': '下行',
                                                 'start_date': trend_state.loc[trend_state.index[-1], 'end_date'],
                                                 'end_date': loop_date,
                                                 'target_price': target_price,
                                                 'long_state': trend_state.loc[trend_state.index[-1], 'long_state'],
                                                 'ls_start_date': trend_state['ls_start_date'].iloc[-1]}
            # trend_state = trend_state.append(trend_update, ignore_index=True)
            """ 此处相对matlab程序删除了如前一趋势为横盘，则更改趋势起始日期为横盘区间最低价位置语句块"""
            """ 判定是否更改大趋势状态及起始日期 """
            if trend_state.loc[trend_state.index[-1], 'long_state'] == '上行' \
                    and valley_value[1] < np.min(stk_close[
                                                     trend_state.loc[trend_state.index[-2]:,
                                                     'start_date'].values].values):
                trend_ls_sdate = trend_state.loc[trend_state.index[-1], 'ls_start_date']
                # peak_temp = crthr_turn.query("index >= @trend_ls_sdate and index <= @loop_date"
                #                              )['peak'].dropna(how='all')
                peak_temp = crthr_turn[trend_ls_sdate:loop_date]['peak'].dropna(how='all')
                new_turn_date = peak_temp.index[np.argmax(peak_temp.values)]
                trend_need_change = trend_state.query("start_date >= @new_turn_date").index
                if len(trend_need_change) != 0:
                    trend_state.loc[trend_need_change, 'long_state'] = '下行'
                    trend_state.loc[trend_need_change, 'ls_start_date'] = new_turn_date
            """ 当前长趋势为下行，如当前趋势满足相关条件，则抹去前一上行长趋势
                    定位当前下行趋势now的起始点drop_trend，回溯前一上行趋势now-1起始点pre_rise_trend，再前溯至now-2下行趋势的起点
                    pre_pre_drop_start_close
                    判定条件：（1）如now-2下行起始价格高于now-1上行结束价格，
                            （2）now-1上行起始价格高于当前波谷值，
                            （3）now-1上行趋势持续周期低于80天，
                            （4）now-1上行趋势跌幅低于40%
                    符合条件，则将now-1长趋势状态改为下行，且起始日期更改至now-2下行趋势的起始日期"""
            drop_trend = trend_state[(trend_state['start_date'].values ==
                                      trend_state.loc[trend_state.index[-1], 'ls_start_date']) &
                                     (trend_state['state'].values == '下行')]
            if len(drop_trend) != 0 \
                    and trend_state.loc[trend_state.index[-1], 'long_state'] == '下行' and drop_trend.index[0] > 0:
                pre_rise_trend = trend_state[:drop_trend.index[0]]
                if len(pre_rise_trend) > 1:
                    pre_drop_endlsdate = pre_rise_trend.loc[pre_rise_trend.index[-2], 'ls_start_date']
                    pre_rise_trend = pre_rise_trend.query("start_date == @pre_drop_endlsdate and state == '上行'")
                    if len(pre_rise_trend) != 0 \
                            and pre_rise_trend.index[0] > 0 \
                            and (pd.to_datetime(drop_trend.loc[drop_trend.index[0], 'start_date']) -
                                 pd.to_datetime(pre_rise_trend.loc[pre_rise_trend.index[0], 'start_date'])).days < 80 \
                            and abs(stk_close[pre_rise_trend.loc[pre_rise_trend.index[0], 'start_date']] /
                                    stk_close[drop_trend.loc[drop_trend.index[0], 'start_date']] - 1) < 0.4:
                        pre_pre_drop_start_close = stk_close[trend_state.loc[pre_rise_trend.index[0] - 1,
                                                                            'ls_start_date']]
                        pre_rise_start_close = stk_close[pre_rise_trend.loc[
                            pre_rise_trend.index[0], 'ls_start_date']]
                        pre_rise_end_close = stk_close[trend_state.loc[trend_state.index[-1], 'ls_start_date']]
                        if pre_pre_drop_start_close > pre_rise_end_close and pre_rise_start_close > valley_value[1]:
                            trend_state.loc[pre_rise_trend.index[0]:, 'long_state'] = '下行'
                            trend_state.loc[pre_rise_trend.index[0]:, 'ls_start_date'] = \
                                trend_state.loc[pre_rise_trend.index[0] - 1, 'ls_start_date']

            """~~~~~~~~~~~~~~接续下行趋势条目，如突破目标价，则更新目标价~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"""
        elif trend_state.loc[trend_state.index[-1], 'state'] == '横盘' \
                and trend_state.loc[trend_state.index[-2], 'state'] == '下行' \
                and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                      and crthr_turn.loc[loop_date, 'peak'] < horizon_band[0]
                     ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                           and crthr_turn.loc[loop_date, 'valley'] < horizon_band[0] - horizon_half_width)):
            """判定条件：（1）now趋势为横盘，now-1趋势为下行，
                       （2）当前拐点为波峰，价格低于横盘下轨
                         或者 当前拐点为波谷，价格低于横盘下轨-1/2*横盘宽度  """
            trend_state.loc[trend_state.index[-1], 'end_date'] = peak_value[0]
            if pd.notnull(crthr_turn.loc[loop_date, 'peak']):
                peak_value = [loop_date, crthr_turn.loc[loop_date, 'peak']]
            else:
                valley_value = [loop_date, crthr_turn.loc[loop_date, 'valley']]
            trend_state.loc[trend_state.index[-2]:, 'long_state'] = '下行'
            trend_state.loc[trend_state.index[-2]:, 'ls_start_date'] = \
                trend_state['start_date'].iloc[-2]
            """ 更新趋势条目"""
            trend_state.loc[len(trend_state)] = {'judge_date': loop_date,
                                                 'state': '下行',
                                                 'start_date': trend_state.loc[trend_state.index[-1], 'end_date'],
                                                 'end_date': loop_date,
                                                 'target_price': trend_state.loc[trend_state.index[-2], 'target_price'],
                                                 'long_state': trend_state.loc[trend_state.index[-2], 'long_state'],
                                                 'ls_start_date': trend_state.loc[
                                                     trend_state.index[-2], 'ls_start_date']}
            # trend_state = trend_state.append(trend_update, ignore_index=True)
            """ 如突破目标价，重新测算目标价格"""
            if valley_value[1] * 1.1 <= trend_state.loc[trend_state.index[-1], 'target_price']:
                """ 更新目标价：
                    """
                dataprocess = process_drop(trend_state, crthr_turn, stk_close, loop_date)
                peak_rail_value = dataprocess['peak_rail_value']
                valley_left_close = dataprocess['valley_left_close']
                drop_start_date = dataprocess['drop_start_date']
                right_min_close = np.min(stk_close[drop_start_date:loop_date].values)
                target_price = cal_trend_target(peak_rail_value, valley_left_close, right_min_close, '下行')
                trend_state.loc[trend_state.index[-1], 'target_price'] = target_price

            """~~~~~~~~~~~~~~维持上行趋势，如突破目标价，则更新目标价~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"""
        elif trend_state.loc[trend_state.index[-1], 'state'] == '上行' \
                and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                      and crthr_turn.loc[loop_date, 'peak'] > peak_value[1]
                     ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                           and crthr_turn.loc[loop_date, 'valley'] > valley_value[1])):
            """判定条件：（1）now趋势为上行，
                   （2）当前拐点为波峰，价格高于波峰值
                     或者 当前拐点为波谷，价格高于波谷值  """
            if pd.notnull(crthr_turn.loc[loop_date, 'peak']):
                peak_value = [loop_date, crthr_turn.loc[loop_date, 'peak']]
            else:
                valley_value = [loop_date, crthr_turn.loc[loop_date, 'valley']]
            """ 如长趋势为下行，则依标准判定，并更新为上行"""
            trend_turn = trend_state[trend_state['state'].values == '下行']
            if trend_state['long_state'].iloc[-1] == '下行' \
                    and stk_close[trend_turn['start_date'].iloc[-1]] < peak_value[1]:
                turn_date = trend_turn.loc[trend_turn.index[-1], 'ls_start_date']
                rise_start_date = crthr_turn.loc[turn_date:loop_date, 'valley'].idxmin()
                trend_state.loc[trend_state['start_date'] >= rise_start_date, 'long_state'] = '上行'
                trend_state.loc[trend_state['start_date'] >= rise_start_date, 'ls_start_date'] = rise_start_date

            """ 更新目标价格 """
            if peak_value[1] * 0.9 > trend_state.loc[trend_state.index[-1], 'target_price']:
                """ 更新目标价："""
                dataprocess = process_rise(trend_state, crthr_turn, stk_close, peak_value, loop_date)
                peak_left_close = dataprocess['peak_left_close']
                valley_rail_value = dataprocess['valley_rail_value']
                rise_start_date = dataprocess['rise_start_date']
                if len(peak_left_close) != 0:
                    right_max_close = np.max(stk_close[rise_start_date:loop_date].values)
                    target_price = cal_trend_target(valley_rail_value, peak_left_close, right_max_close, '上行')
                else:
                    continue
                trend_state.loc[trend_state.index[-1], 'target_price'] = target_price
                trend_state.loc[trend_state.index[-1], 'end_date'] = loop_date
                trend_state.loc[trend_state.index[-1], 'judge_date'] = loop_date
            else:
                trend_state.loc[trend_state.index[-1], 'end_date'] = loop_date
                trend_state.loc[trend_state.index[-1], 'judge_date'] = loop_date

            """~~~~~~~~~~~~~~维持下行趋势，如突破目标价，则更新目标价~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"""
        elif trend_state.loc[trend_state.index[-1], 'state'] == '下行' \
                and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                      and crthr_turn.loc[loop_date, 'peak'] < peak_value[1]
                     ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                           and crthr_turn.loc[loop_date, 'valley'] < valley_value[1])):
            """判定条件：（1）now趋势为下行，
                   （2）当前拐点为波峰，价格低于波峰值
                     或者 当前拐点为波谷，价格低于波谷值  """
            if pd.notnull(crthr_turn.loc[loop_date, 'peak']):
                peak_value = [loop_date, crthr_turn.loc[loop_date, 'peak']]
            else:
                valley_value = [loop_date, crthr_turn.loc[loop_date, 'valley']]
            """ 判定是否更改大趋势状态及起始日期 """
            if trend_state.loc[trend_state.index[-1], 'long_state'] == '上行' \
                    and valley_value[1] < np.min(stk_close[trend_state.loc[trend_state.index[-2]:,
                                                           'start_date']].values):
                trend_ls_sdate = trend_state['ls_start_date'].iloc[-1]
                peak_temp = crthr_turn.query("index >= @trend_ls_sdate and index <= @loop_date")['peak'].dropna(
                    how='all')
                new_turn_date = peak_temp.index[np.argmax(peak_temp.values)]
                trend_need_change = trend_state.query("start_date >= @new_turn_date").index
                if len(trend_need_change) != 0:
                    trend_state.loc[trend_need_change, 'long_state'] = '下行'
                    trend_state.loc[trend_need_change, 'ls_start_date'] = new_turn_date
            """ 如突破目标价，重新测算目标价格"""
            if valley_value[1] * 1.1 <= trend_state.loc[trend_state.index[-1], 'target_price']:
                """ 更新目标价：  """
                dataprocess = process_drop(trend_state, crthr_turn, stk_close, loop_date)
                peak_rail_value = dataprocess['peak_rail_value']
                valley_left_close = dataprocess['valley_left_close']
                drop_start_date = dataprocess['drop_start_date']
                right_min_close = np.min(stk_close[drop_start_date:loop_date].values)
                target_price = cal_trend_target(peak_rail_value, valley_left_close, right_min_close, '下行')
                trend_state.loc[trend_state.index[-1], 'target_price'] = target_price
                trend_state.loc[trend_state.index[-1], 'end_date'] = loop_date
                trend_state.loc[trend_state.index[-1], 'judge_date'] = loop_date
            else:
                trend_state.loc[trend_state.index[-1], 'end_date'] = loop_date
                trend_state.loc[trend_state.index[-1], 'judge_date'] = loop_date

            """~~~~~~~~~~~~~~维持横盘趋势，更新横盘区间结束日期~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"""
        elif trend_state.loc[trend_state.index[-1], 'state'] == '横盘' \
                and ((pd.notnull(crthr_turn.loc[loop_date, 'peak'])
                      and horizon_band[1] + horizon_half_width > crthr_turn.loc[loop_date, 'peak'] >= horizon_band[0]
                     ) or (pd.notnull(crthr_turn.loc[loop_date, 'valley'])
                           and horizon_band[1] >= crthr_turn.loc[loop_date, 'valley'] >= horizon_band[
                               0] - horizon_half_width)):
            """判定条件：（1）now趋势为横盘，
                       （2）当前拐点为波峰，价格低于横盘上轨+1/2横盘区间，高于横盘下轨
                            或者 当前拐点为波谷，价格低于横盘上轨，高于横盘下轨-1/2横盘区间  """
            trend_state.loc[trend_state.index[-1], 'end_date'] = loop_date
            trend_state.loc[trend_state.index[-1], 'judge_date'] = loop_date
            if pd.notnull(crthr_turn.loc[loop_date, 'peak']):
                peak_value = [loop_date, crthr_turn.loc[loop_date, 'peak']]
            else:
                valley_value = [loop_date, crthr_turn.loc[loop_date, 'valley']]
    try:
        trend_state.loc[trend_state.index[-1], 'end_date'] = trade_date[-1]
    except ValueError:
        pdb.set_trace()
    return trend_state


if __name__ == '__main__':
    # stock_data = pd.read_csv('../dataset/stockData_Adj.csv')
    from function_ai.Func_Base import get_stock_data
    stk = get_stock_data(stk_code='300337.SZ').set_index('trade_date', drop=False)
    # stk = stock_data.query('ts_code == "300337.SZ"')[['trade_date', 'close']]
    # stk = stk.sort_values('trade_date')
    # stk = stk.set_index('trade_date', drop=False)
    pv_turn = peak_valley_turn(stk['close'].values, stk['trade_date'].values)
    trend = trend_judge(stk['close'], pv_turn)
