{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["已连接到 base (Python 3.9.7)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>trade_date</th>\n", "      <th>2024-01-05</th>\n", "      <th>2024-01-08</th>\n", "      <th>2024-01-09</th>\n", "      <th>2024-01-10</th>\n", "      <th>2024-01-11</th>\n", "      <th>2024-01-12</th>\n", "      <th>2024-01-15</th>\n", "      <th>2024-01-16</th>\n", "      <th>2024-01-17</th>\n", "      <th>2024-01-18</th>\n", "      <th>...</th>\n", "      <th>2024-02-01</th>\n", "      <th>2024-02-02</th>\n", "      <th>2024-02-05</th>\n", "      <th>2024-02-06</th>\n", "      <th>2024-02-07</th>\n", "      <th>2024-02-08</th>\n", "      <th>2024-02-19</th>\n", "      <th>2024-02-20</th>\n", "      <th>2024-02-21</th>\n", "      <th>2024-02-22</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>煤炭</th>\n", "      <td>NaN</td>\n", "      <td>1.017871</td>\n", "      <td>1.889976</td>\n", "      <td>1.472810</td>\n", "      <td>-0.750449</td>\n", "      <td>0.185658</td>\n", "      <td>0.789526</td>\n", "      <td>0.350164</td>\n", "      <td>1.366574</td>\n", "      <td>-0.201628</td>\n", "      <td>...</td>\n", "      <td>5.061849</td>\n", "      <td>6.644405</td>\n", "      <td>7.224228</td>\n", "      <td>2.694287</td>\n", "      <td>2.590694</td>\n", "      <td>2.251905</td>\n", "      <td>5.932685</td>\n", "      <td>6.587452</td>\n", "      <td>4.917301</td>\n", "      <td>9.340364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>银行</th>\n", "      <td>NaN</td>\n", "      <td>0.823705</td>\n", "      <td>0.960087</td>\n", "      <td>0.919938</td>\n", "      <td>0.002187</td>\n", "      <td>1.052146</td>\n", "      <td>1.789889</td>\n", "      <td>2.596780</td>\n", "      <td>4.236570</td>\n", "      <td>3.343304</td>\n", "      <td>...</td>\n", "      <td>10.663627</td>\n", "      <td>12.451478</td>\n", "      <td>13.808951</td>\n", "      <td>10.057874</td>\n", "      <td>6.759117</td>\n", "      <td>5.519520</td>\n", "      <td>6.673686</td>\n", "      <td>7.291700</td>\n", "      <td>9.105796</td>\n", "      <td>8.307175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>非银金融</th>\n", "      <td>NaN</td>\n", "      <td>-0.227311</td>\n", "      <td>-0.765484</td>\n", "      <td>-1.179129</td>\n", "      <td>-0.890757</td>\n", "      <td>-1.515020</td>\n", "      <td>-1.404408</td>\n", "      <td>-0.690167</td>\n", "      <td>-0.255645</td>\n", "      <td>0.021353</td>\n", "      <td>...</td>\n", "      <td>4.271248</td>\n", "      <td>3.983508</td>\n", "      <td>2.658455</td>\n", "      <td>3.075659</td>\n", "      <td>2.158303</td>\n", "      <td>2.512309</td>\n", "      <td>0.952092</td>\n", "      <td>1.477555</td>\n", "      <td>1.984232</td>\n", "      <td>2.131258</td>\n", "    </tr>\n", "    <tr>\n", "      <th>家用电器</th>\n", "      <td>NaN</td>\n", "      <td>0.073580</td>\n", "      <td>0.726445</td>\n", "      <td>1.175354</td>\n", "      <td>0.752378</td>\n", "      <td>0.916395</td>\n", "      <td>1.986073</td>\n", "      <td>2.342433</td>\n", "      <td>3.165197</td>\n", "      <td>1.841977</td>\n", "      <td>...</td>\n", "      <td>3.948937</td>\n", "      <td>4.279154</td>\n", "      <td>4.098817</td>\n", "      <td>1.767862</td>\n", "      <td>1.596806</td>\n", "      <td>1.061946</td>\n", "      <td>0.797934</td>\n", "      <td>2.437862</td>\n", "      <td>1.809287</td>\n", "      <td>1.264401</td>\n", "    </tr>\n", "    <tr>\n", "      <th>石油石化</th>\n", "      <td>NaN</td>\n", "      <td>0.417275</td>\n", "      <td>0.531394</td>\n", "      <td>0.233114</td>\n", "      <td>-2.075258</td>\n", "      <td>-1.668916</td>\n", "      <td>-1.041963</td>\n", "      <td>-1.756923</td>\n", "      <td>-1.236957</td>\n", "      <td>-3.100330</td>\n", "      <td>...</td>\n", "      <td>1.300222</td>\n", "      <td>1.261175</td>\n", "      <td>0.311244</td>\n", "      <td>-1.923969</td>\n", "      <td>-2.284357</td>\n", "      <td>-1.818195</td>\n", "      <td>0.249968</td>\n", "      <td>0.082065</td>\n", "      <td>-0.421409</td>\n", "      <td>1.229802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>通信</th>\n", "      <td>NaN</td>\n", "      <td>-0.559748</td>\n", "      <td>-0.579578</td>\n", "      <td>-1.818292</td>\n", "      <td>-0.495779</td>\n", "      <td>-1.831366</td>\n", "      <td>-1.400006</td>\n", "      <td>-1.771079</td>\n", "      <td>-1.511440</td>\n", "      <td>0.269478</td>\n", "      <td>...</td>\n", "      <td>-3.719914</td>\n", "      <td>-4.462410</td>\n", "      <td>-7.653532</td>\n", "      <td>-7.491108</td>\n", "      <td>-9.034191</td>\n", "      <td>-6.440825</td>\n", "      <td>-0.640619</td>\n", "      <td>-0.038199</td>\n", "      <td>-1.937862</td>\n", "      <td>0.368426</td>\n", "    </tr>\n", "    <tr>\n", "      <th>食品饮料</th>\n", "      <td>NaN</td>\n", "      <td>0.111630</td>\n", "      <td>-0.162737</td>\n", "      <td>1.080529</td>\n", "      <td>0.757529</td>\n", "      <td>0.810546</td>\n", "      <td>0.580517</td>\n", "      <td>0.364327</td>\n", "      <td>-0.575411</td>\n", "      <td>-0.038205</td>\n", "      <td>...</td>\n", "      <td>0.050214</td>\n", "      <td>0.589688</td>\n", "      <td>1.426284</td>\n", "      <td>0.632878</td>\n", "      <td>0.179910</td>\n", "      <td>-0.393839</td>\n", "      <td>-1.021009</td>\n", "      <td>-1.966946</td>\n", "      <td>0.408666</td>\n", "      <td>-0.035821</td>\n", "    </tr>\n", "    <tr>\n", "      <th>交通运输</th>\n", "      <td>NaN</td>\n", "      <td>0.303523</td>\n", "      <td>1.015549</td>\n", "      <td>1.082879</td>\n", "      <td>0.785065</td>\n", "      <td>1.635239</td>\n", "      <td>2.941030</td>\n", "      <td>2.991551</td>\n", "      <td>3.527266</td>\n", "      <td>2.169890</td>\n", "      <td>...</td>\n", "      <td>2.012140</td>\n", "      <td>2.202406</td>\n", "      <td>0.321683</td>\n", "      <td>-0.850499</td>\n", "      <td>-1.687267</td>\n", "      <td>-1.221306</td>\n", "      <td>-0.498169</td>\n", "      <td>0.200993</td>\n", "      <td>-0.097698</td>\n", "      <td>-0.267673</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>8 rows × 29 columns</p>\n", "</div>"], "text/plain": ["trade_date  2024-01-05  2024-01-08  2024-01-09  2024-01-10  2024-01-11  \\\n", "煤炭                 NaN    1.017871    1.889976    1.472810   -0.750449   \n", "银行                 NaN    0.823705    0.960087    0.919938    0.002187   \n", "非银金融               NaN   -0.227311   -0.765484   -1.179129   -0.890757   \n", "家用电器               NaN    0.073580    0.726445    1.175354    0.752378   \n", "石油石化               NaN    0.417275    0.531394    0.233114   -2.075258   \n", "通信                 NaN   -0.559748   -0.579578   -1.818292   -0.495779   \n", "食品饮料               NaN    0.111630   -0.162737    1.080529    0.757529   \n", "交通运输               NaN    0.303523    1.015549    1.082879    0.785065   \n", "\n", "trade_date  2024-01-12  2024-01-15  2024-01-16  2024-01-17  2024-01-18  ...  \\\n", "煤炭            0.185658    0.789526    0.350164    1.366574   -0.201628  ...   \n", "银行            1.052146    1.789889    2.596780    4.236570    3.343304  ...   \n", "非银金融         -1.515020   -1.404408   -0.690167   -0.255645    0.021353  ...   \n", "家用电器          0.916395    1.986073    2.342433    3.165197    1.841977  ...   \n", "石油石化         -1.668916   -1.041963   -1.756923   -1.236957   -3.100330  ...   \n", "通信           -1.831366   -1.400006   -1.771079   -1.511440    0.269478  ...   \n", "食品饮料          0.810546    0.580517    0.364327   -0.575411   -0.038205  ...   \n", "交通运输          1.635239    2.941030    2.991551    3.527266    2.169890  ...   \n", "\n", "trade_date  2024-02-01  2024-02-02  2024-02-05  2024-02-06  2024-02-07  \\\n", "煤炭            5.061849    6.644405    7.224228    2.694287    2.590694   \n", "银行           10.663627   12.451478   13.808951   10.057874    6.759117   \n", "非银金融          4.271248    3.983508    2.658455    3.075659    2.158303   \n", "家用电器          3.948937    4.279154    4.098817    1.767862    1.596806   \n", "石油石化          1.300222    1.261175    0.311244   -1.923969   -2.284357   \n", "通信           -3.719914   -4.462410   -7.653532   -7.491108   -9.034191   \n", "食品饮料          0.050214    0.589688    1.426284    0.632878    0.179910   \n", "交通运输          2.012140    2.202406    0.321683   -0.850499   -1.687267   \n", "\n", "trade_date  2024-02-08  2024-02-19  2024-02-20  2024-02-21  2024-02-22  \n", "煤炭            2.251905    5.932685    6.587452    4.917301    9.340364  \n", "银行            5.519520    6.673686    7.291700    9.105796    8.307175  \n", "非银金融          2.512309    0.952092    1.477555    1.984232    2.131258  \n", "家用电器          1.061946    0.797934    2.437862    1.809287    1.264401  \n", "石油石化         -1.818195    0.249968    0.082065   -0.421409    1.229802  \n", "通信           -6.440825   -0.640619   -0.038199   -1.937862    0.368426  \n", "食品饮料         -0.393839   -1.021009   -1.966946    0.408666   -0.035821  \n", "交通运输         -1.221306   -0.498169    0.200993   -0.097698   -0.267673  \n", "\n", "[8 rows x 29 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# 计算指定日期区间行业强弱排序\n", "from function_ai.swindex_funcs import cal_sw_diffratio\n", "sw_diffratio = cal_sw_diffratio(start_date='2024-01-05', end_date='2024-02-22', transform=True)\n", "sw_diffratio.iloc[:8]"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Indus_MV</th>\n", "      <th>Sec_DiffNum</th>\n", "      <th>SecDiff_Rank</th>\n", "      <th>PostSecPeak_PGV_MinRollAvg2Now_LastDays</th>\n", "      <th>MinRollAvg_Rank</th>\n", "      <th>Score</th>\n", "    </tr>\n", "    <tr>\n", "      <th>indus</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>社会服务</th>\n", "      <td>4376.84</td>\n", "      <td>8.366667</td>\n", "      <td>1.0</td>\n", "      <td>1.941176</td>\n", "      <td>10.0</td>\n", "      <td>11.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>房地产</th>\n", "      <td>11911.41</td>\n", "      <td>8.433333</td>\n", "      <td>2.0</td>\n", "      <td>1.666667</td>\n", "      <td>9.0</td>\n", "      <td>11.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>美容护理</th>\n", "      <td>3262.23</td>\n", "      <td>10.866667</td>\n", "      <td>10.5</td>\n", "      <td>1.000000</td>\n", "      <td>4.5</td>\n", "      <td>15.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>食品饮料</th>\n", "      <td>50760.13</td>\n", "      <td>9.633333</td>\n", "      <td>4.0</td>\n", "      <td>2.500000</td>\n", "      <td>12.0</td>\n", "      <td>16.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>医药生物</th>\n", "      <td>63896.93</td>\n", "      <td>10.966667</td>\n", "      <td>12.0</td>\n", "      <td>1.000000</td>\n", "      <td>4.5</td>\n", "      <td>16.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>有色金属</th>\n", "      <td>27903.52</td>\n", "      <td>11.500000</td>\n", "      <td>16.0</td>\n", "      <td>1.000000</td>\n", "      <td>4.5</td>\n", "      <td>20.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>农林牧渔</th>\n", "      <td>12557.26</td>\n", "      <td>11.533333</td>\n", "      <td>17.0</td>\n", "      <td>1.000000</td>\n", "      <td>4.5</td>\n", "      <td>21.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>交通运输</th>\n", "      <td>31513.28</td>\n", "      <td>10.066667</td>\n", "      <td>5.5</td>\n", "      <td>5.000000</td>\n", "      <td>17.0</td>\n", "      <td>22.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Indus_MV  Sec_DiffNum  SecDiff_Rank  \\\n", "indus                                        \n", "社会服务    4376.84     8.366667           1.0   \n", "房地产    11911.41     8.433333           2.0   \n", "美容护理    3262.23    10.866667          10.5   \n", "食品饮料   50760.13     9.633333           4.0   \n", "医药生物   63896.93    10.966667          12.0   \n", "有色金属   27903.52    11.500000          16.0   \n", "农林牧渔   12557.26    11.533333          17.0   \n", "交通运输   31513.28    10.066667           5.5   \n", "\n", "       PostSecPeak_PGV_MinRollAvg2Now_LastDays  MinRollAvg_Rank  Score  \n", "indus                                                                   \n", "社会服务                                  1.941176             10.0   11.0  \n", "房地产                                   1.666667              9.0   11.0  \n", "美容护理                                  1.000000              4.5   15.0  \n", "食品饮料                                  2.500000             12.0   16.0  \n", "医药生物                                  1.000000              4.5   16.5  \n", "有色金属                                  1.000000              4.5   20.5  \n", "农林牧渔                                  1.000000              4.5   21.5  \n", "交通运输                                  5.000000             17.0   22.5  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# 测算指定日期转折点品种概率\n", "from function_ai.StkPick_Func_V7 import induspick_turn_state\n", "indus_count, bottom_list = induspick_turn_state(end_date='2024-10-10', now_secdate='2024-10-10')\n", "indus_count.iloc[:8]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["筛选行业： 计算机\n", "行业MaxSum均值排名：\n", " industry\n", "计算机    47.908\n", "Name: Max_SumRatio, dtype: float64\n"]}], "source": ["from function_ai.StkPick_ModelFunc import get_dailytrack_quota\n", "industry = [\"计算机\"]\n", "prepared_quota, result_pull = get_dailytrack_quota(end_date='2024-09-18', sec_pickdate='2024-09-18', pull_startdate='2024-07-24', limit_num=0, industry=industry)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["result_pull_index = result_pull[['ts_code', 'name', 'industry', 'PostSecStart_PGV_MaxRollAvg', 'PostSecStart_PGV_MaxRollAvg_Date',\n", "                                 'PreSecMaxRollAvg_PGV_MinRollAvg', 'PostSecMaxRollAvg_PGV_MinRollAvg', \n", "                                 'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays', 'PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio',\n", "                                 'SecConcave_LastDays', 'SecConcave_TO_Sum', 'SecConcave_RatioBand', 'Max_SumRatio'\n", "                                 ]].sort_values(by='SecConcave_RatioBand', ascending=True)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# 获取指定日期计算指标\n", "from function_ai.StkPick_Func_V7 import get_result_3\n", "result = get_result_3(end_date='2024-09-18')"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["result_pick = result.query('industry in [\"食品饮料\", \"医药生物\", \"家用电器\", \"计算机\"] & '\n", "                           '(Now_PGV_RollAvg==PostSecStart_PGV_MinRollAvg | '\n", "                           '(PostSecPeak_PGV_MinRollAvg2Now_LastDays<=2 & PostSecPeak_PGV_MinRollAvg2MaxRatio<0.5))')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>SecConcave_LastDays</th>\n", "      <th>CoverDays_BreakDate_RatioBand</th>\n", "      <th>ConCover_DayRatio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>太平洋</td>\n", "      <td>1214.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>信达证券</td>\n", "      <td>143.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>法狮龙</td>\n", "      <td>233.0</td>\n", "      <td>35.452</td>\n", "      <td>0.152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>安妮股份</td>\n", "      <td>140.0</td>\n", "      <td>50.239</td>\n", "      <td>0.359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>首创证券</td>\n", "      <td>166.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>扬子新材</td>\n", "      <td>181.0</td>\n", "      <td>40.086</td>\n", "      <td>0.221</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>青松建化</td>\n", "      <td>49.0</td>\n", "      <td>15.294</td>\n", "      <td>0.312</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>拉卡拉</td>\n", "      <td>92.0</td>\n", "      <td>41.984</td>\n", "      <td>0.456</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   name  SecConcave_LastDays  CoverDays_BreakDate_RatioBand  ConCover_DayRatio\n", "0   太平洋               1214.0                          0.000              0.000\n", "1  信达证券                143.0                          0.000              0.000\n", "2   法狮龙                233.0                         35.452              0.152\n", "3  安妮股份                140.0                         50.239              0.359\n", "4  首创证券                166.0                          0.000              0.000\n", "5  扬子新材                181.0                         40.086              0.221\n", "6  青松建化                 49.0                         15.294              0.312\n", "7   拉卡拉                 92.0                         41.984              0.456"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["prepared_quota['ConCover_DayRatio'] = round(prepared_quota['CoverDays_BreakDate_RatioBand'] / prepared_quota['SecConcave_LastDays'], 3)\n", "prepared_quota.query('Track_Categ==\"Track_Pull\"')[['name', 'SecConcave_LastDays', 'CoverDays_BreakDate_RatioBand', 'ConCover_DayRatio']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 筛选行业及强势品种\n", "end_date = '2024-04-16'\n", "from function_ai.Func_Base import cal_period_ratio\n", "period_ratio = cal_period_ratio(start_date='2024-03-18', end_date=end_date, mode='Max')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import induspick_turn_state\n", "indus_count, bottom_list = induspick_turn_state(end_date=end_date, now_secdate=end_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["industry = ['基础化工']\n", "period_ratio_indus = period_ratio.query('industry in @industry')\n", "result_indus = pd.merge(result, period_ratio_indus[['ts_code', 'Max_SumRatio']], on='ts_code', how='inner')\n", "result_indus = result_indus.query('PostSecPeak2Now_LastDays>1 & '\n", "                                  'PostSecStart_PGV_MaxRollAvg2Now_LastDays>5 & '\n", "                                  '(PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays>3 | '\n", "                                  'PostSecStart_PGV_MaxRollAvg2Now_LastDays>15) & Max_SumRatio>15').sort_values(by='Max_SumRatio', ascending=False).groupby('industry').head(10)\n", "result_indus_temp = result_indus[['name', 'industry', 'Max_SumRatio', 'PostSecPeak2Now_LastDays', 'PreNowSec_LastDays','PostSecStart_Over7Num', 'PostSecStart_MedianPeakGap', 'PostSecStart_PGV_MaxRollAvg','PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio', 'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays', 'PostSecStart_PGV_MaxRollAvg2Now_LastDays']].copy()\n", "result_indus_temp['Max_SumRatio_Rank'] = result_indus_temp.groupby('industry')['Max_SumRatio'].rank(ascending=False)\n", "result_indus_temp['MaxRollAvg2Now_LastDays_Rank'] = result_indus_temp.groupby('industry')[\n", "    'PostSecStart_PGV_MaxRollAvg2Now_LastDays'].rank(ascending=False)\n", "result_indus_temp['MinRollAvg2Now_LastDays_Rank'] = result_indus_temp.groupby('industry')['PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays'].rank(ascending=False)\n", "result_indus_temp['MedianPeakGap_Rank'] = result_indus_temp.groupby('industry')[\n", "    'PostSecStart_MedianPeakGap'].rank(ascending=False)\n", "result_indus_temp['Avg2Max_Rank'] = result_indus_temp.groupby('industry')['PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio'].rank(ascending=False)\n", "result_indus_temp['PostSec_Over7Num_Rank'] = result_indus_temp.groupby('industry')['PostSecStart_Over7Num'].rank(ascending=True)\n", "result_indus_temp['Score'] = result_indus_temp['Max_SumRatio_Rank'] + result_indus_temp['MedianPeakGap_Rank'] + \\\n", "                            result_indus_temp['MaxRollAvg2Now_LastDays_Rank'] + result_indus_temp['MinRollAvg2Now_LastDays_Rank']\n", "result_indus_temp = result_indus_temp.sort_values(by='Score', ascending=True)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}