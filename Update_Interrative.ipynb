# 更新当日数据
from function_ai.swindex_crap import crap_swindex_from_web, level1_update
crap = crap_swindex_from_web(end_date='2025-07-01', mode='today')
level1_update(mode='replace')

# 更新昨日数据
from function_ai.swindex_crap import crap_swindex_from_web, level1_update
crap = crap_swindex_from_web(end_date='2025-07-02', mode='history')
level1_update(mode='replace')

# 更新日指标数据
from data_update.model_update import model_predict_update
result_forest_check = model_predict_update(end_date='2024-10-11', cal_mode=None, model_train=True, freq=True)

# 更新指定日期区间的日指标数据
from function_ai.StkPick_Func_V7 import get_result_3
from function_ai.StkQuota_Func_V7 import update_result_column
from function_ai.Func_Base import get_trade_date
import pandas as pd
trade_dates = get_trade_date()
turn_dates = ['2024-10-30']
dates = []
for date in turn_dates:
    check_dates = trade_dates[trade_dates>date][:8]
    dates.extend(check_dates)
dates = list(set(dates))
dates.sort()
print('计算日期：', dates)
result_store = get_result_3(end_date=dates, stk_list=['600088.SH'])
count = 0
for date in dates:
    if pd.isnull(result_store.query('Cal_Date==@date')['PostPeak_Recent_Neg4_DropDate_Close'].iloc[0]):
        resultB = update_result_column(end_date=date, mode='First_Half')
    count += 1
    print('finish date:', date)
    print('finish count:', count, '/', len(dates))


from function_ai.StkQuota_Func_V7 import update_result_column
second_dates = ['2024-10-31', '2024-11-01', '2024-11-07']
first_dates = ['2024-06-25', '2024-07-19', '2024-07-24']
for date in first_dates:
    resultB = update_result_column(end_date=date, mode='First_Half')
for date in second_dates:
    resultB = update_result_column(end_date=date, mode='Second_Half')


# 存储指定日期step1筛选结果
from function_ai.StkPick_ModelFunc import stkpick_dailytrack_step1
from function_ai.Func_Base import get_trade_date
trade_dates = get_trade_date()
turn_dates = ['2025-01-03']
# dates = []
# for date in turn_dates:
#     check_dates = trade_dates[trade_dates>date][:8]
#     dates.extend(check_dates)
# dates = list(set(dates))
# dates.sort()
count = 0
for date in turn_dates:
    output_DropRecov, output_DropRecov_Indus, result_totalmv, DropRecov_Predict = stkpick_dailytrack_step1(turntrack_startdate='2024-11-26', 
                                                                                                            recentdrop_startdate='2024-12-30', 
                                                                                                            end_date=date, 
                                                                                                            scope='all', 
                                                                                                            model_adj=True, 
                                                                                                            store_mode=True,
                                                                                                            now_secdate='2025-01-02')
    count += 1
    print('finish date:', date)
    print('finish count:', count, '/', len(turn_dates))


from function_ai.StkPick_ModelFunc import stkpick_dailytrack_step1
from function_ai.Func_Base import get_trade_date
trade_dates = get_trade_date()
dates = trade_dates[(trade_dates>='2024-11-22') & (trade_dates<='2024-12-18')]
count = 0
for date in dates:
    recentdrop_startdate = '2024-10-08' if date<='2024-11-13' else '2024-11-07'
    output_droprecov, output_droprecov_indus, result_totalmv, model_droprecov = stkpick_dailytrack_step1(turntrack_startdate='2024-09-18', 
                                                                                              recentdrop_startdate=recentdrop_startdate, 
                                                                                              end_date=date, 
                                                                                              store_mode=True, 
                                                                                              scope='all',
                                                                                              model_adj=False)
    count += 1
    print('finish date:', date)
    print('finish count:', count, '/', len(dates))


from function_ai.StkQuota_Func_V7 import stksfit_result_3
stk_temp, _ = stksfit_result_3(end_date='2024-12-24', mode='pick')

from function_ai.StkQuota_Func_V7 import stksfit_result_3, update_result_column
dates = ['2024-11-28','2024-11-29']
for date in dates:
    resultB = update_result_column(end_date=date, mode='First_Half')

resultB.query('ts_code=="002227.SZ"')[['PreNowPeak2NowSec_MinRatioMovAvg', 'PreNowPeak2NowSec_MinRatioMovAvg_Date','PreNow_PeakDate']]

result_pick = resultB.query('PostNowSec_Recover_TopOpen_Date==Cal_Date & PreNowPeak2NowSec_RatioMovAvg_NowSecRank==1')

from function_ai.StkPick_Func_V7 import get_result_3
result_store = get_result_3(end_date='2024-11-28')

industry = ['公用事业','银行','石油石化']
result_store_indus = result_store.query('industry in @industry').copy()
result_store_indus_head = result_store_indus.sort_values(by=['industry', 'Total_MV'], ascending=[True, False]).groupby('industry').head(20)
result_store_indus_head2 = result_store_indus_head[['name', 'industry', 'Section_StartDate', 'Now_SecDate', 
                                                    'Peak2Sec_PGV_MinRollAvg2Sec_LastDays', 'Peak2Sec_LastDays']]

result_pick_2 = result_store.query('PostPeak_Recent_Neg4_DropDate<=Now_SecDate & '
                                                'PostPeak_Recent_Neg4_DropDate>=PreNow_SecDate & '
                                                'PostPeak_Recent_Neg4_RecovDate>Now_SecDate & '
                                                'PostPeak_Recent_Neg4_RecovDate==Cal_Date & '
                                                'Recent3Day_PGV_MinRollAvg<=PostSecStart_PGV_RollAvg_LowQuntl & '
                                                'PreNowSec_LastDays>=8 & PostNowSec_LastDays<=3 & '                                              
                                                '((Section_StartDate<Now_SecDate & '
                                                'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays>0) | '
                                                '(Section_StartDate==Now_SecDate & '
                                                '10>Peak2Sec_PGV_MinRollAvg2Sec_LastDays>0))'
                                                ).sort_values(by='PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays', ascending=True)


from machine_learn.Func_MacinLn import stkpick_model_predict
result_check, result_model = stkpick_model_predict(predict_date='2024-11-28', 
                                                   stk_list=result_pick_2['ts_code'].tolist(),
                                                   label_style='Label',
                                                   model_select='XGB',
                                                   model_date='2024-10-30',
                                                   pretreat='recov',
                                                   lag_num=30)

from data_update.freqdata_func_tdx import process_tdx_data
from function_ai.StkQuota_Func_V7 import update_result_column, stksfit_result_3
from function_ai.Func_Base import get_trade_date

dates = ['2024-01-25']

# dates = ['2025-03-04', '2025-03-03', '2025-01-27']
for date in dates:
    if date == '2024-01-25':
        stk_temp = stksfit_result_3(end_date=date, cal_mode='Second_Half', freqdata_source='local', storemode=True) 
    else:
        stk_temp = stksfit_result_3(end_date=date, cal_mode='All', freqdata_source='local', storemode=True)      
    print('finish date:', date)

from function_ai.StkQuota_Func_V7 import update_result_column, stksfit_result_3
from function_ai.Func_Base import get_trade_date

trade_dates = get_trade_date()
dates = trade_dates[(trade_dates>='2024-12-26') & (trade_dates<='2024-12-31')]
# 
# dates = ['2024-07-10']
for date in dates:
    
    # if date == '2024-09-06':
        # stk_temp = update_result_column(end_date=date, mode='Second_Half', freqdata_source='local')
    # else:
    stk_temp = stksfit_result_3(end_date=date, mode='pick', freqdata_source='local')      
    print('finish date:', date)


result_break = stk_temp[0]
result_break['Cal_Date'].iloc[-1]

from data_update.freqdata_func_tdx import process_tdx_data
from function_ai.StkQuota_Func_V7 import update_result_column, stksfit_result_3
from function_ai.Func_Base import get_trade_date
# process_tdx_data()

dates = ['2025-06-30']
for date in dates:
    stk_temp = stksfit_result_3(end_date=date, cal_mode='Second_Half', freqdata_source='local', storemode=True)
    print('finish date:', date)
    

result_break= stk_temp[0]

from sqlalchemy import create_engine, text
import config.config_Ali as config
conf = config.configModel()
engine = create_engine(
    'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
        conf.DC_DB_PORT) + '/stocksfit')
import pandas as pd
pd.io.sql.to_sql(result_break, 'stk_results_3', engine, index=False, schema='stocksfit',
                             if_exists='append')
engine.dispose()

from data_update.freqdata_func_tdx import process_tdx_data
process_tdx_data()

from data_update.freqdata_func_tdx import process_tdx_data
from function_ai.StkQuota_Func_V7 import update_result_column, stksfit_result_3
from function_ai.Func_Base import get_trade_date
process_tdx_data()


dates = ['2025-05-06']
for date in dates:
    # if date == '2025-03-03':
    # stk_temp = stksfit_result_3(end_date=date, mode='pick', freqdata_source='local')
    # else:
    stk_temp = update_result_column(end_date=date, mode='Second_Half', freqdata_source='local')
    print('finish date:', date)

from data_update.freqdata_func_tdx import process_tdx_data
from function_ai.StkQuota_Func_V7 import update_result_column, stksfit_result_3
from function_ai.Func_Base import get_trade_date
# process_tdx_data()

trade_dates = get_trade_date()
dates = trade_dates[(trade_dates>='2025-02-05') & (trade_dates<='2025-02-21')].tolist()
dates = dates[::-1]
# dates = ['2025-02-25']
for date in dates:
    # if date == '2025-02-21':
    # stk_temp = stksfit_result_3(end_date=date, mode='pick', freqdata_source='local')
    # else:
    stk_temp = update_result_column(end_date=date, mode='First_Half', freqdata_source='local')
    print('finish date:', date)