{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["已连接到 base (Python 3.9.7)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["完成数据更新： 2025-09-02 \n", " 数据已保存到Excel文件: /Users/<USER>/PycharmProjects/AI_Stock/index_study/申万指数历史行情_1.xls\n", "完成申万行业指数数据替换： 2025-09-02\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "trade_date", "rawType": "object", "type": "string"}, {"name": "农林牧渔", "rawType": "float64", "type": "float"}, {"name": "基础化工", "rawType": "float64", "type": "float"}, {"name": "钢铁", "rawType": "float64", "type": "float"}, {"name": "有色金属", "rawType": "float64", "type": "float"}, {"name": "电子", "rawType": "float64", "type": "float"}, {"name": "家用电器", "rawType": "float64", "type": "float"}, {"name": "食品饮料", "rawType": "float64", "type": "float"}, {"name": "纺织服饰", "rawType": "float64", "type": "float"}, {"name": "轻工制造", "rawType": "float64", "type": "float"}, {"name": "医药生物", "rawType": "float64", "type": "float"}, {"name": "公用事业", "rawType": "float64", "type": "float"}, {"name": "交通运输", "rawType": "float64", "type": "float"}, {"name": "房地产", "rawType": "float64", "type": "float"}, {"name": "商贸零售", "rawType": "float64", "type": "float"}, {"name": "社会服务", "rawType": "float64", "type": "float"}, {"name": "综合", "rawType": "float64", "type": "float"}, {"name": "建筑材料", "rawType": "float64", "type": "float"}, {"name": "建筑装饰", "rawType": "float64", "type": "float"}, {"name": "电力设备", "rawType": "float64", "type": "float"}, {"name": "国防军工", "rawType": "float64", "type": "float"}, {"name": "计算机", "rawType": "float64", "type": "float"}, {"name": "传媒", "rawType": "float64", "type": "float"}, {"name": "通信", "rawType": "float64", "type": "float"}, {"name": "银行", "rawType": "float64", "type": "float"}, {"name": "非银金融", "rawType": "float64", "type": "float"}, {"name": "汽车", "rawType": "float64", "type": "float"}, {"name": "机械设备", "rawType": "float64", "type": "float"}, {"name": "煤炭", "rawType": "float64", "type": "float"}, {"name": "石油石化", "rawType": "float64", "type": "float"}, {"name": "环保", "rawType": "float64", "type": "float"}, {"name": "美容护理", "rawType": "float64", "type": "float"}], "ref": "1190a72a-8f05-4a58-b2ca-4bc3360eb0f0", "rows": [["0", "2025-09-02", "2981.89", "4004.26", "2473.19", "6561.01", "5978.22", "8992.01", "17755.82", "1644.6", "2263.41", "9218.69", "2437.49", "2229.79", "2227.79", "2372.06", "9033.82", "3951.73", "5138.05", "2058.9", "8358.6", "1833.86", "5550.46", "820.26", "4809.36", "4301.65", "2069.39", "7832.99", "1975.34", "2639.88", "2345.57", "1932.71", "4999.62"]], "shape": {"columns": 32, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trade_date</th>\n", "      <th>农林牧渔</th>\n", "      <th>基础化工</th>\n", "      <th>钢铁</th>\n", "      <th>有色金属</th>\n", "      <th>电子</th>\n", "      <th>家用电器</th>\n", "      <th>食品饮料</th>\n", "      <th>纺织服饰</th>\n", "      <th>轻工制造</th>\n", "      <th>...</th>\n", "      <th>传媒</th>\n", "      <th>通信</th>\n", "      <th>银行</th>\n", "      <th>非银金融</th>\n", "      <th>汽车</th>\n", "      <th>机械设备</th>\n", "      <th>煤炭</th>\n", "      <th>石油石化</th>\n", "      <th>环保</th>\n", "      <th>美容护理</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-09-02</td>\n", "      <td>2981.89</td>\n", "      <td>4004.26</td>\n", "      <td>2473.19</td>\n", "      <td>6561.01</td>\n", "      <td>5978.22</td>\n", "      <td>8992.01</td>\n", "      <td>17755.82</td>\n", "      <td>1644.6</td>\n", "      <td>2263.41</td>\n", "      <td>...</td>\n", "      <td>820.26</td>\n", "      <td>4809.36</td>\n", "      <td>4301.65</td>\n", "      <td>2069.39</td>\n", "      <td>7832.99</td>\n", "      <td>1975.34</td>\n", "      <td>2639.88</td>\n", "      <td>2345.57</td>\n", "      <td>1932.71</td>\n", "      <td>4999.62</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 32 columns</p>\n", "</div>"], "text/plain": ["   trade_date     农林牧渔     基础化工       钢铁     有色金属       电子     家用电器      食品饮料  \\\n", "0  2025-09-02  2981.89  4004.26  2473.19  6561.01  5978.22  8992.01  17755.82   \n", "\n", "     纺织服饰     轻工制造  ...      传媒       通信       银行     非银金融       汽车     机械设备  \\\n", "0  1644.6  2263.41  ...  820.26  4809.36  4301.65  2069.39  7832.99  1975.34   \n", "\n", "        煤炭     石油石化       环保     美容护理  \n", "0  2639.88  2345.57  1932.71  4999.62  \n", "\n", "[1 rows x 32 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# 更新当日数据\n", "from function_ai.swindex_crap import crap_swindex_from_web, level1_update\n", "crap = crap_swindex_from_web(end_date='2025-09-02', mode='today')\n", "level1_update(mode='replace')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["完成数据更新： 2025-09-01 \n", " 数据已保存到Excel文件: /Users/<USER>/PycharmProjects/AI_Stock/index_study/申万指数历史行情_1.xls\n", "完成申万行业指数数据替换： 2025-09-01\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "trade_date", "rawType": "object", "type": "string"}, {"name": "农林牧渔", "rawType": "float64", "type": "float"}, {"name": "基础化工", "rawType": "float64", "type": "float"}, {"name": "钢铁", "rawType": "float64", "type": "float"}, {"name": "有色金属", "rawType": "float64", "type": "float"}, {"name": "电子", "rawType": "float64", "type": "float"}, {"name": "家用电器", "rawType": "float64", "type": "float"}, {"name": "食品饮料", "rawType": "float64", "type": "float"}, {"name": "纺织服饰", "rawType": "float64", "type": "float"}, {"name": "轻工制造", "rawType": "float64", "type": "float"}, {"name": "医药生物", "rawType": "float64", "type": "float"}, {"name": "公用事业", "rawType": "float64", "type": "float"}, {"name": "交通运输", "rawType": "float64", "type": "float"}, {"name": "房地产", "rawType": "float64", "type": "float"}, {"name": "商贸零售", "rawType": "float64", "type": "float"}, {"name": "社会服务", "rawType": "float64", "type": "float"}, {"name": "综合", "rawType": "float64", "type": "float"}, {"name": "建筑材料", "rawType": "float64", "type": "float"}, {"name": "建筑装饰", "rawType": "float64", "type": "float"}, {"name": "电力设备", "rawType": "float64", "type": "float"}, {"name": "国防军工", "rawType": "float64", "type": "float"}, {"name": "计算机", "rawType": "float64", "type": "float"}, {"name": "传媒", "rawType": "float64", "type": "float"}, {"name": "通信", "rawType": "float64", "type": "float"}, {"name": "银行", "rawType": "float64", "type": "float"}, {"name": "非银金融", "rawType": "float64", "type": "float"}, {"name": "汽车", "rawType": "float64", "type": "float"}, {"name": "机械设备", "rawType": "float64", "type": "float"}, {"name": "煤炭", "rawType": "float64", "type": "float"}, {"name": "石油石化", "rawType": "float64", "type": "float"}, {"name": "环保", "rawType": "float64", "type": "float"}, {"name": "美容护理", "rawType": "float64", "type": "float"}], "ref": "41746b57-2bc5-4d74-b480-1d50c7221705", "rows": [["0", "2025-09-01", "3002.9", "4090.04", "2498.6", "6635.29", "6218.8", "8915.02", "17757.95", "1646.62", "2292.79", "9316.77", "2414.74", "2244.79", "2246.31", "2407.09", "9176.78", "3937.71", "5261.71", "2078.45", "8488.8", "1882.45", "5786.18", "836.72", "5102.61", "4220.8", "2091.09", "7804.0", "1998.13", "2643.49", "2337.82", "1950.93", "5078.77"]], "shape": {"columns": 32, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trade_date</th>\n", "      <th>农林牧渔</th>\n", "      <th>基础化工</th>\n", "      <th>钢铁</th>\n", "      <th>有色金属</th>\n", "      <th>电子</th>\n", "      <th>家用电器</th>\n", "      <th>食品饮料</th>\n", "      <th>纺织服饰</th>\n", "      <th>轻工制造</th>\n", "      <th>...</th>\n", "      <th>传媒</th>\n", "      <th>通信</th>\n", "      <th>银行</th>\n", "      <th>非银金融</th>\n", "      <th>汽车</th>\n", "      <th>机械设备</th>\n", "      <th>煤炭</th>\n", "      <th>石油石化</th>\n", "      <th>环保</th>\n", "      <th>美容护理</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-09-01</td>\n", "      <td>3002.9</td>\n", "      <td>4090.04</td>\n", "      <td>2498.6</td>\n", "      <td>6635.29</td>\n", "      <td>6218.8</td>\n", "      <td>8915.02</td>\n", "      <td>17757.95</td>\n", "      <td>1646.62</td>\n", "      <td>2292.79</td>\n", "      <td>...</td>\n", "      <td>836.72</td>\n", "      <td>5102.61</td>\n", "      <td>4220.8</td>\n", "      <td>2091.09</td>\n", "      <td>7804.0</td>\n", "      <td>1998.13</td>\n", "      <td>2643.49</td>\n", "      <td>2337.82</td>\n", "      <td>1950.93</td>\n", "      <td>5078.77</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 32 columns</p>\n", "</div>"], "text/plain": ["   trade_date    农林牧渔     基础化工      钢铁     有色金属      电子     家用电器      食品饮料  \\\n", "0  2025-09-01  3002.9  4090.04  2498.6  6635.29  6218.8  8915.02  17757.95   \n", "\n", "      纺织服饰     轻工制造  ...      传媒       通信      银行     非银金融      汽车     机械设备  \\\n", "0  1646.62  2292.79  ...  836.72  5102.61  4220.8  2091.09  7804.0  1998.13   \n", "\n", "        煤炭     石油石化       环保     美容护理  \n", "0  2643.49  2337.82  1950.93  5078.77  \n", "\n", "[1 rows x 32 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# 更新昨日数据\n", "from function_ai.swindex_crap import crap_swindex_from_web, level1_update\n", "crap = crap_swindex_from_web(end_date='2025-09-01', mode='history')\n", "level1_update(mode='replace')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkQuota_Func_V7 import stksfit_result_3\n", "stk_temp, _ = stksfit_result_3(end_date='2024-12-24', mode='pick')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["starttime:  2025-08-28 10:08:32.492498\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 5153/5153 [11:03<00:00,  7.76it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["FirstHalf ResultCommon has been stored!\n", "计算Gap指标：\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 5153/5153 [04:15<00:00, 20.15it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SecondHalf ResultGapValue has been stored!\n", "endtime:  2025-08-28 13:31:00.555436\n", "耗时：  3:22:28.062938\n", "finish date:  2025-07-17\n", "finish date: 2025-07-17\n", "starttime:  2025-08-28 13:31:01.588233\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 5153/5153 [11:00<00:00,  7.80it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["FirstHalf ResultCommon has been stored!\n", "计算Gap指标：\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 5153/5153 [03:54<00:00, 21.95it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SecondHalf ResultGapValue has been stored!\n", "endtime:  2025-08-28 16:57:16.784877\n", "耗时：  3:26:15.196644\n", "finish date:  2025-08-13\n", "finish date: 2025-08-13\n"]}], "source": ["from function_ai.StkQuota_Func_V7 import stksfit_result_3\n", "from function_ai.Func_Base import get_trade_date\n", "\n", "# dates = ['2024-05-09', '2024-05-13', '2024-05-17']\n", "dates = ['2025-07-17', '2025-08-13']\n", "for date in dates:\n", "    # if date == '2025-06-23':\n", "        # stk_temp = stksfit_result_3(end_date=date, cal_mode='First_Half', freqdata_source='local', storemode=True) \n", "    # else:\n", "    stk_temp = stksfit_result_3(end_date=date, cal_mode='All', freqdata_source='local', storemode=True)      \n", "    print('finish date:', date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkQuota_Func_V7 import update_result_column, stksfit_result_3\n", "from function_ai.Func_Base import get_trade_date\n", "\n", "trade_dates = get_trade_date()\n", "dates = trade_dates[(trade_dates>'2025-08-18') & (trade_dates<='2025-08-21')]\n", "\n", "# dates = ['2023-12-11', '2023-11-21', '2023-09-22', '2023-12-28']\n", "for date in dates:\n", "    if date == '2025-08-13':\n", "        continue\n", "    # if date == '2024-09-06':\n", "        # stk_temp = update_result_column(end_date=date, mode='Second_Half', freqdata_source='local')\n", "    # else:\n", "    stk_temp = stksfit_result_3(end_date=date, cal_mode='All', freqdata_source='local', storemode=True)      \n", "    print('finish date:', date)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2024-07-03'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["result_break = stk_temp[0]\n", "result_break['Cal_Date'].iloc[-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-06-13T05:52:27.824365Z", "start_time": "2025-06-13T05:15:53.778921Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["starttime:  2025-09-02 13:43:48.322064\n", "已读取存储的First_Half数据!\n", "计算Gap指标：\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 5153/5153 [04:32<00:00, 18.90it/s]\n"]}], "source": ["from function_ai.StkQuota_Func_V7 import update_result_column, stksfit_result_3\n", "from function_ai.Func_Base import get_trade_date\n", "\n", "dates = ['2025-09-01']\n", "for date in dates:\n", "    stk_temp = stksfit_result_3(end_date=date, cal_mode='Second_Half', freqdata_source='local', storemode=True)\n", "    print('finish date:', date)\n", "    "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理进度: 99.88% (成功: 5150, 失败: 0)\n", "处理完成!\n", "总股票数: 5156\n", "已处理: 5156\n", "成功: 5156\n", "失败: 0\n", "完成率: 100.00%\n", "耗时(秒): 977.64\n", "starttime:  2025-09-01 17:40:22.745746\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 5153/5153 [09:34<00:00,  8.98it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["FirstHalf ResultCommon has been stored!\n", "计算Gap指标：\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 5153/5153 [04:19<00:00, 19.85it/s]\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/DailyGap_Func.py:1167: FutureWarning: The behavior of Series.idxmax with all-NA values, or any-NA and skipna=False, is deprecated. In a future version this will raise ValueError\n", "  postprenow_maxeff_date = postprenow_eff.idxmax()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SecondHalf ResultGapValue has been stored!\n", "endtime:  2025-09-01 21:01:37.348314\n", "耗时：  3:21:14.602568\n", "finish date:  2025-09-01\n", "finish date: 2025-09-01\n"]}], "source": ["from function_ai.StkQuota_Func_V7 import update_result_column, stksfit_result_3\n", "from data_update.freqdata_func_tdx import process_tdx_freqdata\n", "from function_ai.Func_Base import get_trade_date\n", "process_tdx_freqdata()\n", "\n", "dates = ['2025-09-01']\n", "for date in dates:\n", "    stk_temp = stksfit_result_3(end_date=date, cal_mode='All', freqdata_source='local', storemode=True)\n", "    print('finish date:', date)\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from data_update.dailydata_func_tdx import read_day_data, process_tdx_daydata\n", "from data_update.freqdata_func_tdx import process_tdx_freqdata\n", "# file_path = '/Users/<USER>/PycharmProjects/Stock_Data_Files/vipdoc/sh/lday/sh600104.day'\n", "# df = read_day_data(file_path=file_path)\n", "# process_tdx_daydata()\n", "process_tdx_freqdata()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sqlalchemy import create_engine, text\n", "import config.config_<PERSON> as config\n", "conf = config.configModel()\n", "engine = create_engine(\n", "    'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(\n", "        conf.DC_DB_PORT) + '/stocksfit')\n", "import pandas as pd\n", "pd.io.sql.to_sql(result_break, 'stk_results_3', engine, index=False, schema='stocksfit',\n", "                             if_exists='append')\n", "engine.dispose()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 2}