{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:12:12.188361Z", "start_time": "2025-05-12T00:12:12.185088Z"}, "collapsed": false}, "outputs": [], "source": ["import sys\n", "sys.path.append('../')\n", "\n", "import pandas as pd\n", "from datetime import datetime\n", "from dateutil.relativedelta import relativedelta\n", "import matplotlib.pyplot as plt\n", "from function_ai.swindex_funcs import cal_sw_diffratio, cal_swindex_state, get_swindex_data,\\\n", "    judge_index_peak, judge_index_valley, get_zzindex_data, cal_sw_periodratio, \\\n", "    cal_prepeak_date, cal_recent_valley\n", "\n", "plt.rcParams[\"font.sans-serif\"]=[\"SimHei\"] #设置字体\n", "plt.rcParams[\"axes.unicode_minus\"]=False #该语句解决图像中的“-”负号的乱码问题"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:12:12.193710Z", "start_time": "2025-05-12T00:12:12.191840Z"}, "collapsed": false}, "outputs": [], "source": ["end_date, Recent_TurnDate  = '2025-06-27', '2025-06-19'\n", "Trend_TurnDate, PreTrend_StartDate = '2025-04-08', '2025-03-06'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:12:12.244840Z", "start_time": "2025-05-12T00:12:12.243439Z"}}, "outputs": [], "source": ["# end_date, Recent_TurnDate = '2024-08-08', '2024-08-05'\n", "# Trend_TurnDate, PreTrend_StartDate ='2024-05-20' , '2024-04-16'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:12:12.270945Z", "start_time": "2025-05-12T00:12:12.269576Z"}, "collapsed": false}, "outputs": [], "source": ["# end_date, Recent_TurnDate = '2023-06-27', '2023-06-21'\n", "# Trend_TurnDate, PreTrend_StartDate = '2023-04-18', '2023-03-16'"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:37.767982Z", "start_time": "2025-05-12T00:12:12.296147Z"}, "collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/index_study/../function_ai/swindex_funcs.py:381: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/index_study/../function_ai/swindex_funcs.py:565: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/index_study/../function_ai/swindex_funcs.py:636: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/index_study/../function_ai/swindex_funcs.py:381: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/index_study/../function_ai/swindex_funcs.py:565: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/index_study/../function_ai/swindex_funcs.py:636: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/index_study/../function_ai/swindex_funcs.py:381: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/index_study/../function_ai/swindex_funcs.py:565: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/index_study/../function_ai/swindex_funcs.py:636: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["指数底部日期： 2025-04-09\n", "\n", "指数底部日跌幅： 1.275 %  //\t指数底部跌幅排名分位： 1.0\n", "\n", "测算前一波峰日期为: 2024-10-08\n"]}], "source": ["zzindex_daily_df = get_zzindex_data()\n", "prepeak_date = cal_prepeak_date(zzindex_daily_df, end_date)\n", "PreDrop_StartDate = Trend_TurnDate\n", "Bottom_List, Cumret_List, Rise_List = cal_swindex_state(end_date=end_date, index_preturn_date=Recent_TurnDate)\n", "\n", "nowmonth_end_date = pd.Timestamp(end_date)\n", "premonth_date = nowmonth_end_date - relativedelta(months=1)\n", "pre2month_date = nowmonth_end_date - relativedelta(months=2)\n", "premonth_last_date = datetime(premonth_date.year, premonth_date.month, 27 \n", "                              if premonth_date.month==2 else 30).strftime('%Y-%m-%d')\n", "pre2month_last_date = datetime(pre2month_date.year, pre2month_date.month, 27 \n", "                              if pre2month_date.month==2 else 30).strftime('%Y-%m-%d')\n", "PreMonth_DropStartDate = cal_prepeak_date(zzindex_daily_df, premonth_last_date)\n", "PreM_Bottom_List, _, _ = cal_swindex_state(end_date=premonth_last_date, index_preturn_date=PreMonth_DropStartDate)\n", "\n", "Pre2Month_DropStartDate = cal_prepeak_date(zzindex_daily_df, pre2month_last_date)\n", "Pre2M_Bottom_List, _, _ = cal_swindex_state(end_date=pre2month_last_date, index_preturn_date=Pre2Month_DropStartDate)\n", "\n", "index_MinDate = zzindex_daily_df.loc[PreDrop_StartDate:end_date, 'low'].idxmin()\n", "zzindex_daily_df['Ratio'] = round((zzindex_daily_df['close'] / zzindex_daily_df['close'].shift(1) - 1) * 100, 3)\n", "zzindex_adj = zzindex_daily_df.loc[PreDrop_StartDate:index_MinDate].copy()\n", "zzindex_adj['Rank'] = zzindex_adj['Ratio'].rank()\n", "\n", "print('指数底部日期：', index_MinDate)\n", "print('\\n指数底部日跌幅：' ,zzindex_adj['Ratio'].iloc[-1],'%  //\\t指数底部跌幅排名分位：', zzindex_adj['Rank'].iloc[-1])\n", "\n", "print('\\n测算前一波峰日期为:', prepeak_date)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:37.804187Z", "start_time": "2025-05-12T00:13:37.793877Z"}, "collapsed": false}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "indus", "rawType": "object", "type": "string"}, {"name": "MaxRise", "rawType": "float64", "type": "float"}, {"name": "AvgRatio", "rawType": "float64", "type": "float"}, {"name": "start_date", "rawType": "object", "type": "string"}, {"name": "end_date", "rawType": "object", "type": "string"}, {"name": "Rise_Lastdays", "rawType": "object", "type": "unknown"}, {"name": "Recent_Rise_EndDate", "rawType": "object", "type": "unknown"}, {"name": "Recent_Rise_Sum", "rawType": "float64", "type": "float"}, {"name": "Recent_Rise_Avg", "rawType": "float64", "type": "float"}, {"name": "Recent_Rise_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Recent2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "RiseEnd2Now_Lastdays", "rawType": "object", "type": "unknown"}, {"name": "Rise_Seg_MaxRise_EndDate", "rawType": "object", "type": "unknown"}, {"name": "Rise_Seg_MaxRise_Sum", "rawType": "float64", "type": "float"}, {"name": "Rise_Seg_MaxRise_Avg", "rawType": "float64", "type": "float"}, {"name": "Rise_Seg_MaxRise_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Rise_Seg_Over1_Num", "rawType": "object", "type": "unknown"}, {"name": "In_Period", "rawType": "object", "type": "string"}], "ref": "62ab7738-2550-4771-8c8f-11bfd47a2573", "rows": [["6", "食品饮料", "0.0", "0.0", "2025-06-27", "2025-06-27", "1", null, "0.0", "0.0", "0", "0.0", "1", null, "0.0", "0.0", "0", "0", "True"], ["23", "银行", "33.42", "0.22", "2024-11-11", "2025-06-26", "152", "2025-06-23", "4.556", "1.139", "4", "0.136", "2", "2025-04-08", "6.526", "1.631", "4", "6", "-"], ["28", "石油石化", "9.38", "0.132", "2025-03-06", "2025-06-19", "71", "2025-06-10", "1.632", "0.544", "3", "0.174", "7", "2025-05-28", "1.686", "0.562", "3", "0", "-"], ["3", "有色金属", "17.43", "0.163", "2024-12-31", "2025-06-13", "107", "2025-06-13", "5.9", "0.983", "6", "0.338", "11", "2025-04-14", "4.706", "1.569", "3", "3", "-"], ["9", "医药生物", "11.66", "0.131", "2025-01-24", "2025-06-12", "89", "2025-06-04", "3.637", "0.909", "4", "0.312", "12", "2025-06-04", "3.637", "0.909", "4", "0", "-"], ["1", "基础化工", "8.42", "0.054", "2024-10-18", "2025-06-09", "155", "2025-05-27", "2.322", "0.774", "3", "0.276", "15", "2024-11-21", "3.115", "1.038", "3", "1", "-"], ["7", "纺织服饰", "17.88", "0.112", "2024-10-09", "2025-06-04", "159", "2025-05-28", "3.528", "1.176", "3", "0.197", "18", "2024-12-16", "6.339", "1.585", "4", "2", "-"], ["30", "美容护理", "17.7", "0.096", "2024-08-26", "2025-06-04", "184", "2025-05-28", "1.738", "0.579", "3", "0.098", "18", "2024-10-08", "13.498", "3.375", "4", "2", "-"], ["11", "交通运输", "8.29", "0.124", "2025-02-21", "2025-05-30", "67", "2025-05-28", "2.38", "0.793", "3", "0.287", "20", "2025-04-09", "4.229", "1.057", "4", "1", "-"], ["29", "环保", "16.84", "0.081", "2024-07-19", "2025-05-29", "207", "2025-05-29", "4.688", "0.938", "5", "0.278", "21", "2024-10-16", "3.336", "1.112", "3", "2", "-"], ["10", "公用事业", "11.02", "0.298", "2025-03-06", "2025-04-28", "37", "2025-04-28", "3.217", "1.072", "3", "0.292", "41", "2025-04-08", "5.34", "1.78", "3", "3", "-"], ["16", "建筑材料", "8.37", "0.254", "2025-02-21", "2025-04-09", "33", "2025-04-09", "3.246", "0.812", "4", "0.388", "54", "2024-10-16", "6.406", "1.281", "5", "1", "-"], ["0", "农林牧渔", "16.86", "0.527", "2025-02-21", "2025-04-08", "32", "2025-04-08", "13.044", "3.261", "4", "0.774", "55", "2025-04-08", "13.044", "3.261", "4", "1", "-"], ["27", "煤炭", "10.19", "0.443", "2025-03-06", "2025-04-08", "23", "2025-04-08", "3.17", "1.057", "3", "0.311", "55", "2025-04-08", "3.17", "1.057", "3", "1", "-"], ["25", "汽车", "37.18", "0.139", "2024-02-06", "2025-03-20", "267", "2025-03-20", "5.837", "1.167", "5", "0.157", "67", "2024-02-27", "8.401", "1.68", "5", "6", "-"], ["2", "钢铁", "12.86", "0.122", "2024-10-09", "2025-03-13", "105", "2025-03-13", "3.418", "1.139", "3", "0.266", "72", "2024-11-01", "3.512", "1.171", "3", "2", "-"], ["26", "机械设备", "29.36", "0.189", "2024-07-17", "2025-03-10", "155", "2025-03-10", "5.874", "1.175", "5", "0.2", "75", "2025-02-26", "8.639", "1.44", "6", "4", "-"], ["20", "计算机", "38.17", "0.308", "2024-08-27", "2025-03-06", "124", "2025-03-06", "5.891", "1.473", "4", "0.154", "77", "2025-02-10", "12.364", "3.091", "4", "7", "-"], ["4", "电子", "36.78", "0.375", "2024-09-26", "2025-02-26", "98", "2025-02-26", "8.371", "1.395", "6", "0.228", "83", "2024-10-09", "15.726", "3.931", "4", "6", "-"], ["22", "通信", "38.07", "0.154", "2024-02-07", "2025-02-21", "247", "2025-02-21", "5.917", "1.972", "3", "0.155", "86", "2024-02-20", "8.051", "2.684", "3", "10", "-"], ["21", "传媒", "25.73", "0.192", "2024-07-24", "2025-02-14", "134", "2025-02-14", "6.63", "1.326", "5", "0.258", "91", "2025-02-14", "6.63", "1.326", "5", "8", "-"], ["5", "家用电器", "17.23", "0.152", "2024-07-25", "2025-01-09", "113", "2025-01-09", "5.807", "1.452", "4", "0.337", "111", "2025-01-09", "5.807", "1.452", "4", "6", "-"], ["13", "商贸零售", "35.44", "0.331", "2024-07-10", "2024-12-16", "107", "2024-12-16", "11.568", "2.314", "5", "0.326", "128", "2024-12-16", "11.568", "2.314", "5", "4", "-"], ["15", "综合", "35.02", "0.294", "2024-06-24", "2024-12-16", "119", "2024-12-16", "6.415", "1.604", "4", "0.183", "128", "2024-12-03", "8.024", "2.006", "4", "5", "-"], ["14", "社会服务", "22.06", "0.227", "2024-07-24", "2024-12-16", "97", "2024-12-16", "6.807", "1.361", "5", "0.309", "128", "2024-12-16", "6.807", "1.361", "5", "3", "-"], ["8", "轻工制造", "17.02", "0.177", "2024-07-24", "2024-12-13", "96", "2024-12-13", "4.076", "1.359", "3", "0.239", "129", "2024-10-28", "4.646", "1.549", "3", "2", "-"], ["17", "建筑装饰", "12.13", "0.282", "2024-10-09", "2024-12-06", "43", "2024-11-18", "3.92", "0.98", "4", "0.323", "134", "2024-10-16", "4.374", "1.458", "3", "1", "-"], ["18", "电力设备", "15.62", "0.17", "2024-07-10", "2024-11-25", "92", "2024-11-13", "3.834", "0.958", "4", "0.245", "143", "2024-10-08", "9.244", "3.081", "3", "2", "-"], ["19", "国防军工", "17.53", "0.626", "2024-09-26", "2024-11-11", "28", "2024-11-06", "6.368", "2.123", "3", "0.363", "153", "2024-11-06", "6.368", "2.123", "3", "3", "-"], ["24", "非银金融", "39.74", "0.286", "2024-04-12", "2024-11-07", "139", "2024-10-18", "2.967", "0.989", "3", "0.075", "155", "2024-10-09", "15.358", "2.194", "7", "1", "-"], ["12", "房地产", "20.83", "0.26", "2024-07-10", "2024-11-07", "80", "2024-11-07", "4.801", "1.6", "3", "0.23", "155", "2024-10-16", "10.18", "2.545", "4", "6", "-"]], "shape": {"columns": 18, "rows": 31}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>indus</th>\n", "      <th>MaxRise</th>\n", "      <th>AvgRatio</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>Rise_Lastdays</th>\n", "      <th>Recent_Rise_EndDate</th>\n", "      <th>Recent_Rise_Sum</th>\n", "      <th>Recent_Rise_Avg</th>\n", "      <th>Recent_Rise_LastDays</th>\n", "      <th>Recent2Max_Ratio</th>\n", "      <th>RiseEnd2Now_Lastdays</th>\n", "      <th>Rise_Seg_MaxRise_EndDate</th>\n", "      <th>Rise_Seg_MaxRise_Sum</th>\n", "      <th>Rise_Seg_MaxRise_Avg</th>\n", "      <th>Rise_Seg_MaxRise_LastDays</th>\n", "      <th>Rise_Seg_Over1_Num</th>\n", "      <th>In_Period</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>食品饮料</td>\n", "      <td>0.00</td>\n", "      <td>0.000</td>\n", "      <td>2025-06-27</td>\n", "      <td>2025-06-27</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>银行</td>\n", "      <td>33.42</td>\n", "      <td>0.220</td>\n", "      <td>2024-11-11</td>\n", "      <td>2025-06-26</td>\n", "      <td>152</td>\n", "      <td>2025-06-23</td>\n", "      <td>4.556</td>\n", "      <td>1.139</td>\n", "      <td>4</td>\n", "      <td>0.136</td>\n", "      <td>2</td>\n", "      <td>2025-04-08</td>\n", "      <td>6.526</td>\n", "      <td>1.631</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>石油石化</td>\n", "      <td>9.38</td>\n", "      <td>0.132</td>\n", "      <td>2025-03-06</td>\n", "      <td>2025-06-19</td>\n", "      <td>71</td>\n", "      <td>2025-06-10</td>\n", "      <td>1.632</td>\n", "      <td>0.544</td>\n", "      <td>3</td>\n", "      <td>0.174</td>\n", "      <td>7</td>\n", "      <td>2025-05-28</td>\n", "      <td>1.686</td>\n", "      <td>0.562</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>有色金属</td>\n", "      <td>17.43</td>\n", "      <td>0.163</td>\n", "      <td>2024-12-31</td>\n", "      <td>2025-06-13</td>\n", "      <td>107</td>\n", "      <td>2025-06-13</td>\n", "      <td>5.900</td>\n", "      <td>0.983</td>\n", "      <td>6</td>\n", "      <td>0.338</td>\n", "      <td>11</td>\n", "      <td>2025-04-14</td>\n", "      <td>4.706</td>\n", "      <td>1.569</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>医药生物</td>\n", "      <td>11.66</td>\n", "      <td>0.131</td>\n", "      <td>2025-01-24</td>\n", "      <td>2025-06-12</td>\n", "      <td>89</td>\n", "      <td>2025-06-04</td>\n", "      <td>3.637</td>\n", "      <td>0.909</td>\n", "      <td>4</td>\n", "      <td>0.312</td>\n", "      <td>12</td>\n", "      <td>2025-06-04</td>\n", "      <td>3.637</td>\n", "      <td>0.909</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>基础化工</td>\n", "      <td>8.42</td>\n", "      <td>0.054</td>\n", "      <td>2024-10-18</td>\n", "      <td>2025-06-09</td>\n", "      <td>155</td>\n", "      <td>2025-05-27</td>\n", "      <td>2.322</td>\n", "      <td>0.774</td>\n", "      <td>3</td>\n", "      <td>0.276</td>\n", "      <td>15</td>\n", "      <td>2024-11-21</td>\n", "      <td>3.115</td>\n", "      <td>1.038</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>纺织服饰</td>\n", "      <td>17.88</td>\n", "      <td>0.112</td>\n", "      <td>2024-10-09</td>\n", "      <td>2025-06-04</td>\n", "      <td>159</td>\n", "      <td>2025-05-28</td>\n", "      <td>3.528</td>\n", "      <td>1.176</td>\n", "      <td>3</td>\n", "      <td>0.197</td>\n", "      <td>18</td>\n", "      <td>2024-12-16</td>\n", "      <td>6.339</td>\n", "      <td>1.585</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>美容护理</td>\n", "      <td>17.70</td>\n", "      <td>0.096</td>\n", "      <td>2024-08-26</td>\n", "      <td>2025-06-04</td>\n", "      <td>184</td>\n", "      <td>2025-05-28</td>\n", "      <td>1.738</td>\n", "      <td>0.579</td>\n", "      <td>3</td>\n", "      <td>0.098</td>\n", "      <td>18</td>\n", "      <td>2024-10-08</td>\n", "      <td>13.498</td>\n", "      <td>3.375</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>交通运输</td>\n", "      <td>8.29</td>\n", "      <td>0.124</td>\n", "      <td>2025-02-21</td>\n", "      <td>2025-05-30</td>\n", "      <td>67</td>\n", "      <td>2025-05-28</td>\n", "      <td>2.380</td>\n", "      <td>0.793</td>\n", "      <td>3</td>\n", "      <td>0.287</td>\n", "      <td>20</td>\n", "      <td>2025-04-09</td>\n", "      <td>4.229</td>\n", "      <td>1.057</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>环保</td>\n", "      <td>16.84</td>\n", "      <td>0.081</td>\n", "      <td>2024-07-19</td>\n", "      <td>2025-05-29</td>\n", "      <td>207</td>\n", "      <td>2025-05-29</td>\n", "      <td>4.688</td>\n", "      <td>0.938</td>\n", "      <td>5</td>\n", "      <td>0.278</td>\n", "      <td>21</td>\n", "      <td>2024-10-16</td>\n", "      <td>3.336</td>\n", "      <td>1.112</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>公用事业</td>\n", "      <td>11.02</td>\n", "      <td>0.298</td>\n", "      <td>2025-03-06</td>\n", "      <td>2025-04-28</td>\n", "      <td>37</td>\n", "      <td>2025-04-28</td>\n", "      <td>3.217</td>\n", "      <td>1.072</td>\n", "      <td>3</td>\n", "      <td>0.292</td>\n", "      <td>41</td>\n", "      <td>2025-04-08</td>\n", "      <td>5.340</td>\n", "      <td>1.780</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>建筑材料</td>\n", "      <td>8.37</td>\n", "      <td>0.254</td>\n", "      <td>2025-02-21</td>\n", "      <td>2025-04-09</td>\n", "      <td>33</td>\n", "      <td>2025-04-09</td>\n", "      <td>3.246</td>\n", "      <td>0.812</td>\n", "      <td>4</td>\n", "      <td>0.388</td>\n", "      <td>54</td>\n", "      <td>2024-10-16</td>\n", "      <td>6.406</td>\n", "      <td>1.281</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>农林牧渔</td>\n", "      <td>16.86</td>\n", "      <td>0.527</td>\n", "      <td>2025-02-21</td>\n", "      <td>2025-04-08</td>\n", "      <td>32</td>\n", "      <td>2025-04-08</td>\n", "      <td>13.044</td>\n", "      <td>3.261</td>\n", "      <td>4</td>\n", "      <td>0.774</td>\n", "      <td>55</td>\n", "      <td>2025-04-08</td>\n", "      <td>13.044</td>\n", "      <td>3.261</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>煤炭</td>\n", "      <td>10.19</td>\n", "      <td>0.443</td>\n", "      <td>2025-03-06</td>\n", "      <td>2025-04-08</td>\n", "      <td>23</td>\n", "      <td>2025-04-08</td>\n", "      <td>3.170</td>\n", "      <td>1.057</td>\n", "      <td>3</td>\n", "      <td>0.311</td>\n", "      <td>55</td>\n", "      <td>2025-04-08</td>\n", "      <td>3.170</td>\n", "      <td>1.057</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>汽车</td>\n", "      <td>37.18</td>\n", "      <td>0.139</td>\n", "      <td>2024-02-06</td>\n", "      <td>2025-03-20</td>\n", "      <td>267</td>\n", "      <td>2025-03-20</td>\n", "      <td>5.837</td>\n", "      <td>1.167</td>\n", "      <td>5</td>\n", "      <td>0.157</td>\n", "      <td>67</td>\n", "      <td>2024-02-27</td>\n", "      <td>8.401</td>\n", "      <td>1.680</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>钢铁</td>\n", "      <td>12.86</td>\n", "      <td>0.122</td>\n", "      <td>2024-10-09</td>\n", "      <td>2025-03-13</td>\n", "      <td>105</td>\n", "      <td>2025-03-13</td>\n", "      <td>3.418</td>\n", "      <td>1.139</td>\n", "      <td>3</td>\n", "      <td>0.266</td>\n", "      <td>72</td>\n", "      <td>2024-11-01</td>\n", "      <td>3.512</td>\n", "      <td>1.171</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>机械设备</td>\n", "      <td>29.36</td>\n", "      <td>0.189</td>\n", "      <td>2024-07-17</td>\n", "      <td>2025-03-10</td>\n", "      <td>155</td>\n", "      <td>2025-03-10</td>\n", "      <td>5.874</td>\n", "      <td>1.175</td>\n", "      <td>5</td>\n", "      <td>0.200</td>\n", "      <td>75</td>\n", "      <td>2025-02-26</td>\n", "      <td>8.639</td>\n", "      <td>1.440</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>计算机</td>\n", "      <td>38.17</td>\n", "      <td>0.308</td>\n", "      <td>2024-08-27</td>\n", "      <td>2025-03-06</td>\n", "      <td>124</td>\n", "      <td>2025-03-06</td>\n", "      <td>5.891</td>\n", "      <td>1.473</td>\n", "      <td>4</td>\n", "      <td>0.154</td>\n", "      <td>77</td>\n", "      <td>2025-02-10</td>\n", "      <td>12.364</td>\n", "      <td>3.091</td>\n", "      <td>4</td>\n", "      <td>7</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>电子</td>\n", "      <td>36.78</td>\n", "      <td>0.375</td>\n", "      <td>2024-09-26</td>\n", "      <td>2025-02-26</td>\n", "      <td>98</td>\n", "      <td>2025-02-26</td>\n", "      <td>8.371</td>\n", "      <td>1.395</td>\n", "      <td>6</td>\n", "      <td>0.228</td>\n", "      <td>83</td>\n", "      <td>2024-10-09</td>\n", "      <td>15.726</td>\n", "      <td>3.931</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>通信</td>\n", "      <td>38.07</td>\n", "      <td>0.154</td>\n", "      <td>2024-02-07</td>\n", "      <td>2025-02-21</td>\n", "      <td>247</td>\n", "      <td>2025-02-21</td>\n", "      <td>5.917</td>\n", "      <td>1.972</td>\n", "      <td>3</td>\n", "      <td>0.155</td>\n", "      <td>86</td>\n", "      <td>2024-02-20</td>\n", "      <td>8.051</td>\n", "      <td>2.684</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>传媒</td>\n", "      <td>25.73</td>\n", "      <td>0.192</td>\n", "      <td>2024-07-24</td>\n", "      <td>2025-02-14</td>\n", "      <td>134</td>\n", "      <td>2025-02-14</td>\n", "      <td>6.630</td>\n", "      <td>1.326</td>\n", "      <td>5</td>\n", "      <td>0.258</td>\n", "      <td>91</td>\n", "      <td>2025-02-14</td>\n", "      <td>6.630</td>\n", "      <td>1.326</td>\n", "      <td>5</td>\n", "      <td>8</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>家用电器</td>\n", "      <td>17.23</td>\n", "      <td>0.152</td>\n", "      <td>2024-07-25</td>\n", "      <td>2025-01-09</td>\n", "      <td>113</td>\n", "      <td>2025-01-09</td>\n", "      <td>5.807</td>\n", "      <td>1.452</td>\n", "      <td>4</td>\n", "      <td>0.337</td>\n", "      <td>111</td>\n", "      <td>2025-01-09</td>\n", "      <td>5.807</td>\n", "      <td>1.452</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>商贸零售</td>\n", "      <td>35.44</td>\n", "      <td>0.331</td>\n", "      <td>2024-07-10</td>\n", "      <td>2024-12-16</td>\n", "      <td>107</td>\n", "      <td>2024-12-16</td>\n", "      <td>11.568</td>\n", "      <td>2.314</td>\n", "      <td>5</td>\n", "      <td>0.326</td>\n", "      <td>128</td>\n", "      <td>2024-12-16</td>\n", "      <td>11.568</td>\n", "      <td>2.314</td>\n", "      <td>5</td>\n", "      <td>4</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>综合</td>\n", "      <td>35.02</td>\n", "      <td>0.294</td>\n", "      <td>2024-06-24</td>\n", "      <td>2024-12-16</td>\n", "      <td>119</td>\n", "      <td>2024-12-16</td>\n", "      <td>6.415</td>\n", "      <td>1.604</td>\n", "      <td>4</td>\n", "      <td>0.183</td>\n", "      <td>128</td>\n", "      <td>2024-12-03</td>\n", "      <td>8.024</td>\n", "      <td>2.006</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>社会服务</td>\n", "      <td>22.06</td>\n", "      <td>0.227</td>\n", "      <td>2024-07-24</td>\n", "      <td>2024-12-16</td>\n", "      <td>97</td>\n", "      <td>2024-12-16</td>\n", "      <td>6.807</td>\n", "      <td>1.361</td>\n", "      <td>5</td>\n", "      <td>0.309</td>\n", "      <td>128</td>\n", "      <td>2024-12-16</td>\n", "      <td>6.807</td>\n", "      <td>1.361</td>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>轻工制造</td>\n", "      <td>17.02</td>\n", "      <td>0.177</td>\n", "      <td>2024-07-24</td>\n", "      <td>2024-12-13</td>\n", "      <td>96</td>\n", "      <td>2024-12-13</td>\n", "      <td>4.076</td>\n", "      <td>1.359</td>\n", "      <td>3</td>\n", "      <td>0.239</td>\n", "      <td>129</td>\n", "      <td>2024-10-28</td>\n", "      <td>4.646</td>\n", "      <td>1.549</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>建筑装饰</td>\n", "      <td>12.13</td>\n", "      <td>0.282</td>\n", "      <td>2024-10-09</td>\n", "      <td>2024-12-06</td>\n", "      <td>43</td>\n", "      <td>2024-11-18</td>\n", "      <td>3.920</td>\n", "      <td>0.980</td>\n", "      <td>4</td>\n", "      <td>0.323</td>\n", "      <td>134</td>\n", "      <td>2024-10-16</td>\n", "      <td>4.374</td>\n", "      <td>1.458</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>电力设备</td>\n", "      <td>15.62</td>\n", "      <td>0.170</td>\n", "      <td>2024-07-10</td>\n", "      <td>2024-11-25</td>\n", "      <td>92</td>\n", "      <td>2024-11-13</td>\n", "      <td>3.834</td>\n", "      <td>0.958</td>\n", "      <td>4</td>\n", "      <td>0.245</td>\n", "      <td>143</td>\n", "      <td>2024-10-08</td>\n", "      <td>9.244</td>\n", "      <td>3.081</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>国防军工</td>\n", "      <td>17.53</td>\n", "      <td>0.626</td>\n", "      <td>2024-09-26</td>\n", "      <td>2024-11-11</td>\n", "      <td>28</td>\n", "      <td>2024-11-06</td>\n", "      <td>6.368</td>\n", "      <td>2.123</td>\n", "      <td>3</td>\n", "      <td>0.363</td>\n", "      <td>153</td>\n", "      <td>2024-11-06</td>\n", "      <td>6.368</td>\n", "      <td>2.123</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>非银金融</td>\n", "      <td>39.74</td>\n", "      <td>0.286</td>\n", "      <td>2024-04-12</td>\n", "      <td>2024-11-07</td>\n", "      <td>139</td>\n", "      <td>2024-10-18</td>\n", "      <td>2.967</td>\n", "      <td>0.989</td>\n", "      <td>3</td>\n", "      <td>0.075</td>\n", "      <td>155</td>\n", "      <td>2024-10-09</td>\n", "      <td>15.358</td>\n", "      <td>2.194</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>房地产</td>\n", "      <td>20.83</td>\n", "      <td>0.260</td>\n", "      <td>2024-07-10</td>\n", "      <td>2024-11-07</td>\n", "      <td>80</td>\n", "      <td>2024-11-07</td>\n", "      <td>4.801</td>\n", "      <td>1.600</td>\n", "      <td>3</td>\n", "      <td>0.230</td>\n", "      <td>155</td>\n", "      <td>2024-10-16</td>\n", "      <td>10.180</td>\n", "      <td>2.545</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>-</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   indus  MaxRise  AvgRatio  start_date    end_date Rise_Lastdays  \\\n", "6   食品饮料     0.00     0.000  2025-06-27  2025-06-27             1   \n", "23    银行    33.42     0.220  2024-11-11  2025-06-26           152   \n", "28  石油石化     9.38     0.132  2025-03-06  2025-06-19            71   \n", "3   有色金属    17.43     0.163  2024-12-31  2025-06-13           107   \n", "9   医药生物    11.66     0.131  2025-01-24  2025-06-12            89   \n", "1   基础化工     8.42     0.054  2024-10-18  2025-06-09           155   \n", "7   纺织服饰    17.88     0.112  2024-10-09  2025-06-04           159   \n", "30  美容护理    17.70     0.096  2024-08-26  2025-06-04           184   \n", "11  交通运输     8.29     0.124  2025-02-21  2025-05-30            67   \n", "29    环保    16.84     0.081  2024-07-19  2025-05-29           207   \n", "10  公用事业    11.02     0.298  2025-03-06  2025-04-28            37   \n", "16  建筑材料     8.37     0.254  2025-02-21  2025-04-09            33   \n", "0   农林牧渔    16.86     0.527  2025-02-21  2025-04-08            32   \n", "27    煤炭    10.19     0.443  2025-03-06  2025-04-08            23   \n", "25    汽车    37.18     0.139  2024-02-06  2025-03-20           267   \n", "2     钢铁    12.86     0.122  2024-10-09  2025-03-13           105   \n", "26  机械设备    29.36     0.189  2024-07-17  2025-03-10           155   \n", "20   计算机    38.17     0.308  2024-08-27  2025-03-06           124   \n", "4     电子    36.78     0.375  2024-09-26  2025-02-26            98   \n", "22    通信    38.07     0.154  2024-02-07  2025-02-21           247   \n", "21    传媒    25.73     0.192  2024-07-24  2025-02-14           134   \n", "5   家用电器    17.23     0.152  2024-07-25  2025-01-09           113   \n", "13  商贸零售    35.44     0.331  2024-07-10  2024-12-16           107   \n", "15    综合    35.02     0.294  2024-06-24  2024-12-16           119   \n", "14  社会服务    22.06     0.227  2024-07-24  2024-12-16            97   \n", "8   轻工制造    17.02     0.177  2024-07-24  2024-12-13            96   \n", "17  建筑装饰    12.13     0.282  2024-10-09  2024-12-06            43   \n", "18  电力设备    15.62     0.170  2024-07-10  2024-11-25            92   \n", "19  国防军工    17.53     0.626  2024-09-26  2024-11-11            28   \n", "24  非银金融    39.74     0.286  2024-04-12  2024-11-07           139   \n", "12   房地产    20.83     0.260  2024-07-10  2024-11-07            80   \n", "\n", "   Recent_Rise_EndDate  Recent_Rise_Sum  Recent_Rise_Avg Recent_Rise_LastDays  \\\n", "6                 None            0.000            0.000                    0   \n", "23          2025-06-23            4.556            1.139                    4   \n", "28          2025-06-10            1.632            0.544                    3   \n", "3           2025-06-13            5.900            0.983                    6   \n", "9           2025-06-04            3.637            0.909                    4   \n", "1           2025-05-27            2.322            0.774                    3   \n", "7           2025-05-28            3.528            1.176                    3   \n", "30          2025-05-28            1.738            0.579                    3   \n", "11          2025-05-28            2.380            0.793                    3   \n", "29          2025-05-29            4.688            0.938                    5   \n", "10          2025-04-28            3.217            1.072                    3   \n", "16          2025-04-09            3.246            0.812                    4   \n", "0           2025-04-08           13.044            3.261                    4   \n", "27          2025-04-08            3.170            1.057                    3   \n", "25          2025-03-20            5.837            1.167                    5   \n", "2           2025-03-13            3.418            1.139                    3   \n", "26          2025-03-10            5.874            1.175                    5   \n", "20          2025-03-06            5.891            1.473                    4   \n", "4           2025-02-26            8.371            1.395                    6   \n", "22          2025-02-21            5.917            1.972                    3   \n", "21          2025-02-14            6.630            1.326                    5   \n", "5           2025-01-09            5.807            1.452                    4   \n", "13          2024-12-16           11.568            2.314                    5   \n", "15          2024-12-16            6.415            1.604                    4   \n", "14          2024-12-16            6.807            1.361                    5   \n", "8           2024-12-13            4.076            1.359                    3   \n", "17          2024-11-18            3.920            0.980                    4   \n", "18          2024-11-13            3.834            0.958                    4   \n", "19          2024-11-06            6.368            2.123                    3   \n", "24          2024-10-18            2.967            0.989                    3   \n", "12          2024-11-07            4.801            1.600                    3   \n", "\n", "    Recent2Max_Ratio RiseEnd2Now_Lastdays Rise_Seg_MaxRise_EndDate  \\\n", "6              0.000                    1                     None   \n", "23             0.136                    2               2025-04-08   \n", "28             0.174                    7               2025-05-28   \n", "3              0.338                   11               2025-04-14   \n", "9              0.312                   12               2025-06-04   \n", "1              0.276                   15               2024-11-21   \n", "7              0.197                   18               2024-12-16   \n", "30             0.098                   18               2024-10-08   \n", "11             0.287                   20               2025-04-09   \n", "29             0.278                   21               2024-10-16   \n", "10             0.292                   41               2025-04-08   \n", "16             0.388                   54               2024-10-16   \n", "0              0.774                   55               2025-04-08   \n", "27             0.311                   55               2025-04-08   \n", "25             0.157                   67               2024-02-27   \n", "2              0.266                   72               2024-11-01   \n", "26             0.200                   75               2025-02-26   \n", "20             0.154                   77               2025-02-10   \n", "4              0.228                   83               2024-10-09   \n", "22             0.155                   86               2024-02-20   \n", "21             0.258                   91               2025-02-14   \n", "5              0.337                  111               2025-01-09   \n", "13             0.326                  128               2024-12-16   \n", "15             0.183                  128               2024-12-03   \n", "14             0.309                  128               2024-12-16   \n", "8              0.239                  129               2024-10-28   \n", "17             0.323                  134               2024-10-16   \n", "18             0.245                  143               2024-10-08   \n", "19             0.363                  153               2024-11-06   \n", "24             0.075                  155               2024-10-09   \n", "12             0.230                  155               2024-10-16   \n", "\n", "    Rise_Seg_MaxRise_Sum  Rise_Seg_MaxRise_Avg Rise_Seg_MaxRise_LastDays  \\\n", "6                  0.000                 0.000                         0   \n", "23                 6.526                 1.631                         4   \n", "28                 1.686                 0.562                         3   \n", "3                  4.706                 1.569                         3   \n", "9                  3.637                 0.909                         4   \n", "1                  3.115                 1.038                         3   \n", "7                  6.339                 1.585                         4   \n", "30                13.498                 3.375                         4   \n", "11                 4.229                 1.057                         4   \n", "29                 3.336                 1.112                         3   \n", "10                 5.340                 1.780                         3   \n", "16                 6.406                 1.281                         5   \n", "0                 13.044                 3.261                         4   \n", "27                 3.170                 1.057                         3   \n", "25                 8.401                 1.680                         5   \n", "2                  3.512                 1.171                         3   \n", "26                 8.639                 1.440                         6   \n", "20                12.364                 3.091                         4   \n", "4                 15.726                 3.931                         4   \n", "22                 8.051                 2.684                         3   \n", "21                 6.630                 1.326                         5   \n", "5                  5.807                 1.452                         4   \n", "13                11.568                 2.314                         5   \n", "15                 8.024                 2.006                         4   \n", "14                 6.807                 1.361                         5   \n", "8                  4.646                 1.549                         3   \n", "17                 4.374                 1.458                         3   \n", "18                 9.244                 3.081                         3   \n", "19                 6.368                 2.123                         3   \n", "24                15.358                 2.194                         7   \n", "12                10.180                 2.545                         4   \n", "\n", "   Rise_Seg_Over1_Num In_Period  \n", "6                   0      True  \n", "23                  6         -  \n", "28                  0         -  \n", "3                   3         -  \n", "9                   0         -  \n", "1                   1         -  \n", "7                   2         -  \n", "30                  2         -  \n", "11                  1         -  \n", "29                  2         -  \n", "10                  3         -  \n", "16                  1         -  \n", "0                   1         -  \n", "27                  1         -  \n", "25                  6         -  \n", "2                   2         -  \n", "26                  4         -  \n", "20                  7         -  \n", "4                   6         -  \n", "22                 10         -  \n", "21                  8         -  \n", "5                   6         -  \n", "13                  4         -  \n", "15                  5         -  \n", "14                  3         -  \n", "8                   2         -  \n", "17                  1         -  \n", "18                  2         -  \n", "19                  3         -  \n", "24                  1         -  \n", "12                  6         -  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Rise_List['In_Period'] = Rise_List['start_date'].apply(lambda fn: 'True' if fn>=PreDrop_StartDate else '-')\n", "Rise_List = Rise_List.sort_values(['end_date', 'MaxRise'], ascending=[False, False])\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "Rise_List"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["### 回档幅度排序"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:37.882299Z", "start_time": "2025-05-12T00:13:37.869928Z"}, "collapsed": false}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "indus", "rawType": "object", "type": "string"}, {"name": "MaxDrop", "rawType": "float64", "type": "float"}, {"name": "start_date", "rawType": "object", "type": "string"}, {"name": "end_date", "rawType": "object", "type": "string"}, {"name": "Drop_Lastdays", "rawType": "object", "type": "unknown"}, {"name": "AvgDrop", "rawType": "float64", "type": "float"}, {"name": "End2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "End2Max_LastDays", "rawType": "object", "type": "unknown"}, {"name": "End2Max_MaxDate", "rawType": "object", "type": "string"}, {"name": "Indus_MV", "rawType": "float64", "type": "float"}, {"name": "Bottom_State", "rawType": "object", "type": "string"}, {"name": "Bottom_Date", "rawType": "object", "type": "string"}, {"name": "Bottom2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_EndDate", "rawType": "object", "type": "string"}, {"name": "Recent_Drop_Sum", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_Avg", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Drop_Seg_MaxDrop_EndDate", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_Sum", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_Avg", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Drop_Seg_MaxDrop_End_To_Now_Days", "rawType": "object", "type": "unknown"}, {"name": "Drop_Seg_Und1_Num", "rawType": "object", "type": "unknown"}, {"name": "Recent2Avg_Ratio", "rawType": "float64", "type": "float"}], "ref": "9d2b6d2b-d0e3-4c58-8f01-72f89d6262c2", "rows": [["0", "食品饮料", "-29.4", "2023-11-03", "2025-06-27", "399", "-0.074", "0.0", "0", "2025-06-27", "45208.08", "True", "2025-06-27", "0.0", "2025-06-27", "-2.596", "-0.519", "5", "2025-06-16", "-6.599", "-0.55", "12", "9", "4", "7.014"], ["1", "非银金融", "-19.69", "2024-11-07", "2025-04-16", "107", "-0.184", "10.78", "40", "2025-06-17", "74617.18", "-", "2024-04-12", "39.745", "2025-04-16", "-1.042", "-0.208", "5", "2025-01-10", "-7.813", "-0.977", "8", "61", "1", "1.13"], ["2", "通信", "-20.43", "2025-02-21", "2025-04-08", "32", "-0.638", "16.477", "48", "2025-06-19", "50581.61", "-", "2024-02-07", "38.073", "2025-04-08", "-7.953", "-2.651", "3", "2025-03-03", "-10.026", "-1.671", "6", "25", "3", "4.155"], ["3", "煤炭", "-52.81", "2024-02-22", "2025-03-06", "251", "-0.21", "10.192", "22", "2025-04-08", "17424.73", "True", "2025-03-06", "10.192", "2025-03-06", "-2.773", "-0.924", "3", "2024-10-09", "-19.238", "-3.206", "6", "99", "15", "4.4"], ["4", "石油石化", "-32.03", "2024-04-19", "2025-03-06", "212", "-0.151", "9.384", "70", "2025-06-19", "42023.77", "True", "2025-03-06", "9.384", "2025-03-06", "-3.636", "-0.909", "4", "2024-10-09", "-12.536", "-1.791", "7", "99", "4", "6.02"], ["5", "公用事业", "-31.51", "2024-07-08", "2025-03-06", "160", "-0.197", "11.016", "36", "2025-04-28", "34092.81", "True", "2025-03-06", "11.016", "2025-03-06", "-2.163", "-0.541", "4", "2024-10-08", "-17.388", "-2.898", "6", "100", "3", "2.746"], ["6", "建筑材料", "-30.7", "2023-09-04", "2025-02-21", "352", "-0.087", "8.373", "32", "2025-04-09", "6936.1", "-", "2024-06-20", "2.979", "2025-02-19", "-2.061", "-0.515", "4", "2024-02-07", "-8.371", "-1.196", "7", "246", "5", "5.92"], ["7", "农林牧渔", "-30.66", "2023-12-07", "2025-02-21", "290", "-0.106", "16.86", "31", "2025-04-08", "12785.0", "True", "2025-02-21", "16.86", "2025-02-12", "-3.086", "-0.617", "5", "2024-08-23", "-8.841", "-1.105", "8", "116", "5", "5.821"], ["8", "交通运输", "-16.69", "2024-08-05", "2025-02-21", "131", "-0.127", "8.289", "66", "2025-05-30", "32132.57", "True", "2025-02-21", "8.289", "2025-02-21", "-2.282", "-0.57", "4", "2024-10-09", "-11.294", "-1.613", "7", "90", "1", "4.488"], ["9", "医药生物", "-30.43", "2023-12-13", "2025-01-24", "272", "-0.112", "11.657", "88", "2025-06-12", "64661.45", "True", "2025-01-24", "11.657", "2025-01-24", "-1.046", "-0.261", "4", "2024-01-31", "-5.822", "-1.456", "4", "237", "2", "2.33"], ["10", "有色金属", "-27.11", "2024-04-12", "2024-12-31", "177", "-0.153", "17.432", "106", "2025-06-13", "31235.26", "True", "2024-12-31", "17.432", "2024-12-25", "-5.672", "-0.567", "10", "2024-07-25", "-11.906", "-1.701", "7", "106", "5", "3.706"], ["11", "银行", "-27.47", "2024-08-27", "2024-11-11", "48", "-0.572", "33.425", "151", "2025-06-26", "154075.14", "True", "2024-11-11", "33.425", "2024-11-11", "-7.606", "-1.268", "6", "2024-10-08", "-16.993", "-4.248", "4", "24", "4", "2.217"], ["12", "家用电器", "-15.15", "2024-09-23", "2024-11-07", "29", "-0.522", "15.435", "44", "2025-01-09", "19132.52", "-", "2024-07-25", "17.235", "2024-11-07", "-9.129", "-1.304", "7", "2024-11-07", "-9.129", "-1.304", "7", "0", "2", "2.498"], ["13", "基础化工", "-19.22", "2024-01-04", "2024-10-18", "188", "-0.102", "8.42", "154", "2025-06-09", "34229.44", "True", "2024-10-18", "8.42", "2024-10-14", "-2.865", "-0.573", "5", "2024-02-05", "-10.656", "-1.776", "6", "165", "2", "5.618"], ["14", "纺织服饰", "-34.21", "2024-01-17", "2024-10-09", "172", "-0.199", "17.88", "158", "2025-06-04", "6199.59", "True", "2024-10-09", "17.88", "2024-10-09", "-6.45", "-0.921", "7", "2024-02-07", "-16.871", "-2.109", "8", "156", "1", "4.628"], ["15", "建筑装饰", "-21.13", "2023-08-22", "2024-10-09", "271", "-0.078", "12.134", "42", "2024-12-06", "18169.87", "-", "2024-06-06", "7.391", "2024-10-09", "-3.697", "-0.924", "4", "2024-02-07", "-14.3", "-2.383", "6", "156", "2", "11.846"], ["16", "钢铁", "-19.42", "2024-01-05", "2024-10-09", "180", "-0.108", "12.861", "104", "2025-03-13", "8219.8", "-", "2024-06-24", "8.319", "2024-09-11", "-1.562", "-0.39", "4", "2024-02-06", "-4.815", "-0.963", "5", "157", "3", "3.611"], ["17", "计算机", "-37.56", "2023-06-20", "2024-08-27", "290", "-0.13", "38.171", "123", "2025-03-06", "42115.08", "-", "2024-06-06", "35.681", "2024-08-22", "-1.974", "-0.658", "3", "2023-06-28", "-9.908", "-2.477", "4", "285", "8", "5.062"], ["18", "美容护理", "-33.34", "2023-09-05", "2024-08-26", "236", "-0.141", "17.7", "183", "2025-06-04", "3200.68", "-", "2024-06-25", "9.365", "2024-08-26", "-6.608", "-1.101", "6", "2024-02-05", "-12.444", "-1.037", "12", "133", "6", "7.809"], ["19", "传媒", "-37.47", "2023-06-20", "2024-07-24", "266", "-0.141", "25.728", "133", "2025-02-14", "15319.77", "-", "2024-06-25", "24.166", "2024-07-16", "-1.141", "-0.38", "3", "2023-12-28", "-12.443", "-2.489", "5", "136", "9", "2.695"], ["20", "社会服务", "-29.53", "2023-06-27", "2024-07-24", "263", "-0.112", "22.056", "96", "2024-12-16", "4608.81", "-", "2024-06-24", "20.894", "2024-07-16", "-2.176", "-0.725", "3", "2024-02-07", "-16.647", "-1.85", "9", "108", "5", "6.473"], ["21", "轻工制造", "-29.47", "2023-12-15", "2024-07-24", "146", "-0.202", "17.022", "95", "2024-12-13", "9177.47", "-", "2024-06-24", "11.744", "2024-07-19", "-3.923", "-0.785", "5", "2024-02-07", "-18.611", "-2.326", "8", "108", "4", "3.886"], ["22", "电力设备", "-29.93", "2023-06-30", "2024-07-10", "250", "-0.12", "15.624", "91", "2024-11-25", "55288.1", "True", "2024-07-10", "15.624", "2024-07-10", "-1.261", "-0.42", "3", "2024-02-05", "-13.579", "-1.132", "12", "100", "3", "3.5"], ["23", "商贸零售", "-28.72", "2023-12-15", "2024-07-10", "136", "-0.211", "35.444", "106", "2024-12-16", "9953.1", "-", "2024-06-24", "35.136", "2024-07-10", "-2.776", "-0.925", "3", "2024-02-07", "-13.997", "-1.75", "8", "98", "3", "4.384"], ["24", "综合", "-42.09", "2023-12-15", "2024-06-24", "124", "-0.339", "35.018", "118", "2024-12-16", "1187.56", "True", "2024-06-24", "35.018", "2024-06-24", "-3.92", "-1.307", "3", "2024-02-07", "-21.861", "-2.733", "8", "86", "6", "3.855"], ["25", "房地产", "-39.23", "2023-08-29", "2024-04-24", "157", "-0.25", "22.248", "130", "2024-11-07", "11105.82", "-", "2024-07-10", "20.828", "2024-04-22", "-1.195", "-0.398", "3", "2024-02-07", "-9.64", "-3.213", "3", "47", "7", "1.592"], ["26", "环保", "-24.09", "2024-01-12", "2024-02-07", "19", "-1.268", "18.715", "311", "2025-05-29", "7955.72", "-", "2024-07-19", "16.837", "2024-02-07", "-18.863", "-2.358", "8", "2024-02-07", "-18.863", "-2.358", "8", "0", "2", "1.86"], ["27", "机械设备", "-19.6", "2024-01-02", "2024-02-07", "27", "-0.726", "30.725", "257", "2025-03-10", "42717.17", "-", "2024-07-17", "29.362", "2024-02-07", "-11.775", "-1.308", "9", "2024-02-07", "-11.775", "-1.308", "9", "0", "1", "1.802"], ["28", "汽车", "-21.77", "2023-11-28", "2024-02-06", "50", "-0.435", "37.181", "266", "2025-03-20", "52946.89", "True", "2024-02-06", "37.181", "2024-02-06", "-14.669", "-0.978", "15", "2024-02-06", "-14.669", "-0.978", "15", "0", "1", "2.248"], ["29", "电子", "-23.91", "2023-12-15", "2024-02-05", "36", "-0.664", "45.834", "251", "2025-02-26", "89052.34", "-", "2024-09-26", "36.785", "2024-01-31", "-7.8", "-1.95", "4", "2024-01-31", "-7.8", "-1.95", "4", "3", "1", "2.937"], ["30", "国防军工", "-23.13", "2023-06-30", "2024-02-05", "150", "-0.154", "25.912", "181", "2024-11-11", "24375.44", "-", "2024-09-26", "17.527", "2024-02-05", "-9.596", "-2.399", "4", "2024-02-05", "-9.596", "-2.399", "4", "0", "3", "15.578"]], "shape": {"columns": 24, "rows": 31}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>indus</th>\n", "      <th>MaxDrop</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>Drop_Lastdays</th>\n", "      <th>AvgDrop</th>\n", "      <th>End2Max_Ratio</th>\n", "      <th>End2Max_LastDays</th>\n", "      <th>End2Max_MaxDate</th>\n", "      <th>Indus_MV</th>\n", "      <th>Bottom_State</th>\n", "      <th>Bottom_Date</th>\n", "      <th>Bottom2Max_Ratio</th>\n", "      <th>Recent_Drop_EndDate</th>\n", "      <th>Recent_Drop_Sum</th>\n", "      <th>Recent_Drop_Avg</th>\n", "      <th>Recent_Drop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_EndDate</th>\n", "      <th>Drop_Seg_MaxDrop_Sum</th>\n", "      <th>Drop_Seg_MaxDrop_Avg</th>\n", "      <th>Drop_Seg_MaxDrop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_End_To_Now_Days</th>\n", "      <th>Drop_Seg_Und1_Num</th>\n", "      <th>Recent2Avg_Ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>食品饮料</td>\n", "      <td>-29.40</td>\n", "      <td>2023-11-03</td>\n", "      <td>2025-06-27</td>\n", "      <td>399</td>\n", "      <td>-0.074</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>2025-06-27</td>\n", "      <td>45208.08</td>\n", "      <td>True</td>\n", "      <td>2025-06-27</td>\n", "      <td>0.000</td>\n", "      <td>2025-06-27</td>\n", "      <td>-2.596</td>\n", "      <td>-0.519</td>\n", "      <td>5</td>\n", "      <td>2025-06-16</td>\n", "      <td>-6.599</td>\n", "      <td>-0.550</td>\n", "      <td>12</td>\n", "      <td>9</td>\n", "      <td>4</td>\n", "      <td>7.014</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>非银金融</td>\n", "      <td>-19.69</td>\n", "      <td>2024-11-07</td>\n", "      <td>2025-04-16</td>\n", "      <td>107</td>\n", "      <td>-0.184</td>\n", "      <td>10.780</td>\n", "      <td>40</td>\n", "      <td>2025-06-17</td>\n", "      <td>74617.18</td>\n", "      <td>-</td>\n", "      <td>2024-04-12</td>\n", "      <td>39.745</td>\n", "      <td>2025-04-16</td>\n", "      <td>-1.042</td>\n", "      <td>-0.208</td>\n", "      <td>5</td>\n", "      <td>2025-01-10</td>\n", "      <td>-7.813</td>\n", "      <td>-0.977</td>\n", "      <td>8</td>\n", "      <td>61</td>\n", "      <td>1</td>\n", "      <td>1.130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>通信</td>\n", "      <td>-20.43</td>\n", "      <td>2025-02-21</td>\n", "      <td>2025-04-08</td>\n", "      <td>32</td>\n", "      <td>-0.638</td>\n", "      <td>16.477</td>\n", "      <td>48</td>\n", "      <td>2025-06-19</td>\n", "      <td>50581.61</td>\n", "      <td>-</td>\n", "      <td>2024-02-07</td>\n", "      <td>38.073</td>\n", "      <td>2025-04-08</td>\n", "      <td>-7.953</td>\n", "      <td>-2.651</td>\n", "      <td>3</td>\n", "      <td>2025-03-03</td>\n", "      <td>-10.026</td>\n", "      <td>-1.671</td>\n", "      <td>6</td>\n", "      <td>25</td>\n", "      <td>3</td>\n", "      <td>4.155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>煤炭</td>\n", "      <td>-52.81</td>\n", "      <td>2024-02-22</td>\n", "      <td>2025-03-06</td>\n", "      <td>251</td>\n", "      <td>-0.210</td>\n", "      <td>10.192</td>\n", "      <td>22</td>\n", "      <td>2025-04-08</td>\n", "      <td>17424.73</td>\n", "      <td>True</td>\n", "      <td>2025-03-06</td>\n", "      <td>10.192</td>\n", "      <td>2025-03-06</td>\n", "      <td>-2.773</td>\n", "      <td>-0.924</td>\n", "      <td>3</td>\n", "      <td>2024-10-09</td>\n", "      <td>-19.238</td>\n", "      <td>-3.206</td>\n", "      <td>6</td>\n", "      <td>99</td>\n", "      <td>15</td>\n", "      <td>4.400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>石油石化</td>\n", "      <td>-32.03</td>\n", "      <td>2024-04-19</td>\n", "      <td>2025-03-06</td>\n", "      <td>212</td>\n", "      <td>-0.151</td>\n", "      <td>9.384</td>\n", "      <td>70</td>\n", "      <td>2025-06-19</td>\n", "      <td>42023.77</td>\n", "      <td>True</td>\n", "      <td>2025-03-06</td>\n", "      <td>9.384</td>\n", "      <td>2025-03-06</td>\n", "      <td>-3.636</td>\n", "      <td>-0.909</td>\n", "      <td>4</td>\n", "      <td>2024-10-09</td>\n", "      <td>-12.536</td>\n", "      <td>-1.791</td>\n", "      <td>7</td>\n", "      <td>99</td>\n", "      <td>4</td>\n", "      <td>6.020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>公用事业</td>\n", "      <td>-31.51</td>\n", "      <td>2024-07-08</td>\n", "      <td>2025-03-06</td>\n", "      <td>160</td>\n", "      <td>-0.197</td>\n", "      <td>11.016</td>\n", "      <td>36</td>\n", "      <td>2025-04-28</td>\n", "      <td>34092.81</td>\n", "      <td>True</td>\n", "      <td>2025-03-06</td>\n", "      <td>11.016</td>\n", "      <td>2025-03-06</td>\n", "      <td>-2.163</td>\n", "      <td>-0.541</td>\n", "      <td>4</td>\n", "      <td>2024-10-08</td>\n", "      <td>-17.388</td>\n", "      <td>-2.898</td>\n", "      <td>6</td>\n", "      <td>100</td>\n", "      <td>3</td>\n", "      <td>2.746</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>建筑材料</td>\n", "      <td>-30.70</td>\n", "      <td>2023-09-04</td>\n", "      <td>2025-02-21</td>\n", "      <td>352</td>\n", "      <td>-0.087</td>\n", "      <td>8.373</td>\n", "      <td>32</td>\n", "      <td>2025-04-09</td>\n", "      <td>6936.10</td>\n", "      <td>-</td>\n", "      <td>2024-06-20</td>\n", "      <td>2.979</td>\n", "      <td>2025-02-19</td>\n", "      <td>-2.061</td>\n", "      <td>-0.515</td>\n", "      <td>4</td>\n", "      <td>2024-02-07</td>\n", "      <td>-8.371</td>\n", "      <td>-1.196</td>\n", "      <td>7</td>\n", "      <td>246</td>\n", "      <td>5</td>\n", "      <td>5.920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>农林牧渔</td>\n", "      <td>-30.66</td>\n", "      <td>2023-12-07</td>\n", "      <td>2025-02-21</td>\n", "      <td>290</td>\n", "      <td>-0.106</td>\n", "      <td>16.860</td>\n", "      <td>31</td>\n", "      <td>2025-04-08</td>\n", "      <td>12785.00</td>\n", "      <td>True</td>\n", "      <td>2025-02-21</td>\n", "      <td>16.860</td>\n", "      <td>2025-02-12</td>\n", "      <td>-3.086</td>\n", "      <td>-0.617</td>\n", "      <td>5</td>\n", "      <td>2024-08-23</td>\n", "      <td>-8.841</td>\n", "      <td>-1.105</td>\n", "      <td>8</td>\n", "      <td>116</td>\n", "      <td>5</td>\n", "      <td>5.821</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>交通运输</td>\n", "      <td>-16.69</td>\n", "      <td>2024-08-05</td>\n", "      <td>2025-02-21</td>\n", "      <td>131</td>\n", "      <td>-0.127</td>\n", "      <td>8.289</td>\n", "      <td>66</td>\n", "      <td>2025-05-30</td>\n", "      <td>32132.57</td>\n", "      <td>True</td>\n", "      <td>2025-02-21</td>\n", "      <td>8.289</td>\n", "      <td>2025-02-21</td>\n", "      <td>-2.282</td>\n", "      <td>-0.570</td>\n", "      <td>4</td>\n", "      <td>2024-10-09</td>\n", "      <td>-11.294</td>\n", "      <td>-1.613</td>\n", "      <td>7</td>\n", "      <td>90</td>\n", "      <td>1</td>\n", "      <td>4.488</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>医药生物</td>\n", "      <td>-30.43</td>\n", "      <td>2023-12-13</td>\n", "      <td>2025-01-24</td>\n", "      <td>272</td>\n", "      <td>-0.112</td>\n", "      <td>11.657</td>\n", "      <td>88</td>\n", "      <td>2025-06-12</td>\n", "      <td>64661.45</td>\n", "      <td>True</td>\n", "      <td>2025-01-24</td>\n", "      <td>11.657</td>\n", "      <td>2025-01-24</td>\n", "      <td>-1.046</td>\n", "      <td>-0.261</td>\n", "      <td>4</td>\n", "      <td>2024-01-31</td>\n", "      <td>-5.822</td>\n", "      <td>-1.456</td>\n", "      <td>4</td>\n", "      <td>237</td>\n", "      <td>2</td>\n", "      <td>2.330</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>有色金属</td>\n", "      <td>-27.11</td>\n", "      <td>2024-04-12</td>\n", "      <td>2024-12-31</td>\n", "      <td>177</td>\n", "      <td>-0.153</td>\n", "      <td>17.432</td>\n", "      <td>106</td>\n", "      <td>2025-06-13</td>\n", "      <td>31235.26</td>\n", "      <td>True</td>\n", "      <td>2024-12-31</td>\n", "      <td>17.432</td>\n", "      <td>2024-12-25</td>\n", "      <td>-5.672</td>\n", "      <td>-0.567</td>\n", "      <td>10</td>\n", "      <td>2024-07-25</td>\n", "      <td>-11.906</td>\n", "      <td>-1.701</td>\n", "      <td>7</td>\n", "      <td>106</td>\n", "      <td>5</td>\n", "      <td>3.706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>银行</td>\n", "      <td>-27.47</td>\n", "      <td>2024-08-27</td>\n", "      <td>2024-11-11</td>\n", "      <td>48</td>\n", "      <td>-0.572</td>\n", "      <td>33.425</td>\n", "      <td>151</td>\n", "      <td>2025-06-26</td>\n", "      <td>154075.14</td>\n", "      <td>True</td>\n", "      <td>2024-11-11</td>\n", "      <td>33.425</td>\n", "      <td>2024-11-11</td>\n", "      <td>-7.606</td>\n", "      <td>-1.268</td>\n", "      <td>6</td>\n", "      <td>2024-10-08</td>\n", "      <td>-16.993</td>\n", "      <td>-4.248</td>\n", "      <td>4</td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "      <td>2.217</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>家用电器</td>\n", "      <td>-15.15</td>\n", "      <td>2024-09-23</td>\n", "      <td>2024-11-07</td>\n", "      <td>29</td>\n", "      <td>-0.522</td>\n", "      <td>15.435</td>\n", "      <td>44</td>\n", "      <td>2025-01-09</td>\n", "      <td>19132.52</td>\n", "      <td>-</td>\n", "      <td>2024-07-25</td>\n", "      <td>17.235</td>\n", "      <td>2024-11-07</td>\n", "      <td>-9.129</td>\n", "      <td>-1.304</td>\n", "      <td>7</td>\n", "      <td>2024-11-07</td>\n", "      <td>-9.129</td>\n", "      <td>-1.304</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>2.498</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>基础化工</td>\n", "      <td>-19.22</td>\n", "      <td>2024-01-04</td>\n", "      <td>2024-10-18</td>\n", "      <td>188</td>\n", "      <td>-0.102</td>\n", "      <td>8.420</td>\n", "      <td>154</td>\n", "      <td>2025-06-09</td>\n", "      <td>34229.44</td>\n", "      <td>True</td>\n", "      <td>2024-10-18</td>\n", "      <td>8.420</td>\n", "      <td>2024-10-14</td>\n", "      <td>-2.865</td>\n", "      <td>-0.573</td>\n", "      <td>5</td>\n", "      <td>2024-02-05</td>\n", "      <td>-10.656</td>\n", "      <td>-1.776</td>\n", "      <td>6</td>\n", "      <td>165</td>\n", "      <td>2</td>\n", "      <td>5.618</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>纺织服饰</td>\n", "      <td>-34.21</td>\n", "      <td>2024-01-17</td>\n", "      <td>2024-10-09</td>\n", "      <td>172</td>\n", "      <td>-0.199</td>\n", "      <td>17.880</td>\n", "      <td>158</td>\n", "      <td>2025-06-04</td>\n", "      <td>6199.59</td>\n", "      <td>True</td>\n", "      <td>2024-10-09</td>\n", "      <td>17.880</td>\n", "      <td>2024-10-09</td>\n", "      <td>-6.450</td>\n", "      <td>-0.921</td>\n", "      <td>7</td>\n", "      <td>2024-02-07</td>\n", "      <td>-16.871</td>\n", "      <td>-2.109</td>\n", "      <td>8</td>\n", "      <td>156</td>\n", "      <td>1</td>\n", "      <td>4.628</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>建筑装饰</td>\n", "      <td>-21.13</td>\n", "      <td>2023-08-22</td>\n", "      <td>2024-10-09</td>\n", "      <td>271</td>\n", "      <td>-0.078</td>\n", "      <td>12.134</td>\n", "      <td>42</td>\n", "      <td>2024-12-06</td>\n", "      <td>18169.87</td>\n", "      <td>-</td>\n", "      <td>2024-06-06</td>\n", "      <td>7.391</td>\n", "      <td>2024-10-09</td>\n", "      <td>-3.697</td>\n", "      <td>-0.924</td>\n", "      <td>4</td>\n", "      <td>2024-02-07</td>\n", "      <td>-14.300</td>\n", "      <td>-2.383</td>\n", "      <td>6</td>\n", "      <td>156</td>\n", "      <td>2</td>\n", "      <td>11.846</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>钢铁</td>\n", "      <td>-19.42</td>\n", "      <td>2024-01-05</td>\n", "      <td>2024-10-09</td>\n", "      <td>180</td>\n", "      <td>-0.108</td>\n", "      <td>12.861</td>\n", "      <td>104</td>\n", "      <td>2025-03-13</td>\n", "      <td>8219.80</td>\n", "      <td>-</td>\n", "      <td>2024-06-24</td>\n", "      <td>8.319</td>\n", "      <td>2024-09-11</td>\n", "      <td>-1.562</td>\n", "      <td>-0.390</td>\n", "      <td>4</td>\n", "      <td>2024-02-06</td>\n", "      <td>-4.815</td>\n", "      <td>-0.963</td>\n", "      <td>5</td>\n", "      <td>157</td>\n", "      <td>3</td>\n", "      <td>3.611</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>计算机</td>\n", "      <td>-37.56</td>\n", "      <td>2023-06-20</td>\n", "      <td>2024-08-27</td>\n", "      <td>290</td>\n", "      <td>-0.130</td>\n", "      <td>38.171</td>\n", "      <td>123</td>\n", "      <td>2025-03-06</td>\n", "      <td>42115.08</td>\n", "      <td>-</td>\n", "      <td>2024-06-06</td>\n", "      <td>35.681</td>\n", "      <td>2024-08-22</td>\n", "      <td>-1.974</td>\n", "      <td>-0.658</td>\n", "      <td>3</td>\n", "      <td>2023-06-28</td>\n", "      <td>-9.908</td>\n", "      <td>-2.477</td>\n", "      <td>4</td>\n", "      <td>285</td>\n", "      <td>8</td>\n", "      <td>5.062</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>美容护理</td>\n", "      <td>-33.34</td>\n", "      <td>2023-09-05</td>\n", "      <td>2024-08-26</td>\n", "      <td>236</td>\n", "      <td>-0.141</td>\n", "      <td>17.700</td>\n", "      <td>183</td>\n", "      <td>2025-06-04</td>\n", "      <td>3200.68</td>\n", "      <td>-</td>\n", "      <td>2024-06-25</td>\n", "      <td>9.365</td>\n", "      <td>2024-08-26</td>\n", "      <td>-6.608</td>\n", "      <td>-1.101</td>\n", "      <td>6</td>\n", "      <td>2024-02-05</td>\n", "      <td>-12.444</td>\n", "      <td>-1.037</td>\n", "      <td>12</td>\n", "      <td>133</td>\n", "      <td>6</td>\n", "      <td>7.809</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>传媒</td>\n", "      <td>-37.47</td>\n", "      <td>2023-06-20</td>\n", "      <td>2024-07-24</td>\n", "      <td>266</td>\n", "      <td>-0.141</td>\n", "      <td>25.728</td>\n", "      <td>133</td>\n", "      <td>2025-02-14</td>\n", "      <td>15319.77</td>\n", "      <td>-</td>\n", "      <td>2024-06-25</td>\n", "      <td>24.166</td>\n", "      <td>2024-07-16</td>\n", "      <td>-1.141</td>\n", "      <td>-0.380</td>\n", "      <td>3</td>\n", "      <td>2023-12-28</td>\n", "      <td>-12.443</td>\n", "      <td>-2.489</td>\n", "      <td>5</td>\n", "      <td>136</td>\n", "      <td>9</td>\n", "      <td>2.695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>社会服务</td>\n", "      <td>-29.53</td>\n", "      <td>2023-06-27</td>\n", "      <td>2024-07-24</td>\n", "      <td>263</td>\n", "      <td>-0.112</td>\n", "      <td>22.056</td>\n", "      <td>96</td>\n", "      <td>2024-12-16</td>\n", "      <td>4608.81</td>\n", "      <td>-</td>\n", "      <td>2024-06-24</td>\n", "      <td>20.894</td>\n", "      <td>2024-07-16</td>\n", "      <td>-2.176</td>\n", "      <td>-0.725</td>\n", "      <td>3</td>\n", "      <td>2024-02-07</td>\n", "      <td>-16.647</td>\n", "      <td>-1.850</td>\n", "      <td>9</td>\n", "      <td>108</td>\n", "      <td>5</td>\n", "      <td>6.473</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>轻工制造</td>\n", "      <td>-29.47</td>\n", "      <td>2023-12-15</td>\n", "      <td>2024-07-24</td>\n", "      <td>146</td>\n", "      <td>-0.202</td>\n", "      <td>17.022</td>\n", "      <td>95</td>\n", "      <td>2024-12-13</td>\n", "      <td>9177.47</td>\n", "      <td>-</td>\n", "      <td>2024-06-24</td>\n", "      <td>11.744</td>\n", "      <td>2024-07-19</td>\n", "      <td>-3.923</td>\n", "      <td>-0.785</td>\n", "      <td>5</td>\n", "      <td>2024-02-07</td>\n", "      <td>-18.611</td>\n", "      <td>-2.326</td>\n", "      <td>8</td>\n", "      <td>108</td>\n", "      <td>4</td>\n", "      <td>3.886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>电力设备</td>\n", "      <td>-29.93</td>\n", "      <td>2023-06-30</td>\n", "      <td>2024-07-10</td>\n", "      <td>250</td>\n", "      <td>-0.120</td>\n", "      <td>15.624</td>\n", "      <td>91</td>\n", "      <td>2024-11-25</td>\n", "      <td>55288.10</td>\n", "      <td>True</td>\n", "      <td>2024-07-10</td>\n", "      <td>15.624</td>\n", "      <td>2024-07-10</td>\n", "      <td>-1.261</td>\n", "      <td>-0.420</td>\n", "      <td>3</td>\n", "      <td>2024-02-05</td>\n", "      <td>-13.579</td>\n", "      <td>-1.132</td>\n", "      <td>12</td>\n", "      <td>100</td>\n", "      <td>3</td>\n", "      <td>3.500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>商贸零售</td>\n", "      <td>-28.72</td>\n", "      <td>2023-12-15</td>\n", "      <td>2024-07-10</td>\n", "      <td>136</td>\n", "      <td>-0.211</td>\n", "      <td>35.444</td>\n", "      <td>106</td>\n", "      <td>2024-12-16</td>\n", "      <td>9953.10</td>\n", "      <td>-</td>\n", "      <td>2024-06-24</td>\n", "      <td>35.136</td>\n", "      <td>2024-07-10</td>\n", "      <td>-2.776</td>\n", "      <td>-0.925</td>\n", "      <td>3</td>\n", "      <td>2024-02-07</td>\n", "      <td>-13.997</td>\n", "      <td>-1.750</td>\n", "      <td>8</td>\n", "      <td>98</td>\n", "      <td>3</td>\n", "      <td>4.384</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>综合</td>\n", "      <td>-42.09</td>\n", "      <td>2023-12-15</td>\n", "      <td>2024-06-24</td>\n", "      <td>124</td>\n", "      <td>-0.339</td>\n", "      <td>35.018</td>\n", "      <td>118</td>\n", "      <td>2024-12-16</td>\n", "      <td>1187.56</td>\n", "      <td>True</td>\n", "      <td>2024-06-24</td>\n", "      <td>35.018</td>\n", "      <td>2024-06-24</td>\n", "      <td>-3.920</td>\n", "      <td>-1.307</td>\n", "      <td>3</td>\n", "      <td>2024-02-07</td>\n", "      <td>-21.861</td>\n", "      <td>-2.733</td>\n", "      <td>8</td>\n", "      <td>86</td>\n", "      <td>6</td>\n", "      <td>3.855</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>房地产</td>\n", "      <td>-39.23</td>\n", "      <td>2023-08-29</td>\n", "      <td>2024-04-24</td>\n", "      <td>157</td>\n", "      <td>-0.250</td>\n", "      <td>22.248</td>\n", "      <td>130</td>\n", "      <td>2024-11-07</td>\n", "      <td>11105.82</td>\n", "      <td>-</td>\n", "      <td>2024-07-10</td>\n", "      <td>20.828</td>\n", "      <td>2024-04-22</td>\n", "      <td>-1.195</td>\n", "      <td>-0.398</td>\n", "      <td>3</td>\n", "      <td>2024-02-07</td>\n", "      <td>-9.640</td>\n", "      <td>-3.213</td>\n", "      <td>3</td>\n", "      <td>47</td>\n", "      <td>7</td>\n", "      <td>1.592</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>环保</td>\n", "      <td>-24.09</td>\n", "      <td>2024-01-12</td>\n", "      <td>2024-02-07</td>\n", "      <td>19</td>\n", "      <td>-1.268</td>\n", "      <td>18.715</td>\n", "      <td>311</td>\n", "      <td>2025-05-29</td>\n", "      <td>7955.72</td>\n", "      <td>-</td>\n", "      <td>2024-07-19</td>\n", "      <td>16.837</td>\n", "      <td>2024-02-07</td>\n", "      <td>-18.863</td>\n", "      <td>-2.358</td>\n", "      <td>8</td>\n", "      <td>2024-02-07</td>\n", "      <td>-18.863</td>\n", "      <td>-2.358</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1.860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>机械设备</td>\n", "      <td>-19.60</td>\n", "      <td>2024-01-02</td>\n", "      <td>2024-02-07</td>\n", "      <td>27</td>\n", "      <td>-0.726</td>\n", "      <td>30.725</td>\n", "      <td>257</td>\n", "      <td>2025-03-10</td>\n", "      <td>42717.17</td>\n", "      <td>-</td>\n", "      <td>2024-07-17</td>\n", "      <td>29.362</td>\n", "      <td>2024-02-07</td>\n", "      <td>-11.775</td>\n", "      <td>-1.308</td>\n", "      <td>9</td>\n", "      <td>2024-02-07</td>\n", "      <td>-11.775</td>\n", "      <td>-1.308</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1.802</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>汽车</td>\n", "      <td>-21.77</td>\n", "      <td>2023-11-28</td>\n", "      <td>2024-02-06</td>\n", "      <td>50</td>\n", "      <td>-0.435</td>\n", "      <td>37.181</td>\n", "      <td>266</td>\n", "      <td>2025-03-20</td>\n", "      <td>52946.89</td>\n", "      <td>True</td>\n", "      <td>2024-02-06</td>\n", "      <td>37.181</td>\n", "      <td>2024-02-06</td>\n", "      <td>-14.669</td>\n", "      <td>-0.978</td>\n", "      <td>15</td>\n", "      <td>2024-02-06</td>\n", "      <td>-14.669</td>\n", "      <td>-0.978</td>\n", "      <td>15</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2.248</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>电子</td>\n", "      <td>-23.91</td>\n", "      <td>2023-12-15</td>\n", "      <td>2024-02-05</td>\n", "      <td>36</td>\n", "      <td>-0.664</td>\n", "      <td>45.834</td>\n", "      <td>251</td>\n", "      <td>2025-02-26</td>\n", "      <td>89052.34</td>\n", "      <td>-</td>\n", "      <td>2024-09-26</td>\n", "      <td>36.785</td>\n", "      <td>2024-01-31</td>\n", "      <td>-7.800</td>\n", "      <td>-1.950</td>\n", "      <td>4</td>\n", "      <td>2024-01-31</td>\n", "      <td>-7.800</td>\n", "      <td>-1.950</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>2.937</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>国防军工</td>\n", "      <td>-23.13</td>\n", "      <td>2023-06-30</td>\n", "      <td>2024-02-05</td>\n", "      <td>150</td>\n", "      <td>-0.154</td>\n", "      <td>25.912</td>\n", "      <td>181</td>\n", "      <td>2024-11-11</td>\n", "      <td>24375.44</td>\n", "      <td>-</td>\n", "      <td>2024-09-26</td>\n", "      <td>17.527</td>\n", "      <td>2024-02-05</td>\n", "      <td>-9.596</td>\n", "      <td>-2.399</td>\n", "      <td>4</td>\n", "      <td>2024-02-05</td>\n", "      <td>-9.596</td>\n", "      <td>-2.399</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>15.578</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   indus  MaxDrop  start_date    end_date Drop_Lastdays  AvgDrop  \\\n", "0   食品饮料   -29.40  2023-11-03  2025-06-27           399   -0.074   \n", "1   非银金融   -19.69  2024-11-07  2025-04-16           107   -0.184   \n", "2     通信   -20.43  2025-02-21  2025-04-08            32   -0.638   \n", "3     煤炭   -52.81  2024-02-22  2025-03-06           251   -0.210   \n", "4   石油石化   -32.03  2024-04-19  2025-03-06           212   -0.151   \n", "5   公用事业   -31.51  2024-07-08  2025-03-06           160   -0.197   \n", "6   建筑材料   -30.70  2023-09-04  2025-02-21           352   -0.087   \n", "7   农林牧渔   -30.66  2023-12-07  2025-02-21           290   -0.106   \n", "8   交通运输   -16.69  2024-08-05  2025-02-21           131   -0.127   \n", "9   医药生物   -30.43  2023-12-13  2025-01-24           272   -0.112   \n", "10  有色金属   -27.11  2024-04-12  2024-12-31           177   -0.153   \n", "11    银行   -27.47  2024-08-27  2024-11-11            48   -0.572   \n", "12  家用电器   -15.15  2024-09-23  2024-11-07            29   -0.522   \n", "13  基础化工   -19.22  2024-01-04  2024-10-18           188   -0.102   \n", "14  纺织服饰   -34.21  2024-01-17  2024-10-09           172   -0.199   \n", "15  建筑装饰   -21.13  2023-08-22  2024-10-09           271   -0.078   \n", "16    钢铁   -19.42  2024-01-05  2024-10-09           180   -0.108   \n", "17   计算机   -37.56  2023-06-20  2024-08-27           290   -0.130   \n", "18  美容护理   -33.34  2023-09-05  2024-08-26           236   -0.141   \n", "19    传媒   -37.47  2023-06-20  2024-07-24           266   -0.141   \n", "20  社会服务   -29.53  2023-06-27  2024-07-24           263   -0.112   \n", "21  轻工制造   -29.47  2023-12-15  2024-07-24           146   -0.202   \n", "22  电力设备   -29.93  2023-06-30  2024-07-10           250   -0.120   \n", "23  商贸零售   -28.72  2023-12-15  2024-07-10           136   -0.211   \n", "24    综合   -42.09  2023-12-15  2024-06-24           124   -0.339   \n", "25   房地产   -39.23  2023-08-29  2024-04-24           157   -0.250   \n", "26    环保   -24.09  2024-01-12  2024-02-07            19   -1.268   \n", "27  机械设备   -19.60  2024-01-02  2024-02-07            27   -0.726   \n", "28    汽车   -21.77  2023-11-28  2024-02-06            50   -0.435   \n", "29    电子   -23.91  2023-12-15  2024-02-05            36   -0.664   \n", "30  国防军工   -23.13  2023-06-30  2024-02-05           150   -0.154   \n", "\n", "    End2Max_Ratio End2Max_LastDays End2Max_MaxDate   Indus_MV Bottom_State  \\\n", "0           0.000                0      2025-06-27   45208.08         True   \n", "1          10.780               40      2025-06-17   74617.18            -   \n", "2          16.477               48      2025-06-19   50581.61            -   \n", "3          10.192               22      2025-04-08   17424.73         True   \n", "4           9.384               70      2025-06-19   42023.77         True   \n", "5          11.016               36      2025-04-28   34092.81         True   \n", "6           8.373               32      2025-04-09    6936.10            -   \n", "7          16.860               31      2025-04-08   12785.00         True   \n", "8           8.289               66      2025-05-30   32132.57         True   \n", "9          11.657               88      2025-06-12   64661.45         True   \n", "10         17.432              106      2025-06-13   31235.26         True   \n", "11         33.425              151      2025-06-26  154075.14         True   \n", "12         15.435               44      2025-01-09   19132.52            -   \n", "13          8.420              154      2025-06-09   34229.44         True   \n", "14         17.880              158      2025-06-04    6199.59         True   \n", "15         12.134               42      2024-12-06   18169.87            -   \n", "16         12.861              104      2025-03-13    8219.80            -   \n", "17         38.171              123      2025-03-06   42115.08            -   \n", "18         17.700              183      2025-06-04    3200.68            -   \n", "19         25.728              133      2025-02-14   15319.77            -   \n", "20         22.056               96      2024-12-16    4608.81            -   \n", "21         17.022               95      2024-12-13    9177.47            -   \n", "22         15.624               91      2024-11-25   55288.10         True   \n", "23         35.444              106      2024-12-16    9953.10            -   \n", "24         35.018              118      2024-12-16    1187.56         True   \n", "25         22.248              130      2024-11-07   11105.82            -   \n", "26         18.715              311      2025-05-29    7955.72            -   \n", "27         30.725              257      2025-03-10   42717.17            -   \n", "28         37.181              266      2025-03-20   52946.89         True   \n", "29         45.834              251      2025-02-26   89052.34            -   \n", "30         25.912              181      2024-11-11   24375.44            -   \n", "\n", "   Bottom_Date  Bottom2Max_Ratio Recent_Drop_EndDate  Recent_Drop_Sum  \\\n", "0   2025-06-27             0.000          2025-06-27           -2.596   \n", "1   2024-04-12            39.745          2025-04-16           -1.042   \n", "2   2024-02-07            38.073          2025-04-08           -7.953   \n", "3   2025-03-06            10.192          2025-03-06           -2.773   \n", "4   2025-03-06             9.384          2025-03-06           -3.636   \n", "5   2025-03-06            11.016          2025-03-06           -2.163   \n", "6   2024-06-20             2.979          2025-02-19           -2.061   \n", "7   2025-02-21            16.860          2025-02-12           -3.086   \n", "8   2025-02-21             8.289          2025-02-21           -2.282   \n", "9   2025-01-24            11.657          2025-01-24           -1.046   \n", "10  2024-12-31            17.432          2024-12-25           -5.672   \n", "11  2024-11-11            33.425          2024-11-11           -7.606   \n", "12  2024-07-25            17.235          2024-11-07           -9.129   \n", "13  2024-10-18             8.420          2024-10-14           -2.865   \n", "14  2024-10-09            17.880          2024-10-09           -6.450   \n", "15  2024-06-06             7.391          2024-10-09           -3.697   \n", "16  2024-06-24             8.319          2024-09-11           -1.562   \n", "17  2024-06-06            35.681          2024-08-22           -1.974   \n", "18  2024-06-25             9.365          2024-08-26           -6.608   \n", "19  2024-06-25            24.166          2024-07-16           -1.141   \n", "20  2024-06-24            20.894          2024-07-16           -2.176   \n", "21  2024-06-24            11.744          2024-07-19           -3.923   \n", "22  2024-07-10            15.624          2024-07-10           -1.261   \n", "23  2024-06-24            35.136          2024-07-10           -2.776   \n", "24  2024-06-24            35.018          2024-06-24           -3.920   \n", "25  2024-07-10            20.828          2024-04-22           -1.195   \n", "26  2024-07-19            16.837          2024-02-07          -18.863   \n", "27  2024-07-17            29.362          2024-02-07          -11.775   \n", "28  2024-02-06            37.181          2024-02-06          -14.669   \n", "29  2024-09-26            36.785          2024-01-31           -7.800   \n", "30  2024-09-26            17.527          2024-02-05           -9.596   \n", "\n", "    Recent_Drop_Avg Recent_Drop_LastDays Drop_Seg_MaxDrop_EndDate  \\\n", "0            -0.519                    5               2025-06-16   \n", "1            -0.208                    5               2025-01-10   \n", "2            -2.651                    3               2025-03-03   \n", "3            -0.924                    3               2024-10-09   \n", "4            -0.909                    4               2024-10-09   \n", "5            -0.541                    4               2024-10-08   \n", "6            -0.515                    4               2024-02-07   \n", "7            -0.617                    5               2024-08-23   \n", "8            -0.570                    4               2024-10-09   \n", "9            -0.261                    4               2024-01-31   \n", "10           -0.567                   10               2024-07-25   \n", "11           -1.268                    6               2024-10-08   \n", "12           -1.304                    7               2024-11-07   \n", "13           -0.573                    5               2024-02-05   \n", "14           -0.921                    7               2024-02-07   \n", "15           -0.924                    4               2024-02-07   \n", "16           -0.390                    4               2024-02-06   \n", "17           -0.658                    3               2023-06-28   \n", "18           -1.101                    6               2024-02-05   \n", "19           -0.380                    3               2023-12-28   \n", "20           -0.725                    3               2024-02-07   \n", "21           -0.785                    5               2024-02-07   \n", "22           -0.420                    3               2024-02-05   \n", "23           -0.925                    3               2024-02-07   \n", "24           -1.307                    3               2024-02-07   \n", "25           -0.398                    3               2024-02-07   \n", "26           -2.358                    8               2024-02-07   \n", "27           -1.308                    9               2024-02-07   \n", "28           -0.978                   15               2024-02-06   \n", "29           -1.950                    4               2024-01-31   \n", "30           -2.399                    4               2024-02-05   \n", "\n", "    Drop_Seg_MaxDrop_Sum  Drop_Seg_MaxDrop_Avg Drop_Seg_MaxDrop_LastDays  \\\n", "0                 -6.599                -0.550                        12   \n", "1                 -7.813                -0.977                         8   \n", "2                -10.026                -1.671                         6   \n", "3                -19.238                -3.206                         6   \n", "4                -12.536                -1.791                         7   \n", "5                -17.388                -2.898                         6   \n", "6                 -8.371                -1.196                         7   \n", "7                 -8.841                -1.105                         8   \n", "8                -11.294                -1.613                         7   \n", "9                 -5.822                -1.456                         4   \n", "10               -11.906                -1.701                         7   \n", "11               -16.993                -4.248                         4   \n", "12                -9.129                -1.304                         7   \n", "13               -10.656                -1.776                         6   \n", "14               -16.871                -2.109                         8   \n", "15               -14.300                -2.383                         6   \n", "16                -4.815                -0.963                         5   \n", "17                -9.908                -2.477                         4   \n", "18               -12.444                -1.037                        12   \n", "19               -12.443                -2.489                         5   \n", "20               -16.647                -1.850                         9   \n", "21               -18.611                -2.326                         8   \n", "22               -13.579                -1.132                        12   \n", "23               -13.997                -1.750                         8   \n", "24               -21.861                -2.733                         8   \n", "25                -9.640                -3.213                         3   \n", "26               -18.863                -2.358                         8   \n", "27               -11.775                -1.308                         9   \n", "28               -14.669                -0.978                        15   \n", "29                -7.800                -1.950                         4   \n", "30                -9.596                -2.399                         4   \n", "\n", "   Drop_Seg_MaxDrop_End_To_Now_Days Drop_Seg_Und1_Num  Recent2Avg_Ratio  \n", "0                                 9                 4             7.014  \n", "1                                61                 1             1.130  \n", "2                                25                 3             4.155  \n", "3                                99                15             4.400  \n", "4                                99                 4             6.020  \n", "5                               100                 3             2.746  \n", "6                               246                 5             5.920  \n", "7                               116                 5             5.821  \n", "8                                90                 1             4.488  \n", "9                               237                 2             2.330  \n", "10                              106                 5             3.706  \n", "11                               24                 4             2.217  \n", "12                                0                 2             2.498  \n", "13                              165                 2             5.618  \n", "14                              156                 1             4.628  \n", "15                              156                 2            11.846  \n", "16                              157                 3             3.611  \n", "17                              285                 8             5.062  \n", "18                              133                 6             7.809  \n", "19                              136                 9             2.695  \n", "20                              108                 5             6.473  \n", "21                              108                 4             3.886  \n", "22                              100                 3             3.500  \n", "23                               98                 3             4.384  \n", "24                               86                 6             3.855  \n", "25                               47                 7             1.592  \n", "26                                0                 2             1.860  \n", "27                                0                 1             1.802  \n", "28                                0                 1             2.248  \n", "29                                3                 1             2.937  \n", "30                                0                 3            15.578  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Bottom_List = Bottom_List.sort_values(['end_date', 'MaxDrop', 'Drop_Lastdays'], ascending=[False, True, False])\n", "Bottom_List = Bottom_List.reset_index(drop=True)\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "Bottom_List['Recent2Avg_Ratio'] = round(Bottom_List['Recent_Drop_Avg'] / Bottom_List['AvgDrop'],3)\n", "Bottom_List"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:38.006562Z", "start_time": "2025-05-12T00:13:37.996636Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "indus", "rawType": "object", "type": "string"}, {"name": "MaxDrop", "rawType": "float64", "type": "float"}, {"name": "start_date", "rawType": "object", "type": "string"}, {"name": "end_date", "rawType": "object", "type": "string"}, {"name": "Drop_Lastdays", "rawType": "object", "type": "unknown"}, {"name": "End2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "End2Max_LastDays", "rawType": "object", "type": "unknown"}, {"name": "End2Max_MaxDate", "rawType": "object", "type": "string"}, {"name": "MinCumRet_Date", "rawType": "object", "type": "string"}, {"name": "MinCumRet_SumRatio", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_DropDays", "rawType": "object", "type": "unknown"}, {"name": "Ret2Now_Ratio", "rawType": "float64", "type": "float"}, {"name": "Ret2Now_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Ret2Now_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "Ret2Now_MaxDate", "rawType": "object", "type": "string"}, {"name": "RetMax2Now_LastDays", "rawType": "object", "type": "unknown"}, {"name": "RecentTurn2Now_Ratio", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_Seg_MaxRise_EndDate", "rawType": "object", "type": "unknown"}, {"name": "MinCumRet_Seg_MaxRise_Sum", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_Seg_MaxRise_Avg", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_Seg_MaxRise_LastDays", "rawType": "object", "type": "unknown"}, {"name": "MinCumRet_Seg_MaxRise_End_To_Now_Days", "rawType": "object", "type": "unknown"}, {"name": "MinCumRet_Seg_Over1_Num", "rawType": "object", "type": "unknown"}, {"name": "MinCumRet_Seg_MaxDecline_EndDate", "rawType": "object", "type": "unknown"}, {"name": "MinCumRet_Seg_MaxDecline_Sum", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_Seg_MaxDecline_Avg", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_Seg_MaxDecline_LastDays", "rawType": "float64", "type": "float"}], "ref": "6d279def-d07d-4615-ac69-661a100a164f", "rows": [["6", "食品饮料", "-29.4", "2023-11-03", "2025-06-27", "399", "0.0", "0", "2025-06-27", "2025-06-27", "-29.402", "-0.074", "398", "0.0", "0", "0.0", "2025-06-27", "0", "-1.944", null, "0.0", "0.0", "0", "0", "0", null, "0.0", "0.0", "0.0"], ["10", "公用事业", "-31.51", "2024-07-08", "2025-03-06", "160", "11.016", "36", "2025-04-28", "2025-06-27", "-5.294", "-0.132", "40", "0.0", "0", "0.0", "2025-06-27", "0", "-2.977", null, "0.0", "0.0", "0", "0", "0", "2025-05-08", "-3.151", "-0.63", "5.0"], ["23", "银行", "-27.47", "2024-08-27", "2024-11-11", "48", "33.425", "151", "2025-06-26", "2025-06-27", "-3.72", "-3.72", "1", "0.0", "0", "0.0", "2025-06-27", "0", "-1.395", null, "0.0", "0.0", "0", "0", "0", null, "0.0", "0.0", "0.0"], ["27", "煤炭", "-52.81", "2024-02-22", "2025-03-06", "251", "10.192", "22", "2025-04-08", "2025-06-26", "-8.433", "-0.159", "53", "1.024", "1", "1.024", "2025-06-27", "0", "-1.476", null, "0.0", "0.0", "0", "0", "0", "2025-04-11", "-4.369", "-1.456", "3.0"], ["2", "钢铁", "-19.42", "2024-01-05", "2024-10-09", "180", "12.861", "104", "2025-03-13", "2025-06-26", "-10.587", "-0.151", "70", "0.924", "1", "0.924", "2025-06-27", "0", "-0.518", null, "0.0", "0.0", "0", "0", "0", "2025-03-19", "-4.503", "-1.126", "4.0"], ["9", "医药生物", "-30.43", "2023-12-13", "2025-01-24", "272", "11.657", "88", "2025-06-12", "2025-06-26", "-5.886", "-0.589", "10", "0.54", "1", "0.54", "2025-06-27", "0", "-0.69", null, "0.0", "0.0", "0", "0", "0", "2025-06-19", "-4.655", "-0.931", "5.0"], ["30", "美容护理", "-33.34", "2023-09-05", "2024-08-26", "236", "17.7", "183", "2025-06-04", "2025-06-26", "-10.806", "-0.675", "16", "0.433", "1", "0.433", "2025-06-27", "0", "-1.215", null, "0.0", "0.0", "0", "0", "0", "2025-06-20", "-7.019", "-1.17", "6.0"], ["11", "交通运输", "-16.69", "2024-08-05", "2025-02-21", "131", "8.289", "66", "2025-05-30", "2025-06-26", "-3.707", "-0.206", "18", "0.194", "1", "0.194", "2025-06-27", "0", "-1.72", null, "0.0", "0.0", "0", "0", "0", "2025-06-26", "-2.934", "-0.978", "3.0"], ["22", "通信", "-20.43", "2025-02-21", "2025-04-08", "32", "16.477", "48", "2025-06-19", "2025-06-25", "-1.31", "-0.327", "4", "3.687", "2", "1.844", "2025-06-27", "0", "2.377", null, "0.0", "0.0", "0", "0", "0", null, "0.0", "0.0", "0.0"], ["3", "有色金属", "-27.11", "2024-04-12", "2024-12-31", "177", "17.432", "106", "2025-06-13", "2025-06-25", "-3.721", "-0.465", "8", "3.355", "2", "1.677", "2025-06-27", "0", "2.191", null, "0.0", "0.0", "0", "0", "0", "2025-06-20", "-2.482", "-0.827", "3.0"], ["7", "纺织服饰", "-34.21", "2024-01-17", "2024-10-09", "172", "17.88", "158", "2025-06-04", "2025-06-25", "-7.899", "-0.527", "15", "1.869", "2", "0.935", "2025-06-27", "0", "0.807", null, "0.0", "0.0", "0", "0", "0", "2025-06-20", "-5.443", "-0.907", "6.0"], ["1", "基础化工", "-19.22", "2024-01-04", "2024-10-18", "188", "8.42", "154", "2025-06-09", "2025-06-25", "-2.368", "-0.197", "12", "0.907", "2", "0.453", "2025-06-27", "0", "0.167", null, "0.0", "0.0", "0", "0", "0", "2025-06-20", "-1.694", "-0.424", "4.0"], ["16", "建筑材料", "-30.7", "2023-09-04", "2025-02-21", "352", "8.373", "32", "2025-04-09", "2025-06-25", "-6.87", "-0.135", "51", "0.332", "2", "0.166", "2025-06-27", "0", "0.272", null, "0.0", "0.0", "0", "0", "0", "2025-04-28", "-2.036", "-0.509", "4.0"], ["28", "石油石化", "-32.03", "2024-04-19", "2025-03-06", "212", "9.384", "70", "2025-06-19", "2025-06-25", "-6.539", "-1.635", "4", "0.329", "2", "0.164", "2025-06-26", "1", "-6.211", null, "0.0", "0.0", "0", "0", "0", null, "0.0", "0.0", "0.0"], ["25", "汽车", "-21.77", "2023-11-28", "2024-02-06", "50", "37.181", "266", "2025-03-20", "2025-06-23", "-8.854", "-0.443", "20", "0.632", "4", "0.158", "2025-06-24", "3", "-0.149", null, "0.0", "0.0", "0", "0", "0", "2025-04-09", "-8.817", "-2.204", "4.0"], ["13", "商贸零售", "-28.72", "2023-12-15", "2024-07-10", "136", "35.444", "106", "2024-12-16", "2025-06-20", "-6.819", "-0.175", "39", "1.917", "5", "0.383", "2025-06-26", "1", "1.095", "2025-06-26", "2.348", "0.587", "4", "1", "0", "2024-12-19", "-8.415", "-2.805", "3.0"], ["26", "机械设备", "-19.6", "2024-01-02", "2024-02-07", "27", "30.725", "257", "2025-03-10", "2025-06-20", "-6.275", "-0.224", "28", "1.714", "5", "0.343", "2025-06-27", "0", "0.693", null, "0.0", "0.0", "0", "0", "0", "2025-04-08", "-8.668", "-2.889", "3.0"], ["14", "社会服务", "-29.53", "2023-06-27", "2024-07-24", "263", "22.056", "96", "2024-12-16", "2025-06-20", "-7.882", "-0.188", "42", "1.645", "5", "0.329", "2025-06-27", "0", "0.78", null, "0.0", "0.0", "0", "0", "0", "2024-12-25", "-12.643", "-1.806", "7.0"], ["29", "环保", "-24.09", "2024-01-12", "2024-02-07", "19", "18.715", "311", "2025-05-29", "2025-06-20", "-3.598", "-0.24", "15", "1.475", "5", "0.295", "2025-06-27", "0", "1.177", null, "0.0", "0.0", "0", "0", "0", "2025-06-20", "-1.679", "-0.56", "3.0"], ["24", "非银金融", "-19.69", "2024-11-07", "2025-04-16", "107", "10.78", "40", "2025-06-17", "2025-06-19", "-2.582", "-1.291", "2", "5.873", "6", "0.979", "2025-06-25", "2", "5.873", "2025-06-25", "7.047", "1.762", "4", "2", "1", null, "0.0", "0.0", "0.0"], ["12", "房地产", "-39.23", "2023-08-29", "2024-04-24", "157", "22.248", "130", "2024-11-07", "2025-06-19", "-20.223", "-0.137", "148", "0.471", "6", "0.078", "2025-06-27", "0", "0.471", null, "0.0", "0.0", "0", "0", "0", "2024-12-26", "-4.944", "-1.236", "4.0"], ["5", "家用电器", "-15.15", "2024-09-23", "2024-11-07", "29", "15.435", "44", "2025-01-09", "2025-06-16", "-11.411", "-0.113", "101", "0.532", "9", "0.059", "2025-06-20", "5", "-0.111", null, "0.0", "0.0", "0", "0", "0", "2025-01-14", "-5.515", "-1.838", "3.0"], ["18", "电力设备", "-29.93", "2023-06-30", "2024-07-10", "250", "15.624", "91", "2024-11-25", "2025-06-03", "-2.847", "-0.203", "14", "2.365", "18", "0.131", "2025-06-27", "0", "1.863", "2025-06-25", "1.978", "0.659", "3", "2", "0", "2025-04-09", "-6.15", "-1.538", "4.0"], ["4", "电子", "-23.91", "2023-12-15", "2024-02-05", "36", "45.834", "251", "2025-02-26", "2025-05-23", "-6.973", "-0.536", "13", "5.635", "24", "0.235", "2025-06-27", "0", "1.737", "2025-06-19", "2.753", "0.688", "4", "6", "0", "2025-04-08", "-9.779", "-3.26", "3.0"], ["20", "计算机", "-37.56", "2023-06-20", "2024-08-27", "290", "38.171", "123", "2025-03-06", "2025-05-23", "-6.255", "-0.481", "13", "5.27", "24", "0.22", "2025-06-26", "1", "2.979", "2025-06-26", "4.481", "1.12", "4", "1", "1", "2025-04-08", "-5.315", "-1.772", "3.0"], ["0", "农林牧渔", "-30.66", "2023-12-07", "2025-02-21", "290", "16.86", "31", "2025-04-08", "2025-05-12", "-10.769", "-0.513", "21", "1.019", "33", "0.031", "2025-06-11", "12", "-1.675", "2025-06-04", "3.158", "1.053", "3", "17", "1", "2025-04-11", "-5.189", "-1.73", "3.0"], ["15", "综合", "-42.09", "2023-12-15", "2024-06-24", "124", "35.018", "118", "2024-12-16", "2025-04-08", "-13.638", "-0.187", "73", "9.172", "54", "0.17", "2025-06-17", "8", "0.67", "2025-05-21", "5.956", "0.993", "6", "26", "0", "2024-12-19", "-6.347", "-2.116", "3.0"], ["21", "传媒", "-37.47", "2023-06-20", "2024-07-24", "266", "25.728", "133", "2025-02-14", "2025-04-08", "-11.889", "-0.33", "36", "7.64", "54", "0.141", "2025-06-16", "9", "-1.066", "2025-05-06", "4.045", "0.809", "5", "37", "0", "2025-02-28", "-4.831", "-0.966", "5.0"], ["8", "轻工制造", "-29.47", "2023-12-15", "2024-07-24", "146", "17.022", "95", "2024-12-13", "2025-04-08", "-12.239", "-0.165", "74", "6.507", "54", "0.12", "2025-06-12", "11", "1.137", "2025-05-20", "3.26", "0.815", "4", "27", "0", "2024-12-25", "-4.818", "-1.606", "3.0"], ["17", "建筑装饰", "-21.13", "2023-08-22", "2024-10-09", "271", "12.134", "42", "2024-12-06", "2025-04-01", "-2.599", "-0.371", "7", "1.258", "58", "0.022", "2025-06-06", "15", "0.925", "2025-05-19", "1.109", "0.37", "3", "28", "0", "2024-12-26", "-3.185", "-0.796", "4.0"], ["19", "国防军工", "-23.13", "2023-06-30", "2024-02-05", "150", "25.912", "181", "2024-11-11", "2025-02-18", "-14.445", "-0.226", "64", "13.862", "88", "0.158", "2025-06-27", "0", "3.267", "2025-03-11", "4.764", "1.588", "3", "73", "4", "2024-11-18", "-8.446", "-1.689", "5.0"]], "shape": {"columns": 28, "rows": 31}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>indus</th>\n", "      <th>MaxDrop</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>Drop_Lastdays</th>\n", "      <th>End2Max_Ratio</th>\n", "      <th>End2Max_LastDays</th>\n", "      <th>End2Max_MaxDate</th>\n", "      <th>MinCumRet_Date</th>\n", "      <th>MinCumRet_SumRatio</th>\n", "      <th>MinCumRet_AvgRatio</th>\n", "      <th>MinCumRet_DropDays</th>\n", "      <th>Ret2Now_Ratio</th>\n", "      <th>Ret2Now_LastDays</th>\n", "      <th>Ret2Now_AvgRatio</th>\n", "      <th>Ret2Now_MaxDate</th>\n", "      <th>RetMax2Now_LastDays</th>\n", "      <th>RecentTurn2Now_Ratio</th>\n", "      <th>MinCumRet_Seg_MaxRise_EndDate</th>\n", "      <th>MinCumRet_Seg_MaxRise_Sum</th>\n", "      <th>MinCumRet_Seg_MaxRise_Avg</th>\n", "      <th>MinCumRet_Seg_MaxRise_LastDays</th>\n", "      <th>MinCumRet_Seg_MaxRise_End_To_Now_Days</th>\n", "      <th>MinCumRet_Seg_Over1_Num</th>\n", "      <th>MinCumRet_Seg_MaxDecline_EndDate</th>\n", "      <th>MinCumRet_Seg_MaxDecline_Sum</th>\n", "      <th>MinCumRet_Seg_MaxDecline_Avg</th>\n", "      <th>MinCumRet_Seg_MaxDecline_LastDays</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>食品饮料</td>\n", "      <td>-29.40</td>\n", "      <td>2023-11-03</td>\n", "      <td>2025-06-27</td>\n", "      <td>399</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>2025-06-27</td>\n", "      <td>2025-06-27</td>\n", "      <td>-29.402</td>\n", "      <td>-0.074</td>\n", "      <td>398</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>-1.944</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>公用事业</td>\n", "      <td>-31.51</td>\n", "      <td>2024-07-08</td>\n", "      <td>2025-03-06</td>\n", "      <td>160</td>\n", "      <td>11.016</td>\n", "      <td>36</td>\n", "      <td>2025-04-28</td>\n", "      <td>2025-06-27</td>\n", "      <td>-5.294</td>\n", "      <td>-0.132</td>\n", "      <td>40</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>-2.977</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-05-08</td>\n", "      <td>-3.151</td>\n", "      <td>-0.630</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>银行</td>\n", "      <td>-27.47</td>\n", "      <td>2024-08-27</td>\n", "      <td>2024-11-11</td>\n", "      <td>48</td>\n", "      <td>33.425</td>\n", "      <td>151</td>\n", "      <td>2025-06-26</td>\n", "      <td>2025-06-27</td>\n", "      <td>-3.720</td>\n", "      <td>-3.720</td>\n", "      <td>1</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0.000</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>-1.395</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>煤炭</td>\n", "      <td>-52.81</td>\n", "      <td>2024-02-22</td>\n", "      <td>2025-03-06</td>\n", "      <td>251</td>\n", "      <td>10.192</td>\n", "      <td>22</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-06-26</td>\n", "      <td>-8.433</td>\n", "      <td>-0.159</td>\n", "      <td>53</td>\n", "      <td>1.024</td>\n", "      <td>1</td>\n", "      <td>1.024</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>-1.476</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-04-11</td>\n", "      <td>-4.369</td>\n", "      <td>-1.456</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>钢铁</td>\n", "      <td>-19.42</td>\n", "      <td>2024-01-05</td>\n", "      <td>2024-10-09</td>\n", "      <td>180</td>\n", "      <td>12.861</td>\n", "      <td>104</td>\n", "      <td>2025-03-13</td>\n", "      <td>2025-06-26</td>\n", "      <td>-10.587</td>\n", "      <td>-0.151</td>\n", "      <td>70</td>\n", "      <td>0.924</td>\n", "      <td>1</td>\n", "      <td>0.924</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>-0.518</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-03-19</td>\n", "      <td>-4.503</td>\n", "      <td>-1.126</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>医药生物</td>\n", "      <td>-30.43</td>\n", "      <td>2023-12-13</td>\n", "      <td>2025-01-24</td>\n", "      <td>272</td>\n", "      <td>11.657</td>\n", "      <td>88</td>\n", "      <td>2025-06-12</td>\n", "      <td>2025-06-26</td>\n", "      <td>-5.886</td>\n", "      <td>-0.589</td>\n", "      <td>10</td>\n", "      <td>0.540</td>\n", "      <td>1</td>\n", "      <td>0.540</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>-0.690</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-06-19</td>\n", "      <td>-4.655</td>\n", "      <td>-0.931</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>美容护理</td>\n", "      <td>-33.34</td>\n", "      <td>2023-09-05</td>\n", "      <td>2024-08-26</td>\n", "      <td>236</td>\n", "      <td>17.700</td>\n", "      <td>183</td>\n", "      <td>2025-06-04</td>\n", "      <td>2025-06-26</td>\n", "      <td>-10.806</td>\n", "      <td>-0.675</td>\n", "      <td>16</td>\n", "      <td>0.433</td>\n", "      <td>1</td>\n", "      <td>0.433</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>-1.215</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-06-20</td>\n", "      <td>-7.019</td>\n", "      <td>-1.170</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>交通运输</td>\n", "      <td>-16.69</td>\n", "      <td>2024-08-05</td>\n", "      <td>2025-02-21</td>\n", "      <td>131</td>\n", "      <td>8.289</td>\n", "      <td>66</td>\n", "      <td>2025-05-30</td>\n", "      <td>2025-06-26</td>\n", "      <td>-3.707</td>\n", "      <td>-0.206</td>\n", "      <td>18</td>\n", "      <td>0.194</td>\n", "      <td>1</td>\n", "      <td>0.194</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>-1.720</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-06-26</td>\n", "      <td>-2.934</td>\n", "      <td>-0.978</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>通信</td>\n", "      <td>-20.43</td>\n", "      <td>2025-02-21</td>\n", "      <td>2025-04-08</td>\n", "      <td>32</td>\n", "      <td>16.477</td>\n", "      <td>48</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025-06-25</td>\n", "      <td>-1.310</td>\n", "      <td>-0.327</td>\n", "      <td>4</td>\n", "      <td>3.687</td>\n", "      <td>2</td>\n", "      <td>1.844</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>2.377</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>有色金属</td>\n", "      <td>-27.11</td>\n", "      <td>2024-04-12</td>\n", "      <td>2024-12-31</td>\n", "      <td>177</td>\n", "      <td>17.432</td>\n", "      <td>106</td>\n", "      <td>2025-06-13</td>\n", "      <td>2025-06-25</td>\n", "      <td>-3.721</td>\n", "      <td>-0.465</td>\n", "      <td>8</td>\n", "      <td>3.355</td>\n", "      <td>2</td>\n", "      <td>1.677</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>2.191</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-06-20</td>\n", "      <td>-2.482</td>\n", "      <td>-0.827</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>纺织服饰</td>\n", "      <td>-34.21</td>\n", "      <td>2024-01-17</td>\n", "      <td>2024-10-09</td>\n", "      <td>172</td>\n", "      <td>17.880</td>\n", "      <td>158</td>\n", "      <td>2025-06-04</td>\n", "      <td>2025-06-25</td>\n", "      <td>-7.899</td>\n", "      <td>-0.527</td>\n", "      <td>15</td>\n", "      <td>1.869</td>\n", "      <td>2</td>\n", "      <td>0.935</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>0.807</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-06-20</td>\n", "      <td>-5.443</td>\n", "      <td>-0.907</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>基础化工</td>\n", "      <td>-19.22</td>\n", "      <td>2024-01-04</td>\n", "      <td>2024-10-18</td>\n", "      <td>188</td>\n", "      <td>8.420</td>\n", "      <td>154</td>\n", "      <td>2025-06-09</td>\n", "      <td>2025-06-25</td>\n", "      <td>-2.368</td>\n", "      <td>-0.197</td>\n", "      <td>12</td>\n", "      <td>0.907</td>\n", "      <td>2</td>\n", "      <td>0.453</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>0.167</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-06-20</td>\n", "      <td>-1.694</td>\n", "      <td>-0.424</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>建筑材料</td>\n", "      <td>-30.70</td>\n", "      <td>2023-09-04</td>\n", "      <td>2025-02-21</td>\n", "      <td>352</td>\n", "      <td>8.373</td>\n", "      <td>32</td>\n", "      <td>2025-04-09</td>\n", "      <td>2025-06-25</td>\n", "      <td>-6.870</td>\n", "      <td>-0.135</td>\n", "      <td>51</td>\n", "      <td>0.332</td>\n", "      <td>2</td>\n", "      <td>0.166</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>0.272</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-04-28</td>\n", "      <td>-2.036</td>\n", "      <td>-0.509</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>石油石化</td>\n", "      <td>-32.03</td>\n", "      <td>2024-04-19</td>\n", "      <td>2025-03-06</td>\n", "      <td>212</td>\n", "      <td>9.384</td>\n", "      <td>70</td>\n", "      <td>2025-06-19</td>\n", "      <td>2025-06-25</td>\n", "      <td>-6.539</td>\n", "      <td>-1.635</td>\n", "      <td>4</td>\n", "      <td>0.329</td>\n", "      <td>2</td>\n", "      <td>0.164</td>\n", "      <td>2025-06-26</td>\n", "      <td>1</td>\n", "      <td>-6.211</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>汽车</td>\n", "      <td>-21.77</td>\n", "      <td>2023-11-28</td>\n", "      <td>2024-02-06</td>\n", "      <td>50</td>\n", "      <td>37.181</td>\n", "      <td>266</td>\n", "      <td>2025-03-20</td>\n", "      <td>2025-06-23</td>\n", "      <td>-8.854</td>\n", "      <td>-0.443</td>\n", "      <td>20</td>\n", "      <td>0.632</td>\n", "      <td>4</td>\n", "      <td>0.158</td>\n", "      <td>2025-06-24</td>\n", "      <td>3</td>\n", "      <td>-0.149</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-04-09</td>\n", "      <td>-8.817</td>\n", "      <td>-2.204</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>商贸零售</td>\n", "      <td>-28.72</td>\n", "      <td>2023-12-15</td>\n", "      <td>2024-07-10</td>\n", "      <td>136</td>\n", "      <td>35.444</td>\n", "      <td>106</td>\n", "      <td>2024-12-16</td>\n", "      <td>2025-06-20</td>\n", "      <td>-6.819</td>\n", "      <td>-0.175</td>\n", "      <td>39</td>\n", "      <td>1.917</td>\n", "      <td>5</td>\n", "      <td>0.383</td>\n", "      <td>2025-06-26</td>\n", "      <td>1</td>\n", "      <td>1.095</td>\n", "      <td>2025-06-26</td>\n", "      <td>2.348</td>\n", "      <td>0.587</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2024-12-19</td>\n", "      <td>-8.415</td>\n", "      <td>-2.805</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>机械设备</td>\n", "      <td>-19.60</td>\n", "      <td>2024-01-02</td>\n", "      <td>2024-02-07</td>\n", "      <td>27</td>\n", "      <td>30.725</td>\n", "      <td>257</td>\n", "      <td>2025-03-10</td>\n", "      <td>2025-06-20</td>\n", "      <td>-6.275</td>\n", "      <td>-0.224</td>\n", "      <td>28</td>\n", "      <td>1.714</td>\n", "      <td>5</td>\n", "      <td>0.343</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>0.693</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-04-08</td>\n", "      <td>-8.668</td>\n", "      <td>-2.889</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>社会服务</td>\n", "      <td>-29.53</td>\n", "      <td>2023-06-27</td>\n", "      <td>2024-07-24</td>\n", "      <td>263</td>\n", "      <td>22.056</td>\n", "      <td>96</td>\n", "      <td>2024-12-16</td>\n", "      <td>2025-06-20</td>\n", "      <td>-7.882</td>\n", "      <td>-0.188</td>\n", "      <td>42</td>\n", "      <td>1.645</td>\n", "      <td>5</td>\n", "      <td>0.329</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>0.780</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2024-12-25</td>\n", "      <td>-12.643</td>\n", "      <td>-1.806</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>环保</td>\n", "      <td>-24.09</td>\n", "      <td>2024-01-12</td>\n", "      <td>2024-02-07</td>\n", "      <td>19</td>\n", "      <td>18.715</td>\n", "      <td>311</td>\n", "      <td>2025-05-29</td>\n", "      <td>2025-06-20</td>\n", "      <td>-3.598</td>\n", "      <td>-0.240</td>\n", "      <td>15</td>\n", "      <td>1.475</td>\n", "      <td>5</td>\n", "      <td>0.295</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>1.177</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-06-20</td>\n", "      <td>-1.679</td>\n", "      <td>-0.560</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>非银金融</td>\n", "      <td>-19.69</td>\n", "      <td>2024-11-07</td>\n", "      <td>2025-04-16</td>\n", "      <td>107</td>\n", "      <td>10.780</td>\n", "      <td>40</td>\n", "      <td>2025-06-17</td>\n", "      <td>2025-06-19</td>\n", "      <td>-2.582</td>\n", "      <td>-1.291</td>\n", "      <td>2</td>\n", "      <td>5.873</td>\n", "      <td>6</td>\n", "      <td>0.979</td>\n", "      <td>2025-06-25</td>\n", "      <td>2</td>\n", "      <td>5.873</td>\n", "      <td>2025-06-25</td>\n", "      <td>7.047</td>\n", "      <td>1.762</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>房地产</td>\n", "      <td>-39.23</td>\n", "      <td>2023-08-29</td>\n", "      <td>2024-04-24</td>\n", "      <td>157</td>\n", "      <td>22.248</td>\n", "      <td>130</td>\n", "      <td>2024-11-07</td>\n", "      <td>2025-06-19</td>\n", "      <td>-20.223</td>\n", "      <td>-0.137</td>\n", "      <td>148</td>\n", "      <td>0.471</td>\n", "      <td>6</td>\n", "      <td>0.078</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>0.471</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2024-12-26</td>\n", "      <td>-4.944</td>\n", "      <td>-1.236</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>家用电器</td>\n", "      <td>-15.15</td>\n", "      <td>2024-09-23</td>\n", "      <td>2024-11-07</td>\n", "      <td>29</td>\n", "      <td>15.435</td>\n", "      <td>44</td>\n", "      <td>2025-01-09</td>\n", "      <td>2025-06-16</td>\n", "      <td>-11.411</td>\n", "      <td>-0.113</td>\n", "      <td>101</td>\n", "      <td>0.532</td>\n", "      <td>9</td>\n", "      <td>0.059</td>\n", "      <td>2025-06-20</td>\n", "      <td>5</td>\n", "      <td>-0.111</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-01-14</td>\n", "      <td>-5.515</td>\n", "      <td>-1.838</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>电力设备</td>\n", "      <td>-29.93</td>\n", "      <td>2023-06-30</td>\n", "      <td>2024-07-10</td>\n", "      <td>250</td>\n", "      <td>15.624</td>\n", "      <td>91</td>\n", "      <td>2024-11-25</td>\n", "      <td>2025-06-03</td>\n", "      <td>-2.847</td>\n", "      <td>-0.203</td>\n", "      <td>14</td>\n", "      <td>2.365</td>\n", "      <td>18</td>\n", "      <td>0.131</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>1.863</td>\n", "      <td>2025-06-25</td>\n", "      <td>1.978</td>\n", "      <td>0.659</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>2025-04-09</td>\n", "      <td>-6.150</td>\n", "      <td>-1.538</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>电子</td>\n", "      <td>-23.91</td>\n", "      <td>2023-12-15</td>\n", "      <td>2024-02-05</td>\n", "      <td>36</td>\n", "      <td>45.834</td>\n", "      <td>251</td>\n", "      <td>2025-02-26</td>\n", "      <td>2025-05-23</td>\n", "      <td>-6.973</td>\n", "      <td>-0.536</td>\n", "      <td>13</td>\n", "      <td>5.635</td>\n", "      <td>24</td>\n", "      <td>0.235</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>1.737</td>\n", "      <td>2025-06-19</td>\n", "      <td>2.753</td>\n", "      <td>0.688</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>2025-04-08</td>\n", "      <td>-9.779</td>\n", "      <td>-3.260</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>计算机</td>\n", "      <td>-37.56</td>\n", "      <td>2023-06-20</td>\n", "      <td>2024-08-27</td>\n", "      <td>290</td>\n", "      <td>38.171</td>\n", "      <td>123</td>\n", "      <td>2025-03-06</td>\n", "      <td>2025-05-23</td>\n", "      <td>-6.255</td>\n", "      <td>-0.481</td>\n", "      <td>13</td>\n", "      <td>5.270</td>\n", "      <td>24</td>\n", "      <td>0.220</td>\n", "      <td>2025-06-26</td>\n", "      <td>1</td>\n", "      <td>2.979</td>\n", "      <td>2025-06-26</td>\n", "      <td>4.481</td>\n", "      <td>1.120</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2025-04-08</td>\n", "      <td>-5.315</td>\n", "      <td>-1.772</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>农林牧渔</td>\n", "      <td>-30.66</td>\n", "      <td>2023-12-07</td>\n", "      <td>2025-02-21</td>\n", "      <td>290</td>\n", "      <td>16.860</td>\n", "      <td>31</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-05-12</td>\n", "      <td>-10.769</td>\n", "      <td>-0.513</td>\n", "      <td>21</td>\n", "      <td>1.019</td>\n", "      <td>33</td>\n", "      <td>0.031</td>\n", "      <td>2025-06-11</td>\n", "      <td>12</td>\n", "      <td>-1.675</td>\n", "      <td>2025-06-04</td>\n", "      <td>3.158</td>\n", "      <td>1.053</td>\n", "      <td>3</td>\n", "      <td>17</td>\n", "      <td>1</td>\n", "      <td>2025-04-11</td>\n", "      <td>-5.189</td>\n", "      <td>-1.730</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>综合</td>\n", "      <td>-42.09</td>\n", "      <td>2023-12-15</td>\n", "      <td>2024-06-24</td>\n", "      <td>124</td>\n", "      <td>35.018</td>\n", "      <td>118</td>\n", "      <td>2024-12-16</td>\n", "      <td>2025-04-08</td>\n", "      <td>-13.638</td>\n", "      <td>-0.187</td>\n", "      <td>73</td>\n", "      <td>9.172</td>\n", "      <td>54</td>\n", "      <td>0.170</td>\n", "      <td>2025-06-17</td>\n", "      <td>8</td>\n", "      <td>0.670</td>\n", "      <td>2025-05-21</td>\n", "      <td>5.956</td>\n", "      <td>0.993</td>\n", "      <td>6</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>2024-12-19</td>\n", "      <td>-6.347</td>\n", "      <td>-2.116</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>传媒</td>\n", "      <td>-37.47</td>\n", "      <td>2023-06-20</td>\n", "      <td>2024-07-24</td>\n", "      <td>266</td>\n", "      <td>25.728</td>\n", "      <td>133</td>\n", "      <td>2025-02-14</td>\n", "      <td>2025-04-08</td>\n", "      <td>-11.889</td>\n", "      <td>-0.330</td>\n", "      <td>36</td>\n", "      <td>7.640</td>\n", "      <td>54</td>\n", "      <td>0.141</td>\n", "      <td>2025-06-16</td>\n", "      <td>9</td>\n", "      <td>-1.066</td>\n", "      <td>2025-05-06</td>\n", "      <td>4.045</td>\n", "      <td>0.809</td>\n", "      <td>5</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>2025-02-28</td>\n", "      <td>-4.831</td>\n", "      <td>-0.966</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>轻工制造</td>\n", "      <td>-29.47</td>\n", "      <td>2023-12-15</td>\n", "      <td>2024-07-24</td>\n", "      <td>146</td>\n", "      <td>17.022</td>\n", "      <td>95</td>\n", "      <td>2024-12-13</td>\n", "      <td>2025-04-08</td>\n", "      <td>-12.239</td>\n", "      <td>-0.165</td>\n", "      <td>74</td>\n", "      <td>6.507</td>\n", "      <td>54</td>\n", "      <td>0.120</td>\n", "      <td>2025-06-12</td>\n", "      <td>11</td>\n", "      <td>1.137</td>\n", "      <td>2025-05-20</td>\n", "      <td>3.260</td>\n", "      <td>0.815</td>\n", "      <td>4</td>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>2024-12-25</td>\n", "      <td>-4.818</td>\n", "      <td>-1.606</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>建筑装饰</td>\n", "      <td>-21.13</td>\n", "      <td>2023-08-22</td>\n", "      <td>2024-10-09</td>\n", "      <td>271</td>\n", "      <td>12.134</td>\n", "      <td>42</td>\n", "      <td>2024-12-06</td>\n", "      <td>2025-04-01</td>\n", "      <td>-2.599</td>\n", "      <td>-0.371</td>\n", "      <td>7</td>\n", "      <td>1.258</td>\n", "      <td>58</td>\n", "      <td>0.022</td>\n", "      <td>2025-06-06</td>\n", "      <td>15</td>\n", "      <td>0.925</td>\n", "      <td>2025-05-19</td>\n", "      <td>1.109</td>\n", "      <td>0.370</td>\n", "      <td>3</td>\n", "      <td>28</td>\n", "      <td>0</td>\n", "      <td>2024-12-26</td>\n", "      <td>-3.185</td>\n", "      <td>-0.796</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>国防军工</td>\n", "      <td>-23.13</td>\n", "      <td>2023-06-30</td>\n", "      <td>2024-02-05</td>\n", "      <td>150</td>\n", "      <td>25.912</td>\n", "      <td>181</td>\n", "      <td>2024-11-11</td>\n", "      <td>2025-02-18</td>\n", "      <td>-14.445</td>\n", "      <td>-0.226</td>\n", "      <td>64</td>\n", "      <td>13.862</td>\n", "      <td>88</td>\n", "      <td>0.158</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>3.267</td>\n", "      <td>2025-03-11</td>\n", "      <td>4.764</td>\n", "      <td>1.588</td>\n", "      <td>3</td>\n", "      <td>73</td>\n", "      <td>4</td>\n", "      <td>2024-11-18</td>\n", "      <td>-8.446</td>\n", "      <td>-1.689</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   indus  MaxDrop  start_date    end_date Drop_Lastdays  End2Max_Ratio  \\\n", "6   食品饮料   -29.40  2023-11-03  2025-06-27           399          0.000   \n", "10  公用事业   -31.51  2024-07-08  2025-03-06           160         11.016   \n", "23    银行   -27.47  2024-08-27  2024-11-11            48         33.425   \n", "27    煤炭   -52.81  2024-02-22  2025-03-06           251         10.192   \n", "2     钢铁   -19.42  2024-01-05  2024-10-09           180         12.861   \n", "9   医药生物   -30.43  2023-12-13  2025-01-24           272         11.657   \n", "30  美容护理   -33.34  2023-09-05  2024-08-26           236         17.700   \n", "11  交通运输   -16.69  2024-08-05  2025-02-21           131          8.289   \n", "22    通信   -20.43  2025-02-21  2025-04-08            32         16.477   \n", "3   有色金属   -27.11  2024-04-12  2024-12-31           177         17.432   \n", "7   纺织服饰   -34.21  2024-01-17  2024-10-09           172         17.880   \n", "1   基础化工   -19.22  2024-01-04  2024-10-18           188          8.420   \n", "16  建筑材料   -30.70  2023-09-04  2025-02-21           352          8.373   \n", "28  石油石化   -32.03  2024-04-19  2025-03-06           212          9.384   \n", "25    汽车   -21.77  2023-11-28  2024-02-06            50         37.181   \n", "13  商贸零售   -28.72  2023-12-15  2024-07-10           136         35.444   \n", "26  机械设备   -19.60  2024-01-02  2024-02-07            27         30.725   \n", "14  社会服务   -29.53  2023-06-27  2024-07-24           263         22.056   \n", "29    环保   -24.09  2024-01-12  2024-02-07            19         18.715   \n", "24  非银金融   -19.69  2024-11-07  2025-04-16           107         10.780   \n", "12   房地产   -39.23  2023-08-29  2024-04-24           157         22.248   \n", "5   家用电器   -15.15  2024-09-23  2024-11-07            29         15.435   \n", "18  电力设备   -29.93  2023-06-30  2024-07-10           250         15.624   \n", "4     电子   -23.91  2023-12-15  2024-02-05            36         45.834   \n", "20   计算机   -37.56  2023-06-20  2024-08-27           290         38.171   \n", "0   农林牧渔   -30.66  2023-12-07  2025-02-21           290         16.860   \n", "15    综合   -42.09  2023-12-15  2024-06-24           124         35.018   \n", "21    传媒   -37.47  2023-06-20  2024-07-24           266         25.728   \n", "8   轻工制造   -29.47  2023-12-15  2024-07-24           146         17.022   \n", "17  建筑装饰   -21.13  2023-08-22  2024-10-09           271         12.134   \n", "19  国防军工   -23.13  2023-06-30  2024-02-05           150         25.912   \n", "\n", "   End2Max_LastDays End2Max_MaxDate MinCumRet_Date  MinCumRet_SumRatio  \\\n", "6                 0      2025-06-27     2025-06-27             -29.402   \n", "10               36      2025-04-28     2025-06-27              -5.294   \n", "23              151      2025-06-26     2025-06-27              -3.720   \n", "27               22      2025-04-08     2025-06-26              -8.433   \n", "2               104      2025-03-13     2025-06-26             -10.587   \n", "9                88      2025-06-12     2025-06-26              -5.886   \n", "30              183      2025-06-04     2025-06-26             -10.806   \n", "11               66      2025-05-30     2025-06-26              -3.707   \n", "22               48      2025-06-19     2025-06-25              -1.310   \n", "3               106      2025-06-13     2025-06-25              -3.721   \n", "7               158      2025-06-04     2025-06-25              -7.899   \n", "1               154      2025-06-09     2025-06-25              -2.368   \n", "16               32      2025-04-09     2025-06-25              -6.870   \n", "28               70      2025-06-19     2025-06-25              -6.539   \n", "25              266      2025-03-20     2025-06-23              -8.854   \n", "13              106      2024-12-16     2025-06-20              -6.819   \n", "26              257      2025-03-10     2025-06-20              -6.275   \n", "14               96      2024-12-16     2025-06-20              -7.882   \n", "29              311      2025-05-29     2025-06-20              -3.598   \n", "24               40      2025-06-17     2025-06-19              -2.582   \n", "12              130      2024-11-07     2025-06-19             -20.223   \n", "5                44      2025-01-09     2025-06-16             -11.411   \n", "18               91      2024-11-25     2025-06-03              -2.847   \n", "4               251      2025-02-26     2025-05-23              -6.973   \n", "20              123      2025-03-06     2025-05-23              -6.255   \n", "0                31      2025-04-08     2025-05-12             -10.769   \n", "15              118      2024-12-16     2025-04-08             -13.638   \n", "21              133      2025-02-14     2025-04-08             -11.889   \n", "8                95      2024-12-13     2025-04-08             -12.239   \n", "17               42      2024-12-06     2025-04-01              -2.599   \n", "19              181      2024-11-11     2025-02-18             -14.445   \n", "\n", "    MinCumRet_AvgRatio MinCumRet_DropDays  Ret2Now_Ratio Ret2Now_LastDays  \\\n", "6               -0.074                398          0.000                0   \n", "10              -0.132                 40          0.000                0   \n", "23              -3.720                  1          0.000                0   \n", "27              -0.159                 53          1.024                1   \n", "2               -0.151                 70          0.924                1   \n", "9               -0.589                 10          0.540                1   \n", "30              -0.675                 16          0.433                1   \n", "11              -0.206                 18          0.194                1   \n", "22              -0.327                  4          3.687                2   \n", "3               -0.465                  8          3.355                2   \n", "7               -0.527                 15          1.869                2   \n", "1               -0.197                 12          0.907                2   \n", "16              -0.135                 51          0.332                2   \n", "28              -1.635                  4          0.329                2   \n", "25              -0.443                 20          0.632                4   \n", "13              -0.175                 39          1.917                5   \n", "26              -0.224                 28          1.714                5   \n", "14              -0.188                 42          1.645                5   \n", "29              -0.240                 15          1.475                5   \n", "24              -1.291                  2          5.873                6   \n", "12              -0.137                148          0.471                6   \n", "5               -0.113                101          0.532                9   \n", "18              -0.203                 14          2.365               18   \n", "4               -0.536                 13          5.635               24   \n", "20              -0.481                 13          5.270               24   \n", "0               -0.513                 21          1.019               33   \n", "15              -0.187                 73          9.172               54   \n", "21              -0.330                 36          7.640               54   \n", "8               -0.165                 74          6.507               54   \n", "17              -0.371                  7          1.258               58   \n", "19              -0.226                 64         13.862               88   \n", "\n", "    Ret2Now_AvgRatio Ret2Now_MaxDate RetMax2Now_LastDays  \\\n", "6              0.000      2025-06-27                   0   \n", "10             0.000      2025-06-27                   0   \n", "23             0.000      2025-06-27                   0   \n", "27             1.024      2025-06-27                   0   \n", "2              0.924      2025-06-27                   0   \n", "9              0.540      2025-06-27                   0   \n", "30             0.433      2025-06-27                   0   \n", "11             0.194      2025-06-27                   0   \n", "22             1.844      2025-06-27                   0   \n", "3              1.677      2025-06-27                   0   \n", "7              0.935      2025-06-27                   0   \n", "1              0.453      2025-06-27                   0   \n", "16             0.166      2025-06-27                   0   \n", "28             0.164      2025-06-26                   1   \n", "25             0.158      2025-06-24                   3   \n", "13             0.383      2025-06-26                   1   \n", "26             0.343      2025-06-27                   0   \n", "14             0.329      2025-06-27                   0   \n", "29             0.295      2025-06-27                   0   \n", "24             0.979      2025-06-25                   2   \n", "12             0.078      2025-06-27                   0   \n", "5              0.059      2025-06-20                   5   \n", "18             0.131      2025-06-27                   0   \n", "4              0.235      2025-06-27                   0   \n", "20             0.220      2025-06-26                   1   \n", "0              0.031      2025-06-11                  12   \n", "15             0.170      2025-06-17                   8   \n", "21             0.141      2025-06-16                   9   \n", "8              0.120      2025-06-12                  11   \n", "17             0.022      2025-06-06                  15   \n", "19             0.158      2025-06-27                   0   \n", "\n", "    RecentTurn2Now_Ratio MinCumRet_Seg_MaxRise_EndDate  \\\n", "6                 -1.944                          None   \n", "10                -2.977                          None   \n", "23                -1.395                          None   \n", "27                -1.476                          None   \n", "2                 -0.518                          None   \n", "9                 -0.690                          None   \n", "30                -1.215                          None   \n", "11                -1.720                          None   \n", "22                 2.377                          None   \n", "3                  2.191                          None   \n", "7                  0.807                          None   \n", "1                  0.167                          None   \n", "16                 0.272                          None   \n", "28                -6.211                          None   \n", "25                -0.149                          None   \n", "13                 1.095                    2025-06-26   \n", "26                 0.693                          None   \n", "14                 0.780                          None   \n", "29                 1.177                          None   \n", "24                 5.873                    2025-06-25   \n", "12                 0.471                          None   \n", "5                 -0.111                          None   \n", "18                 1.863                    2025-06-25   \n", "4                  1.737                    2025-06-19   \n", "20                 2.979                    2025-06-26   \n", "0                 -1.675                    2025-06-04   \n", "15                 0.670                    2025-05-21   \n", "21                -1.066                    2025-05-06   \n", "8                  1.137                    2025-05-20   \n", "17                 0.925                    2025-05-19   \n", "19                 3.267                    2025-03-11   \n", "\n", "    MinCumRet_Seg_MaxRise_Sum  MinCumRet_Seg_MaxRise_Avg  \\\n", "6                       0.000                      0.000   \n", "10                      0.000                      0.000   \n", "23                      0.000                      0.000   \n", "27                      0.000                      0.000   \n", "2                       0.000                      0.000   \n", "9                       0.000                      0.000   \n", "30                      0.000                      0.000   \n", "11                      0.000                      0.000   \n", "22                      0.000                      0.000   \n", "3                       0.000                      0.000   \n", "7                       0.000                      0.000   \n", "1                       0.000                      0.000   \n", "16                      0.000                      0.000   \n", "28                      0.000                      0.000   \n", "25                      0.000                      0.000   \n", "13                      2.348                      0.587   \n", "26                      0.000                      0.000   \n", "14                      0.000                      0.000   \n", "29                      0.000                      0.000   \n", "24                      7.047                      1.762   \n", "12                      0.000                      0.000   \n", "5                       0.000                      0.000   \n", "18                      1.978                      0.659   \n", "4                       2.753                      0.688   \n", "20                      4.481                      1.120   \n", "0                       3.158                      1.053   \n", "15                      5.956                      0.993   \n", "21                      4.045                      0.809   \n", "8                       3.260                      0.815   \n", "17                      1.109                      0.370   \n", "19                      4.764                      1.588   \n", "\n", "   MinCumRet_Seg_MaxRise_LastDays MinCumRet_Seg_MaxRise_End_To_Now_Days  \\\n", "6                               0                                     0   \n", "10                              0                                     0   \n", "23                              0                                     0   \n", "27                              0                                     0   \n", "2                               0                                     0   \n", "9                               0                                     0   \n", "30                              0                                     0   \n", "11                              0                                     0   \n", "22                              0                                     0   \n", "3                               0                                     0   \n", "7                               0                                     0   \n", "1                               0                                     0   \n", "16                              0                                     0   \n", "28                              0                                     0   \n", "25                              0                                     0   \n", "13                              4                                     1   \n", "26                              0                                     0   \n", "14                              0                                     0   \n", "29                              0                                     0   \n", "24                              4                                     2   \n", "12                              0                                     0   \n", "5                               0                                     0   \n", "18                              3                                     2   \n", "4                               4                                     6   \n", "20                              4                                     1   \n", "0                               3                                    17   \n", "15                              6                                    26   \n", "21                              5                                    37   \n", "8                               4                                    27   \n", "17                              3                                    28   \n", "19                              3                                    73   \n", "\n", "   MinCumRet_Seg_Over1_Num MinCumRet_Seg_MaxDecline_EndDate  \\\n", "6                        0                             None   \n", "10                       0                       2025-05-08   \n", "23                       0                             None   \n", "27                       0                       2025-04-11   \n", "2                        0                       2025-03-19   \n", "9                        0                       2025-06-19   \n", "30                       0                       2025-06-20   \n", "11                       0                       2025-06-26   \n", "22                       0                             None   \n", "3                        0                       2025-06-20   \n", "7                        0                       2025-06-20   \n", "1                        0                       2025-06-20   \n", "16                       0                       2025-04-28   \n", "28                       0                             None   \n", "25                       0                       2025-04-09   \n", "13                       0                       2024-12-19   \n", "26                       0                       2025-04-08   \n", "14                       0                       2024-12-25   \n", "29                       0                       2025-06-20   \n", "24                       1                             None   \n", "12                       0                       2024-12-26   \n", "5                        0                       2025-01-14   \n", "18                       0                       2025-04-09   \n", "4                        0                       2025-04-08   \n", "20                       1                       2025-04-08   \n", "0                        1                       2025-04-11   \n", "15                       0                       2024-12-19   \n", "21                       0                       2025-02-28   \n", "8                        0                       2024-12-25   \n", "17                       0                       2024-12-26   \n", "19                       4                       2024-11-18   \n", "\n", "    MinCumRet_Seg_MaxDecline_Sum  MinCumRet_Seg_MaxDecline_Avg  \\\n", "6                          0.000                         0.000   \n", "10                        -3.151                        -0.630   \n", "23                         0.000                         0.000   \n", "27                        -4.369                        -1.456   \n", "2                         -4.503                        -1.126   \n", "9                         -4.655                        -0.931   \n", "30                        -7.019                        -1.170   \n", "11                        -2.934                        -0.978   \n", "22                         0.000                         0.000   \n", "3                         -2.482                        -0.827   \n", "7                         -5.443                        -0.907   \n", "1                         -1.694                        -0.424   \n", "16                        -2.036                        -0.509   \n", "28                         0.000                         0.000   \n", "25                        -8.817                        -2.204   \n", "13                        -8.415                        -2.805   \n", "26                        -8.668                        -2.889   \n", "14                       -12.643                        -1.806   \n", "29                        -1.679                        -0.560   \n", "24                         0.000                         0.000   \n", "12                        -4.944                        -1.236   \n", "5                         -5.515                        -1.838   \n", "18                        -6.150                        -1.538   \n", "4                         -9.779                        -3.260   \n", "20                        -5.315                        -1.772   \n", "0                         -5.189                        -1.730   \n", "15                        -6.347                        -2.116   \n", "21                        -4.831                        -0.966   \n", "8                         -4.818                        -1.606   \n", "17                        -3.185                        -0.796   \n", "19                        -8.446                        -1.689   \n", "\n", "    MinCumRet_Seg_MaxDecline_LastDays  \n", "6                                 0.0  \n", "10                                5.0  \n", "23                                0.0  \n", "27                                3.0  \n", "2                                 4.0  \n", "9                                 5.0  \n", "30                                6.0  \n", "11                                3.0  \n", "22                                0.0  \n", "3                                 3.0  \n", "7                                 6.0  \n", "1                                 4.0  \n", "16                                4.0  \n", "28                                0.0  \n", "25                                4.0  \n", "13                                3.0  \n", "26                                3.0  \n", "14                                7.0  \n", "29                                3.0  \n", "24                                0.0  \n", "12                                4.0  \n", "5                                 3.0  \n", "18                                4.0  \n", "4                                 3.0  \n", "20                                3.0  \n", "0                                 3.0  \n", "15                                3.0  \n", "21                                5.0  \n", "8                                 3.0  \n", "17                                4.0  \n", "19                                5.0  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# MinCumRet转折点接近当前日期的行业\n", "Cumret_List = Cumret_List.sort_values(by=['MinCumRet_Date', 'Ret2Now_Ratio','MinCumRet_AvgRatio'], ascending=[False, False, False])\n", "# Cumret_List_Recent = Cumret_List.query('Ret2Now_Ratio>0.3 & (MinCumRet_DropDays>9 | Ret2Now_Ratio>2)')\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "Cumret_List"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:38.165819Z", "start_time": "2025-05-12T00:13:38.155441Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "indus", "rawType": "object", "type": "string"}, {"name": "MaxDrop", "rawType": "float64", "type": "float"}, {"name": "start_date", "rawType": "object", "type": "string"}, {"name": "end_date", "rawType": "object", "type": "string"}, {"name": "Drop_Lastdays", "rawType": "object", "type": "unknown"}, {"name": "End2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "End2Max_LastDays", "rawType": "object", "type": "unknown"}, {"name": "End2Max_MaxDate", "rawType": "object", "type": "string"}, {"name": "MinCumRet_Date", "rawType": "object", "type": "string"}, {"name": "MinCumRet_SumRatio", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_DropDays", "rawType": "object", "type": "unknown"}, {"name": "Ret2Now_Ratio", "rawType": "float64", "type": "float"}, {"name": "Ret2Now_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Ret2Now_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "Ret2Now_MaxDate", "rawType": "object", "type": "string"}, {"name": "RetMax2Now_LastDays", "rawType": "object", "type": "unknown"}, {"name": "RecentTurn2Now_Ratio", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_Seg_MaxRise_EndDate", "rawType": "object", "type": "unknown"}, {"name": "MinCumRet_Seg_MaxRise_Sum", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_Seg_MaxRise_Avg", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_Seg_MaxRise_LastDays", "rawType": "object", "type": "unknown"}, {"name": "MinCumRet_Seg_MaxRise_End_To_Now_Days", "rawType": "object", "type": "unknown"}, {"name": "MinCumRet_Seg_Over1_Num", "rawType": "object", "type": "unknown"}, {"name": "MinCumRet_Seg_MaxDecline_EndDate", "rawType": "object", "type": "string"}, {"name": "MinCumRet_Seg_MaxDecline_Sum", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_Seg_MaxDecline_Avg", "rawType": "float64", "type": "float"}, {"name": "MinCumRet_Seg_MaxDecline_LastDays", "rawType": "float64", "type": "float"}], "ref": "bd681e44-706a-4b39-9318-5ead23b3bea6", "rows": [["12", "房地产", "-39.23", "2023-08-29", "2024-04-24", "157", "22.248", "130", "2024-11-07", "2025-06-19", "-20.223", "-0.137", "148", "0.471", "6", "0.078", "2025-06-27", "0", "0.471", null, "0.0", "0.0", "0", "0", "0", "2024-12-26", "-4.944", "-1.236", "4.0"], ["18", "电力设备", "-29.93", "2023-06-30", "2024-07-10", "250", "15.624", "91", "2024-11-25", "2025-06-03", "-2.847", "-0.203", "14", "2.365", "18", "0.131", "2025-06-27", "0", "1.863", "2025-06-25", "1.978", "0.659", "3", "2", "0", "2025-04-09", "-6.15", "-1.538", "4.0"], ["20", "计算机", "-37.56", "2023-06-20", "2024-08-27", "290", "38.171", "123", "2025-03-06", "2025-05-23", "-6.255", "-0.481", "13", "5.27", "24", "0.22", "2025-06-26", "1", "2.979", "2025-06-26", "4.481", "1.12", "4", "1", "1", "2025-04-08", "-5.315", "-1.772", "3.0"], ["4", "电子", "-23.91", "2023-12-15", "2024-02-05", "36", "45.834", "251", "2025-02-26", "2025-05-23", "-6.973", "-0.536", "13", "5.635", "24", "0.235", "2025-06-27", "0", "1.737", "2025-06-19", "2.753", "0.688", "4", "6", "0", "2025-04-08", "-9.779", "-3.26", "3.0"], ["19", "国防军工", "-23.13", "2023-06-30", "2024-02-05", "150", "25.912", "181", "2024-11-11", "2025-02-18", "-14.445", "-0.226", "64", "13.862", "88", "0.158", "2025-06-27", "0", "3.267", "2025-03-11", "4.764", "1.588", "3", "73", "4", "2024-11-18", "-8.446", "-1.689", "5.0"]], "shape": {"columns": 28, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>indus</th>\n", "      <th>MaxDrop</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>Drop_Lastdays</th>\n", "      <th>End2Max_Ratio</th>\n", "      <th>End2Max_LastDays</th>\n", "      <th>End2Max_MaxDate</th>\n", "      <th>MinCumRet_Date</th>\n", "      <th>MinCumRet_SumRatio</th>\n", "      <th>MinCumRet_AvgRatio</th>\n", "      <th>MinCumRet_DropDays</th>\n", "      <th>Ret2Now_Ratio</th>\n", "      <th>Ret2Now_LastDays</th>\n", "      <th>Ret2Now_AvgRatio</th>\n", "      <th>Ret2Now_MaxDate</th>\n", "      <th>RetMax2Now_LastDays</th>\n", "      <th>RecentTurn2Now_Ratio</th>\n", "      <th>MinCumRet_Seg_MaxRise_EndDate</th>\n", "      <th>MinCumRet_Seg_MaxRise_Sum</th>\n", "      <th>MinCumRet_Seg_MaxRise_Avg</th>\n", "      <th>MinCumRet_Seg_MaxRise_LastDays</th>\n", "      <th>MinCumRet_Seg_MaxRise_End_To_Now_Days</th>\n", "      <th>MinCumRet_Seg_Over1_Num</th>\n", "      <th>MinCumRet_Seg_MaxDecline_EndDate</th>\n", "      <th>MinCumRet_Seg_MaxDecline_Sum</th>\n", "      <th>MinCumRet_Seg_MaxDecline_Avg</th>\n", "      <th>MinCumRet_Seg_MaxDecline_LastDays</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>房地产</td>\n", "      <td>-39.23</td>\n", "      <td>2023-08-29</td>\n", "      <td>2024-04-24</td>\n", "      <td>157</td>\n", "      <td>22.248</td>\n", "      <td>130</td>\n", "      <td>2024-11-07</td>\n", "      <td>2025-06-19</td>\n", "      <td>-20.223</td>\n", "      <td>-0.137</td>\n", "      <td>148</td>\n", "      <td>0.471</td>\n", "      <td>6</td>\n", "      <td>0.078</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>0.471</td>\n", "      <td>None</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2024-12-26</td>\n", "      <td>-4.944</td>\n", "      <td>-1.236</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>电力设备</td>\n", "      <td>-29.93</td>\n", "      <td>2023-06-30</td>\n", "      <td>2024-07-10</td>\n", "      <td>250</td>\n", "      <td>15.624</td>\n", "      <td>91</td>\n", "      <td>2024-11-25</td>\n", "      <td>2025-06-03</td>\n", "      <td>-2.847</td>\n", "      <td>-0.203</td>\n", "      <td>14</td>\n", "      <td>2.365</td>\n", "      <td>18</td>\n", "      <td>0.131</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>1.863</td>\n", "      <td>2025-06-25</td>\n", "      <td>1.978</td>\n", "      <td>0.659</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>2025-04-09</td>\n", "      <td>-6.150</td>\n", "      <td>-1.538</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>计算机</td>\n", "      <td>-37.56</td>\n", "      <td>2023-06-20</td>\n", "      <td>2024-08-27</td>\n", "      <td>290</td>\n", "      <td>38.171</td>\n", "      <td>123</td>\n", "      <td>2025-03-06</td>\n", "      <td>2025-05-23</td>\n", "      <td>-6.255</td>\n", "      <td>-0.481</td>\n", "      <td>13</td>\n", "      <td>5.270</td>\n", "      <td>24</td>\n", "      <td>0.220</td>\n", "      <td>2025-06-26</td>\n", "      <td>1</td>\n", "      <td>2.979</td>\n", "      <td>2025-06-26</td>\n", "      <td>4.481</td>\n", "      <td>1.120</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2025-04-08</td>\n", "      <td>-5.315</td>\n", "      <td>-1.772</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>电子</td>\n", "      <td>-23.91</td>\n", "      <td>2023-12-15</td>\n", "      <td>2024-02-05</td>\n", "      <td>36</td>\n", "      <td>45.834</td>\n", "      <td>251</td>\n", "      <td>2025-02-26</td>\n", "      <td>2025-05-23</td>\n", "      <td>-6.973</td>\n", "      <td>-0.536</td>\n", "      <td>13</td>\n", "      <td>5.635</td>\n", "      <td>24</td>\n", "      <td>0.235</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>1.737</td>\n", "      <td>2025-06-19</td>\n", "      <td>2.753</td>\n", "      <td>0.688</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>2025-04-08</td>\n", "      <td>-9.779</td>\n", "      <td>-3.260</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>国防军工</td>\n", "      <td>-23.13</td>\n", "      <td>2023-06-30</td>\n", "      <td>2024-02-05</td>\n", "      <td>150</td>\n", "      <td>25.912</td>\n", "      <td>181</td>\n", "      <td>2024-11-11</td>\n", "      <td>2025-02-18</td>\n", "      <td>-14.445</td>\n", "      <td>-0.226</td>\n", "      <td>64</td>\n", "      <td>13.862</td>\n", "      <td>88</td>\n", "      <td>0.158</td>\n", "      <td>2025-06-27</td>\n", "      <td>0</td>\n", "      <td>3.267</td>\n", "      <td>2025-03-11</td>\n", "      <td>4.764</td>\n", "      <td>1.588</td>\n", "      <td>3</td>\n", "      <td>73</td>\n", "      <td>4</td>\n", "      <td>2024-11-18</td>\n", "      <td>-8.446</td>\n", "      <td>-1.689</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   indus  MaxDrop  start_date    end_date Drop_Lastdays  End2Max_Ratio  \\\n", "12   房地产   -39.23  2023-08-29  2024-04-24           157         22.248   \n", "18  电力设备   -29.93  2023-06-30  2024-07-10           250         15.624   \n", "20   计算机   -37.56  2023-06-20  2024-08-27           290         38.171   \n", "4     电子   -23.91  2023-12-15  2024-02-05            36         45.834   \n", "19  国防军工   -23.13  2023-06-30  2024-02-05           150         25.912   \n", "\n", "   End2Max_LastDays End2Max_MaxDate MinCumRet_Date  MinCumRet_SumRatio  \\\n", "12              130      2024-11-07     2025-06-19             -20.223   \n", "18               91      2024-11-25     2025-06-03              -2.847   \n", "20              123      2025-03-06     2025-05-23              -6.255   \n", "4               251      2025-02-26     2025-05-23              -6.973   \n", "19              181      2024-11-11     2025-02-18             -14.445   \n", "\n", "    MinCumRet_AvgRatio MinCumRet_DropDays  Ret2Now_Ratio Ret2Now_LastDays  \\\n", "12              -0.137                148          0.471                6   \n", "18              -0.203                 14          2.365               18   \n", "20              -0.481                 13          5.270               24   \n", "4               -0.536                 13          5.635               24   \n", "19              -0.226                 64         13.862               88   \n", "\n", "    Ret2Now_AvgRatio Ret2Now_MaxDate RetMax2Now_LastDays  \\\n", "12             0.078      2025-06-27                   0   \n", "18             0.131      2025-06-27                   0   \n", "20             0.220      2025-06-26                   1   \n", "4              0.235      2025-06-27                   0   \n", "19             0.158      2025-06-27                   0   \n", "\n", "    RecentTurn2Now_Ratio MinCumRet_Seg_MaxRise_EndDate  \\\n", "12                 0.471                          None   \n", "18                 1.863                    2025-06-25   \n", "20                 2.979                    2025-06-26   \n", "4                  1.737                    2025-06-19   \n", "19                 3.267                    2025-03-11   \n", "\n", "    MinCumRet_Seg_MaxRise_Sum  MinCumRet_Seg_MaxRise_Avg  \\\n", "12                      0.000                      0.000   \n", "18                      1.978                      0.659   \n", "20                      4.481                      1.120   \n", "4                       2.753                      0.688   \n", "19                      4.764                      1.588   \n", "\n", "   MinCumRet_Seg_MaxRise_LastDays MinCumRet_Seg_MaxRise_End_To_Now_Days  \\\n", "12                              0                                     0   \n", "18                              3                                     2   \n", "20                              4                                     1   \n", "4                               4                                     6   \n", "19                              3                                    73   \n", "\n", "   MinCumRet_Seg_Over1_Num MinCumRet_Seg_MaxDecline_EndDate  \\\n", "12                       0                       2024-12-26   \n", "18                       0                       2025-04-09   \n", "20                       1                       2025-04-08   \n", "4                        0                       2025-04-08   \n", "19                       4                       2024-11-18   \n", "\n", "    MinCumRet_Seg_MaxDecline_Sum  MinCumRet_Seg_MaxDecline_Avg  \\\n", "12                        -4.944                        -1.236   \n", "18                        -6.150                        -1.538   \n", "20                        -5.315                        -1.772   \n", "4                         -9.779                        -3.260   \n", "19                        -8.446                        -1.689   \n", "\n", "    MinCumRet_Seg_MaxDecline_LastDays  \n", "12                                4.0  \n", "18                                4.0  \n", "20                                3.0  \n", "4                                 3.0  \n", "19                                5.0  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 当前驱动行业\n", "# Cumret_List_Trend = Cumret_List.query('RetMax2Now_LastDays<=2 & (MinCumRet_Date<=@Recent_TurnDate | MinCumRet_DropDays<=2)').copy()\n", "Cumret_List_Trend = Cumret_List.query('RetMax2Now_LastDays<=2 & MinCumRet_Date<=@Recent_TurnDate & MinCumRet_DropDays>2').copy()\n", "# Cumret_List_Trend['Ret2Now_Ratio'] = Cumret_List_Trend.apply(lambda fn: fn['End2Max_Ratio'] \n", "#                                                              if fn['MinCumRet_DropDays']<=2\n", "#                                                               else fn['Ret2Now_Ratio'], axis=1)\n", "# Cumret_List_Trend['MinCumRet_Date'] = Cumret_List_Trend.apply(lambda fn: fn['end_date'] \n", "#                                                               if fn['MinCumRet_DropDays']<=2\n", "#                                                               else fn['MinCumRet_Date'], axis=1)\n", "Cumret_List_Trend = Cumret_List_Trend.sort_values(by=['MinCumRet_Date', 'RecentTurn2Now_Ratio'], ascending=[False, False]).groupby('MinCumRet_Date').head(2)\n", "# Cumret_List_Trend = Cumret_List_Trend.sort_values(by=['MinCumRet_Seg_MaxRise_EndDate', 'MinCumRet_Seg_MaxRise_Avg','End2Max_Ratio'], ascending=[False, False, False])\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "Cumret_List_Trend"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"['房地产', '电力设备', '计算机', '电子', '国防军工']\""]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["str(Cumret_List_Trend['indus'].values.tolist())"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:38.305046Z", "start_time": "2025-05-12T00:13:38.295603Z"}, "collapsed": false}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "indus", "rawType": "object", "type": "string"}, {"name": "MaxDrop", "rawType": "float64", "type": "float"}, {"name": "start_date", "rawType": "object", "type": "string"}, {"name": "end_date", "rawType": "object", "type": "string"}, {"name": "premonth_enddate", "rawType": "object", "type": "string"}, {"name": "Drop_Lastdays", "rawType": "object", "type": "string"}, {"name": "AvgDrop", "rawType": "float64", "type": "float"}, {"name": "End2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "End2Max_LastDays", "rawType": "object", "type": "string"}, {"name": "End2Max_MaxDate", "rawType": "object", "type": "string"}, {"name": "Indus_MV", "rawType": "float64", "type": "float"}, {"name": "Bottom_State", "rawType": "object", "type": "string"}, {"name": "Bottom_Date", "rawType": "object", "type": "string"}, {"name": "Bottom2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_EndDate", "rawType": "object", "type": "string"}, {"name": "Recent_Drop_Sum", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_Avg", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_LastDays", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_EndDate", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_Sum", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_Avg", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_LastDays", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_End_To_Now_Days", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_Und1_Num", "rawType": "object", "type": "string"}, {"name": "Recent2Avg_Ratio", "rawType": "float64", "type": "float"}], "ref": "6df1f349-2a32-4de6-a644-f670649d0195", "rows": [], "shape": {"columns": 25, "rows": 0}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>indus</th>\n", "      <th>MaxDrop</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>premonth_enddate</th>\n", "      <th>Drop_Lastdays</th>\n", "      <th>AvgDrop</th>\n", "      <th>End2Max_Ratio</th>\n", "      <th>End2Max_LastDays</th>\n", "      <th>End2Max_MaxDate</th>\n", "      <th>Indus_MV</th>\n", "      <th>Bottom_State</th>\n", "      <th>Bottom_Date</th>\n", "      <th>Bottom2Max_Ratio</th>\n", "      <th>Recent_Drop_EndDate</th>\n", "      <th>Recent_Drop_Sum</th>\n", "      <th>Recent_Drop_Avg</th>\n", "      <th>Recent_Drop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_EndDate</th>\n", "      <th>Drop_Seg_MaxDrop_Sum</th>\n", "      <th>Drop_Seg_MaxDrop_Avg</th>\n", "      <th>Drop_Seg_MaxDrop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_End_To_Now_Days</th>\n", "      <th>Drop_Seg_Und1_Num</th>\n", "      <th>Recent2Avg_Ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [indus, MaxDrop, start_date, end_date, premonth_enddate, Drop_Lastdays, AvgDrop, End2Max_Ratio, End2Max_LastDays, End2Max_MaxDate, Indus_MV, Bottom_State, Bottom_Date, Bottom2Max_Ratio, Recent_Drop_EndDate, Recent_Drop_Sum, Recent_Drop_Avg, Recent_Drop_LastDays, Drop_Seg_MaxDrop_EndDate, Drop_Seg_MaxDrop_Sum, Drop_Seg_MaxDrop_Avg, Drop_Seg_MaxDrop_LastDays, Drop_Seg_MaxDrop_End_To_Now_Days, Drop_Seg_Und1_Num, Recent2Avg_Ratio]\n", "Index: []"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 前一月份出现转折点，且本月出现转折点的行业\n", "premonth_firstdate = datetime(premonth_date.year, premonth_date.month, 1).strftime('%Y-%m-%d')\n", "\n", "nowmonth_firstdate = datetime(nowmonth_end_date.year, nowmonth_end_date.month, 1).strftime('%Y-%m-%d')\n", "PreM_Indus_List = PreM_Bottom_List.query('end_date>=@premonth_firstdate').copy().rename(columns={'end_date':'premonth_end_date'})\n", "NowM_Indus_List = Bottom_List.query('end_date>=@nowmonth_firstdate').copy()\n", "Hold_Indus_List = pd.merge(NowM_Indus_List, PreM_Indus_List[['indus', 'premonth_end_date']], on='indus', how='inner')\n", "premonth_enddate = Hold_Indus_List['premonth_end_date']\n", "Hold_Indus_List = Hold_Indus_List.drop(columns=['premonth_end_date'])\n", "Hold_Indus_List.insert(loc=4, column='premonth_enddate', value=premonth_enddate)\n", "Hold_Indus_List.sort_values(['end_date', 'MaxDrop', 'Drop_Lastdays'], ascending=[False, True, False])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:38.391048Z", "start_time": "2025-05-12T00:13:38.383059Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "indus", "rawType": "object", "type": "string"}, {"name": "MaxDrop", "rawType": "float64", "type": "float"}, {"name": "start_date", "rawType": "object", "type": "string"}, {"name": "end_date", "rawType": "object", "type": "string"}, {"name": "pre2month_enddate", "rawType": "object", "type": "string"}, {"name": "premonth_enddate", "rawType": "object", "type": "string"}, {"name": "Drop_Lastdays", "rawType": "object", "type": "string"}, {"name": "AvgDrop", "rawType": "float64", "type": "float"}, {"name": "End2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "End2Max_LastDays", "rawType": "object", "type": "string"}, {"name": "End2Max_MaxDate", "rawType": "object", "type": "string"}, {"name": "Indus_MV", "rawType": "float64", "type": "float"}, {"name": "Bottom_State", "rawType": "object", "type": "string"}, {"name": "Bottom_Date", "rawType": "object", "type": "string"}, {"name": "Bottom2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_EndDate", "rawType": "object", "type": "string"}, {"name": "Recent_Drop_Sum", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_Avg", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_LastDays", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_EndDate", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_Sum", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_Avg", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_LastDays", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_End_To_Now_Days", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_Und1_Num", "rawType": "object", "type": "string"}, {"name": "Recent2Avg_Ratio", "rawType": "float64", "type": "float"}], "ref": "c1179958-3df7-42e4-83b7-a0195ee68e1d", "rows": [], "shape": {"columns": 26, "rows": 0}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>indus</th>\n", "      <th>MaxDrop</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>pre2month_enddate</th>\n", "      <th>premonth_enddate</th>\n", "      <th>Drop_Lastdays</th>\n", "      <th>AvgDrop</th>\n", "      <th>End2Max_Ratio</th>\n", "      <th>End2Max_LastDays</th>\n", "      <th>End2Max_MaxDate</th>\n", "      <th>Indus_MV</th>\n", "      <th>Bottom_State</th>\n", "      <th>Bottom_Date</th>\n", "      <th>Bottom2Max_Ratio</th>\n", "      <th>Recent_Drop_EndDate</th>\n", "      <th>Recent_Drop_Sum</th>\n", "      <th>Recent_Drop_Avg</th>\n", "      <th>Recent_Drop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_EndDate</th>\n", "      <th>Drop_Seg_MaxDrop_Sum</th>\n", "      <th>Drop_Seg_MaxDrop_Avg</th>\n", "      <th>Drop_Seg_MaxDrop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_End_To_Now_Days</th>\n", "      <th>Drop_Seg_Und1_Num</th>\n", "      <th>Recent2Avg_Ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [indus, MaxDrop, start_date, end_date, pre2month_enddate, premonth_enddate, Drop_Lastdays, AvgDrop, End2Max_Ratio, End2Max_LastDays, End2Max_MaxDate, Indus_MV, Bottom_State, Bottom_Date, Bottom2Max_Ratio, Recent_Drop_EndDate, Recent_Drop_Sum, Recent_Drop_Avg, Recent_Drop_LastDays, Drop_Seg_MaxDrop_EndDate, Drop_Seg_MaxDrop_Sum, Drop_Seg_MaxDrop_Avg, Drop_Seg_MaxDrop_LastDays, Drop_Seg_MaxDrop_End_To_Now_Days, Drop_Seg_Und1_Num, Recent2Avg_Ratio]\n", "Index: []"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# 连续三个月出现转折点的行业\n", "pre2month_firstdate = datetime(pre2month_date.year, pre2month_date.month, 1).strftime('%Y-%m-%d')\n", "Pre2M_Indus_List = Pre2M_Bottom_List.query('end_date>=@pre2month_firstdate').copy().rename(columns={'end_date':'pre2month_end_date'})\n", "Hold2M_Indus_List = pd.merge(Hold_Indus_List, Pre2M_Indus_List[['indus', 'pre2month_end_date']], on='indus', how='inner')\n", "pre2month_enddate = Hold2M_Indus_List['pre2month_end_date']\n", "Hold2M_Indus_List = Hold2M_Indus_List.drop(columns=['pre2month_end_date'])\n", "Hold2M_Indus_List.insert(loc=4, column='pre2month_enddate', value=pre2month_enddate)\n", "Hold2M_Indus_List.sort_values(['end_date', 'MaxDrop', 'Drop_Lastdays'], ascending=[False, True, False])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:38.482321Z", "start_time": "2025-05-12T00:13:38.475388Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "indus", "rawType": "object", "type": "string"}, {"name": "MaxDrop", "rawType": "float64", "type": "float"}, {"name": "start_date", "rawType": "object", "type": "string"}, {"name": "end_date", "rawType": "object", "type": "string"}, {"name": "Drop_Lastdays", "rawType": "object", "type": "string"}, {"name": "AvgDrop", "rawType": "float64", "type": "float"}, {"name": "End2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "End2Max_LastDays", "rawType": "object", "type": "string"}, {"name": "End2Max_MaxDate", "rawType": "object", "type": "string"}, {"name": "Indus_MV", "rawType": "float64", "type": "float"}, {"name": "Bottom_State", "rawType": "object", "type": "string"}, {"name": "Bottom_Date", "rawType": "object", "type": "string"}, {"name": "Bottom2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_EndDate", "rawType": "object", "type": "string"}, {"name": "Recent_Drop_Sum", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_Avg", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_LastDays", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_EndDate", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_Sum", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_Avg", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_LastDays", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_End_To_Now_Days", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_Und1_Num", "rawType": "object", "type": "string"}, {"name": "Recent2Avg_Ratio", "rawType": "float64", "type": "float"}], "ref": "594e9f30-c110-4ea5-8ade-102803a7740c", "rows": [], "shape": {"columns": 24, "rows": 0}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>indus</th>\n", "      <th>MaxDrop</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>Drop_Lastdays</th>\n", "      <th>AvgDrop</th>\n", "      <th>End2Max_Ratio</th>\n", "      <th>End2Max_LastDays</th>\n", "      <th>End2Max_MaxDate</th>\n", "      <th>Indus_MV</th>\n", "      <th>Bottom_State</th>\n", "      <th>Bottom_Date</th>\n", "      <th>Bottom2Max_Ratio</th>\n", "      <th>Recent_Drop_EndDate</th>\n", "      <th>Recent_Drop_Sum</th>\n", "      <th>Recent_Drop_Avg</th>\n", "      <th>Recent_Drop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_EndDate</th>\n", "      <th>Drop_Seg_MaxDrop_Sum</th>\n", "      <th>Drop_Seg_MaxDrop_Avg</th>\n", "      <th>Drop_Seg_MaxDrop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_End_To_Now_Days</th>\n", "      <th>Drop_Seg_Und1_Num</th>\n", "      <th>Recent2Avg_Ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [indus, MaxDrop, start_date, end_date, Drop_Lastdays, AvgDrop, End2Max_Ratio, End2Max_LastDays, End2Max_MaxDate, Indus_MV, Bottom_State, Bottom_Date, Bottom2Max_Ratio, Recent_Drop_EndDate, Recent_Drop_Sum, Recent_Drop_Avg, Recent_Drop_LastDays, Drop_Seg_MaxDrop_EndDate, Drop_Seg_MaxDrop_Sum, Drop_Seg_MaxDrop_Avg, Drop_Seg_MaxDrop_LastDays, Drop_Seg_MaxDrop_End_To_Now_Days, Drop_Seg_Und1_Num, Recent2Avg_Ratio]\n", "Index: []"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# 强势行业\n", "median_ratio = Bottom_List['End2Max_Ratio'].median()\n", "threemonth_date = nowmonth_end_date - relativedelta(months=3)\n", "threemonth_firstdate = datetime(threemonth_date.year, threemonth_date.month, 1).strftime('%Y-%m-%d')\n", "QS_Bottom_List = Bottom_List.query('end_date>@threemonth_firstdate & End2Max_Ratio>=@median_ratio'\n", "                                   ).sort_values(by=['end_date', 'End2Max_Ratio'], ascending=[False, False])\n", "# QS_Bottom_List = Bottom_List.sort_values(by=['end_date', 'End2Max_Ratio'], ascending=[False, False])\n", "QS_Bottom_List"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:38.554496Z", "start_time": "2025-05-12T00:13:38.546165Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "indus", "rawType": "object", "type": "string"}, {"name": "MaxDrop", "rawType": "float64", "type": "float"}, {"name": "start_date", "rawType": "object", "type": "string"}, {"name": "end_date", "rawType": "object", "type": "string"}, {"name": "Drop_Lastdays", "rawType": "object", "type": "unknown"}, {"name": "AvgDrop", "rawType": "float64", "type": "float"}, {"name": "End2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "End2Max_LastDays", "rawType": "object", "type": "unknown"}, {"name": "End2Max_MaxDate", "rawType": "object", "type": "string"}, {"name": "Indus_MV", "rawType": "float64", "type": "float"}, {"name": "Bottom_State", "rawType": "object", "type": "string"}, {"name": "Bottom_Date", "rawType": "object", "type": "string"}, {"name": "Bottom2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_EndDate", "rawType": "object", "type": "string"}, {"name": "Recent_Drop_Sum", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_Avg", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Drop_Seg_MaxDrop_EndDate", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_Sum", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_Avg", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Drop_Seg_MaxDrop_End_To_Now_Days", "rawType": "object", "type": "unknown"}, {"name": "Drop_Seg_Und1_Num", "rawType": "object", "type": "unknown"}, {"name": "Recent2Avg_Ratio", "rawType": "float64", "type": "float"}], "ref": "0df98477-bcb1-4104-a6f5-d6e2290bfcfd", "rows": [["11", "银行", "-27.47", "2024-08-27", "2024-11-11", "48", "-0.572", "33.425", "151", "2025-06-26", "154075.14", "True", "2024-11-11", "33.425", "2024-11-11", "-7.606", "-1.268", "6", "2024-10-08", "-16.993", "-4.248", "4", "24", "4", "2.217"]], "shape": {"columns": 24, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>indus</th>\n", "      <th>MaxDrop</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>Drop_Lastdays</th>\n", "      <th>AvgDrop</th>\n", "      <th>End2Max_Ratio</th>\n", "      <th>End2Max_LastDays</th>\n", "      <th>End2Max_MaxDate</th>\n", "      <th>Indus_MV</th>\n", "      <th>Bottom_State</th>\n", "      <th>Bottom_Date</th>\n", "      <th>Bottom2Max_Ratio</th>\n", "      <th>Recent_Drop_EndDate</th>\n", "      <th>Recent_Drop_Sum</th>\n", "      <th>Recent_Drop_Avg</th>\n", "      <th>Recent_Drop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_EndDate</th>\n", "      <th>Drop_Seg_MaxDrop_Sum</th>\n", "      <th>Drop_Seg_MaxDrop_Avg</th>\n", "      <th>Drop_Seg_MaxDrop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_End_To_Now_Days</th>\n", "      <th>Drop_Seg_Und1_Num</th>\n", "      <th>Recent2Avg_Ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>银行</td>\n", "      <td>-27.47</td>\n", "      <td>2024-08-27</td>\n", "      <td>2024-11-11</td>\n", "      <td>48</td>\n", "      <td>-0.572</td>\n", "      <td>33.425</td>\n", "      <td>151</td>\n", "      <td>2025-06-26</td>\n", "      <td>154075.14</td>\n", "      <td>True</td>\n", "      <td>2024-11-11</td>\n", "      <td>33.425</td>\n", "      <td>2024-11-11</td>\n", "      <td>-7.606</td>\n", "      <td>-1.268</td>\n", "      <td>6</td>\n", "      <td>2024-10-08</td>\n", "      <td>-16.993</td>\n", "      <td>-4.248</td>\n", "      <td>4</td>\n", "      <td>24</td>\n", "      <td>4</td>\n", "      <td>2.217</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   indus  MaxDrop  start_date    end_date Drop_Lastdays  AvgDrop  \\\n", "11    银行   -27.47  2024-08-27  2024-11-11            48   -0.572   \n", "\n", "    End2Max_Ratio End2Max_LastDays End2Max_MaxDate   Indus_MV Bottom_State  \\\n", "11         33.425              151      2025-06-26  154075.14         True   \n", "\n", "   Bottom_Date  Bottom2Max_Ratio Recent_Drop_EndDate  Recent_Drop_Sum  \\\n", "11  2024-11-11            33.425          2024-11-11           -7.606   \n", "\n", "    Recent_Drop_Avg Recent_Drop_LastDays Drop_Seg_MaxDrop_EndDate  \\\n", "11           -1.268                    6               2024-10-08   \n", "\n", "    Drop_Seg_MaxDrop_Sum  Drop_Seg_MaxDrop_Avg Drop_Seg_MaxDrop_LastDays  \\\n", "11               -16.993                -4.248                         4   \n", "\n", "   Drop_Seg_MaxDrop_End_To_Now_Days Drop_Seg_Und1_Num  Recent2Avg_Ratio  \n", "11                               24                 4             2.217  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 行业相对高点日期晚于近期指数Peak日期的行业\n", "riseindus_list = Rise_List.query('end_date>@Recent_TurnDate')['indus'].to_list()\n", "median_value = Bottom_List.query('indus in @riseindus_list')['End2Max_Ratio'].median()\n", "Bottom_List_Recent = Bottom_List.query(\n", "    'indus in @riseindus_list & End2Max_Ratio>@median_value').sort_values(by=['End2Max_MaxDate', 'Drop_Lastdays'], ascending=[True, False])\n", "Bottom_List_Recent"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:38.616485Z", "start_time": "2025-05-12T00:13:38.610036Z"}, "collapsed": false}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "indus", "rawType": "object", "type": "string"}, {"name": "MaxDrop", "rawType": "float64", "type": "float"}, {"name": "start_date", "rawType": "object", "type": "string"}, {"name": "end_date", "rawType": "object", "type": "string"}, {"name": "Drop_Lastdays", "rawType": "object", "type": "unknown"}, {"name": "AvgDrop", "rawType": "float64", "type": "float"}, {"name": "End2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "End2Max_LastDays", "rawType": "object", "type": "unknown"}, {"name": "End2Max_MaxDate", "rawType": "object", "type": "string"}, {"name": "Indus_MV", "rawType": "float64", "type": "float"}, {"name": "Bottom_State", "rawType": "object", "type": "string"}, {"name": "Bottom_Date", "rawType": "object", "type": "string"}, {"name": "Bottom2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_EndDate", "rawType": "object", "type": "string"}, {"name": "Recent_Drop_Sum", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_Avg", "rawType": "float64", "type": "float"}, {"name": "Recent_Drop_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Drop_Seg_MaxDrop_EndDate", "rawType": "object", "type": "string"}, {"name": "Drop_Seg_MaxDrop_Sum", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_Avg", "rawType": "float64", "type": "float"}, {"name": "Drop_Seg_MaxDrop_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Drop_Seg_MaxDrop_End_To_Now_Days", "rawType": "object", "type": "unknown"}, {"name": "Drop_Seg_Und1_Num", "rawType": "object", "type": "unknown"}, {"name": "Recent2Avg_Ratio", "rawType": "float64", "type": "float"}], "ref": "fcce446b-e72c-472d-9d29-72312f742187", "rows": [["1", "非银金融", "-19.69", "2024-11-07", "2025-04-16", "107", "-0.184", "10.78", "40", "2025-06-17", "74617.18", "-", "2024-04-12", "39.745", "2025-04-16", "-1.042", "-0.208", "5", "2025-01-10", "-7.813", "-0.977", "8", "61", "1", "1.13"], ["0", "食品饮料", "-29.4", "2023-11-03", "2025-06-27", "399", "-0.074", "0.0", "0", "2025-06-27", "45208.08", "True", "2025-06-27", "0.0", "2025-06-27", "-2.596", "-0.519", "5", "2025-06-16", "-6.599", "-0.55", "12", "9", "4", "7.014"]], "shape": {"columns": 24, "rows": 2}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>indus</th>\n", "      <th>MaxDrop</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "      <th>Drop_Lastdays</th>\n", "      <th>AvgDrop</th>\n", "      <th>End2Max_Ratio</th>\n", "      <th>End2Max_LastDays</th>\n", "      <th>End2Max_MaxDate</th>\n", "      <th>Indus_MV</th>\n", "      <th>Bottom_State</th>\n", "      <th>Bottom_Date</th>\n", "      <th>Bottom2Max_Ratio</th>\n", "      <th>Recent_Drop_EndDate</th>\n", "      <th>Recent_Drop_Sum</th>\n", "      <th>Recent_Drop_Avg</th>\n", "      <th>Recent_Drop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_EndDate</th>\n", "      <th>Drop_Seg_MaxDrop_Sum</th>\n", "      <th>Drop_Seg_MaxDrop_Avg</th>\n", "      <th>Drop_Seg_MaxDrop_LastDays</th>\n", "      <th>Drop_Seg_MaxDrop_End_To_Now_Days</th>\n", "      <th>Drop_Seg_Und1_Num</th>\n", "      <th>Recent2Avg_Ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>非银金融</td>\n", "      <td>-19.69</td>\n", "      <td>2024-11-07</td>\n", "      <td>2025-04-16</td>\n", "      <td>107</td>\n", "      <td>-0.184</td>\n", "      <td>10.78</td>\n", "      <td>40</td>\n", "      <td>2025-06-17</td>\n", "      <td>74617.18</td>\n", "      <td>-</td>\n", "      <td>2024-04-12</td>\n", "      <td>39.745</td>\n", "      <td>2025-04-16</td>\n", "      <td>-1.042</td>\n", "      <td>-0.208</td>\n", "      <td>5</td>\n", "      <td>2025-01-10</td>\n", "      <td>-7.813</td>\n", "      <td>-0.977</td>\n", "      <td>8</td>\n", "      <td>61</td>\n", "      <td>1</td>\n", "      <td>1.130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>食品饮料</td>\n", "      <td>-29.40</td>\n", "      <td>2023-11-03</td>\n", "      <td>2025-06-27</td>\n", "      <td>399</td>\n", "      <td>-0.074</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2025-06-27</td>\n", "      <td>45208.08</td>\n", "      <td>True</td>\n", "      <td>2025-06-27</td>\n", "      <td>0.000</td>\n", "      <td>2025-06-27</td>\n", "      <td>-2.596</td>\n", "      <td>-0.519</td>\n", "      <td>5</td>\n", "      <td>2025-06-16</td>\n", "      <td>-6.599</td>\n", "      <td>-0.550</td>\n", "      <td>12</td>\n", "      <td>9</td>\n", "      <td>4</td>\n", "      <td>7.014</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  indus  MaxDrop  start_date    end_date Drop_Lastdays  AvgDrop  \\\n", "1  非银金融   -19.69  2024-11-07  2025-04-16           107   -0.184   \n", "0  食品饮料   -29.40  2023-11-03  2025-06-27           399   -0.074   \n", "\n", "   End2Max_Ratio End2Max_LastDays End2Max_MaxDate  Indus_MV Bottom_State  \\\n", "1          10.78               40      2025-06-17  74617.18            -   \n", "0           0.00                0      2025-06-27  45208.08         True   \n", "\n", "  Bottom_Date  Bottom2Max_Ratio Recent_Drop_EndDate  Recent_Drop_Sum  \\\n", "1  2024-04-12            39.745          2025-04-16           -1.042   \n", "0  2025-06-27             0.000          2025-06-27           -2.596   \n", "\n", "   Recent_Drop_Avg Recent_Drop_LastDays Drop_Seg_MaxDrop_EndDate  \\\n", "1           -0.208                    5               2025-01-10   \n", "0           -0.519                    5               2025-06-16   \n", "\n", "   Drop_Seg_MaxDrop_Sum  Drop_Seg_MaxDrop_Avg Drop_Seg_MaxDrop_LastDays  \\\n", "1                -7.813                -0.977                         8   \n", "0                -6.599                -0.550                        12   \n", "\n", "  Drop_Seg_MaxDrop_End_To_Now_Days Drop_Seg_Und1_Num  Recent2Avg_Ratio  \n", "1                               61                 1             1.130  \n", "0                                9                 4             7.014  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 下行持续天数超过80天\n", "Bottom_pick = Bottom_List.query('Drop_Lastdays>80 & end_date>@PreDrop_StartDate').sort_values('End2Max_Ratio', ascending=False)\n", "Bottom_pick"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:27:14.957363Z", "start_time": "2025-05-12T00:27:13.498533Z"}, "collapsed": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 设置绘图参数\n", "indus = '电力设备'\n", "years, months = 2, 6\n", "\n", "# 计算日期范围\n", "start_date = (pd.to_datetime(end_date) - relativedelta(years=years)).strftime('%Y-%m-%d')\n", "end_date_adj = (pd.to_datetime(end_date) + relativedelta(months=months)).strftime('%Y-%m-%d')\n", "\n", "# 获取数据\n", "diff_ratio = cal_sw_diffratio(start_date=start_date, end_date=end_date_adj, indus=indus)\n", "swidx_data = get_swindex_data(start_date=start_date, end_date=end_date_adj)[indus]\n", "# diff_ratio = diff_ratio.rolling(window=3).mean()\n", "sw_ratio = (swidx_data/swidx_data.iloc[0] - 1) * 100\n", "# sw_ratio = sw_ratio.rolling(window=3).mean()\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # macOS系统使用\n", "plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题\n", "\n", "# 创建图表\n", "plt.figure(figsize=(12, 8))\n", "plt.plot(diff_ratio.index, diff_ratio.values, 'm-', label='相对指数')\n", "plt.plot(sw_ratio.index, sw_ratio.values, 'b--', label='申万指数')\n", "\n", "# 设置图表属性\n", "plt.title(f'{indus}行业指数走势对比图')\n", "plt.xticks(diff_ratio.index[::int(len(diff_ratio)/20)], rotation=45)  # 将10改为20以增加横坐标标签密度\n", "plt.grid(True, linestyle='--', alpha=0.7)\n", "plt.legend(loc='upper left')\n", "\n", "# 添加坐标轴标签\n", "plt.xlabel('日期')\n", "plt.ylabel('涨跌幅(%)')\n", "\n", "# 调整布局并显示\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:13:40.144538Z", "start_time": "2025-05-12T00:13:40.141736Z"}}, "outputs": [{"data": {"text/plain": ["0.8868312757201646"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["mean_diffratio = (diff_ratio.max()+diff_ratio.iloc[-1])/2\n", "len(diff_ratio.loc[diff_ratio.idxmax():][diff_ratio.loc[diff_ratio.idxmax():]<mean_diffratio])/len(diff_ratio)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2025-05-12T00:27:52.711099Z", "start_time": "2025-05-12T00:27:52.707833Z"}, "collapsed": false}, "outputs": [{"data": {"text/plain": ["('2025-06-03', -0.5960019155633844, 33)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["diff_ratio.loc['2025-05-13':].idxmin(), diff_ratio.loc['2025-05-28'] - diff_ratio.loc['2025-05-23'], len(diff_ratio.loc['2025-05-13':])\n", "# len(diff_ratio.loc['2024-01-30':'2024-04-24'])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "vscode": {"interpreter": {"hash": "40d3a090f54c6569ab1632332b64b2c03c39dcf918b08424e98f38b5ae0af88f"}}}, "nbformat": 4, "nbformat_minor": 0}