
import sys
sys.path.append("..")
import config.config_local as config
import config.config_SQLServer as config_SQLServer
import pymssql
from sqlalchemy import create_engine

conf = config_SQLServer.configModel()
con = pymssql.connect(host=conf.DC_DB_URL,
                      port=conf.DC_DB_PORT,
                      user=conf.DC_DB_USER,
                      password=conf.DC_DB_PASS,
                      database='JYDB', charset='GBK')

conf_m = config.configModel()
engine = create_engine(
    'mysql+pymysql://' + conf_m.DC_DB_USER + ':' + conf_m.DC_DB_PASS + '@' + conf_m.DC_DB_URL + ':' + str(
        conf_m.DC_DB_PORT) + '/ext')

sql_PublicFund = """
    SELECT
        t1.* 
    FROM
        (
        SELECT
            * 
        FROM
            MF_FundManagerNew 
        WHERE
            PostName = 1 
            AND 
            (DimissionDate >= '2005-12-31' OR DimissionDate IS NULL) 
            AND
            DATEDIFF(DAY, AccessionDate, ISNULL(DimissionDate, GETDATE())) > 180 
        ) t1
        INNER JOIN
            (SELECT
                DISTINCT InnerCode
            FROM
                MF_StockPortfolioDetail 
            WHERE 
                StockInnerCode < 1000000
            ) t2 
            ON 
                t1.InnerCode = t2.InnerCode
"""
df_PublicFund = pd.read_sql(sql_PublicFund, con=con)

# 清理原数据
clear_sql1 = '''
        delete from ext.e_ReturnAttribution_PublicFund_Detail
    '''
clear_sql2 = '''
        delete from ext.e_ReturnAttribution_PublicFund_Total
    '''
clear_sql3 = '''
        delete from ext.e_ReturnAttribution_PublicFund_Final
    '''
clear_sql4 = '''
        delete from ext.e_ReturnAttribution_PublicFund_ErrorID
    '''
logging.info(clear_sql1)
logging.info(clear_sql2)
logging.info(clear_sql3)
logging.info(clear_sql4)
engine.connect().execute(clear_sql1)
engine.connect().execute(clear_sql2)
engine.connect().execute(clear_sql3)
engine.connect().execute(clear_sql4)


# 输出结果入库
pd.io.sql.to_sql(df_res1, 'e_ReturnAttribution_PublicFund_Detail', engine, index=False,
                 schema='ext', if_exists='append')
pd.io.sql.to_sql(df_res2, 'e_ReturnAttribution_PublicFund_Total', engine, index=False,
                 schema='ext', if_exists='append')
pd.io.sql.to_sql(df_return_final, 'e_ReturnAttribution_PublicFund_Final', engine, index=False,
                 schema='ext', if_exists='append')

engine.dispose()
con.close()
