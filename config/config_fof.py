import os
import sys
import yaml


class configModel:
    def __init__(self):
        currPath=os.path.dirname(os.path.realpath(__file__))
        # f=open(currPath+"/Valuation_table_config/application_fof.yaml")
        f = open(currPath + "/config/application_fof.yaml")
        # yaml.warnings({'YAMLLoadWarning': False})
        # conf=yaml.load(f, Loader=yaml.FullLoader)
        conf=yaml.load(f, Loader=yaml.FullLoader)

        # logging.info(conf)
        # DC 数据库连接，DC_DB_NAME根据情况修改
        self.DC_DB_URL=conf["DataCenter"]["DC_DB_URL"]
        self.DC_DB_URL=conf["DataCenter"]["DC_DB_URL"]
        self.DC_DB_USER= conf["DataCenter"]["DC_DB_USER"]
        self.DC_DB_PASS= conf["DataCenter"]["DC_DB_PASS"]
        self.DC_DB_PORT= conf["DataCenter"]["DC_DB_PORT"]

class configJYModel:
    def __init__(self):
        currPath=os.path.dirname(os.path.realpath(__file__))
        # f=open(currPath+"/Valuation_table_config/application_fof.yaml")
        f = open(currPath + "/config/application_fof.yaml")
        # yaml.warnings({'YAMLLoadWarning': False})
        # conf=yaml.load(f, Loader=yaml.FullLoader)
        conf=yaml.load(f, Loader=yaml.FullLoader)

        # logging.info(conf)
        # DC 数据库连接，DC_DB_NAME根据情况修改
        self.DC_DB_URL=conf["DataCenter"]["DC_DB_URL"]
        self.DC_DB_URL=conf["DataCenter"]["DC_DB_URL"]
        self.DC_DB_USER= conf["DataCenter"]["DC_DB_USER"]
        self.DC_DB_PASS= conf["DataCenter"]["DC_DB_PASS"]
        self.DC_DB_PORT= conf["DataCenter"]["DC_DB_PORT"]

      