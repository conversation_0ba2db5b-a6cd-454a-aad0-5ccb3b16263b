# -- coding: utf-8 --
# 准备机器学习所需学习数据和测试数据

import pandas as pd
import numpy as np
import os
import pickle
import pdb

from function_ai.StkPick_Func_V7 import get_result_3
from sklearn.base import BaseEstimator, TransformerMixin
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from function_ai.Func_Base import get_trade_date, get_stock_data
from function_ai.DailyGap_Func import get_gap_mindata

from pathlib import Path

rank_num = [0.15]
rank_label = ['前15%', '前85%']
ratio_num = [15]
ratio_label = ['UpRatio', 'DownRatio']


def cal_osci_ratio(result, stock_data, trade_dates, cal_startdate=None, lag_num=20):
    """计算股票区间最大涨幅"""
    stk_code = result['ts_code']
    if cal_startdate is None:
        date = result['Cal_Date']
    else:
        date = cal_startdate
    lennum = min(lag_num, len(trade_dates[trade_dates > date]) - 1)
    cal_enddate = trade_dates[(trade_dates > date)][lennum]
    stk_data = stock_data.query('ts_code==@stk_code').set_index('trade_date', drop=False)
    if len(stk_data.loc[date:cal_enddate]) > 3:
        cal_startdate = stk_data.loc[date:cal_enddate, 'close'].index[1]
        return (stk_data.loc[cal_startdate:cal_enddate, 'close'].iloc[2:].max() /
                stk_data.loc[date:cal_enddate, 'close'].iloc[1] - 1) * 100
        # return (stk_data.loc[cal_startdate:cal_enddate, 'close'].iloc[5] /
        #         stk_data.loc[date:cal_enddate, 'close'].iloc[1] - 1) * 100
    else:
        return None


def cal_mean_ratio(result, stock_data, trade_dates, cal_startdate=None, lag_num=20):
    """计算secdate之后平均涨幅，如低于0，设定为0"""
    stk_code = result['ts_code']
    if cal_startdate is None:
        date = result['Cal_Date']
    else:
        date = cal_startdate
    lennum = min(lag_num, len(trade_dates[trade_dates > date]) - 1)
    cal_enddate = trade_dates[(trade_dates > date)][lennum]
    stk_data = stock_data.query('ts_code==@stk_code').set_index('trade_date', drop=False)
    if len(stk_data.loc[date:cal_enddate]) > 3:
        cal_maxdate = stk_data.loc[date:cal_enddate, 'close'].iloc[2:].idxmax()
        mean_ratio = (stk_data.loc[date:cal_maxdate, 'close'].iloc[2:].mean() /
                      stk_data.loc[date:, 'close'].iloc[1] - 1) * 100
        # mean_ratio = (stk_data.loc[date:, 'close'].iloc[2:6].mean() /
        #               stk_data.loc[date:, 'close'].iloc[1] - 1) * 100
        return mean_ratio
    else:
        return None


def cal_strenth_ratio(result, stock_data, trade_dates, cal_startdate=None, lag_num=20,
                      epsilon=1e-6, threshold=0):
    """计算secdate之后夏普指标，如低于0，设定为0"""
    stk_code = result['ts_code']
    if cal_startdate is None:
        date = result['Cal_Date']
    else:
        date = cal_startdate
    lennum = min(lag_num, len(trade_dates[trade_dates > date]) - 1)
    cal_enddate = trade_dates[(trade_dates > date)][lennum]
    stk_data = stock_data.query('ts_code==@stk_code').set_index('trade_date', drop=False).copy()
    if len(stk_data.loc[date:cal_enddate]) > 3:
        strenth_ratio = calculate_strength_ratio(stk_data.loc[date:cal_enddate].iloc[1:])
        return strenth_ratio
    else:
        return None


def calculate_strength_ratio(stk_df, epsilon=1e-6, threshold=0, alpha=0.5):
    """
    计算强势比值（最高收益率/下方波动率 * (1+连续上行天数占比)），避免除零错误

    参数:
    - max_return: 最高收益率
    - downside_vol: 下方波动率
    - epsilon: 平滑因子，当下方波动率为0时使用

    返回:
    - 强势比值
    """
    max_return = stk_df.iloc[1:]['close'].max()/stk_df['close'].iloc[0]

    stk_df['ratio'] = round((stk_df['close'] / stk_df['close'].shift(1) - 1) * 100, 2)

    upward_streaks = calculate_upward_streak(stk_df['ratio'])
    if upward_streaks:
        max_streak = max(upward_streaks)  # 选择最长的连续上涨天数
    else:
        max_streak = 0
    # 计算上涨持续天数占比
    total_days = len(stk_df)
    upward_ratio = max_streak / total_days

    downside_returns = stk_df.apply(lambda x: min(x['ratio'] - threshold, 0), axis=1)
    downside_vol = np.sqrt((downside_returns ** 2).mean())

    # 如果下方波动率为0，使用epsilon替代
    adjusted_vol = max(downside_vol, epsilon)
    strength_ratio = round((max_return / adjusted_vol) * (1 + alpha * upward_ratio), 3)
    # strength_ratio = round(max_return / adjusted_vol, 3)
    return strength_ratio


def calculate_upward_streak(returns):
    """
    计算连续上涨天数（上涨持续天数）
    参数:
    - returns: pd.Series类型，表示每日收益率
    返回:
    - 连续上涨天数
    """
    streaks = []
    streak = 0
    for r in returns:
        if r > 0:
            streak += 1
        else:
            if streak > 0:
                streaks.append(streak)
            streak = 0
    if streak > 0:
        streaks.append(streak)  # 最后的一段持续上涨天数
    return streaks


def set_label(result, thresholds, mode):
    """计算排名位置并分类"""
    global rank_label, ratio_label
    if mode == 'Label':
        labels = rank_label
    elif mode == 'Ratio':
        labels = ratio_label
    elif mode == "Mean_Ratio":
        labels = ratio_label
    elif mode == "Strength_Label":
        labels = rank_label
    else:
        labels = rank_label
    ratio = result['Ratio']
    cal_date = result['Cal_Date']
    for num, threshold in enumerate(thresholds[cal_date]):
        if ratio >= threshold:
            return labels[num]
        else:
            continue
    return labels[-1]


def get_train_label(result=None, label_style='Label', lag_num=20):
    """获取训练数据，并处理分类结果"""
    global ratio_num, rank_num
    if label_style == 'Label' or label_style == "Mean_Label" or label_style == "Strength_Label":
        threshold_num = [1-i for i in rank_num]
    elif label_style == 'Ratio' or label_style == 'Mean_Ratio':
        threshold_num = ratio_num
    # if result is None:
    #     result_data = get_result_3(start_date=start_date, end_date=end_date)
    # else:
    result_data = result.copy()
    train_dates = result_data['Cal_Date'].unique()

    trading_date = get_trade_date(start_date=min(train_dates))
    lennum = min(lag_num, len(trading_date[trading_date > max(train_dates)]) - 1)
    stk_end_date = trading_date[trading_date > max(train_dates)][lennum]

    stock_data = get_stock_data(start_date=min(train_dates), end_date=stk_end_date)
    if label_style == 'Mean_Label' or label_style == "Mean_Ratio":
        result_data['Ratio'] = result_data.apply(func=cal_mean_ratio, args=(stock_data, trading_date,),
                                                 axis=1, result_type='expand')
    elif label_style == 'Strength_Label':
        result_data['Ratio'] = result_data.apply(func=cal_strenth_ratio, args=(stock_data, trading_date,),
                                                 axis=1, result_type='expand')
    else:
        result_data['Ratio'] = result_data.apply(func=cal_osci_ratio, args=(stock_data, trading_date,),
                                                 axis=1, result_type='expand')
    result_data = result_data.dropna(subset=['Ratio'], how='any').sort_values('Ratio', ascending=False)
    if label_style == 'Label' or label_style == 'Strength_Label':
        thresholds = {}
        for trade_date in train_dates:
            thre_list = []
            for pertile in threshold_num:
                thre_list.append(result_data.query('Cal_Date==@trade_date')['Ratio'].quantile(pertile))
            # thre_list.sort(reverse=True)
            thresholds.update({trade_date: thre_list})
        result_data['Label'] = result_data[['Ratio', 'Cal_Date']].apply(func=set_label, args=(thresholds, label_style, ),
                                                                        axis=1, result_type='expand')
        return result_data.drop('Ratio', axis=1)
    elif label_style == 'Mean_Label':
        thresholds = {}
        for trade_date in train_dates:
            thre_list = []
            for pertile in threshold_num:
                thre_list.append(result_data.query('Cal_Date==@trade_date')['Ratio'].quantile(pertile))
            # thre_list.sort(reverse=True)
            # thre_list.append(0)
            thresholds.update({trade_date: thre_list})
        result_data['Label'] = result_data[['Ratio', 'Cal_Date']].apply(func=set_label, args=(thresholds, label_style,),
                                                                        axis=1, result_type='expand')
        return result_data.drop('Ratio', axis=1)
    elif label_style == 'Ratio' or label_style == 'Mean_Ratio':
        thresholds = {}
        for trade_date in train_dates:
            thresholds.update({trade_date: threshold_num})
        result_data['Label'] = result_data[['Ratio', 'Cal_Date']].apply(func=set_label, args=(thresholds, label_style,),
                                                                        axis=1, result_type='expand')
        return result_data.drop('Ratio', axis=1)
    else:
        return result_data


def split_train_test(data, test_ratio):
    # 对长度为len(data)的连续数据随机排序
    shuffled_indices = np.random.permutation(len(data))
    test_set_size = int(len(data) * test_ratio)
    test_indices = shuffled_indices[:test_set_size]
    train_indices = shuffled_indices[test_set_size:]
    return data.iloc[train_indices], data.iloc[test_indices]


def data_split(siftresult):
    # 分层采样
    from sklearn.model_selection import StratifiedShuffleSplit
    strat_train_set, strat_test_set = None, None
    siftresult = siftresult.reset_index(drop=True)
    split = StratifiedShuffleSplit(n_splits=1, test_size=0.2, random_state=42)
    for train_index, test_index in split.split(siftresult, siftresult['Label']):
        strat_train_set = siftresult.loc[train_index]
        strat_test_set = siftresult.loc[test_index]
    # for set in (strat_train_set, strat_test_set):
    #     set.drop(['Label'], axis=1, inplace=True)
    return strat_train_set, strat_test_set


def prepare_cat(result):
    """处理df中的字符类型数据"""
    result = result.copy()
    trade_dates = get_trade_date()
    """转换为间隔天数：
        Long_PeakDate 转换为距离当前日期的天数
        Period_Break_Date 转换为距离当前日期的天数
        Section_PeakDate 转换为距离当前日期的天数
        Sec_IndexDiff_StartDate 转换为距离当前日期的天数
        PostTurn_Recent_Neg4_DropDate 转换为距离当前日期的天数
        PostPeak_Recent_Neg4_DropDate 转换为距离当前日期的天数
        PrePeak_Over4_Date 和 Convex_Break_DropDate 转换为两个日期间的间隔天数"""
    def get_interval_dates(start_date, end_date):
        # global trade_dates
        if start_date is not None:
            return len(trade_dates[(trade_dates > start_date) & (trade_dates <= end_date)])
        else:
            return 0

    column_list_1 = ['Long_PeakDate', 'Period_Break_Date',
                     'Section_PeakDate', 'Sec_IndexDiff_StartDate',
                     'PostTurn_Recent_Neg4_DropDate', 'PostPeak_Recent_Neg4_DropDate',
                     'PostTurn_Recent_Neg4_RecovDate', 'PostPeak_Recent_Neg4_RecovDate',
                     'PostSecStart_PGV_MaxRollAvg_Date']
    column_list_1 = [i for i in column_list_1 if i in result.columns]
    for column in column_list_1:
        result[column] = result.apply(lambda fn: get_interval_dates(fn[column], fn['Cal_Date']), axis=1)
    result['Convex_Break_DropDate'] = result.apply(
        lambda fn: get_interval_dates(fn['PrePeak_Over4_Date'], fn['Convex_Break_DropDate']), axis=1)
    column_list_1.append('Convex_Break_DropDate')

    """
    Rule2 转换为指数排序序号
    Period_TurnDate
    Section_StartDate
    """
    # global market_turndates
    market_turndates = ['2021-11-02', '2021-11-30', '2021-12-08', '2021-12-13',
                        '2021-12-20', '2022-01-28', '2022-02-14', '2022-03-15',
                        '2022-04-26', '2022-07-15', '2022-08-03', '2022-09-01',
                        '2022-09-26', '2022-10-11', '2022-10-31', '2022-11-10',
                        '2022-11-21', '2022-11-28']
    market_turndates.sort(reverse=True)

    # 调整Period_TurnDate之前先调整Bottom_Date
    result['Bottom_Date'] = result.apply(lambda fn: 1 if fn['Bottom_Date'] == fn['Period_TurnDate'] else 0, axis=1)

    def cal_turndate(turndate):
        # global market_turndates, trade_dates
        for num, date in enumerate(market_turndates):
            if turndate is not None:
                interval = len(trade_dates[(trade_dates > turndate) & (trade_dates <= date)]) \
                    if turndate < date \
                    else len(trade_dates[(trade_dates > date) & (trade_dates <= turndate)])
                if interval <= 3:
                    return num
            else:
                continue
        return 100

    column_list_2 = ['Period_TurnDate', 'Section_StartDate']
    column_list_2 = [i for i in column_list_2 if i in result.columns]
    for column in column_list_2:
        result[column] = result[column].apply(func=cal_turndate, )
    result['Same_TurnDate'] = result[['Period_TurnDate', 'Section_StartDate', 'Period_Trend']].apply(
        lambda fn: 1 if fn['Period_Trend'] == '下行' or fn['Period_TurnDate'] == fn['Section_StartDate'] else 0, axis=1)
    column_list_2.append('Same_TurnDate')

    """
    ## Rule3 转换为类别数字
    Period_Trend 转换为类别数字（不放弃None）
    Long_Trend 转换为类别数字（不放弃None)
    Now_State 转换为类别数字（不放弃None）
    Now_Vol_Trend 转换为类别数字（不放弃None）
    Sec_IndexDiff_Signal 转换为类别数字（不放弃None）
    Bottom_Date 转换为是否与Period_TurnDate相同的二元判定特征
    industry 转换为类别数字
    area 转换为类别数字
    """
    from sklearn.preprocessing import LabelEncoder
    label_encoder = LabelEncoder()
    column_list_3 = ['Period_Trend', 'Long_Trend', 'Now_State', 'Now_Vol_Trend',
                     'Sec_IndexDiff_Signal', 'industry', 'area']
    column_list_3 = [i for i in column_list_3 if i in result.columns]
    # if 'Label' in result.columns:
    #     column_list_3.append('Label')
    feature_map = {}
    for column in column_list_3:
        temp_map = {}
        result[column] = result[column].fillna(value='普通')
        result[column] = label_encoder.fit_transform(result[column])
        for cl in label_encoder.classes_:
            temp_map.update({cl: label_encoder.transform([cl])[0]})
        feature_map.update({column: temp_map})
    column_list_3.append('Bottom_Date')

    """
    ## Rule4 放弃
    放弃 'id' 'ts_code' 'name' 'Target_Price'
     'Press_Price' 'Stop_Lose_Price'
    放弃 Long_Trend_StartDate, Period_Trend_StartDate
    放弃 Concave_Break_Date，因为已有Concave_Break_Days2Now
    放弃 PostTurn_Reg_EndDate
    """
    column_list_4 = ['id', 'ts_code', 'name', 'Target_Price', 'Press_Price',
                     'Stop_Lose_Price', 'Long_Trend_StartDate', 'Period_Trend_StartDate',
                     'Concave_Break_Date', 'PostTurn_Reg_EndDate', 'PrePeak_Over4_Date', 'Cal_Date',
                     'PostTurn_RiseRatio', 'PostTurn_MaxDrop']
    column_list_4 = [i for i in column_list_4 if i in result.columns]

    """处理数值型特征，使用StandardScaler, None值全部填充为0"""
    # cat_columns = column_list_1 + column_list_2 + column_list_3 + column_list_4
    origin_columns = result.columns.drop('id')
    num_columns = []
    for column in origin_columns:
        column_values = result[column].dropna()
        if column_values.empty and 'date' != column.lower()[-4:] \
                and 'state' != column.lower()[-5:] and 'trend' != column.lower()[-5:]:
            result[column] = 0  # 填充0
            column_values = result[column].dropna()
        try:
            if len(column_values) > 0 \
                    and column_values.apply(lambda x: isinstance(x, (float, int))).any() \
                    and 'date' != column.lower()[-4:] \
                    and 'state' != column.lower()[-5:] \
                    and 'trend' != column.lower()[-5:]:
                num_columns.append(column)
        except Exception as e:
            print(e)
            continue
    # num_columns = list(set(result.columns.to_list()).difference(set(cat_columns)))
    cat_columns = column_list_1 + column_list_2 + column_list_3
    num_columns = list(set(num_columns) - set(cat_columns))
    if 'Label' in cat_columns:
        cat_columns.remove('Label')
    if 'Label' in num_columns:
        num_columns.remove('Label')
    """删除放弃的特征"""
    Column_List = cat_columns + num_columns
    if 'Label' in origin_columns:
        Column_List.append('Label')
    result = result[Column_List].copy()
    result = result.fillna(value=0)
    return result, num_columns, cat_columns


# 将dataframe转换为numpy
class DataFrameSelector(BaseEstimator, TransformerMixin):
    def __init__(self, attribute_names):
        self.attribute_names = attribute_names

    def fit(self, X, y=None):
        return self

    def transform(self, X):
        return X[self.attribute_names].values


def prepare_data(train_data, mode_style='train'):
    from sklearn.pipeline import Pipeline, FeatureUnion
    from sklearn.impute import SimpleImputer
    from sklearn.preprocessing import StandardScaler
    # from machine_learn.Func_MacinLn import DataFrameSelector, prepare_cat
    train_data, num_columns, cat_columns = prepare_cat(train_data)
    num_pipline = Pipeline([
        ('selector', DataFrameSelector(num_columns)),
        ('imputer', SimpleImputer(missing_values=np.nan, strategy='constant', fill_value=0)),
        ('stdscaler', StandardScaler()),
    ])
    cat_pipline = Pipeline([
        ('selector', DataFrameSelector(cat_columns))
    ])
    full_pipline = FeatureUnion(transformer_list=[
        ('num_pipline', num_pipline),
        ('cat_pipline', cat_pipline)
    ])

    def find_string_cells(df):
        # 使用 applymap 检查每个单元格是否为字符串类型
        is_string = df.applymap(lambda x: isinstance(x, str))

        # 获取所有字符串单元格的位置（行列索引）
        string_cells = is_string[is_string].stack().index.tolist()

        return string_cells

    if mode_style == 'train':
        X_train = train_data.drop('Label', axis=1)
        y_train = train_data['Label'].values
        return full_pipline.fit_transform(X_train), y_train
    elif mode_style == 'predict':
        str_cells = find_string_cells(train_data)
        output = None
        try:
            output = full_pipline.fit_transform(train_data)
        except Exception as e:
            print(e)
        return output, None
    else:
        print('mode_style输入错误')
        return


def clf_rf(result, model_select='Rf', model_style=None,
           label_style='Label', model_date=None, treat_style=None,
           lag_num=20):
    """调用随机森林模型进行训练，并存储训练模型
        mode参数设定为train为训练模型，设定为predict为预测结果
    """
    from sklearn.model_selection import cross_val_score
    global rank_label, ratio_label
    data = result.copy()

    # 命名模型文件
    if model_style == 'train':
        pkl_date = result['Cal_Date'].max()
    elif model_date is None:
        pkl_date = input('输入模型生成数据日期：')
    else:
        pkl_date = model_date
    if treat_style in ['turn', 'recov']:
        pkl_filename = Path('/Users/<USER>/PycharmProjects/AI_Stock/machine_learn/file_set/' +
                            model_select.upper() + '_model(' +
                            label_style + '_' + treat_style.lower() + '_' +
                            pd.to_datetime(pkl_date).strftime('%m%d') + ').pkl')
    else:
        pkl_filename = Path('/Users/<USER>/PycharmProjects/AI_Stock/machine_learn/file_set/' +
                            model_select.upper() + '_model(' +
                            label_style + '_' + pd.to_datetime(pkl_date).strftime('%m%d') + ').pkl')

    # 数据处理
    if model_style == 'train':
        # if treat_style.lower() == 'recov':
        #     data = get_train_label(data, label_style=label_style, lag_num=lag_num)
        # else:
        data = get_train_label(data, label_style=label_style, lag_num=lag_num)
        from sklearn.preprocessing import LabelEncoder
        lec = LabelEncoder()
        data['Label'] = lec.fit_transform(data['Label'])
    X_train_origin, y_train_origin = prepare_data(data, mode_style=model_style)

    # 显示验证结果
    def display_scores(scores):
        print("Scores:", scores)
        print("Mean:", scores.mean())
        print("Standard deviation:", scores.std())

    if model_style == 'train':
        if model_select.upper() == 'RF':
            # 随机森林
            from sklearn.ensemble import RandomForestClassifier
            model_reg = RandomForestClassifier(n_estimators=200,
                                               max_depth=20,
                                               min_samples_split=5,
                                               min_samples_leaf=2,
                                               max_features='sqrt',
                                               bootstrap=True,
                                               class_weight='balanced',
                                               random_state=0,
                                               n_jobs=-1,
                                               oob_score=True
                                               )
            # model_reg = RandomForestClassifier(class_weight='balanced',
            #                                    random_state=0,
            #                                    n_jobs=-1)
            model_reg.fit(X_train_origin, y_train_origin)
        elif model_select.upper() == 'RFADJ':
            # 随机森林
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.model_selection import RandomizedSearchCV
            # 定义参数空间
            param_dist = {
                'n_estimators': [int(x) for x in np.linspace(start=100, stop=1000, num=10)],  # 树的数量
                'max_depth': [int(x) for x in np.linspace(10, 110, num=11)] + [None],  # 树的最大深度
                'min_samples_split': [2, 5, 10],  # 分裂节点所需的最小样本数
                'min_samples_leaf': [1, 2, 4],  # 叶子节点的最小样本数
                'max_features': ['sqrt', 'log2', None],  # 每次分裂时考虑的最大特征数
                'bootstrap': [True, False]  # 是否使用有放回抽样
            }

            # 创建随机森林分类器
            rf_classifier = RandomForestClassifier(random_state=0, class_weight='balanced')

            # 使用随机搜索进行超参数调优
            random_search = RandomizedSearchCV(estimator=rf_classifier, param_distributions=param_dist,
                                               n_iter=10, cv=3, scoring='accuracy', n_jobs=-1, verbose=1,
                                               random_state=0)

            # 训练模型并寻找最优参数
            random_search.fit(X_train_origin, y_train_origin)

            # 输出最优参数
            print(f"Best Parameters: {random_search.best_params_}")

            # 使用最优参数重新训练模型
            model_reg = random_search.best_estimator_
            # model_reg = RandomForestClassifier(random_state=0, n_jobs=-1, class_weight='balanced')
            model_reg.fit(X_train_origin, y_train_origin)

        elif model_select.upper() == 'GDBT':
            from sklearn.ensemble import GradientBoostingClassifier
            from sklearn.model_selection import RandomizedSearchCV
            # 定义参数空间
            param_grid = {
                'n_estimators': [100, 200, 300],  # 弱学习器的数量
                'learning_rate': [0.01, 0.1, 0.2],  # 学习率
                'max_depth': [3, 5, 7],  # 树的最大深度
                'min_samples_split': [2, 5, 10],  # 分裂节点所需的最小样本数
                'min_samples_leaf': [1, 2, 4],  # 叶子节点最小样本数
                'subsample': [0.8, 1.0],  # 每棵树使用的数据比例
                'max_features': ['sqrt', 'log2', None]  # 每棵树使用的最大特征数
            }
            # 创建 GradientBoostingClassifier 模型
            gbc = GradientBoostingClassifier()
            # 使用网格搜索进行超参数调优
            random_search = RandomizedSearchCV(estimator=gbc, param_distributions=param_grid,
                                             n_iter=10, cv=5, scoring='accuracy', n_jobs=-1, verbose=1,
                                             random_state=0)

            # 训练模型
            random_search.fit(X_train_origin, y_train_origin)

            # 输出最优参数
            print(f"Best Parameters: {random_search.best_params_}")

            # 使用最优参数重新训练模型
            model_reg = GradientBoostingClassifier(**random_search.best_params_)

            # 重新用最佳参数训练模型
            model_reg.fit(X_train_origin, y_train_origin)

        elif model_select.upper() == 'XGB':
            import xgboost as xgb
            import optuna
            from sklearn.model_selection import RandomizedSearchCV, train_test_split, cross_val_score

            # 将训练集分成两部分，一部分用于训练，另一部分用于验证
            X_train, X_eval, y_train, y_eval = train_test_split(X_train_origin, y_train_origin,
                                                                test_size=0.2, random_state=42)

            # 定义参数空间
            # param_dist = {
            #     'n_estimators': [250, 300, 350, 400, 450],  # 树的数量
            #     'max_depth': [7, 8, 9, 10],  # 树的最大深度
            #     'learning_rate': [0.05, 0.1, 0.15, 0.2],  # 学习率
            #     'subsample': [0.7, 0.8, 0.9, 1.0]  # 每棵树使用的样本比例
            #     # 'colsample_bytree': [0.6, 0.8, 1.0],  # 每棵树使用的特征比例
            #     # 'gamma': [0, 0.1, 0.3],  # 节点分裂所需的最小损失减少量
            #     # 'min_child_weight': [1, 3, 5],  # 控制子叶中实例的权重和数量的最小值
            #     # 'reg_alpha': [0, 0.1, 1],  # L1 正则化
            #     # 'reg_lambda': [1, 1.5, 2]  # L2 正则化
            # }

            # # 创建 XGBoost 模型
            # xgb_classifier = xgb.XGBClassifier(objective='binary:logistic',
            #                                    eval_metric='logloss')
            #
            # # 使用网格搜索进行超参数调优
            # random_search = RandomizedSearchCV(estimator=xgb_classifier, param_distributions=param_dist,
            #                                    n_iter=50,  # 随机搜索次数，可以根据需要调整
            #                                    scoring='accuracy',  # 使用准确率作为评分标准
            #                                    cv=3 # 5 折交叉验证
            #                                    # verbose=1,  # 输出详细信息
            #                                    # random_state=42,  # 固定随机种子
            #                                    # n_jobs=-1
            #                                    )
            #
            # # 训练模型
            # random_search.fit(X_train_prepared, y_train)
            #
            # # 输出最优参数
            # print(f"Best Parameters: {random_search.best_params_}")

            # 定义目标函数用于贝叶斯优化
            def objective(trial):
                # 定义超参数搜索空间
                params = {
                    'n_estimators': trial.suggest_int('n_estimators', 100, 300),
                    'max_depth': trial.suggest_int('max_depth', 4, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.2),
                    'subsample': trial.suggest_float('subsample', 0.7, 1.0),
                    # 其他参数可以根据需要进行调整
                    'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                    'gamma': trial.suggest_float('gamma', 0, 0.5),
                    'min_child_weight': trial.suggest_int('min_child_weight', 1, 3),
                    'reg_alpha': trial.suggest_float('reg_alpha', 0, 0.5),
                    'reg_lambda': trial.suggest_float('reg_lambda', 1, 5)
                }
                # 初始化 XGBoost 模型
                model = xgb.XGBClassifier(objective='binary:logistic', eval_metric='logloss', **params)

                # 使用交叉验证评估模型
                score = cross_val_score(model, X_train_origin, y_train_origin, cv=3, scoring='f1').mean()

                return score

            # 创建 Optuna study 对象并进行优化
            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=80)  # 设置搜索次数

            # 输出最优参数
            print(f"Best Parameters: {study.best_params}")
            print(f"Best Score: {study.best_value}")

            # 使用最优参数重新训练模型
            best_params = study.best_params
            model_reg = xgb.XGBClassifier(objective='binary:logistic', eval_metric='logloss', **best_params)
            model_reg.fit(X_train, y_train)

            # 验证模型
            accuracy = model_reg.score(X_eval, y_eval)
            print(f"Evaluation Accuracy: {accuracy}")

            # 使用最优参数重新训练模型
            # model_reg = random_search.best_estimator_
            # model_reg = xgb.XGBClassifier(
            #     n_estimators=100,  # 较少的树数量
            #     max_depth=6,  # 控制树的深度
            #     learning_rate=0.1,  # 学习率
            #     subsample=0.8,  # 部分样本训练
            #     colsample_bytree=0.8,  # 部分特征训练
            #     reg_alpha=0.5,  # L1 正则化
            #     reg_lambda=1.0  # L2 正则化
            # )
            #
            # model_reg.fit(X_train_prepared, y_train)
        else:
            print('model_select参数错误(RF or XGB or GDBT)')
            return

        # 交叉验证
        try:
            model_scores = cross_val_score(model_reg, X_train_origin, y_train_origin, scoring='accuracy', cv=10)
            display_scores(model_scores)
        except Exception as e:
            print('交叉验证报错：', e)

        # 存储模型
        with open(pkl_filename, 'wb') as file:
            pickle.dump(model_reg, file)
        print(model_select.upper() + '训练模型已存储至：', model_select.upper() + '_model(' +
              label_style + '_' + treat_style + '_'+ pd.to_datetime(pkl_date).strftime('%m%d') + ').pkl')
        return

        # from sklearn.model_selection import GridSearchCV
        # forest_cv = RandomForestClassifier(class_weight='balanced')
        # param = {"n_estimators": range(1, 101, 10)}
        # gridsearch = GridSearchCV(forest_cv, cv=10, verbose=0, n_jobs=-1, param_grid=param, scoring="accuracy")
        # best_model = gridsearch.fit(X_train_prepared, y_train)

        # 验证模型结果
        # forest_score = flg.score(X_test_prepared, y_test)
        # print('随机森林预测评估：')
        # display_scores(forest_score)
    else:
        if not os.path.exists(pkl_filename):
            print('缺失存储的模型和丢弃特征文件！')
            return
        # 读取存储的模型pickle文件
        with open(pkl_filename, 'rb') as file:
            clf_pred = pickle.load(file)

        y_label = clf_pred.predict(X_train_origin)
        y_pro = clf_pred.predict_proba(X_train_origin)
        if label_style == 'Label' or label_style == "Mean_Label" or label_style == "Strength_Label":
            target_label = rank_label
        elif label_style == 'Ratio' or label_style == "Mean_Ratio":
            ratio_label.reverse()
            target_label = ratio_label
        y_target = [target_label[i] for i in y_label]
        data['Label'] = y_target
        data[target_label] = y_pro
        # ln = 0 if label_style == 'Label' else -1
        ln = 0
        data = data.sort_values(['Cal_Date', 'Label', target_label[ln]], ascending=[True, True, False])
        data['Model'] = model_select.upper()
        return data


def stkpick_model_train(start_date=None, end_date=None, label_style='Label',
                        model_select='XGB',
                        pretreat='none', lag_num=30):
    """获取指定日期result数据进行训练或预测"""
    from function_ai.Func_Base import get_trade_date
    trade_dates = get_trade_date()
    if start_date is None:
        start_date = end_date
    model_dates = get_model_traindate(start_date=start_date, end_date=end_date)

    origin_data = get_result_3(end_date=model_dates)
    if pretreat.lower() == 'turn':
        # origin_data = origin_data.query('PostNowSec_LastDays<=3 & '
        #                                 '(DownConsecutive2Now_LastDays<=2 | '
        #                                 'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays<=5)'
        #                                 )
        origin_data = origin_data.query('Now_SecDate==Cal_Date')
    elif pretreat.lower() == 'recov':
        origin_data = origin_data.query('PostPeak_Recent_Neg4_RecovDays>0 & '
                                        'PostPeak_Recent_Neg4_DropDate>=PreNow_SecDate')
    print('训练集天数：', len(origin_data['Cal_Date'].unique()))
    if len(origin_data) == 0:
        print('无符合PreTreat条件的待选品种')
        return None, None, None
    result = clf_rf(origin_data, model_select=model_select,
                    model_style='train', label_style=label_style,
                    treat_style=pretreat, lag_num=lag_num)
    return result


def stkpick_model_predict(predict_date=None, stk_list=None,
                          label_style='Label', model_select='XGB', model_date=None,
                          store_mode=False,
                          pretreat='none', lag_num=30,
                          industry=None):
    """获取指定日期result数据进行训练或预测"""
    from function_ai.Func_Base import get_trade_date
    trade_dates = get_trade_date()
    if stk_list is None:
        origin_data = get_result_3(end_date=predict_date)
    else:
        origin_data = get_result_3(end_date=predict_date, stk_list=stk_list)
    if pretreat.lower() == 'turn':
        origin_data = origin_data.query('PostNowSec_LastDays<=3 & '
                                        '(DownConsecutive2Now_LastDays<=2 | '
                                        'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays<=5)'
                                        )
    elif pretreat.lower() == 'recov':
        origin_data = origin_data.query('PostPeak_Recent_Neg4_RecovDays>0 & '
                                        'PostPeak_Recent_Neg4_DropDate>=PreNow_SecDate')

    if len(origin_data) == 0:
        print('无符合PreTreat条件的待选品种')
        return None, None, None

    result = clf_rf(origin_data, model_select=model_select,
                    model_style='predict', label_style=label_style, model_date=model_date,
                    treat_style=pretreat, lag_num=lag_num)

    if label_style == 'Label' or label_style == "Mean_Label" or label_style == "Strength_Label":
        sort_label = rank_label[0]
    elif label_style == 'Ratio' or label_style == 'Mean_Ratio':
        sort_label = ratio_label[-1]
    # result = result[result[sort_label] >= 0.5].copy()
    result = result.sort_values(['Cal_Date', sort_label, 'Total_MV'], ascending=[True, False, False])
    result['Prop_Rank'] = result.groupby('Cal_Date')[sort_label].rank(method='min', ascending=False)

    if store_mode:
        import config.config_Ali as config
        conf = config.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')
        Sesstion = sessionmaker(bind=engine)
        session = Sesstion()
        # 构建 SQL 查询和参数
        # sql_date = ','.join([f":date{i}" for i in range(len(date))])
        sql_date = ','.join(["'%s'" % item for item in predict_date])
        check_sql = f"""SELECT * FROM stocksfit.stk_results_modelrf
                        WHERE Cal_Date IN ({sql_date})"""
        # params = {f"date{i}": item for i, item in enumerate(date)}

        # 使用参数化查询
        check_result = pd.read_sql(check_sql, engine)

        # 检查结果，并执行删除操作
        if len(check_result) > 0:
            del_dates = check_result['Cal_Date'].values.tolist()
            try:
                delete_sql = text("DELETE FROM stocksfit.stk_results_modelrf WHERE Cal_Date IN :dates")
                with session.begin():
                    session.execute(delete_sql, {"dates": tuple(del_dates)})
            except Exception as e:
                session.rollback()
                print('An error occurred:', e)

        try:
            pd.io.sql.to_sql(result, 'stk_results_modelrf', engine, index=False, schema='stocksfit',
                             if_exists='append')
            print('数据已存储!')
        except Exception as e:
            print('An error occurred:', e)
        session.close()
        engine.dispose()

    result_check = result[~result['name'].str.contains('ST') & ~result['Target_Price'].isnull()
                          ].copy().sort_values(
        by=['Cal_Date', 'Prop_Rank'], ascending=[True, True])
    result_check = result_check.query('PostPreNowBottom_SlowTrend_Deviation<10 | '
                                      'PreNowPeak2NowSec_SlowTrend_Deviation<10 | '
                                      'PostNowSec_SlowTrend_Deviation<10'
                                      ).iloc[:40].sort_values(by='PreNow2PostSec_PGV_MeanRollAvg_Ratio',
                                                    ascending=False).copy()

    # 重新排列列顺序
    cols = [
            'PostSecStart_MedianPeakGap',
            'PostPreNowBottom_SlowTrend_Deviation',
            'PreNowPeak2NowSec_SlowTrend_Deviation',
            'PostNowSec_SlowTrend_Deviation',
            'PostSecStart_UpConsecutive_MaxLastDays'
           ]
    # 将需要移动的列放在前面，其余列保持顺序不变
    result_check = result_check[cols + [col for col in result_check.columns if col not in cols]]
    if industry is not None:
        if isinstance(industry, str):
            industry = [industry]
        result = result[result['industry'].isin(industry)]
        result_check = result_check[result['industry'].isin(industry)]
    # else:
    #     threshold_num = -0.15
    #     result_adj = result[~result['name'].str.contains('ST') & ~result['Target_Price'].isnull()
    #                       ].query('(Now_PGVRollAvg_DownCount>=3 | '
    #                               'DownConsecutive2Now_LastDays==PostSecPeak_PGV_MinRollAvg2Now_LastDays | '
    #                               'DownConsecutive2Now_LastDays==PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays |'
    #                               '(Section_StartDate==Now_SecDate & DownConsecutive2Now_LastDays<=2))')
    return result_check, result



def get_model_traindate(start_date, end_date):
    """获取valleygap超限的日期"""
    if end_date is None:
        trade_dates = get_trade_date()
        end_date = trade_dates[-1]
    gap_data, _ = get_gap_mindata(stk_code='000906.SH', start_date=start_date, end_date=end_date, style='Index')
    gap_data = gap_data.set_index('trade_date')
    train_dates = []
    for index in gap_data.index:
        if gap_data.loc[index, 'valley_gap'] > max(20, gap_data.loc[:index, 'valley_gap'].quantile(0.75)) or \
                gap_data.loc[index, 'pgv_rollavg'] < min(10, gap_data.loc[:index, 'pgv_rollavg'].quantile(0.1)):
            train_dates.append(index)
    return train_dates



def verify_model_effctive(start_date=None, end_date=None, label_style='Ratio',
                           model_select='RF', model_date=None):
    """验证模型有效性"""
    import sklearn.metrics as sm
    if start_date is None:
        start_date = end_date
    if model_date is None:
        pkl_date = input('输入模型生成数据日期：')
    else:
        pkl_date = model_date
    pkl_filename = Path('/Users/<USER>/PycharmProjects/AI_Stock/machine_learn/file_set/' +
                        model_select.upper() + '_model(' +
                        label_style + '_' + pd.to_datetime(pkl_date).strftime('%m%d') + ').pkl')
    if not os.path.exists(pkl_filename):
        print('缺失存储的模型和丢弃特征文件！')
        return
    check_quota = get_result_3(start_date=start_date, end_date=end_date)
    check_quota = get_train_label(check_quota, label_style=label_style)
    from sklearn.preprocessing import LabelEncoder
    lec = LabelEncoder()
    check_quota['Label'] = lec.fit_transform(check_quota['Label'])
    X_test_prepared, y_test = prepare_data(check_quota, mode_style='train')
    with open(pkl_filename, 'rb') as file:
        clf_pred = pickle.load(file)
    tree_predicted = clf_pred.predict(X_test_prepared)
    tree_cp = sm.classification_report(y_test, tree_predicted)
    print('决策树分类报告：\n', tree_cp)
    return check_quota


def match_model_columns(train_startdate=None, train_enddate=None,
                        test_startdate=None, test_enddate=None):
    """对比num_columns"""
    result_train = get_result_3(start_date=train_startdate, end_date=train_enddate)
    result_test = get_result_3(start_date=test_startdate, end_date=test_enddate)
    result_train_prepared, num_columns_train, cat_columns_train = prepare_cat(result_train)
    result_test_prepared, num_columns_test, cat_columns_test = prepare_cat(result_test)
    return num_columns_train, cat_columns_train, num_columns_test, cat_columns_test

# def get_predict_data(start_date=None, end_date=None):
#     """从stk_result_modelrf数据库取数"""
#     import config.config_Ali as config
#     conf = config.configModel()
#     engine = create_engine(
#         'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
#             conf.DC_DB_PORT) + '/stocksfit')
#     if start_date is None:
#         start_date = end_date
#     rf_sql = f"""SELECT * FROM stocksfit.stk_results_modelrf
#                                 WHERE Cal_Date between '{start_date}' and '{end_date}'"""
#     df = pd.read_sql(rf_sql, engine)
#     return df


if __name__ == '__main__':
    # 训练
    result = get_result_3(end_date='2024-05-08')
    train_label = get_train_label(result, label_style='Mean_Label', lag_num=10)
    # model_select = 'XGB'
    # from function_ai.Func_Base import get_trade_date
    # result_train, _, model_date = stkpick_model_process(start_date='2024-08-19', end_date='2024-09-30',
    #                                                   model_select=model_select, model_style='train',
    #                                                   label_style='Label', store_mode=False,
    #                                                   pretreat=True)

    # 预测
    # result_check, result_predict, _ = stkpick_model_process(start_date='2024-10-31', end_date='2024-10-31',
    #                                                         model_style='predict', label_style='Label',
    #                                                         model_select='XGB', model_date='2024-08-20', pretreat=True)
    # result_check_adj = result_check[
    #     ['ts_code', 'name', 'industry', 'PostSecPeak_DownConsecutive_Num', 'PostSecMaxRollAvg_DownConsecutive_Num',
    #      'MinRollAvg_Truncated_Diff', 'Now_PGVRollAvg_DownCount', 'PostSecPeak_PGV_MinRollAvg2Now_LastDays']]

    # result_check, result_predict, _ = stkpick_model_process(start_date='2024-05-08', end_date='2024-05-08',
    #                                                         model_style='predict', model_select='XGB',
    #                                                         model_date='2024-08-20', label_style='Mean_Label',
    #                                                         pretreat='turn')
    # from sklearn.preprocessing import LabelEncoder
    # label_style = 'Label'
    # mode_input = input('输入处理类型：（"t" for train or "p" for predict）')
    # if mode_input == 't':
    #     model_style = 'train'
    # elif mode_input == 'p':
    #     model_style = 'predict'
    # else:
    #     model_style = 'wrong'
    #     print('输出错误')
    # print('当前调用为：', model_style)
    # if model_style == 'train':
    #     turn_dates = ['2024-06-25', '2024-07-24', '2024-08-05']
    #     trading_dates = get_trade_date()
        # start_date, end_date = '2022-09-26', '2022-10-31'
        # origin_data = pd.DataFrame()
        # for turn_date in turn_dates:
            # end_date = trading_dates[trading_dates > turn_date][
            #     min(8, len(trading_dates[trading_dates > turn_date]) - 1)]
            # result = get_result_3(end_date=turn_date)
            # origin_data = pd.concat([origin_data, result], ignore_index=True).reset_index(drop=True)
        # train_data = get_train_label(result=origin_data, label_style=label_style)
    #     result = clf_rf(origin_data, model_style=model_style, label_style=label_style)
    # elif model_style == 'predict':
    #     predict_date = '2024-07-31'
        # from function_ai.Func_Base import collect_indusnum
        # indus_num = collect_indusnum(start_date='2022-10-31', end_date='2022-12-06', mode='result3')
        # indus_threshold = 0.3
        # indus_num_adj = indus_num.query('ComRatio>@indus_threshold')
        # print('获取行业强势排序：done')
        # print(indus_num_adj['industry'].iloc[:10].tolist())
        # result = get_result_3(end_date=predict_date)
        # result = result.query('PostPeak_Gap2Peak_Ratio<=-10 & '
        #                       '(Period_Trend=="下行" | Period_Trend_StartDate==Section_StartDate) & '
        #                       '(PreTurn_Period_R2>0.7 | Now_Period_R2>0.7) & '
        #                       '(CoverDays_Bf_BreakDate>300 | CoverDays_Aft_BreakDate>300)')
        # print('获取股票筛选结果:done')
        # ml_result = clf_rf(result, model_style=model_style, label_style=label_style)
        # ml_result = ml_result.sort_values('前25%', ascending=False)

        # 获取前picknum位
        # picknum = 10
        # ml_result_adj = pd.DataFrame()
        # labels = ml_result['Label'].unique()
        # for label in labels:
        #     result_temp = ml_result.query(
        #         'Label==@label').sort_values(label, ascending=False).head(
        #         min(picknum, len(ml_result.query('Label==@label'))))
        #     ml_result_adj = pd.concat([ml_result_adj, result_temp], ignore_index=True).reset_index(drop=True)

        # 测试筛选结果胜率



    # # 将文本和日期类别特征转换为数值,并分割训练集、测试集。label转换单独处理
    # sfitresult_adj, num_columns, cat_columns = prepare_cat(sfitresult)
    # le = LabelEncoder()
    # sfitresult_adj['Label'] = le.fit_transform(sfitresult_adj['Label'])
    # train_set, test_set = data_split(sfitresult_adj)
    #
    # # 分离训练数据和分类结果数据
    # X_train = train_set.drop('Label', axis=1)
    # y_train = train_set['Label'].values
    # X_test = test_set.drop('Label', axis=1)
    # y_test = test_set['Label'].values
    #
    # # 显示验证结果
    # def display_scores(scores):
    #     print("Scores:", scores)
    #     print("Mean:", scores.mean())
    #     print("Standard deviation:", scores.std())
    #
    # # 构造管道
    # num_pipline = Pipeline([
    #     ('selector', DataFrameSelector(num_columns)),
    #     ('imputer', SimpleImputer(missing_values=np.nan, strategy='constant', fill_value=0)),
    #     ('stdscaler', StandardScaler()),
    # ])
    # cat_pipline = Pipeline([
    #     ('selector', DataFrameSelector(cat_columns))
    # ])
    # full_piplne = FeatureUnion(transformer_list=[
    #     ('num_pipline', num_pipline),
    #     ('cat_pipline', cat_pipline)
    # ])
    #
    # X_train_prepared = full_piplne.fit_transform(X_train)
    # X_test_prepared = full_piplne.fit_transform(X_test)
    #
    # # 特性选择
    # from sklearn.ensemble import ExtraTreesClassifier
    # from sklearn.feature_selection import SelectFromModel
    # clf = ExtraTreesClassifier()
    # clf = clf.fit(X_train_prepared, y_train)
    # model = SelectFromModel(clf, prefit=True)
    # X_train_prepared = model.transform(X_train_prepared)
    # X_test_prepared = model.transform(X_test_prepared)
    #
    # # 决策树模型
    # from sklearn.tree import DecisionTreeClassifier
    #
    # tree_reg = DecisionTreeClassifier()
    # clf = tree_reg.fit(X_train_prepared, y_train)
    #
    # tree_score = clf.score(X_test_prepared, y_test)
    # print('决策树预测评估：')
    # display_scores(tree_score)
    #
    # # 决策树交叉验证
    # from sklearn.model_selection import cross_val_score
    # scores = cross_val_score(tree_reg, X_train_prepared, y_train, scoring='accuracy', cv=10)
    # display_scores(scores)
    #
    # # 随机森林
    # from sklearn.ensemble import RandomForestClassifier
    # forest_reg = RandomForestClassifier()
    # flg = forest_reg.fit(X_train_prepared, y_train)
    #
    # forest_score = flg.score(X_test_prepared, y_test)
    # print('随机森林预测评估：')
    # display_scores(forest_score)
    #
    # # 交叉验证-随机森林
    # forest_scores = cross_val_score(forest_reg, X_train_prepared, y_train, scoring='accuracy', cv=10)
    # display_scores(forest_scores)
    #
    # # 提取预测数据及特征标识
    # y_predicted = forest_reg.predict(X_test_prepared)
    # y_predict_pro = forest_reg.predict_proba(X_test_prepared)
    # print(list(le.inverse_transform(y_predicted[:5])))
    # print(y_predict_pro[:5])
    #
    # # 支持向量机
    # from sklearn.svm import SVC
    # svm_reg = SVC()
    # slg = svm_reg.fit(X_train_prepared, y_train)
    #
    # svm_score = slg.score(X_test_prepared, y_test)
    # print('支持向量机预测评估：')
    # display_scores(svm_score)
    #
    # # 交叉验证-支持向量机
    # svm_scores = cross_val_score(svm_reg, X_train_prepared, y_train, scoring='accuracy', cv=10)
    # display_scores(svm_scores)
