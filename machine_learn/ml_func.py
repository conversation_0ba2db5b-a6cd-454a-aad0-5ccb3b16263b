# -*- coding: UTF-8 -*-
# 准备机器学习所需学习数据和测试数据

import pandas as pd
from function_ai.stkpick_funcs_old.StkPick_Func_3 import get_result_3
from function_ai.Func_Base import get_stock_data, get_trade_date


def cal_ratio(result, stock_data):
    """计算股票区间最大涨幅"""
    stk_code = result['ts_code']
    date = result['Cal_Date']
    stk_data = stock_data.query('ts_code==@stk_code').set_index('trade_date', drop=False)
    if len(stk_data) > 0:
        return (stk_data['close'].max() / stk_data.loc[date:, 'close'].iloc[1] - 1) * 100
    else:
        return None


def set_label(ratio, thresholds):
    """计算排名位置并分类"""
    labels = ['前20%', '后80%']
    for num, threshold in enumerate(thresholds):
        if ratio > threshold:
            return labels[num]
        else:
            continue
    return labels[-1]


def get_mldata(train_dates, end_date=None):
    """获取训练数据，并处理分类结果"""
    pertiles = [0.20]
    if isinstance(train_dates, str):
        train_dates = [train_dates]
    if end_date is not None and end_date < max(train_dates):
        print('结束日期早于测试数据日期！')
        return
    result_data = pd.DataFrame()
    for train_date in train_dates:
        temp_data = get_result_3(end_date=train_date)
        result_data = pd.concat([result_data, temp_data], ignore_index=True)
    min_date = min(train_dates)
    if end_date is None:
        trading_date = get_trade_date(start_date=min_date)
        lennum = min(20, len(trading_date[trading_date > max(train_dates)])-1)
        end_date = trading_date[trading_date > max(train_dates)][lennum]
    stock_data = get_stock_data(start_date=min_date, end_date=end_date)
    result_data['Ratio'] = result_data.apply(func=cal_ratio, args=(stock_data, ), axis=1, result_type='expand')
    result_data = result_data.dropna(subset=['Ratio'], how='any').sort_values('Ratio', ascending=False)
    thresholds = []
    for pertile in pertiles:
        thresholds.append(result_data['Ratio'].quantile(pertile))
    thresholds.sort(reverse=True)
    result_data['Label'] = result_data['Ratio'].apply(func=set_label, args=(thresholds, ))
    return result_data.drop('Ratio', axis=1)


def prepare_data(result):
    """处理df中的字符类型数据"""
    global trade_dates
    trade_dates = get_trade_date()
    """转换为间隔天数：
        Long_PeakDate 转换为距离当前日期的天数
        Period_Break_Date 转换为距离当前日期的天数
        Section_PeakDate 转换为距离当前日期的天数
        Sec_IndexDiff_StartDate 转换为距离当前日期的天数
        PostTurn_Recent_Neg4_DropDate 转换为距离当前日期的天数
        PostPeak_Recent_Neg4_DropDate 转换为距离当前日期的天数
        PrePeak_Over4_Date 和 Convex_Break_DropDate 转换为两个日期间的间隔天数"""
    def get_interval_dates(start_date, end_date):
        global trade_dates
        if start_date is not None:
            return len(trade_dates[(trade_dates > start_date) & (trade_dates <= end_date)])
        else:
            return 0
    column_list_1 = ['Long_PeakDate', 'Period_Break_Date',
                     'Section_PeakDate', 'Sec_IndexDiff_StartDate',
                     'PostTurn_Recent_Neg4_DropDate', 'PostPeak_Recent_Neg4_DropDate']
    for column in column_list_1:
        result[column] = result.apply(lambda fn: get_interval_dates(fn[column], fn['Cal_Date']), axis=1)
    result['Convex_Break_DropDate'] = result.apply(
        lambda fn: get_interval_dates(fn['PrePeak_Over4_Date'], fn['Convex_Break_DropDate']), axis=1)
    result = result.drop(['PrePeak_Over4_Date'], axis=1)

    """
    Rule2 转换为指数排序序号
    Period_TurnDate
    Section_StartDate
    """
    global market_turndates
    market_turndates = ['2021-11-02', '2021-11-30', '2021-12-08', '2021-12-13',
                        '2021-12-20', '2022-01-28', '2022-02-14', '2022-03-15',
                        '2022-04-26', '2022-07-15', '2022-08-03', '2022-09-01',
                        '2022-09-26', '2022-10-11', '2022-10-31', '2022-11-10',
                        '2022-11-21', '2022-11-28']
    market_turndates.sort(reverse=True)
    def cal_turndate(turndate):
        global market_turndates, trade_dates
        for num, date in enumerate(market_turndates):
            if turndate is not None:
                interval = len(trade_dates[(trade_dates > turndate) & (trade_dates <= date)]) \
                    if turndate < date \
                    else len(trade_dates[(trade_dates > date) & (trade_dates <= turndate)])
                if interval <= 3:
                    return num
            else:
                continue
        return 100
    result['Period_TurnDate'] = result['Period_TurnDate'].apply(func=cal_turndate, )
    result['Section_StartDate'] = result['Section_StartDate'].apply(func=cal_turndate, )

    """
    ## Rule3 转换为类别数字
    Period_Trend 转换为类别数字（不放弃None）
    Long_Trend 转换为类别数字（不放弃None)
    Now_State 转换为类别数字（不放弃None）
    Now_Vol_Trend 转换为类别数字（不放弃None）
    Sec_IndexDiff_Signal 转换为类别数字（不放弃None）
    Bottom_Date 转换为是否与Period_TurnDate相同的二元判定特征
    industry 转换为类别数字
    area 转换为类别数字
    """
    from sklearn.preprocessing import LabelEncoder
    label_encoder = LabelEncoder()
    column_list_2 = ['Period_Trend', 'Long_Trend', 'Now_State', 'Now_Vol_Trend',
                     'Sec_IndexDiff_Signal', 'industry', 'area']
    feature_map = {}
    for column in column_list_2:
        temp_map = {}
        result[column] = result[column].fillna(value='普通')
        result[column] = label_encoder.fit_transform(result[column])
        for cl in label_encoder.classes_:
            temp_map.update({cl: label_encoder.transform([cl])[0]})
        feature_map.update({column: temp_map})
    result['Bottom_Date'] = result.apply(lambda fn: 1 if fn['Bottom_Date']==fn['Period_TurnDate'] else 0, axis=1)

    """
    ## Rule4 放弃
    放弃 'id' 'ts_code' 'name' 'Target_Price'
     'Press_Price' 'Stop_Lose_Price'
    放弃 Long_Trend_StartDate, Period_Trend_StartDate
    放弃 Concave_Break_Date，因为已有Concave_Break_Days2Now
    放弃 PostTurn_Reg_EndDate
    """
    result = result.drop(columns=['id', 'ts_code', 'name', 'Target_Price', 'Press_Price',
                                 'Stop_Lose_Price', 'Long_Trend_StartDate', 'Period_Trend_StartDate',
                                 'Concave_Break_Date', 'PostTurn_Reg_EndDate', 'Cal_Date'])

    """NA值全部填充为0"""
    result = result.fillna(0)
    return result





