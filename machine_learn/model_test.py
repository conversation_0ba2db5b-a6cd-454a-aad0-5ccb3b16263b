"""对筛选结果后续收益率进行计算，获取胜率和平均收益率"""
import pdb
import pandas as pd
import numpy as np
import os
import pickle
from function_ai.Func_Base import get_trade_date, get_stock_data
from machine_learn.Func_MacinLn import cal_osci_ratio, stkpick_model_forest
from sklearn.base import BaseEstimator, TransformerMixin
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker


def get_model_test(start_date, end_date, rank_num=10, model_date=None):
    """计算指定起止日期内的收益率"""
    trade_dates = get_trade_date()
    if model_date is None:
        import config.config_Ali as config
        conf = config.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')

        read_sql = f"""select * from stocksfit.stk_results_modelrf 
                       where Cal_Date between '{start_date}' and '{end_date}' and Prop_Rank<='{rank_num}'"""
        model_data = pd.read_sql_query(read_sql, engine)
        model_data = model_data[~model_data['name'].str.contains('ST')].copy()
    else:
        date_list = trade_dates[(trade_dates >= start_date) & (trade_dates <= end_date)].tolist()
        model_data = stkpick_model_forest(date=date_list, model_style='predict', label_style='Ratio',
                                          model_date=model_date, store_mode=False)
    stock_data = get_stock_data(start_date=start_date)
    model_data['Lag_Ratio'] = model_data.apply(func=cal_osci_ratio, args=(stock_data, trade_dates,),
                                               axis=1, result_type='expand')
    model_stat = model_data[['Cal_Date', 'Lag_Ratio']].groupby(by='Cal_Date', as_index=False)['Lag_Ratio'].mean()
    model_count = model_data[['Cal_Date', 'ts_code']].groupby(by='Cal_Date', as_index=False)['ts_code'].count()
    model_count = model_count.rename(columns={'ts_code': 'stk_count'})
    model_stat = pd.merge(model_stat, model_count, on='Cal_Date', how='left')
    model_stat['ToNow_LastDays'] = model_stat['Cal_Date'].apply(lambda fn: len(trade_dates[trade_dates>fn]))
    return model_stat, model_data