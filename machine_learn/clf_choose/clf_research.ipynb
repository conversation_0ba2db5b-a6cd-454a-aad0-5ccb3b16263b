{"cells": [{"cell_type": "markdown", "source": ["# 调用准备好的数据，测试模型预测效果"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["### 参数设定"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 1, "outputs": [], "source": ["import pandas as pd\n", "from machine_learn.Func_MacinLn import get_train_label, data_split\n", "from machine_learn.Func_MacinLn import prepare_data\n", "from function_ai.Func_Base import get_trade_date\n", "from function_ai.StkPick_Func_3 import get_result_3\n", "from sklearn.preprocessing import LabelEncoder\n", "import sklearn.metrics as sm\n", "\n", "# start_date, end_date = '2022-09-26', '2022-10-31'\n", "predict_date, sec_date = '2022-12-01', '2022-11-28'\n", "label_style = 'Label'\n", "\n", "trading_dates = get_trade_date()"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["### 获取并准备训练数据"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 2, "outputs": [], "source": ["# column_names = ['ts_code', 'name', 'industry', 'area', 'Target_Ratio',\n", "#                        'TargetRatio_High', 'Period_TurnDate',\n", "#                        'Period_Trend', 'Period_Trend_StartDate',\n", "#                        'PreTurn_Period_Lastdays', 'PreTurn_Period_SumRatio',\n", "#                        'PreTurn_Period_AvgRatio', 'PreTurn_Period_R2',\n", "#                        'Now_Period_Lastdays', 'Now_Period_SumRatio', 'Now_Period_AvgRatio', 'Now_Period_R2',\n", "#                        'Bottom_Date', 'Bottom_RiseRatio', 'Bottom_LastDays',\n", "#                        'PostBottom_MaxDrop',\n", "#                        'CoverDaysDiff_10', 'CoverRatioBand_10',\n", "#                        'CoverDaysDiff_20', 'CoverRatioBand_20',\n", "#                        'Period_Break_Ratio', 'Period_Break_Date',\n", "#                        'CoverDays_Bf_BreakDate', 'CoverDays_Aft_BreakDate',\n", "#                        'Peak_Pres_Num', 'Peak_Pres_Lastdays', 'Valley_Pres_Num', 'Valley_Pres_Lastdays',\n", "#                        'PreTurn_Period_GapRatio',\n", "#                        'PostTurn_Sec_Max_SumRatio',\n", "#                        'PostTurn_Sec_Min_SumRatio',\n", "#                        'Period_AftTurn_SumRatio', 'Period_AftTurn_Lastdays',\n", "#                        'PreTurn_LastSec_Lastdays', 'PreTurn_LastSec_AvgRatio',\n", "#                        'PreTurn_LastSec_SumRatio', 'PreTurn_LastSec_ExtreRatio',\n", "#                        'PreTurn_LastSec_AvgAmount',\n", "#                        'PostTurn_1stSec_Lastdays', 'PostTurn_1stSec_AvgRatio',\n", "#                        'PostTurn_1stSec_SumRatio', 'PostTurn_1stSec_ExtreRatio',\n", "#                        'PostTurn_1stSec_AvgAmount', 'PostTurn_Sec_RiseNum',\n", "#                        'PostTurn_Sec_DropNum',\n", "#                        'Now_DayRatio',\n", "#                        'Now_MaxSum',\n", "#                        'Now_State', 'Now_Vol_Trend',\n", "#                        'Section_PeakDate', 'Section_StartDate', 'SectionStart_Position',\n", "#                        'Now_Sec_Lastdays', 'Now_Sec_SumRatio',\n", "#                        'Now_Sec_AvgRatio', 'Now_Sec_ExtreRatio', 'Now_Sec_AvgAmount',\n", "#                        'Bf_PreSec_Lastdays', 'Bf_PreSec_SumRatio',\n", "#                        'Bf_PreSec_AvgRatio', 'Bf_PreSec_ExtreRatio',\n", "#                        'Bf_PreSec_AvgAmount',\n", "#                        'Aft_PreSec_Lastdays', 'Aft_PreSec_SumRatio',\n", "#                        'Aft_PreSec_AvgRatio', 'Aft_PreSec_ExtreRatio',\n", "#                        'Aft_PreSec_AvgAmount',\n", "#                        'Recent5Days_MinRatio',\n", "#                        'Recent_MeanRatio', 'Recent_MaxRatio', 'Recent_Mean_ClsOpenRatio',\n", "#                        'Recent_PreChg_SumRatio', 'Recent_Ratio_Chg',\n", "#                        'Sec_IndexDiff_Signal', 'Sec_IndexDiff_Ratio', 'Sec_IndexDiff_StartDate',\n", "#                        'Section_Break_Ratio',\n", "#                        'Now_Sec_Proportion', 'Now_Sec_BelowMovAvg_Days',\n", "#                        'Now_Sec_BelowMovAvg_ContiDays', 'Now_Sec_BelowMovAvg_MaxDropRatio',\n", "#                        'Section_PostPeak_MaxDrop',\n", "#                        'Recent4P_MaxLastDays', 'PostTurn_Over7_Num',\n", "#                        'Now_Over7_ContiNum',\n", "#                        'PostTurn_Recent_Neg4_DropDate', 'PostTurn_Recent_Neg4_RecovDays',\n", "#                        'PostTurn_Recent_Neg4_DropRatio',\n", "#                        'PostPeak_Recent_Neg4_DropDate', 'PostPeak_Recent_Neg4_RecovDays',\n", "#                        'PostPeak_Recent_Neg4_DropRatio',\n", "#                        'PostSec_Over5_Num',\n", "#                        'PostTurn_Reg_R2', 'PostTurn_Reg_Sum2Std',\n", "#                        'PostTurn_Reg_Lastdays',\n", "#                        'PostSecStart_Reg_R2', 'PostSecStart_Reg_Sum2Std',\n", "#                        'PrePeak_Sec_Drop_AvgRatio', 'PrePeak_Sec_Drop_Lastdays',\n", "#                        'PrePeak_Sec_Rise_AvgRatio', 'PrePeak_Sec_Rise_Lastdays',\n", "#                        'PrePeak_Sec_Rise_ExtreRatio', 'PrePeak_Over4_Date', 'Convex_Break_DropDate',\n", "#                        'GapRatio2Sec',\n", "#                        'PostPeak_Gap2Peak_Ratio',\n", "#                        'PostPeak_GapLastDays', 'PostPeak_GapCovDays', 'PostPeak_Gap2NowDays',\n", "#                        'TurnDiff_Period_AvgRatio_Div', 'TurnDiff_Period_LastDays_Div',\n", "#                        'TurnDiff_Period_AvgAmount_Div', 'TurnDiff_Sec_Recov_Position',\n", "#                        'TurnDiff_Sec_LastDays_Div', 'TurnDiff_Recoved_Sec_Num',\n", "#                        'TurnDiff_Recoved_Sec_LastDays_Div', 'TurnDiff_Recoved_Sec_BMA_Proportion',\n", "#                        'SecValley_GapRatio', 'SecDiff_Sec_Recov_Position',\n", "#                        'SecDiff_Sec_LastDays_Div', 'SecDiff_Sec_AvgRatio_Div',\n", "#                        'SecDiff_Sec_AvgAmount_Div', 'CoverDays_BreakDate_RatioBand',\n", "#                        'Narrow_LastDays', 'Narrow_E2N_Days',\n", "#                        'Narrow_E2N_Return', 'Narrow_E2N_MaxReturn',\n", "#                        'Narrow_E2N_MinReturn',\n", "#                        'PrePeak_Sec_Recent_MeanRatio', 'PrePeak_Sec_Recent_MaxRatio', 'PrePeak_Sec_Recent_ClsOpenRatio',\n", "#                        'Cal_Date'\n", "#                     ]"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 3, "outputs": [], "source": ["# 依据转折日期后10天日期构建训练数据\n", "# turn_dates = ['2022-09-26', '2022-10-11']\n", "# origin_data = pd.DataFrame()\n", "# end_dates = []\n", "# for turn_date in turn_dates:\n", "#     end_date = trading_dates[trading_dates>turn_date][min(10, len(trading_dates[trading_dates>turn_date])-1)]\n", "#     end_dates.append(end_date)\n", "#     result = get_result_3(start_date=turn_date, end_date=end_date, sec_date=turn_date)\n", "#     origin_data = pd.concat([origin_data, result], ignore_index=True).reset_index(drop=True)\n", "# print('结束日期：', end_dates)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 4, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集日期： 2022-11-04\n"]}], "source": ["# 选择单一日期构建训练数据\n", "# lennum = len(trading_dates[(trading_dates>sec_date) & (trading_dates<=predict_date)])\n", "# train_date = trading_dates[trading_dates>train_turn][lennum-1]\n", "train_date = '2022-11-04'\n", "origin_data = get_result_3(end_date=train_date)\n", "# origin_data = origin_data[column_names]\n", "print('训练集日期：', train_date)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 5, "outputs": [], "source": ["train_data_origin = get_train_label(result=origin_data, label_style=label_style)\n", "le = LabelEncoder()\n", "train_data_origin['Label'] = le.fit_transform(train_data_origin['Label'])\n", "train_set, test_set = data_split(train_data_origin)\n", "X_train_prepared, y_train = prepare_data(train_set, mode_style='train')\n", "X_test_prepared, y_test = prepare_data(test_set, mode_style='train')"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 6, "outputs": [{"data": {"text/plain": "array(['前25%', '前50%', '前80%', '后20%'], dtype=object)"}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["le.classes_"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 7, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0: 0.2, 1: 0.3, 2: 0.2, 3: 0.2}\n"]}], "source": ["weight = dict()\n", "for i in range(0,len(le.classes_)):\n", "    weight.update({i: round(len(train_data_origin.query('Label==@i'))/len(train_data_origin), 1)})\n", "print(weight)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 8, "outputs": [], "source": ["# test_data = get_train_data(start_date=start_date, end_date=end_date, label_style='Test')"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 9, "outputs": [], "source": ["# import matplotlib.pyplot as plt\n", "# plt.hist(test_data['Ratio'].values, bins=20)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 10, "outputs": [], "source": ["# 计算特征与Ratio的相关系数，查看哪些对结果影响度高\n", "# test_data = get_train_data(start_date=start_date, end_date=end_date, mode='ratio')\n", "# test_adj, num_test, cat_test, test_map = prepare_cat(test_data)\n", "# corr_matrix = test_adj.corr()\n", "# corr_matrix['Ratio'].sort_values(ascending=False)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["### 特征选择，剔除无关特征项"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 11, "outputs": [], "source": ["# from sklearn.ensemble import ExtraTreesClassifier\n", "# from sklearn.feature_selection import SelectFromModel\n", "# et_clf = ExtraTreesClassifier()\n", "# choose_clf = et_clf.fit(X_train_prepared, y_train)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 12, "outputs": [], "source": ["# model = SelectFromModel(choose_clf, prefit=True)\n", "# X_train_prepared = model.transform(X_train_prepared)\n", "# X_test_prepared = model.transform(X_test_prepared)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["## 测试各种模型效果"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 13, "outputs": [], "source": ["def display_scores(scores):\n", "    print(\"Scores:\", scores)\n", "    print(\"Mean:\", scores.mean())\n", "    print(\"Standard deviation:\", scores.std())"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### 决策树模型"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 14, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["决策树预测评估：\n", "Scores: 0.2757201646090535\n", "Mean: 0.2757201646090535\n", "Standard deviation: 0.0\n"]}], "source": ["from sklearn.tree import DecisionTreeClassifier\n", "tree_reg = DecisionTreeClassifier(class_weight='balanced')\n", "clf = tree_reg.fit(X_train_prepared, y_train)\n", "\n", "tree_score = clf.score(X_test_prepared, y_test)\n", "print('决策树预测评估：')\n", "display_scores(tree_score)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 15, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scores: [0.30077121 0.31362468 0.26478149 0.31362468 0.28608247 0.32989691\n", " 0.31185567 0.30670103 0.32474227 0.30154639]\n", "Mean: 0.3053626798823311\n", "Standard deviation: 0.017922536063305777\n"]}], "source": ["# 决策树交叉验证\n", "from sklearn.model_selection import cross_val_score\n", "scores = cross_val_score(tree_reg, X_train_prepared, y_train, scoring='accuracy', cv=10)\n", "display_scores(scores)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 16, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["决策树分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.19      0.25      0.22       195\n", "           1       0.32      0.35      0.33       291\n", "           2       0.30      0.24      0.26       243\n", "           3       0.29      0.24      0.26       243\n", "\n", "    accuracy                           0.28       972\n", "   macro avg       0.27      0.27      0.27       972\n", "weighted avg       0.28      0.28      0.28       972\n", "\n"]}], "source": ["# 决策树分类报告\n", "tree_predicted = tree_reg.predict(X_test_prepared)\n", "tree_cp = sm.classification_report(y_test, tree_predicted)\n", "print('决策树分类报告：\\n', tree_cp)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### 随机森林"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 17, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机森林预测评估：\n", "Scores: 0.36728395061728397\n", "Mean: 0.36728395061728397\n", "Standard deviation: 0.0\n"]}], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "forest_reg = RandomForestClassifier(random_state=0, n_jobs=-1, class_weight='balanced')\n", "flg = forest_reg.fit(X_train_prepared, y_train)\n", "\n", "forest_score = flg.score(X_test_prepared, y_test)\n", "print('随机森林预测评估：')\n", "display_scores(forest_score)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 18, "outputs": [], "source": ["# from pathlib import Path\n", "# import pickle\n", "# pkl_filename = Path('/Users/<USER>/PycharmProjects/AI_Stock/machine_learn/file_set/pickle_model.pkl')\n", "# with open(pkl_filename, 'wb') as file:\n", "#     pickle.dump(forest_reg, file)\n"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 19, "outputs": [], "source": ["# y_predicted = forest_reg.predict(X_test_prepared)\n", "# y_predict_pro = forest_reg.predict_proba(X_test_prepared)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 20, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scores: [0.39845758 0.36246787 0.40616967 0.3933162  0.38917526 0.35309278\n", " 0.42010309 0.36082474 0.36340206 0.34793814]\n", "Mean: 0.37949473935282113\n", "Standard deviation: 0.023654310473415114\n"]}], "source": ["# 交叉验证-随机森林\n", "forest_scores = cross_val_score(forest_reg, X_train_prepared, y_train, scoring='accuracy', cv=10)\n", "display_scores(forest_scores)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 21, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机森林分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.44      0.24      0.31       195\n", "           1       0.35      0.58      0.44       291\n", "           2       0.32      0.19      0.24       243\n", "           3       0.39      0.39      0.39       243\n", "\n", "    accuracy                           0.37       972\n", "   macro avg       0.38      0.35      0.35       972\n", "weighted avg       0.37      0.37      0.35       972\n", "\n"]}], "source": ["# 随机森林分类报告\n", "forest_predicted = forest_reg.predict(X_test_prepared)\n", "forest_cp = sm.classification_report(y_test, forest_predicted)\n", "print('随机森林分类报告：\\n', forest_cp)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### 线性支持向量机"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 22, "outputs": [{"data": {"text/plain": "LinearSVC(C=1, class_weight={0: 0.2, 1: 0.3, 2: 0.2, 3: 0.2}, dual=False)"}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.svm import LinearSVC\n", "svm_reg = LinearSVC(dual=False, C=1, class_weight=weight)\n", "slg = svm_reg.fit(X_train_prepared, y_train)\n", "slg"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 23, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["支持向量机预测评估：\n", "Scores: 0.33539094650205764\n", "Mean: 0.33539094650205764\n", "Standard deviation: 0.0\n"]}], "source": ["svm_score = slg.score(X_test_prepared, y_test)\n", "print('支持向量机预测评估：')\n", "display_scores(svm_score)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 24, "outputs": [], "source": ["# 交叉验证-支持向量机\n", "# svm_scores = cross_val_score(svm_reg, X_train_prepared, y_train, scoring='accuracy', cv=10)\n", "# display_scores(svm_scores)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 25, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["支持向量机分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.30      0.13      0.18       195\n", "           1       0.33      0.62      0.43       291\n", "           2       0.32      0.16      0.22       243\n", "           3       0.37      0.34      0.35       243\n", "\n", "    accuracy                           0.34       972\n", "   macro avg       0.33      0.31      0.30       972\n", "weighted avg       0.33      0.34      0.31       972\n", "\n"]}], "source": ["# 支持向量机分类报告\n", "svm_predicted = svm_reg.predict(X_test_prepared)\n", "svm_cp = sm.classification_report(y_test, svm_predicted)\n", "print('支持向量机分类报告：\\n', svm_cp)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### K近邻分类模型"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 26, "outputs": [{"data": {"text/plain": "KNeighborsClassifier()"}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.neighbors import KNeighborsClassifier\n", "kneib_reg = KNeighborsClassifier()\n", "klg = kneib_reg.fit(X_train_prepared, y_train)\n", "klg"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 27, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["K近邻预测评估：\n", "Scores: 0.30864197530864196\n", "Mean: 0.30864197530864196\n", "Standard deviation: 0.0\n"]}], "source": ["kneib_score = klg.score(X_test_prepared, y_test)\n", "print('K近邻预测评估：')\n", "display_scores(kneib_score)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 28, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["K近邻分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.27      0.32      0.29       195\n", "           1       0.33      0.36      0.34       291\n", "           2       0.28      0.26      0.27       243\n", "           3       0.34      0.29      0.31       243\n", "\n", "    accuracy                           0.31       972\n", "   macro avg       0.31      0.31      0.31       972\n", "weighted avg       0.31      0.31      0.31       972\n", "\n"]}], "source": ["# K近邻分类报告\n", "kneib_predicted = kneib_reg.predict(X_test_prepared)\n", "kneib_cp = sm.classification_report(y_test, kneib_predicted)\n", "print('K近邻分类报告：\\n', kneib_cp)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### Adaboost集合算法"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 29, "outputs": [{"data": {"text/plain": "AdaBoostClassifier(n_estimators=100, random_state=0)"}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.ensemble import AdaBoostClassifier\n", "adab_reg = AdaBoostClassifier(n_estimators=100, random_state=0)\n", "alg = adab_reg.fit(X_train_prepared, y_train)\n", "alg"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 30, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adaboost预测评估：\n", "Scores: 0.3487654320987654\n", "Mean: 0.3487654320987654\n", "Standard deviation: 0.0\n"]}], "source": ["adab_score = alg.score(X_test_prepared, y_test)\n", "print('Adaboost预测评估：')\n", "display_scores(adab_score)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 31, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AdaBoost分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.34      0.21      0.26       195\n", "           1       0.34      0.52      0.41       291\n", "           2       0.32      0.24      0.28       243\n", "           3       0.39      0.37      0.38       243\n", "\n", "    accuracy                           0.35       972\n", "   macro avg       0.35      0.33      0.33       972\n", "weighted avg       0.35      0.35      0.34       972\n", "\n"]}], "source": ["# adaboost分类报告\n", "adab_predicted = adab_reg.predict(X_test_prepared)\n", "adab_cp = sm.classification_report(y_test, adab_predicted)\n", "print('AdaBoost分类报告：\\n', adab_cp)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### 随机森林最佳参数法"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 32, "outputs": [], "source": ["from sklearn.model_selection import GridSearchCV\n", "forest_cv = RandomForestClassifier(class_weight='balanced')\n", "param = {\"n_estimators\":range(1,101,10)}\n", "gridsearch = GridSearchCV(forest_cv, cv=10, verbose=0, n_jobs=-1, param_grid=param, scoring=\"accuracy\")\n", "best_model = gridsearch.fit(X_train_prepared, y_train)\n"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 33, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RandomForestClassifier(class_weight='balanced', n_estimators=91)\n"]}], "source": ["print(best_model.best_estimator_)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 34, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机森林分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.38      0.19      0.26       195\n", "           1       0.33      0.54      0.41       291\n", "           2       0.28      0.16      0.21       243\n", "           3       0.42      0.42      0.42       243\n", "\n", "    accuracy                           0.35       972\n", "   macro avg       0.35      0.33      0.32       972\n", "weighted avg       0.35      0.35      0.33       972\n", "\n"]}], "source": ["# 随机森林分类报告\n", "best_predicted = best_model.predict(X_test_prepared)\n", "best_cp = sm.classification_report(y_test, best_predicted)\n", "print('随机森林分类报告：\\n', best_cp)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["## 获取测试数据，验证模型有效率"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### 获取强势行业"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 35, "outputs": [], "source": ["# from function_ai.Func_Base import collect_indusnum\n", "# indus_num = collect_indusnum(start_date='2022-10-11', end_date='2022-10-31', mode='result3')"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 36, "outputs": [], "source": ["# indus_threshold = 0.3\n", "# indus_num_adj = indus_num.query('ComRatio>@indus_threshold')\n", "# print(indus_num_adj['industry'].iloc[:10].tolist())"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### 获取指定日期的股票数据"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 37, "outputs": [], "source": ["# result = get_result_3(end_date=predict_date, sec_date=sec_date,\n", "#                               industry=indus_num_adj['industry'].iloc[:10].tolist())\n", "result = get_result_3(end_date=predict_date, sec_date=sec_date)\n", "# result = result[column_names]\n", "test_data_origin = get_train_label(result=result, label_style=label_style)\n", "lec = LabelEncoder()\n", "test_data_origin['Label'] = lec.fit_transform(test_data_origin['Label'])\n", "X_prof_prepared, y_prof = prepare_data(test_data_origin, mode_style='train')"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### 特征选择"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 38, "outputs": [], "source": ["# drop_list = list(set(X_train.columns) - set(X_train.columns[model.get_support()]))\n", "# drop_num = []\n", "# for name in drop_list:\n", "#     drop_num.append(X_prof.columns.get_loc(name))\n", "# X_prof_prepared = np.delete(X_prof_prepared, drop_num, 1)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["决策树验证结果"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 39, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["决策树验证评估：\n", "Scores: 0.26277372262773724\n", "Mean: 0.26277372262773724\n", "Standard deviation: 0.0\n"]}], "source": ["tree_score_prof = clf.score(X_prof_prepared, y_prof)\n", "print('决策树验证评估：')\n", "display_scores(tree_score_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 40, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["决策树分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.21      0.23      0.22       713\n", "           1       0.30      0.35      0.32      1068\n", "           2       0.26      0.24      0.25       890\n", "           3       0.25      0.21      0.23       891\n", "\n", "    accuracy                           0.26      3562\n", "   macro avg       0.26      0.26      0.26      3562\n", "weighted avg       0.26      0.26      0.26      3562\n", "\n"]}], "source": ["# 决策树分类报告\n", "tree_predicted_prof = tree_reg.predict(X_prof_prepared)\n", "tree_cp_prof = sm.classification_report(y_prof, tree_predicted_prof)\n", "print('决策树分类报告：\\n', tree_cp_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["随机森林验证结果"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 41, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机森林验证评估：\n", "Scores: 0.28860190903986527\n", "Mean: 0.28860190903986527\n", "Standard deviation: 0.0\n"]}], "source": ["forest_score_prof = flg.score(X_prof_prepared, y_prof)\n", "print('随机森林验证评估：')\n", "display_scores(forest_score_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 42, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机森林分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.30      0.16      0.21       713\n", "           1       0.29      0.63      0.40      1068\n", "           2       0.31      0.12      0.17       890\n", "           3       0.25      0.15      0.19       891\n", "\n", "    accuracy                           0.29      3562\n", "   macro avg       0.29      0.27      0.24      3562\n", "weighted avg       0.29      0.29      0.25      3562\n", "\n"]}], "source": ["# 随机森林分类报告\n", "forest_predicted_prof = forest_reg.predict(X_prof_prepared)\n", "forest_cp_prof = sm.classification_report(y_prof, forest_predicted_prof)\n", "print('随机森林分类报告：\\n', forest_cp_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["线性支持向量机验证结果"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 43, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["线性支持向量机验证评估：\n", "Scores: 0.27063447501403703\n", "Mean: 0.27063447501403703\n", "Standard deviation: 0.0\n"]}], "source": ["svm_score_prof = slg.score(X_prof_prepared, y_prof)\n", "print('线性支持向量机验证评估：')\n", "display_scores(svm_score_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 44, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["线性SVM分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.26      0.20      0.22       713\n", "           1       0.29      0.43      0.35      1068\n", "           2       0.25      0.15      0.19       890\n", "           3       0.25      0.26      0.25       891\n", "\n", "    accuracy                           0.27      3562\n", "   macro avg       0.26      0.26      0.25      3562\n", "weighted avg       0.27      0.27      0.26      3562\n", "\n"]}], "source": ["# SVM分类报告\n", "svm_predicted_prof = svm_reg.predict(X_prof_prepared)\n", "svm_cp_prof = sm.classification_report(y_prof, svm_predicted_prof)\n", "print('线性SVM分类报告：\\n', svm_cp_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### K近邻算法验证结果"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 45, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["K近邻算法验证评估：\n", "Scores: 0.2798989331836047\n", "Mean: 0.2798989331836047\n", "Standard deviation: 0.0\n"]}], "source": ["kneib_score_prof = klg.score(X_prof_prepared, y_prof)\n", "print('K近邻算法验证评估：')\n", "display_scores(kneib_score_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 46, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["K近邻算法分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.23      0.26      0.25       713\n", "           1       0.31      0.36      0.33      1068\n", "           2       0.26      0.23      0.24       890\n", "           3       0.30      0.25      0.27       891\n", "\n", "    accuracy                           0.28      3562\n", "   macro avg       0.28      0.27      0.27      3562\n", "weighted avg       0.28      0.28      0.28      3562\n", "\n"]}], "source": ["# K近邻分类报告\n", "kneib_predicted_prof = kneib_reg.predict(X_prof_prepared)\n", "kneib_cp_prof = sm.classification_report(y_prof, kneib_predicted_prof)\n", "print('K近邻算法分类报告：\\n', kneib_cp_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["#### Adaboost算法验证结果"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 47, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AdaBoost算法验证评估：\n", "Scores: 0.2956204379562044\n", "Mean: 0.2956204379562044\n", "Standard deviation: 0.0\n"]}], "source": ["adab_score_prof = alg.score(X_prof_prepared, y_prof)\n", "print('AdaBoost算法验证评估：')\n", "display_scores(adab_score_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 48, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AdaBoost算法分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.31      0.28      0.29       713\n", "           1       0.29      0.48      0.37      1068\n", "           2       0.31      0.17      0.22       890\n", "           3       0.28      0.21      0.24       891\n", "\n", "    accuracy                           0.30      3562\n", "   macro avg       0.30      0.29      0.28      3562\n", "weighted avg       0.30      0.30      0.28      3562\n", "\n"]}], "source": ["# K近邻分类报告\n", "adab_predicted_prof = adab_reg.predict(X_prof_prepared)\n", "adab_cp_prof = sm.classification_report(y_prof, adab_predicted_prof)\n", "print('AdaBoost算法分类报告：\\n', adab_cp_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 49, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AdaBoost算法分类报告：\n", "               precision    recall  f1-score   support\n", "\n", "           0       0.27      0.16      0.20       713\n", "           1       0.30      0.62      0.40      1068\n", "           2       0.31      0.12      0.17       890\n", "           3       0.26      0.17      0.21       891\n", "\n", "    accuracy                           0.29      3562\n", "   macro avg       0.29      0.27      0.25      3562\n", "weighted avg       0.29      0.29      0.26      3562\n", "\n"]}], "source": ["# best分类报告\n", "best_predicted_prof = best_model.predict(X_prof_prepared)\n", "best_cp_prof = sm.classification_report(y_prof, best_predicted_prof)\n", "print('AdaBoost算法分类报告：\\n', best_cp_prof)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 49, "outputs": [], "source": [], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}