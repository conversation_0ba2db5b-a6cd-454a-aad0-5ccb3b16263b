"""sklearn分类模型汇总
    对比不同模型的预测准确率，选择合适模型
"""

# coding=utf-8

import time
from sklearn import metrics
# import pickle as pickle
import pandas as pd
import numpy as np
from function_ai.stkpick_funcs_old.StkPick_Func_3 import get_result_3
from function_ai.Func_Base import get_trade_date
from sklearn.preprocessing import LabelEncoder


# Multinomial Naive Bayes Classifier
def naive_bayes_classifier(train_x, train_y):
    from sklearn.naive_bayes import MultinomialNB
    model = MultinomialNB(alpha=0.01)
    model.fit(train_x, train_y)
    return model


# KNN Classifier
def knn_classifier(train_x, train_y):
    from sklearn.neighbors import KNeighborsClassifier
    model = KNeighborsClassifier()
    model.fit(train_x, train_y)
    return model


# Logistic Regression Classifier
def logistic_regression_classifier(train_x, train_y):
    from sklearn.linear_model import LogisticRegression
    model = LogisticRegression(penalty='l2')
    model.fit(train_x, train_y)
    return model


# Random Forest Classifier
def random_forest_classifier(train_x, train_y):
    from sklearn.ensemble import RandomForestClassifier
    model = RandomForestClassifier(n_estimators=8)
    model.fit(train_x, train_y)
    return model


# Decision Tree Classifier
def decision_tree_classifier(train_x, train_y):
    from sklearn import tree
    model = tree.DecisionTreeClassifier()
    model.fit(train_x, train_y)
    return model


# GBDT(Gradient Boosting Decision Tree) Classifier
def gradient_boosting_classifier(train_x, train_y):
    from sklearn.ensemble import GradientBoostingClassifier
    model = GradientBoostingClassifier(n_estimators=200)
    model.fit(train_x, train_y)
    return model


# SVM Classifier
def svm_classifier(train_x, train_y):
    from sklearn.svm import SVC
    model = SVC(kernel='rbf', probability=True)
    model.fit(train_x, train_y)
    return model


# SVM Classifier using cross validation
def svm_cross_validation(train_x, train_y):
    from sklearn.model_selection import GridSearchCV
    from sklearn.svm import SVC
    model = SVC(kernel='rbf', probability=True)
    param_grid = {'C': [1e-3, 1e-2, 1e-1, 1, 10, 100, 1000], 'gamma': [0.001, 0.0001]}
    grid_search = GridSearchCV(model, param_grid, n_jobs=1, verbose=1)
    grid_search.fit(train_x, train_y)
    best_parameters = grid_search.best_estimator_.get_params()
    for para, val in list(best_parameters.items()):
        print(para, val)
    model = SVC(kernel='rbf', C=best_parameters['C'], gamma=best_parameters['gamma'], probability=True)
    model.fit(train_x, train_y)
    return model


def full_pipeline(x_train):
    from sklearn.pipeline import Pipeline, FeatureUnion
    from sklearn.impute import SimpleImputer
    from sklearn.preprocessing import StandardScaler
    from machine_learn.Func_MacinLn import DataFrameSelector, prepare_cat
    train_data, num_columns, cat_columns = prepare_cat(x_train)
    num_pipline = Pipeline([
        ('selector', DataFrameSelector(num_columns)),
        ('imputer', SimpleImputer(missing_values=np.nan, strategy='constant', fill_value=0)),
        ('stdscaler', StandardScaler()),
    ])
    cat_pipline = Pipeline([
        ('selector', DataFrameSelector(cat_columns))
    ])
    full_piplne = FeatureUnion(transformer_list=[
        ('num_pipline', num_pipline),
        ('cat_pipline', cat_pipline)
    ])
    return full_piplne.fit_transform(train_data)


if __name__ == '__main__':
    turn_dates = ['2022-09-26', '2022-10-11']
    predict_date, sec_date = '2022-11-04', '2022-10-31'
    label_style = 'Label'

    # 获取训练数据
    trading_dates = get_trade_date()
    origin_data = pd.DataFrame()
    end_dates = []
    for turn_date in turn_dates:
        end_date = trading_dates[trading_dates > turn_date][min(10, len(trading_dates[trading_dates > turn_date]) - 1)]
        end_dates.append(end_date)
        result = get_result_3(start_date=turn_date, end_date=end_date, sec_date=turn_date)
        origin_data = pd.concat([origin_data, result], ignore_index=True).reset_index(drop=True)
    print('结束日期：', end_dates)

    if isinstance(predict_date, str):
        test_data_origin = get_result_3(end_date=predict_date, sec_date=sec_date)
    elif isinstance(predict_date, list):
        test_data_origin = get_result_3(start_date=min(predict_date), end_date=max(predict_date), sec_date=sec_date)
    else:
        print('验证日期错误')
        test_data_origin = None
        exit()
    origin_data = pd.concat([origin_data, test_data_origin], ignore_index=True).reset_index(drop=True)

    origin_data.to_csv('../file_set/result('+predict_date+').csv', index=False, encoding='utf-8-sig')

    train_data = get_train_data(result=origin_data, label_style=label_style)
    le = LabelEncoder()
    train_data['Label'] = le.fit_transform(train_data['Label'])
    # train_set, test_set = data_split(train_data)
    X_train = train_data.query('Cal_Date!=@predict_date').drop('Label', axis=1)
    y_train = train_data.query('Cal_Date!=@predict_date')['Label'].values
    X_prof = train_data.query('Cal_Date==@predict_date').drop('Label', axis=1)
    y_prof = train_data.query('Cal_Date==@predict_date')['Label'].values
    print('获取数据：done')

    # 数据预处理
    X_train_prepared = full_pipeline(X_train)
    X_prof_prepared = full_pipeline(X_prof)
    print('数据预处理：done')

    # 分类模型处理
    test_classifiers = ['KNN', 'LR', 'RF', 'DT', 'SVM', 'SVMCV', 'GBDT']
    classifiers = {'KNN': knn_classifier,
                   'LR': logistic_regression_classifier,
                   'RF': random_forest_classifier,
                   'DT': decision_tree_classifier,
                   'SVM': svm_classifier,
                   'SVMCV': svm_cross_validation,
                   'GBDT': gradient_boosting_classifier
                   }

    for classifier in test_classifiers:
        print('******************* %s ********************' % classifier)
        start_time = time.time()
        model = classifiers[classifier](X_train_prepared, y_train)
        print('training took %fs!' % (time.time() - start_time))
        predict = model.predict(X_prof_prepared)
        # if model_save_file != None:
        #     model_save[classifier] = model
        # precision = metrics.precision_score(y_prof, predict)
        # recall = metrics.recall_score(y_prof, predict)
        # print('precision: %.2f%%, recall: %.2f%%' % (100 * precision, 100 * recall))
        # accuracy = metrics.accuracy_score(y_prof, predict)
        # print('accuracy: %.2f%%' % (100 * accuracy))
        classifier_report = metrics.classification_report(y_prof, predict)
        print('分类报告：\n', classifier_report)


