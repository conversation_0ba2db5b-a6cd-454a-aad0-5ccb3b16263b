{"cells": [{"cell_type": "markdown", "source": ["# 处理指标特征，判定日期和字符类特征如何处理"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 2, "outputs": [], "source": ["from function_ai.StkPick_Func_3 import *\n", "result = get_result_3(end_date='2022-11-30')"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 3, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['id' 'ts_code' 'name' 'industry' 'area' 'Target_Price' 'Target_Ratio'\n", " 'Press_Price' 'Stop_Lose_Price' 'Return_Risk_Ratio' 'TargetRatio_High'\n", " 'Period_TurnDate' 'Long_Trend' 'Long_Trend_StartDate' 'Period_Trend'\n", " 'Period_Trend_StartDate' 'PreTurn_Period_Lastdays'\n", " 'PreTurn_Period_SumRatio' 'PreTurn_Period_AvgRatio' 'PreTurn_Period_R2'\n", " 'Now_Period_Lastdays' 'Now_Period_SumRatio' 'Now_Period_AvgRatio'\n", " 'Now_Period_R2' 'LongTrend_PeriodNum_Rise' 'LongTrend_PeriodNum_Drop'\n", " 'Bottom_Date' 'Bottom_RiseRatio' 'Bottom_LastDays' 'PostBottom_MaxDrop'\n", " 'Long_PeakDate' 'Concave_Break_Date' 'Concave_Break_Days2Now'\n", " 'CoverDaysDiff_10' 'CoverRatioBand_10' 'CoverDaysDiff_20'\n", " 'CoverRatioBand_20' 'Period_Break_Ratio' 'Period_Break_Date'\n", " 'CoverDays_Bf_BreakDate' 'CoverDays_Aft_BreakDate' 'Peak_Pres_Num'\n", " 'Peak_Pres_Lastdays' 'Valley_Pres_Num' 'Valley_Pres_Lastdays'\n", " 'PreTurn_Period_GapRatio' 'PostTurn_Sec_DropNum'\n", " 'PostTurn_Sec_Max_SumRatio' 'PostTurn_Sec_Min_SumRatio'\n", " 'PostTurn_Sec_Min_ExtreRatio' 'Period_AftTurn_SumRatio'\n", " 'Period_AftTurn_Lastdays' 'PreTurn_LastSec_Lastdays'\n", " 'PreTurn_LastSec_AvgRatio' 'PreTurn_LastSec_SumRatio'\n", " 'PreTurn_LastSec_ExtreRatio' 'PreTurn_LastSec_AvgAmount'\n", " 'PreTurn_LastPeriod_SecNum' 'PostTurn_1stSec_Lastdays'\n", " 'PostTurn_1stSec_AvgRatio' 'PostTurn_1stSec_SumRatio'\n", " 'PostTurn_1stSec_ExtreRatio' 'PostTurn_1stSec_AvgAmount'\n", " 'PostTurn_Sec_RiseNum' 'Now_DayRatio' 'Now_MaxSum' 'Now_State'\n", " 'Now_Vol_Trend' 'Section_PeakDate' 'Section_StartDate'\n", " 'SectionStart_Position' 'Now_Sec_Lastdays' 'Now_Sec_SumRatio'\n", " 'Now_Sec_AvgRatio' 'Now_Sec_ExtreRatio' 'Now_Sec_AvgAmount'\n", " 'Bf_Peak_Sec_Lastdays' 'Bf_Peak_Sec_SumRatio' 'Bf_Peak_Sec_AvgRatio'\n", " 'Bf_Peak_Sec_ExtreRatio' 'Bf_Peak_Sec_AvgAmount' 'Aft_Peak_Sec_Lastdays'\n", " 'Aft_Peak_Sec_SumRatio' 'Aft_Peak_Sec_AvgRatio' 'Aft_Peak_Sec_ExtreRatio'\n", " 'Aft_Peak_Sec_AvgAmount' 'Bf_PreSec_Lastdays' 'Bf_PreSec_SumRatio'\n", " 'Bf_PreSec_AvgRatio' 'Bf_PreSec_ExtreRatio' 'Bf_PreSec_AvgAmount'\n", " 'Aft_PreSec_Lastdays' 'Aft_PreSec_SumRatio' 'Aft_PreSec_AvgRatio'\n", " 'Aft_PreSec_ExtreRatio' 'Aft_PreSec_AvgAmount' 'Recent5Days_MinRatio'\n", " 'Recent_MeanRatio' 'Recent_MaxRatio' 'Recent_Mean_ClsOpenRatio'\n", " 'Recent_PreChg_SumRatio' 'Recent_Ratio_Chg' 'Sec_IndexDiff_Signal'\n", " 'Sec_IndexDiff_Ratio' 'Sec_IndexDiff_StartDate' 'SSDate_LastDays_Diff'\n", " 'Section_Break_Ratio' 'Now_Sec_Proportion' 'Now_Sec_BelowMovAvg_Days'\n", " 'Now_Sec_BelowMovAvg_ContiDays' 'Now_Sec_BelowMovAvg_MaxDropRatio'\n", " 'Section_PostPeak_MaxDrop' 'Recent4P_MaxLastDays' 'PostTurn_Over7_Num'\n", " 'Now_Over7_ContiNum' 'PostTurn_Recent_Neg4_DropDate'\n", " 'PostTurn_Recent_Neg4_RecovDays' 'PostTurn_Recent_Neg4_DropRatio'\n", " 'PostPeak_Recent_Neg4_DropDate' 'PostPeak_Recent_Neg4_RecovDays'\n", " 'PostPeak_Recent_Neg4_DropRatio' 'PostSec_Over5_Num' 'PostTurn_Reg_R2'\n", " 'PostTurn_Reg_Sum2Std' 'PostTurn_Reg_Lastdays' 'PostTurn_Reg_EndDate'\n", " 'PostSecStart_Reg_R2' 'PostSecStart_Reg_Sum2Std'\n", " 'PrePeak_Sec_Drop_AvgRatio' 'PrePeak_Sec_Drop_Lastdays'\n", " 'PrePeak_Sec_Rise_AvgRatio' 'PrePeak_Sec_Rise_Lastdays'\n", " 'PrePeak_Sec_Rise_ExtreRatio' 'PrePeak_Over4_Date'\n", " 'Convex_Break_DropDate' 'GapRatio2Sec' 'PostPeak_Gap2Peak_Ratio'\n", " 'PostPeak_GapLastDays' 'PostPeak_GapCovDays' 'PostPeak_Gap2NowDays'\n", " 'Cal_Date']\n"]}], "source": ["print(result.columns.values)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 4, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ts_code', 'name', 'industry', 'area', 'Press_Price', 'Period_TurnDate', 'Long_Trend', 'Long_Trend_StartDate', 'Period_Trend', 'Period_Trend_StartDate', 'Bottom_Date', 'Long_PeakDate', 'Concave_Break_Date', 'Period_Break_Date', 'Now_State', 'Now_Vol_Trend', 'Section_PeakDate', 'Section_StartDate', 'Sec_IndexDiff_Signal', 'Sec_IndexDiff_StartDate', 'PostTurn_Recent_Neg4_DropDate', 'PostPeak_Recent_Neg4_DropDate', 'PostTurn_Reg_EndDate', 'PrePeak_Over4_Date', 'Convex_Break_DropDate', 'Cal_Date']\n"]}], "source": ["str_column = []\n", "for column in result.columns:\n", "    temp = result[column].dropna()\n", "    if isinstance(temp.iloc[0], str):\n", "        str_column.append(column)\n", "print(str_column)"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["## Rule1 转换天数\n", "Long_PeakDate 转换为距离当前日期的天数\n", "Period_Break_Date 转换为距离当前日期的天数\n", "Section_PeakDate 转换为距离当前日期的天数\n", "Section_IndexDiff_StartDate 转换为距离当前日期的天数\n", "PostTurn_Recent_Neg4_DropDate 转换为距离当前日期的天数\n", "PostPeak_Recent_Neg4_DropDate 转换为距离当前日期的天数\n", "PrePeak_Over4_Date 和 Convex_Break_DropDate 转换为两个日期间的间隔天数\n", "\n", "## Rule2 转换为指数排序序号\n", "Period_TurnDate 转换市场指数转折点的对应序号\n", "Section_StartDate 转换为市场指数转折点的对应序号\n", "如无对应日期，则设定为某一固定数值，如100\n", "\n", "## Rule3 转换为类别数字\n", "Period_Trend 转换为类别数字（不放弃None）\n", "Long_Trend 转换为类别数字（不放弃None)\n", "Bottom_Date 转换为是否与Period_TurnDate相同的二元判定特征\n", "Now_State 转换为类别数字（不放弃None）\n", "Now_Vol_Trend 转换为类别数字（不放弃None）\n", "Sec_IndexDiff_Signal 转换为类别数字（不放弃None）\n", "industry 转换为类别数字\n", "area 转换为类别数字\n", "\n", "**标签编码LabelEncoder或者独热编码OneHotEncoder**\n", "\n", "## Rule4 放弃\n", "放弃 'id' 'ts_code' 'name' 'Target_Price'\n", "     'Press_Price' 'Stop_Lose_Price'\n", "放弃 Long_Trend_StartDate, Period_Trend_StartDate\n", "放弃 Concave_Break_Date，因为已有Concave_Break_Days2Now\n", "放弃 PostTurn_Reg_EndDate\n", "放弃 Cal_Date\n"], "metadata": {"collapsed": false}}, {"cell_type": "markdown", "source": ["市场转折点日期：\n", "['2021-11-02', '2021-11-30', '2021-12-08', '2021-12-13',\n", "'2021-12-20', '2022-01-28', '2022-02-14', '2022-03-15',\n", "'2022-04-26', '2022-07-15', '2022-08-03', '2022-09-01',\n", "'2022-09-26', '2022-10-11', '2022-10-31', '2022-11-10',\n", "'2022-11-21', '2022-11-28']"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(sum(pd.isnull(result['Period_Trend'])))\n", "print(result['Period_Trend_StartDate'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['Bottom_Date'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(len(result.query('Bottom_Date==Period_TurnDate')))"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['Long_PeakDate'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['Concave_Break_Date'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['Period_Break_Date'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['Now_State'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['Now_Vol_Trend'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['Sec_IndexDiff_Signal'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['Sec_IndexDiff_StartDate'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['PostTurn_Recent_Neg4_DropDate'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['PostTurn_Reg_EndDate'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print(result['PrePeak_Over4_Date'].unique())"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["print('中文')"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}