{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["\"\"\"决策树分类器\"\"\""]}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# 算法实现\n", "from sklearn.model_selection import train_test_split\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.20)\n", "\n", "from sklearn.tree import DecisionTreeClassifier\n", "classifier = DecisionTreeClassifier()\n", "classifier.fit(X_train, y_train)\n", "\n", "y_pred = classifier.predict(X_test)\n", "\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "print(confusion_matrix(y_test, y_pred))\n", "print(classification_report(y_test, y_pred))"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# 模型评价\n", "# 混淆矩阵\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn import metrics\n", "\n", "cm_matrix = metrics.confusion_matrix(y_test, y_pred)\n", "cm_matrix\n", "\n", "class_names=[0,1] # name  of classes\n", "fig, ax = plt.subplots()\n", "tick_marks = np.arange(len(class_names))\n", "plt.xticks(tick_marks, class_names)\n", "plt.yticks(tick_marks, class_names)\n", "# create heatmap\n", "sns.heatmap(pd.DataFrame(cm_matrix), annot=True, cmap=\"YlGnBu\" ,fmt='g')\n", "ax.xaxis.set_label_position(\"top\")\n", "plt.tight_layout()\n", "plt.title('Confusion matrix', y=1.1)\n", "plt.ylabel('Actual label')\n", "plt.xlabel('Predicted label')\n", "plt.show()\n", "print(\"Accuracy:\",metrics.accuracy_score(y_test, y_pred))"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# 决策树用于特征创造\n", "# 将每日来盘价、收盘价、交易量等进行环比，得到每天是增是减的分类型变量。\n", "# 创造更多的时间\n", "dataset['Open_N'] = np.where(dataset['open'].shift(-1) > dataset['open'],'Up','Down')\n", "dataset['High_N'] = np.where(dataset['high'].shift(-1) > dataset['high'],'Up','Down')\n", "dataset['Low_N'] = np.where(dataset['low'].shift(-1) > dataset['low'],'Up','Down')\n", "dataset['Close_N'] = np.where(dataset['close'].shift(-1) > dataset['close'],'Up','Down')\n", "dataset['Volume_N'] = np.where(dataset['volume'].shift(-1) > dataset['volume'],'Positive','Negative')\n", "dataset.head()"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# 数据预处理\n", "X = dataset[['Open', 'Open_N', 'Volume_N']].values\n", "y = dataset['Up_Down']\n", "\n", "from sklearn import preprocessing\n", "le_Open = preprocessing.LabelEncoder()\n", "le_Open.fit(['Up','Down'])\n", "X[:,1] = le_Open.transform(X[:,1])\n", "\n", "le_Volume = preprocessing.LabelEncoder()\n", "le_Volume.fit(['Positive', 'Negative'])\n", "X[:,2] = le_Volume.transform(X[:,2])\n", "\n", "from sklearn.model_selection import train_test_split\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.20)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# 模型建立与预测\n", "from sklearn.tree import DecisionTreeClassifier\n", "classifier = DecisionTreeClassifier()\n", "classifier.fit(X_train, y_train)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# 实例化模型\n", "Up_Down_Tree = DecisionTreeClassifier(criterion=\"entropy\", max_depth = 4)\n", "Up_Down_Tree\n", "Up_Down_Tree.fit(X_train,y_train)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# 预测\n", "predTree = Up_Down_Tree.predict(X_test)\n", "print(predTree[0:5])\n", "print(y_test[0:5])"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# 决策树可视化\n", "from sklearn.tree import DecisionTreeClassifier\n", "from IPython.display import Image\n", "from sklearn import tree\n", "# pip install pydotplus\n", "import pydotplus\n", "# 创建决策树实例\n", "clf = DecisionTreeClassifier(random_state=0)\n", "X = dataset.['open', 'high', 'low', 'volume', 'Open_Close', 'High_Low',\n", "             'Increase_Decrease', 'Buy_Sell_on_Open', 'Returns']\n", "y = dataset['Buy_Sell']"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": ["# 训练模型\n", "model = clf.fit(X, y)\n", "# 创建 DOT data\n", "dot_data = tree.export_graphviz(clf, out_file=None,\n", "                                feature_names=X.columns,\n", "                                class_names=X.columns)\n", "# 绘图\n", "graph = pydotplus.graph_from_dot_data(dot_data)\n", "# 展现图形\n", "Image(graph.create_png())"], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}