# 使用iris数据测试随机森林模型
from sklearn.datasets import load_iris
from sklearn.ensemble import RandomForestClassifier
from machine_learn.Func_MacinLn import get_mldata
import pandas as pd
import numpy as np

# 设定随机seed
np.random.seed(0)

train_date = '2022-09-26'
df = get_mldata(train_date)


df['is_train'] = np.random.uniform(0, 1, len(df)) <= .75

train, test = df[df['is_train'] == True], df[df['is_train'] == False]
print('Number of Train:', len(train))
print('Number of Test:', len(test))

features = df.columns[6:140]
label_names = df['Label'].unique()

y = pd.factorize(train['Label'])[0]

clf = RandomForestClassifier(n_jobs=2, random_state=0)
clf.fit(train[features], y)

clf.predict(test[features])

print(clf.predict_proba(test[features])[1:20])

preds = label_names[clf.predict(test[features])]
print(preds[0:5])

test['Label'].head()
#
# pd.crosstab(test['species'], preds, rownames=['Actual Species'], colnames=['Predicted Species'])
#
# preds = iris.target_names[clf.predict([[5.0, 3.6, 1.4, 2.0], [5.0, 3.6, 1.4, 2.0]])]
# print(preds)
