import os
import pandas as pd
import numpy as np
import struct
import logging
from typing import List, Dict, Optional, Tuple
from enum import Enum
from datetime import datetime
from function_ai.Func_Base import get_stock_info
import config.config_<PERSON> as config_Ali

from sqlalchemy import create_engine
import time
from multiprocessing import Pool
from sqlalchemy.pool import QueuePool

class DailyDuplicateStrategy(Enum):
    """数据重复处理策略"""
    REPLACE = 'replace'  # 删除已有数据,重新录入
    SKIP = 'skip'       # 跳过重复数据

class DailyStockFileProcessor:
    """股票文件处理器,用于处理股票日线数据文件"""
    
    def __init__(self, base_path: str):
        """
        初始化处理器
        Args:
            base_path: 基础路径,即day_files目录的路径
        """
        self.base_path = '/Users/<USER>/PycharmProjects/Stock_Data_Files/vipdoc/'
        self.stock_file_map = {}  # 存储股票代码和文件路径的映射
        self.invalid_stocks = []  # 存储没有对应文件的股票代码
        
        # 交易所目录映射
        self.exchange_dirs = {
            'SH': 'sh/lday',
            'SZ': 'sz/lday', 
            'BJ': 'bj/lday'
        }
        
        # 添加需要处理的指数列表
        self.index_codes = ['000906.SH', '000001.SH', '399006.SZ']

    def get_file_path(self, ts_code: str) -> str:
        """
        根据ts_code生成日线文件路径
        """
        code, exchange = ts_code.split('.')
        exchange_dir = self.exchange_dirs.get(exchange)
        
        if not exchange_dir:
            return ""
            
        filename = f"{code}.day"
        return os.path.join(self.base_path, exchange_dir, filename)

    def prepare_stock_files(self) -> Tuple[Dict[str, str], List[str]]:
        """
        准备股票文件列表
        Returns:
            Tuple[Dict[str, str], List[str]]: 
            - 返回有效的股票代码和文件路径的映射字典
            - 返回无效的股票代码列表(找不到对应文件的股票)
        """
        stock_df = get_stock_info()
        valid_stocks = {}
        invalid_stocks = []

        # 处理普通股票
        for ts_code in stock_df['ts_code']:
            filepath = self.get_file_path(ts_code)
            if filepath and os.path.exists(filepath):
                valid_stocks[ts_code] = filepath
            else:
                invalid_stocks.append(ts_code)

        # 处理指数
        for index_code in self.index_codes:
            filepath = self.get_file_path(index_code)
            if filepath and os.path.exists(filepath):
                valid_stocks[index_code] = filepath
            else:
                invalid_stocks.append(index_code)

        self.stock_file_map = valid_stocks
        self.invalid_stocks = invalid_stocks
        
        return valid_stocks, invalid_stocks

    def process_stock_list(self) -> None:
        """处理股票列表并打印统计信息"""
        total_stocks = len(self.stock_file_map) + len(self.invalid_stocks)
        self._print_basic_stats(total_stocks)
        self._print_exchange_stats()
        self._print_invalid_stocks()
        
    def _print_basic_stats(self, total_stocks: int) -> None:
        """打印基本统计信息"""
        print(f"股票处理统计信息:")
        print(f"总股票数量: {total_stocks}")
        print(f"有效股票数量: {len(self.stock_file_map)}")
        print(f"无效股票数量: {len(self.invalid_stocks)}")
        
    def _print_exchange_stats(self) -> None:
        """打印交易所统计信息"""
        exchange_stats = {}
        for ts_code in self.stock_file_map:
            exchange = ts_code.split('.')[1]
            exchange_stats[exchange] = exchange_stats.get(exchange, 0) + 1
            
        print("\n各交易所有效股票统计:")
        for exchange, count in exchange_stats.items():
            print(f"{exchange}: {count}只")
            
    def _print_invalid_stocks(self) -> None:
        """打印无效股票信息"""
        if not self.invalid_stocks:
            return
            
        print("\n以下股票代码找不到对应的数据文件:")
        for stock in self.invalid_stocks[:10]:
            print(f"- {stock}")
        if len(self.invalid_stocks) > 10:
            print(f"... 还有 {len(self.invalid_stocks) - 10} 只股票")

class DailyDataProcessor:
    """日线数据处理器,用于处理和转换日线级别的股票数据"""
    
    def __init__(self, 
                 duplicate_strategy: DailyDuplicateStrategy = DailyDuplicateStrategy.SKIP,
                 batch_size: int = 1000):
        """
        初始化数据处理器
        Args:
            duplicate_strategy: 数据重复处理策略
            batch_size: 批处理大小
        """
        self.duplicate_strategy = duplicate_strategy
        self.batch_size = batch_size
        self.logger = self._setup_logger()
        self.db_pool = None  # 添加数据库连接池
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('DailyDataProcessor')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def process_daily_data(self, file_path: str, ts_code: str) -> Optional[pd.DataFrame]:
        """
        处理日线数据文件
        Args:
            file_path: 文件路径
            ts_code: 股票代码(带市场标识,如600744.SH)
        Returns:
            Optional[pd.DataFrame]: 处理后的数据框或None(如果处理失败)
        """
        try:
            df = read_daily_data(file_path)
            df = self._transform_data(df, ts_code)
            self.logger.info(f"成功读取 {ts_code} 的数据,共 {len(df)} 条记录")
            return df
        except Exception as e:
            self.logger.error(f"处理文件 {file_path} 时发生错误: {str(e)}")
            return None

    def _transform_data(self, df: pd.DataFrame, ts_code: str) -> pd.DataFrame:
        """
        转换数据格式以适应数据库要求
        Args:
            df: 原始数据框
            ts_code: 股票代码
        Returns:
            pd.DataFrame: 处理后的数据框
        """
        try:
            df['ts_code'] = ts_code
            df['trade_date'] = df['date'].dt.strftime('%Y-%m-%d')
            
            # 计算涨跌幅
            df['pre_close'] = df['close'].shift(1)
            df['pct_chg'] = (df['close'] - df['pre_close']) / df['pre_close'] * 100
            
            # 重命名并选择需要的列
            result_df = df[[
                'ts_code',
                'trade_date',
                'open',
                'high',
                'low',
                'close',
                'pre_close',
                'pct_chg',
                'volume',  # 将被重命名为vol
                'amount'
            ]]
            
            # 重命名volume列为vol
            result_df = result_df.rename(columns={'volume': 'vol'})
            
            # 将空值填充为None
            result_df = result_df.where(pd.notnull(result_df), None)
            
            self._validate_data(result_df)
            return result_df
            
        except Exception as e:
            self.logger.error(f"转换数据时发生错误: {str(e)}")
            raise

    def _validate_data(self, df: pd.DataFrame) -> None:
        """
        验证数据有效性
        Args:
            df: 待验证的数据框
        """
        self._check_price_validity(df)
        self._check_high_low_logic(df)
        self._check_volume_amount_match(df)
        
    def _check_price_validity(self, df: pd.DataFrame) -> None:
        """检查价格有效性"""
        price_columns = ['open', 'high', 'low', 'close']
        invalid_prices = df[df[price_columns].le(0).any(axis=1)]
        if not invalid_prices.empty:
            self.logger.warning(f"发现 {len(invalid_prices)} 条无效价格记录")
            
    def _check_high_low_logic(self, df: pd.DataFrame) -> None:
        """检查最高价最低价逻辑"""
        invalid_hl = df[df['high'] < df['low']]
        if not invalid_hl.empty:
            self.logger.warning(f"发现 {len(invalid_hl)} 条高价低于低价的记录")
            
    def _check_volume_amount_match(self, df: pd.DataFrame) -> None:
        """检查成交量和金额匹配"""
        invalid_va = df[(df['volume'] <= 0) & (df['amount'] > 0)]
        if not invalid_va.empty:
            self.logger.warning(f"发现 {len(invalid_va)} 条成交量与金额不匹配的记录")

    def get_data_batches(self, df: pd.DataFrame):
        """
        将数据分批
        Args:
            df: 待分批的数据框
        Yields:
            pd.DataFrame: 数据批次
        """
        for start_idx in range(0, len(df), self.batch_size):
            yield df.iloc[start_idx:start_idx + self.batch_size]

    def _init_db_pool(self):
        """初始化数据库连接池"""
        conf_a = config_Ali.configModel()
        self.engine = create_engine(
            'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + 
            conf_a.DC_DB_URL + ':' + str(conf_a.DC_DB_PORT) + '/stocksfit',
            poolclass=QueuePool,
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=1800
        )
        
    def _check_table_structure(self):
        """验证数据库表结构"""
        required_columns = {
            'ts_code': 'varchar',
            'trade_date': 'varchar',
            'open': 'float',
            'high': 'float',
            'low': 'float',
            'close': 'float',
            'vol': 'float',
            'amount': 'float'
        }
        
        with self.engine.connect() as conn:
            result = conn.execute("""
                SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'stocksfit' 
                AND TABLE_NAME = 'stock_data'
            """)
            
            existing_columns = {row[0]: row[1] for row in result}
            
            for col, dtype in required_columns.items():
                if col not in existing_columns:
                    raise ValueError(f"缺少必要的列: {col}")

    def _check_data_continuity(self, df: pd.DataFrame) -> None:
        """检查数据连续性"""
        df['date'] = pd.to_datetime(df['trade_date'])
        date_diff = df['date'].diff()
        
        # 检查交易日间隔
        invalid_intervals = date_diff[date_diff > pd.Timedelta(days=3)]  # 超过3天的间隔
        
        if not invalid_intervals.empty:
            self.logger.warning(f"发现 {len(invalid_intervals)} 处数据不连续点")

class DailyProcessMonitor:
    """处理进度监控器"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.total_stocks = 0
        self.processed_stocks = 0
        self.success_count = 0
        self.error_count = 0
        self.statistics = {}
        
    def update(self, success: bool, message: str):
        """更新处理状态"""
        self.processed_stocks += 1
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
            
    def get_progress(self) -> dict:
        """获取处理进度"""
        elapsed_time = (datetime.now() - self.start_time).total_seconds()
        return {
            '总股票数': self.total_stocks,
            '已处理': self.processed_stocks,
            '成功': self.success_count,
            '失败': self.error_count,
            '完成率': f"{(self.processed_stocks/self.total_stocks)*100:.2f}%",
            '耗时(秒)': f"{elapsed_time:.2f}"
        }

def read_daily_data(file_path):
    """
    读取通达信日线行情数据文件(.day)并转换为DataFrame
    数据格式：
    - 00-03字节：日期，整型，格式为YYYYMMDD
    - 04-07字节：开盘价*100，整型
    - 08-11字节：最高价*100，整型
    - 12-15字节：最低价*100，整型
    - 16-19字节：收盘价*100，整型
    - 20-23字节：成交额，浮点数
    - 24-27字节：成交量，整型
    - 28-31字节：保留，整型
    """
    dataSet = []
    code = os.path.basename(file_path).replace('.day', '')
    
    with open(file_path, 'rb') as f:
        buffer = f.read()
        row_size = 32
        
        for i in range(0, len(buffer), row_size):
            record = buffer[i:i+row_size]
            
            try:
                # 使用新的解析格式
                data = struct.unpack('IIIIIfII', record)
                
                # 解析日期 (YYYYMMDD格式)
                date_num = data[0]
                date_str = str(date_num)
                date_formatted = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                
                # 价格需要除以100
                open_price = data[1] / 100
                high_price = data[2] / 100
                low_price = data[3] / 100
                close_price = data[4] / 100
                amount = data[5]
                volume = data[6]
                
                # 处理异常数据
                if any(price > 9999999 for price in [open_price, high_price, low_price, close_price]):
                    print(f"跳过异常价格数据: {date_str}")
                    continue
                
                if volume < 0 or amount < 0:
                    print(f"跳过异常成交数据: {date_str}")
                    continue
                
                new_row = [
                    code,                   # 代码
                    date_formatted,         # 日期
                    round(open_price, 2),   # 开盘价
                    round(high_price, 2),   # 最高价
                    round(low_price, 2),    # 最低价
                    round(close_price, 2),  # 收盘价
                    volume,                 # 成交量
                    round(amount, 2)        # 成交额
                ]
                dataSet.append(new_row)
                
            except Exception as e:
                print(f"处理记录错误: {e}")
                continue
    
    # 创建DataFrame并设置数据类型
    df = pd.DataFrame(dataSet, columns=['code', 'date', 'open', 
                                      'high', 'low', 'close', 
                                      'volume', 'amount'])
    
    # 转换数据类型
    df['date'] = pd.to_datetime(df['date'])
    df[['open', 'high', 'low', 'close']] = df[['open', 'high', 'low', 'close']].astype(float)
    df[['volume', 'amount']] = df[['volume', 'amount']].astype(float)
    
    # 按日期排序
    df = df.sort_values('date')
    
    return df


def process_single_daily_stock(file_path: str, 
                        ts_code: str, 
                        duplicate_strategy: DailyDuplicateStrategy = DailyDuplicateStrategy.SKIP) -> Tuple[bool, str]:
    """
    处理单个股票的数据
    Args:
        file_path: 文件路径
        ts_code: 股票代码
        duplicate_strategy: 数据重复处理策略
    Returns:
        Tuple[bool, str]: (是否成功, 处理信息)
    """
    processor = DailyDataProcessor(duplicate_strategy=duplicate_strategy)
    
    try:
        df = processor.process_daily_data(file_path, ts_code)
        if df is None:
            return False, "数据读取失败"

        # 重命名列以匹配数据库表结构
        df = df.rename(columns={
            'volume': 'vol'  # 将volume改为vol以匹配数据库列名
        })
        
        # 添加pre_close列（如果需要）
        df['pre_close'] = None  # 或者根据实际需求计算前收盘价
        
        conf_a = config_Ali.configModel()
        engine_local = create_engine(
            'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + 
            conf_a.DC_DB_URL + ':' + str(conf_a.DC_DB_PORT) + '/stocksfit'
        )

        try:
            for batch_df in processor.get_data_batches(df):
                for _ in range(5):  # 重试5次
                    try:
                        pd.io.sql.to_sql(
                            batch_df, 
                            'stock_data', 
                            engine_local, 
                            index=False, 
                            schema='stocksfit',
                            if_exists='append', 
                            chunksize=5000
                        )
                        break
                    except Exception as e:
                        processor.logger.warning(f"写入数据库失败，正在重试: {str(e)}")
                        time.sleep(3)
        finally:
            engine_local.dispose()

        return True, f"成功处理并写入 {len(df)} 条记录"
    except Exception as e:
        return False, f"处理失败: {str(e)}"


def main():
    """主函数"""
    monitor = DailyProcessMonitor()
    processor = DailyStockFileProcessor(base_path="day_files")
    valid_stocks, invalid_stocks = processor.prepare_stock_files()
    
    monitor.total_stocks = len(valid_stocks)
    
    # 配置进程池
    cpu_count = os.cpu_count()
    process_count = max(1, min(cpu_count - 1, 4))  # 保留一个CPU核心
    
    with Pool(processes=process_count) as pool:
        # 使用imap_unordered实现实时进度更新
        for success, message in pool.imap_unordered(
            lambda x: process_single_daily_stock(*x),
            [(path, code) for code, path in valid_stocks.items()]
        ):
            monitor.update(success, message)
            if monitor.processed_stocks % 10 == 0:  # 每处理10只股票显示一次进度
                progress = monitor.get_progress()
                print("\r", end="")
                print(f"处理进度: {progress['完成率']} "
                      f"(成功: {progress['成功']}, "
                      f"失败: {progress['失败']})", end="")
    
    # 输出最终统计信息
    print("\n处理完成!")
    for key, value in monitor.get_progress().items():
        print(f"{key}: {value}")

class DailyConfig:
    """配置类"""
    BATCH_SIZE = 1000
    MAX_RETRIES = 5
    RETRY_DELAY = 3
    TRADING_HOURS = {
        'morning': ('09:30', '11:30'),
        'afternoon': ('13:00', '15:00')
    }

if __name__ == "__main__":
    main()
