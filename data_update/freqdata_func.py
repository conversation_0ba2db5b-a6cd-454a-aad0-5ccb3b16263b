"""股票分钟级数据下行，并存储至本地数据库"""

import pandas as pd
import tushare as ts
import config.config_local as config_local
from sqlalchemy import create_engine
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import pdb
import akshare as ak
from tqdm import tqdm

import datetime
from datetime import datetime as dt
import time
import numpy as np

from function_ai.Func_Base import get_stock_info, get_trade_date

ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')

# engine_ts = create_engine('mysql://user:mima@127.0.0.1:3306/demos?charset=utf8&use_unicode=1')

def get_freq_data(stk_code=None, start_date=None, end_date=None, trade_df=None, check_store=False):
    pro_api = ts.pro_api()

    # 获取股票代码
    if stk_code is None:
        stk_info = get_stock_info()
        stk_codes = stk_info['ts_code'].tolist()
        # for _ in range(3):
        #     try:
        #         new_data = pro_api.query('stock_basic', exchange='', list_status='L',
        #                                  fields='ts_code,name,industry,list_date')
        #     except:
        #         time.sleep(1)
        #     else:
        #         break
        # stk_codes = new_data['ts_code'].unique()
    elif isinstance(stk_code, str):
        stk_codes = [stk_code]
    else:
        stk_codes = stk_code

    # 检查已存储交易日期
    store_date = []
    if check_store:
        store_date = read_freq_data(start_date, end_date, stk_codes, check_storedate=True)
        if len(stk_codes) > 0:
            store_date = store_date['trade_date'].values.tolist()

    # 获取交易日历
    if trade_df is None:
        trade_dates = get_trade_date()
        trade_df = trade_dates[(trade_df >= start_date) & (trade_df <= end_date)]

        # trade_df = pro_api.trade_cal(exchange='SSE',
        #                              start_date=pd.to_datetime(
        #                                  start_date).strftime('%Y%m%d'),
        #                              end_date=pd.to_datetime(
        #                                  end_date).strftime('%Y%m%d'),
        #                              is_open=1)
        # trade_df = trade_df.rename(columns={'cal_date': 'trade_date'})
        # trade_df['trade_date'] = trade_df['trade_date'].apply(lambda fn: pd.to_datetime(fn).strftime('%Y-%m-%d'))
        # trade_df = trade_df['trade_date'].to_list()
    else:
        trade_df = trade_df[(trade_df >= start_date) & (trade_df <= end_date)]

    if len(store_date) > 0:
        getdata_date = sorted(list(set(trade_df) & set(store_date)))
    else:
        getdata_date = sorted(list(set(trade_df)))

    # 如果没有需要的数据，直接返回空
    if not getdata_date:
        print("No available trade dates within the specified range.")
        return pd.DataFrame()

    # 设置start_time和end_time
    start_time = datetime.datetime.combine(
        pd.to_datetime(min(getdata_date)), datetime.time(9, 25, 0)).strftime('%Y-%m-%d %H:%M:%S')
    end_time = datetime.datetime.combine(
        pd.to_datetime(max(getdata_date)), datetime.time(15, 15, 0)).strftime('%Y-%m-%d %H:%M:%S')

    # 计算数据量并设置参数
    limit = 7000
    max_offset = 99000
    data_length = len(getdata_date) * 241
    
    # 处理每只股票的数据
    stk_freqdata = pd.DataFrame()
    for ts_code in tqdm(stk_codes):
        daily_data = pd.DataFrame()
        offset = 0
        
        # 当数据长度超过max_offset时,按时间分段获取
        if data_length > max_offset:
            # 计算需要分成几段
            date_segments = [getdata_date[i:i+int(max_offset/241)] 
                           for i in range(0, len(getdata_date), int(max_offset/241))]
            
            # 按时间段分别获取数据
            for dates in date_segments:
                seg_start = datetime.datetime.combine(
                    pd.to_datetime(min(dates)), datetime.time(9, 25, 0)).strftime('%Y-%m-%d %H:%M:%S')
                seg_end = datetime.datetime.combine(
                    pd.to_datetime(max(dates)), datetime.time(15, 15, 0)).strftime('%Y-%m-%d %H:%M:%S')
                
                while True:
                    min_data = pd.DataFrame()
                    for _ in range(3):
                        try:
                            min_data = ts.pro_bar(**{"ts_code": ts_code,
                                                 "freq": "1min", 
                                                 "start_date": seg_start,
                                                 "end_date": seg_end,
                                                 "limit": limit,
                                                 "offset": offset})
                            time.sleep(random.uniform(1, 3))
                            if min_data is not None and len(min_data) > 0:
                                min_data['trade_date'] = pd.to_datetime(min_data['trade_time']).dt.strftime('%Y-%m-%d')
                                min_data = min_data[min_data['trade_date'].isin(dates)]
                                daily_data = pd.concat([daily_data, min_data], ignore_index=True)
                        except Exception as e:
                            print(f"Error occurred: {e}")
                            time.sleep(2)
                        else:
                            break
                            
                    if len(min_data) > 0:
                        offset += limit
                        if offset >= max_offset:
                            offset = 0
                            break
                    else:
                        offset = 0
                        break
        else:
            # 数据量较小时直接获取
            while True:
                min_data = pd.DataFrame()
                for _ in range(3):
                    try:
                        min_data = ts.pro_bar(**{"ts_code": ts_code,
                                             "freq": "1min",
                                             "start_date": start_time,
                                             "end_date": end_time,
                                             "limit": limit,
                                             "offset": offset})
                        time.sleep(random.uniform(1, 3))
                        if min_data is not None and len(min_data) > 0:
                            min_data['trade_date'] = pd.to_datetime(min_data['trade_time']).dt.strftime('%Y-%m-%d')
                            min_data = min_data[min_data['trade_date'].isin(getdata_date)]
                            daily_data = pd.concat([daily_data, min_data], ignore_index=True)
                    except Exception as e:
                        print(f"Error occurred: {e}")
                        time.sleep(2)
                    else:
                        break
                        
                if len(min_data) > 0:
                    offset += limit
                else:
                    break

        stk_freqdata = pd.concat([stk_freqdata, daily_data], ignore_index=True)
        if len(stk_freqdata) > 62000:
            write_freq_data(stk_freqdata)
            time.sleep(1)
            stk_freqdata = pd.DataFrame()
    if len(stk_freqdata) > 0:
        write_freq_data(stk_freqdata)
    return stk_freqdata


def write_freq_data(df):
    conf_a = config_local.configModel()
    engine_local = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')
    column_list = ['ts_code', 'trade_time', 'close', 'open', 'high', 'low', 'vol', 'amount', 'trade_date', 'pre_close']
    df = df[column_list]
    for _ in range(5):
        try:
            pd.io.sql.to_sql(df, 'stock_freqdata', engine_local, index=False, schema='stocksfit',
                             if_exists='append', chunksize=5000)
        except Exception as e:
            print(e)
            time.sleep(3)
        else:
            break
    engine_local.dispose()
    # print('finish write_freq_data')
    return


def read_freq_data(start_date, end_date, stk_code=None, check_storedate=False):
    conf_a = config_local.configModel()
    engine_local = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')
    if stk_code is not None and isinstance(stk_code, str):
        stk_code = [stk_code]
    if check_storedate:
        if stk_code is None:
            read_sql = f"""select distinct trade_date from stock_freqdata 
                           where trade_date between '{start_date}' and '{end_date}' order by trade_date desc"""
        else:
            stk_sql = ','.join(["'%s'" % item for item in stk_code])
            read_sql = f"""select distinct trade_date from stock_freqdata 
                           where trade_date between '{start_date}' and '{end_date}' and ts_code in ({stk_sql}) 
                           order by trade_date desc"""
    else:
        if stk_code is None:
            read_sql = f"""select * from stock_freqdata 
                           where trade_date between '{start_date}' and '{end_date}'"""
        else:
            stk_sql = ','.join(["'%s'" % item for item in stk_code])
            read_sql = f"""select * from stock_freqdata where trade_date between '{start_date}' 
                           and '{end_date}' and ts_code in ({stk_sql})"""
    try:
        freq_data = pd.read_sql_query(read_sql, engine_local)
    except Exception as e:
        print(e)
        return pd.DataFrame()
    engine_local.dispose()
    return freq_data


def get_freqdata_maxdate():
    """获取freqdata数据库中最大日期"""
    conf_a = config_local.configModel()
    engine_local = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')
    read_sql = f"""select max(trade_date) as trade_date from stocksfit.stock_freqdata 
                   where ts_code='600000.SH'"""
    freq_date = pd.read_sql_query(read_sql, engine_local)
    engine_local.dispose()
    return freq_date['trade_date'].iloc[-1]


# 按年度切分日期区间
def split_date_by_year(start_date, end_date):
    start_date = pd.to_datetime(start_date)
    end_date = pd.to_datetime(end_date)

    date_ranges = []
    current_date = start_date
    if current_date < end_date:
        while current_date < end_date:
            # 计算下一年的开始日期（每年的1月1日）
            next_year = (current_date + pd.DateOffset(years=1)).replace(month=1, day=1)
            # 计算该年份的结束日期（该年的12月31日或end_date）
            end_of_year = min(next_year - pd.DateOffset(days=1), end_date)
            date_ranges.append((current_date.strftime('%Y-%m-%d'), end_of_year.strftime('%Y-%m-%d')))
            current_date = next_year
    else:
        date_ranges.append((current_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))
    return date_ranges


# 获取并写入分批数据
def fetch_and_store_data(start_date, end_date):
    # 获取交易日数据
    from function_ai.Func_Base import get_trade_date
    trade_df = get_trade_date()

    # 切分日期区间
    date_ranges = split_date_by_year(start_date, end_date)

    # 初始化空数据框
    all_data = pd.DataFrame()

    # 按每月的区间获取数据
    for start, end in date_ranges:
        print(f"Fetching data from {start} to {end}")
        # 获取该区间的数据
        df = get_freq_data(start_date=start, end_date=end, trade_df=trade_df)

        # 写入或保存每次获取的数据
        if not df.empty:
            # write_freq_data(df)
            print('finish store_data: ', start, ' - ', end)
    return


def del_freq_data():
    """删除指定数据"""
    import config.config_local as config
    conf_local = config.configModel()
    engine_local = create_engine(
        'mysql+pymysql://' + conf_local.DC_DB_USER + ':' + conf_local.DC_DB_PASS + '@' + conf_local.DC_DB_URL + ':' +
        str(conf_local.DC_DB_PORT) + '/stocksfit')
    # 创建Session类
    Session = sessionmaker(bind=engine_local)

    def delete_in_batches(condition):
        session = Session()
        rows_affected = 1  # 初始化受影响的行数

        try:
            while rows_affected > 0:
                result = session.execute(f"DELETE FROM stock_freqdata WHERE {condition} LIMIT 1000")
                rows_affected = result.rowcount  # 更新受影响的行数
                session.commit()  # 提交事务

        except Exception as e:
            print(f"Error occurred: {e}")
            session.rollback()  # 出现错误时回滚事务

        finally:
            session.close()  # 关闭会话

    # 调用函数
    delete_in_batches('trade_date>"2024-09-20"')
    engine_local.dispose()
    return

if __name__ == '__main__':
    start_date, end_date = '2024-08-26', '2024-09-09'
    fetch_and_store_data(start_date, end_date)
