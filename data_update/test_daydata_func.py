#!/usr/bin/env python3
"""
测试日线数据处理功能
"""

import sys
import os

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_dir)

from daydata_func_tdx import (
    DayStockFileProcessor, 
    DayDataProcessor, 
    DayDuplicateStrategy,
    read_day_data
)

def test_file_processor():
    """测试文件处理器"""
    print("=== 测试文件处理器 ===")
    
    processor = DayStockFileProcessor("")
    valid_stocks, invalid_stocks = processor.prepare_stock_files()
    
    print(f"有效股票数量: {len(valid_stocks)}")
    print(f"无效股票数量: {len(invalid_stocks)}")
    
    if valid_stocks:
        # 显示前5个有效股票
        print("\n前5个有效股票:")
        for i, (ts_code, file_path) in enumerate(list(valid_stocks.items())[:5]):
            print(f"  {ts_code}: {file_path}")
            
    if invalid_stocks:
        # 显示前5个无效股票
        print(f"\n前5个无效股票:")
        for stock in invalid_stocks[:5]:
            print(f"  {stock}")
    
    processor.process_stock_list()
    return valid_stocks

def test_data_reader(file_path):
    """测试数据读取功能"""
    print(f"\n=== 测试数据读取: {file_path} ===")
    
    try:
        df = read_day_data(file_path)
        if df.empty:
            print("读取的数据为空")
            return None
            
        print(f"读取到 {len(df)} 条记录")
        print(f"日期范围: {df['date'].min()} 到 {df['date'].max()}")
        print(f"数据列: {list(df.columns)}")
        
        # 显示前几条记录
        print("\n前5条记录:")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"读取数据时发生错误: {str(e)}")
        return None

def test_data_processor(ts_code, file_path):
    """测试数据处理器"""
    print(f"\n=== 测试数据处理器: {ts_code} ===")
    
    try:
        processor = DayDataProcessor(
            duplicate_strategy=DayDuplicateStrategy.SKIP,
            batch_size=100
        )
        
        df = processor.process_day_data(file_path, ts_code)
        if df is None:
            print("处理后的数据为空")
            return
            
        print(f"处理后数据: {len(df)} 条记录")
        print(f"数据列: {list(df.columns)}")
        
        # 显示前几条记录
        print("\n前3条处理后的记录:")
        print(df.head(3))
        
        # 测试保存到数据库（注释掉以避免实际写入）
        # success = processor.save_to_database(df)
        # print(f"保存到数据库: {'成功' if success else '失败'}")
        
        processor.close()
        
    except Exception as e:
        print(f"处理数据时发生错误: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试日线数据处理功能...\n")
    
    # 测试文件处理器
    valid_stocks = test_file_processor()
    
    if not valid_stocks:
        print("没有找到有效的股票文件，测试结束")
        return
    
    # 选择第一个有效股票进行测试
    test_ts_code, test_file_path = list(valid_stocks.items())[0]
    
    # 测试数据读取
    df = test_data_reader(test_file_path)
    
    if df is not None:
        # 测试数据处理器
        test_data_processor(test_ts_code, test_file_path)
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
