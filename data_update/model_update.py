"""指标计算及模型数据更新"""
from datetime import datetime
import pdb
import pandas as pd

from function_ai.Func_Base import get_trade_date
from function_ai.StkPick_Func_V7 import get_result_3
from function_ai.StkQuota_Func_V7 import stksfit_result_3, update_result_column
from machine_learn.Func_MacinLn import stkpick_model_process
from data_update.freqdata_func import fetch_and_store_data
from machine_learn.clf_study.algo_RF import train_date


def model_predict_update(end_date=None, model_date=None, cal_mode=None, model_train=True, freq=False):
    """系列动作：计算end_date指标，训练模型， 预测end_date结果并存储数据库"""
    update_starttime = datetime.now()
    trade_dates = get_trade_date()
    train_end = pd.to_datetime(trade_dates[trade_dates<end_date][-20])
    if train_end.day > 15:
        train_end = train_end.replace(day=15)
    else:
        if train_end.month > 1:
            train_end = train_end.replace(month=train_end.month-1, day=30)
        else:
            train_end = train_end.replace(year=train_end.year-1, month=12, day=31)
    model_train_end = trade_dates[trade_dates<=train_end.strftime('%Y-%m-%d')][-1]
    model_train_start = trade_dates[trade_dates<model_train_end][-90]
    if cal_mode is not None:
        result = get_result_3(end_date=end_date)
        print('开始计算指标!')
        if len(result) == 0:
            result, _ = stksfit_result_3(end_date=end_date, mode='pick')
        else:
            _ = update_result_column(end_date=end_date, mode=cal_mode)
        print(end_date, '指标计算完毕！')
    model_select = 'XGB'
    label_style = 'Label'
    if model_train:
        print('模型训练起始日期：', model_train_start, '\n模型训练结束日期：', model_train_end)
        print('开始训练模型!')
        # date_list = trade_dates[(trade_dates >= model_train_start) & (trade_dates <= model_train_end)].tolist()
        _, _, model_date = stkpick_model_process(start_date=model_train_start, end_date=model_train_end,
                                                 model_select=model_select, model_style='train', label_style=label_style,
                                                 model_date=model_train_end, date_style=True)
        print('model训练结束！')
    _, result_predict, _ = stkpick_model_process(end_date=end_date, model_select=model_select,
                                                 model_style='predict', store_mode=True,
                                                 model_date=model_date, label_style=label_style,
                                                 date_style=True)
    print(end_date, '预测结果已存储')
    update_endtime = datetime.now()
    print('模型更新耗时： ', update_endtime - update_starttime)
    if freq:
        print('开始更新股票高频数据')
        fetch_and_store_data(start_date=end_date, end_date=end_date)
        print(end_date, '高频数据已更新')
    return result_predict


if __name__ == '__main__':
    from machine_learn.Func_MacinLn import get_model_traindate
    train_dates = get_model_traindate(start_date='2024-02-05', end_date='2024-09-30')
    count = 0
    for date in train_dates:
        result_old = get_result_3(end_date=date, stk_list='600000.SH')
        if result_old is not None and len(result_old) > 0 and pd.notnull(
                result_old['Post_SecPeak_Und4Count'].iloc[-1]) and pd.isnull(
                result_old['UpConsecutive_PGVRollAvg_DiffRatio'].iloc[-1]):
            resultB = update_result_column(end_date=date, mode='Second_Half')
        elif result_old is not None and len(result_old) > 0 and pd.isnull(
                result_old['Post_SecPeak_Und4Count'].iloc[-1]) and pd.notnull(
                result_old['UpConsecutive_PGVRollAvg_DiffRatio'].iloc[-1]):
            resultB = update_result_column(end_date=date, mode='First_Half')
        elif result_old is None or len(result_old) == 0:
            resultB = stksfit_result_3(end_date=date, mode='pick')
        else:
            continue
        count += 1
        print('finish date:', date)
        print('finish Num:', count, '/', len(train_dates))