{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c64419b0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["600000.SH 计算gap_data数据指标报错，错误为： cannot do slice indexing on Index with these indexers [nan] of type float\n", "600000.SH 计算gap_data数据指标报错\n", "错误发生在第 1149 行\n", "错误详情: cannot do slice indexing on Index with these indexers [nan] of type float\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/DailyGap_Func.py:1148: FutureWarning: The behavior of Series.idxmax with all-NA values, or any-NA and skipna=False, is deprecated. In a future version this will raise ValueError\n", "  'PostSecPeak_MaxTO_Eff_Date': postsecpeak_eff.idxmax(),\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/DailyGap_Func.py:1149: FutureWarning: The behavior of Series.idxmax with all-NA values, or any-NA and skipna=False, is deprecated. In a future version this will raise ValueError\n", "  'PostSecPeak_MaxTO_Eff2Now_LastDays': len(postsecpeak_data.loc[postsecpeak_eff.idxmax():]) - 1,\n"]}], "source": ["from function_ai.StkPick_Func_V7 import get_result_3\n", "from function_ai.StkQuota_Func_V7 import set_resultindexs\n", "from function_ai.DailyGap_Func import cal_gapindex\n", "import pandas as pd\n", "end_date = '2025-08-18'\n", "stk_code = '600000.SH'\n", "result = get_result_3(end_date=end_date, mode='First_Half')\n", "from function_ai.Func_Base import get_trade_date\n", "trade_df = get_trade_date()\n", "stk_temp = result.query('ts_code==@stk_code')\n", "\n", "common_column_names, gapvalue_column_names = set_resultindexs()\n", "# column_names = common_column_names + gapvalue_column_names\n", "Result_GapValue = pd.concat([stk_temp[['ts_code']], pd.DataFrame(columns=gapvalue_column_names)], sort=False, axis=1)\n", "\n", "result_gapvalue = pd.DataFrame()\n", "result_break = cal_gapindex(Result_GapValue.iloc[-1].copy(), stk_temp.iloc[-1].copy(), end_date=end_date, trade_df=trade_df, data_source='local')"]}, {"cell_type": "code", "execution_count": 1, "id": "934feada", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     ts_code      com_name           com_id chairman manager secretary  \\\n", "0  600104.SH  上海汽车集团股份有限公司  310000000000840      王晓秋     贾健旭        陈逊   \n", "\n", "    reg_capital setup_date province city  \\\n", "0  1.157530e+06   19840416       上海  上海市   \n", "\n", "                                        introduction            website  \\\n", "0  公司属于汽车制造行业，目前正努力把握产业发展趋势，加快创新转型，从传统的制造型企业，向为消费...  www.saicmotor.com   \n", "\n", "                   email         office  \\\n", "0  <EMAIL>  上海市静安区威海路489号   \n", "\n", "                                      business_scope  employees  \\\n", "0  汽车,摩托车,拖拉机等各种机动车整车,机械设备,总成及零部件的生产,销售,国内贸易(除专项规...     187739   \n", "\n", "                                       main_business exchange  \n", "0  主要产品:轻型客车以及轿车,重型车,拖拉机变速器,汽车悬架弹簧,汽车散热器,粉末冶金制品,汽...      SSE  \n"]}], "source": ["# 导入tushare\n", "import tushare as ts\n", "# 初始化pro接口\n", "pro = ts.pro_api('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')\n", "\n", "# 拉取数据\n", "df = pro.stock_company(**{\n", "    \"ts_code\": \"600104.SH\",\n", "    \"exchange\": \"\",\n", "    \"status\": \"\",\n", "    \"limit\": \"\",\n", "    \"offset\": \"\"\n", "}, fields=[\n", "    \"ts_code\",\n", "    \"com_name\",\n", "    \"com_id\",\n", "    \"chairman\",\n", "    \"manager\",\n", "    \"secretary\",\n", "    \"reg_capital\",\n", "    \"setup_date\",\n", "    \"province\",\n", "    \"city\",\n", "    \"introduction\",\n", "    \"website\",\n", "    \"email\",\n", "    \"office\",\n", "    \"business_scope\",\n", "    \"employees\",\n", "    \"main_business\",\n", "    \"exchange\"\n", "])\n", "print(df)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "089a8fea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["adj_factor_20250822.csv saved!\n", "adj_factor_20250825.csv saved!\n", "adj_factor_20250826.csv saved!\n", "adj_factor_20250827.csv saved!\n"]}], "source": ["import tushare as ts\n", "from pathlib import Path\n", "newdates = ['20250822', '20250825', '20250826', '20250827']\n", "indusfile = Path('/Users/<USER>/PycharmProjects/AI_Stock/AiCode测试代码/')\n", "ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')\n", "pro = ts.pro_api()\n", "for newdate in newdates:\n", "    adj_factor = pro.adj_factor(ts_code='', trade_date=newdate)\n", "    adj_filename = indusfile / f'adj_factor_{newdate}.csv'\n", "    adj_factor.to_csv(adj_filename)\n", "    print(f'adj_factor_{newdate}.csv saved!')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}