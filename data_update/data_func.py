# 股票数据更新函数汇总
# data_update函数 选择交易所比对存储数据日期和获取数据日期，并进行更新
import pdb

import pandas as pd
import tushare as ts
import config.config_<PERSON> as config_<PERSON>
from sqlalchemy import create_engine
import os

import akshare as ak
from tqdm import tqdm

import datetime
from datetime import datetime as dt
import time
import numpy as np

from function_ai.Func_Base import get_stock_info
from data_update.freqdata_func import get_freqdata_maxdate, fetch_and_store_data
from urllib.parse import quote_plus as urlquote
from pathlib import Path
from dateutil.relativedelta import relativedelta


def data_update(start_date=None, end_date=None, freq_update=False):
    """函数data_update  数据更新。\n
       获取存储的数据，与最新日期对比，如数据未更新，则更新数据，输出并存储。\n
       path参数为数据存储文件;  exchange参数指定交易所：SSE上海交易所；SZSE深圳交易所
       """
    # 读取数据库设置
    result_signal = True
    conf_a = config_Ali.configModel()
    engine_ali = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')

    # 获取当天日期
    newtoday = datetime.date.today()
    formated_today = newtoday.strftime('%Y%m%d')

    # 从tushare获取交易日历
    ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')
    pro = ts.pro_api()
    end_date, trade_dates = refresh_tradedates(end_date, formated_today, pro, start_date)

    # 从tushare读取股票列表信息
    stk_info, result_signal = info_update(pro, result_signal=result_signal)
    if result_signal:
        store_indus_attrib(engine_ali, stk_info)
    else:
        print('股票行业属性未更新')

    # 读取数据库股票最新日期数据
    result_signal = store_stk_dailyquote(engine_ali, pro, result_signal, trade_dates)

    # 更新指数行情数据
    result_signal = store_index_dailyquote(engine_ali, pro, result_signal, trade_dates)

    engine_ali.dispose()

    # 更新股票日内高频数据
    if freq_update:
        freq_maxdate = get_freqdata_maxdate()
        if len(trade_dates[trade_dates > freq_maxdate]) > 0:
            freq_startdate = trade_dates[trade_dates > freq_maxdate][0].strftime('%Y%m%d')
            update_lenth = len(trade_dates[trade_dates > freq_maxdate])
            update_signal = input('待更新交易日数量为：' + str(update_lenth) + '确认是否更新:(y/n)')
            if update_signal.lower() == 'y':
                _ = fetch_and_store_data(start_date=freq_startdate, end_date=trade_dates[-1].strftime('%Y%m%d'),)
                print('完成股票高频行情数据更新')
            else:
                print('暂不更新高频数据')
        else:
            print('股票高频数据已是最新数据，无需更新')

    # if updatemode == 'All' or updatemode == 'of':
    #     # 更新公募基金信息
    #     # result_signal = of_info_update(pro=pro, result_signal=result_signal)
    #
    #     # 更新公募基金净值数据
    #     if result_signal:
    #         sql_of_nav = """select max(nav_date) nav_date from stocksfit.of_nv_data"""
    #         max_date = pd.read_sql_query(sql=sql_of_nav, con=engine_ali)
    #         max_date = pd.to_datetime(max_date['nav_date'].iloc[-1], format='%Y%m%d')
    #         nav_dates = trade_dates[(trade_dates > max_date) & (trade_dates < formated_today)]
    #         new_dates = [dat.strftime('%Y%m%d') for dat in nav_dates]
    #         if len(new_dates) > 0:
    #             nv_datas, result_signal = of_nav_update(pro=pro, newdates=new_dates, result_signal=result_signal)
    #             if result_signal:
    #                 for _ in range(3):
    #                     try:
    #                         pd.io.sql.to_sql(nv_datas, 'of_nv_data', engine_ali,
    #                                          index=False, schema='stocksfit', if_exists='append')
    #                     except:
    #                         time.sleep(3)
    #                     else:
    #                         break
    #                 print('已完成公募基金净值更新，更新日期：', '/'.join(new_dates))
    #         else:
    #             print('公募基金净值已是最新数据，无需更新!')
    #         result_signal = True
    #
    #     # 更新公募基金持仓数据
    #     if result_signal:
    #         sql_port_date = """select max(ann_date) ann_date from stocksfit.of_portfolio """
    #         max_date = pd.read_sql_query(sql=sql_port_date, con=engine_ali)
    #         max_date = pd.to_datetime(max_date['ann_date'].iloc[-1], format='%Y%m%d')
    #         new_dates = trade_dates[trade_dates > max_date].strftime('%Y%m%d')
    #         if len(new_dates) > 0:
    #             result_signal = of_portfolio_update(pro=pro, new_dates=new_dates, result_signal=result_signal)
    #             if result_signal:
    #                 print('已完成公募基金持仓数据更新,日期：', '/'.join(new_dates))
    #             else:
    #                 print('公募基金持仓数据指定区间无更新!')
    #         else:
    #             print('公募基金持仓数据已是最新数据，无需更新!')
    #
    #     # 统计持仓行业占比数据并写表
    #     if result_signal:
    #         portfolio_indus = collect_portfolio_indus(mode='update')
    return result_signal


def store_index_dailyquote(engine_ali, pro, result_signal, trade_dates):
    sql = """select
                        ts_code, MAX(trade_date) as trade_date
                     from
                        stocksfit.index_data group by ts_code"""
    sql_idx_date = pd.read_sql_query(sql, con=engine_ali)
    sql_idx_date['trade_date'] = pd.to_datetime(sql_idx_date['trade_date'].values, format='%Y-%m-%d')
    # idx_min_date = pd.to_datetime(sql_idx_date['trade_date'].min(), format='%Y-%m-%d')
    new_dates = trade_dates[trade_dates > sql_idx_date['trade_date'].min()]
    if len(new_dates) > 0 and result_signal:
        idx_data, result_signal = index_update(pro, trade_date=trade_dates,
                                               sql_date=sql_idx_date, result_signal=result_signal)
        if result_signal and len(idx_data) > 0:
            for _ in range(3):
                try:
                    pd.io.sql.to_sql(idx_data, 'index_data', engine_ali,
                                     index=False, schema='stocksfit', if_exists='append')
                except:
                    time.sleep(3)
                else:
                    break
            new_dates = new_dates.strftime('%Y-%m-%d')
            print('完成指数行情数据更新', '/'.join(new_dates))
        else:
            print('未能完成指数行情数据更新!')
    else:
        print('指数行情已是最新日期，无需更新！')
    return result_signal


def store_stk_dailyquote(engine_ali, pro, result_signal, trade_dates):
    sql = """select
                    MAX(trade_date) as trade_date
                 from 
                    stocksfit.stock_data """
    sql_stk_date = pd.read_sql_query(sql, con=engine_ali)
    stk_max_date = pd.to_datetime(sql_stk_date['trade_date'].iloc[-1], format='%Y-%m-%d')
    new_dates = trade_dates[trade_dates > stk_max_date]
    if len(new_dates) > 0 and result_signal:
        stk_data, result_signal = stk_update(pro, newdates=new_dates, result_signal=result_signal)
        if len(stk_data) > 0 and result_signal:
            for _ in range(3):
                try:
                    pd.io.sql.to_sql(
                        stk_data, 'stock_data', engine_ali, index=False, schema='stocksfit', if_exists='append')
                except:
                    time.sleep(3)
                else:
                    break
            new_dates = new_dates.strftime('%Y-%m-%d')
            print('完成股票数据更新', '/'.join(new_dates))
        else:
            print('未能完成股票数据更新！')
    else:
        print('股票行情数据已是最新日期，无需更新！')
    return result_signal


def store_indus_attrib(engine_ali, stk_info):
    # os.chdir('/Users/<USER>/PycharmProjects/AI_Stock')
    indusfile = Path('/Users/<USER>/PycharmProjects/AI_Stock/data_update/全部A股行业归属.xlsx')
    indus_attrib = pd.read_excel(indusfile)
    indus_attrib.columns = ['ts_code', 'name', 'industry_one', 'industry_two']
    # indus_one = ['机械设备', '医药生物']
    indus_attrib['industry'] = indus_attrib['industry_one'].copy()
    # indus_attrib['industry'] = indus_attrib.apply(
    #     lambda x: x['industry_two'] if x['industry_one'] in indus_one else x['industry_one'], axis=1)
    s = indus_attrib.set_index('ts_code')['industry']
    stk_info['industry'] = stk_info['ts_code'].map(s).fillna(stk_info['industry']).astype(str)
    pd.io.sql.to_sql(stk_info, 'stockinfo', engine_ali, index=False, schema='stocksfit', if_exists='replace')
    print('股票行业属性已更新！')


def refresh_tradedates(end_date, formated_today, pro, start_date):
    if start_date is None or end_date is None:
        ts_dates = pro.trade_cal(exchange='SSE', start_date='20120101', end_date=formated_today,
                                 fields='exchange,cal_date', is_open='1')
        trade_dates = pd.to_datetime(ts_dates['cal_date'].values, format='%Y%m%d')
        trade_dates = trade_dates.sort_values()
        if trade_dates[-1] == pd.to_datetime(formated_today, format='%Y%m%d') and time.localtime()[3] < 16:
            trade_dates = trade_dates[:-1]
        # start_date = trade_dates.min().strftime(format='%Y%m%d')
        end_date = trade_dates.max().strftime(format='%Y%m%d')
    else:
        start_date = pd.to_datetime(start_date, format='%Y-%m-%d').strftime(format='%Y%m%d')
        end_date = pd.to_datetime(end_date, format='%Y-%m-%d').strftime(format='%Y%m%d')
        ts_dates = pro.trade_cal(exchange='SSE', start_date=start_date,
                                 end_date=end_date,
                                 fields='exchange,cal_date', is_open='1')
        trade_dates = pd.to_datetime(ts_dates['cal_date'].values, format='%Y%m%d')
        trade_dates = trade_dates.sort_values()
    return end_date, trade_dates


def info_update(pro, result_signal=None):
    """更新股票基本信息"""
    exchanges = ('SSE', 'SZSE')
    stk_info = pd.DataFrame()
    for exchange in exchanges:
        ts_list = []
        while len(ts_list) == 0:
            ts_list = pro.query('stock_basic', exchange=exchange, list_status='L',
                                fields='ts_code,name,area,industry,list_date')
        if len(ts_list) > 0:
            stk_info = pd.concat([stk_info, ts_list], ignore_index=True)
        else:
            print('未获取到股票基本信息数据！')
            result_signal = False
    return stk_info, result_signal


def stk_update(pro, newdates=None, result_signal=None):
    updatedata = pd.DataFrame()
    for newdate in newdates:
        new_data = pd.DataFrame()
        adj_factor = pd.DataFrame()
        new_data_basic = pd.DataFrame()
        newdate = newdate.strftime('%Y%m%d')
        try:
            new_data = pro.daily(trade_date=newdate)
        except:
            time.sleep(1)
        try:
            adj_factor = pro.adj_factor(ts_code='', trade_date=newdate)
        except:
            time.sleep(1)
        try:
            new_data_basic = pro.daily_basic(trade_date=newdate)
        except:
            time.sleep(1)
        if len(new_data) > 0 and len(adj_factor) > 0 and len(new_data_basic) > 0:
            new_data = pd.merge(new_data, adj_factor[['ts_code', 'adj_factor']], how='inner', on='ts_code')
            new_data_basic = new_data_basic.drop(columns=['trade_date', 'close'])
            new_data = pd.merge(new_data, new_data_basic, how='inner', on='ts_code')
            updatedata = pd.concat([updatedata, new_data], ignore_index=True)
        else:
            result_signal = False
    if result_signal:
        updatedata['trade_date'] = pd.to_datetime(updatedata['trade_date'].values, format='%Y%m%d')
        updatedata['trade_date'] = updatedata['trade_date'].apply(lambda fn: fn.strftime('%Y-%m-%d'))
        updatedata = updatedata.drop(columns=['change'])
    if len(updatedata) < len(new_data) or len(updatedata) == 0:
        print('获取股票数量不完整！')
        result_signal = False
    return updatedata, result_signal


def index_update(pro, trade_date=None, sql_date=None, result_signal=True, mode='update'):
    """更新市场指数数据"""
    if trade_date is None or sql_date is None:
        print('获取指数数据缺失输入参数！')
        return np.nan, False
    index_data = pd.DataFrame()
    trade_date = trade_date.sort_values()
    # index_names = ['000001.SH', '399006.SZ', '399300.SZ']
    if mode == 'update':
        for index in sql_date.index:
            if trade_date[-1] > sql_date.loc[index, 'trade_date']:
                new_date = trade_date[trade_date > sql_date.loc[index, 'trade_date']].strftime('%Y%m%d')[0]
                end_date = trade_date.strftime('%Y%m%d')[-1]
                index_d = pd.DataFrame()
                while len(index_d) == 0:
                    index_d = pro.index_daily(ts_code=sql_date.loc[index, 'ts_code'],
                                              start_date=new_date,
                                              end_date=end_date)
                if len(index_d) > 0:
                    index_data = pd.concat([index_data, index_d], ignore_index=True)
                else:
                    print('未获取到指数数据', sql_date.loc[index, 'ts_code'])
                    result_signal = False
            else:
                result_signal = False
        if len(index_data) > 0 and result_signal:
            index_data['trade_date'] = pd.to_datetime(index_data['trade_date'].values, format='%Y%m%d')
            index_data['trade_date'] = index_data['trade_date'].apply(lambda fn: fn.strftime('%Y-%m-%d'))
            index_data = index_data.sort_values(['ts_code', 'trade_date'], ascending=[True, True])
            index_data = index_data.drop(columns=['change'])
    else:
        print('获取全部指数数据')
        index_names = ['000001.SH', '399006.SZ', '399300.SZ']
        for ts_code in index_names:
            new_date = trade_date.strftime('%Y%m%d')[0]
            end_date = trade_date.strftime('%Y%m%d')[-1]
            index_d = pd.DataFrame()
            while len(index_d) == 0:
                index_d = pro.index_daily(ts_code=ts_code,
                                          start_date=new_date,
                                          end_date=end_date)
            if len(index_d) > 0:
                index_data = pd.concat([index_data, index_d], ignore_index=True)
            else:
                print('未获取到指数数据', ts_code)
                result_signal = False
        if len(index_data) > 0 and result_signal:
            index_data['trade_date'] = pd.to_datetime(index_data['trade_date'].values, format='%Y%m%d')
            index_data['trade_date'] = index_data['trade_date'].apply(lambda fn: fn.strftime('%Y-%m-%d'))
            index_data = index_data.sort_values(['ts_code', 'trade_date'], ascending=[True, True])
            index_data = index_data.drop(columns=['change'])
    return index_data, result_signal


def cctvnews_update():
    """更新CCTV新闻数据"""
    result_signal = True
    conf_a = config_Ali.configModel()
    engine_ali = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')

    yesterday = (datetime.datetime.today().date() - datetime.timedelta(1)).strftime('%Y%m%d')
    date_period = pd.period_range(start='20060701', end=yesterday, freq='D').to_timestamp()

    sql = """select
                MAX(date) as date
             from 
                stocksfit.cctvnewsdata """
    sql_date = pd.read_sql_query(sql, con=engine_ali)
    store_maxdate = pd.to_datetime(sql_date['date'].iloc[-1], format='%Y%m%d')
    date_period = date_period[date_period > store_maxdate]

    if len(date_period) > 0:
        allnews = pd.DataFrame()
        for date in tqdm(date_period):
            news = ak.news_cctv(date=date.strftime('%Y%m%d'))
            allnews = pd.concat([allnews, news], ignore_index=True)
        if len(allnews) > 0:
            pd.io.sql.to_sql(allnews, 'cctvnewsdata', engine_ali, index=False, schema='stocksfit', if_exists='append')
            print('完成CCTV新闻稿数据更新', '/'.join(date_period.strftime('%Y-%m-%d')))
        else:
            result_signal = False
            print('未能获取到CCTV新闻稿数据！')
    else:
        print('新闻数据已是最新日期，无需更新！')
    return result_signal


def of_info_update(pro=None, result_signal=True):
    """更新公募基金基本信息"""
    conf_a = config_Ali.configModel()
    engine_ali = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')
    if pro is None:
        ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')
        pro = ts.pro_api()
    sql_info = """select ts_code from stocksfit.of_info"""
    mf_lists = pd.read_sql_query(sql=sql_info, con=engine_ali)['ts_code'].unique()
    limit = 10000
    of_info = pd.DataFrame()
    for num in range(10):
        offset = num * limit
        df = pro.fund_basic(**{
            "ts_code": "",
            "market": "",
            "update_flag": "",
            "offset": offset,
            "limit": limit,
            "status": "",
            "name": ""
        }, fields=[
            "ts_code",
            "name",
            "management",
            "custodian",
            "fund_type",
            "found_date",
            "due_date",
            "list_date",
            "issue_date",
            "delist_date",
            "issue_amount",
            "m_fee",
            "c_fee",
            "duration_year",
            "p_value",
            "min_amount",
            "exp_return",
            "benchmark",
            "status",
            "invest_type",
            "type",
            "trustee",
            "purc_startdate",
            "redm_startdate",
            "market"
        ])
        if len(df) >= 0:
            of_info = pd.concat([of_info, df], ignore_index=True)
        else:
            break

    of_info = of_info.query('ts_code not in @mf_lists')
    if len(of_info) > 0:
        for _ in range(3):
            try:
                pd.io.sql.to_sql(of_info, 'of_info', engine_ali, index=False, schema='stocksfit', if_exists='append')
            except:
                time.sleep(3)
            else:
                break
    engine_ali.dispose()
    if len(of_info) > 0:
        print('公募基金基本信息有更新，已获取!')
        result_signal = True
    else:
        print('公募基金基本信息无更新!')
        result_signal = True
    return result_signal


def of_nav_update(pro=None, newdates=None, result_signal=True):
    """更新公募基金净值数据"""
    if pro is None:
        ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')
        pro = ts.pro_api()
    conf_a = config_Ali.configModel()
    engine_ali = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')
    if newdates is None:
        today = datetime.date.today().strftime('%Y%m%d')
        start_date = input('请输入起始日期（格式：20201231）:')
        ts_dates = pro.trade_cal(**{
            "exchange": "",
            "cal_date": "",
            "start_date": start_date,
            "end_date": today,
            "is_open": 1,
            "limit": "",
            "offset": ""
        }, fields=[
            "exchange",
            "cal_date"
        ])
        newdates = ts_dates['cal_date'].values.tolist()
    k = 0
    newdates.sort()
    nv_datas = pd.DataFrame()
    for date in newdates:
        print(date)
        limit = 10000
        daily_nv = pd.DataFrame()
        for num in tqdm(range(1000)):
            offset = num * limit
            for _ in range(3):
                try:
                    df = pro.fund_nav(**{
                        "ts_code": "",
                        "nav_date": date,
                        "offset": offset,
                        "limit": limit,
                        "market": "",
                        "start_date": "",
                        "end_date": ""
                    }, fields=[
                        "ts_code",
                        "ann_date",
                        "nav_date",
                        "unit_nav",
                        "accum_nav",
                        "accum_div",
                        "net_asset",
                        "total_netasset",
                        "adj_nav",
                        "update_flag"
                    ])
                except:
                    time.sleep(3)
                else:
                    break
            if len(df) > 0:
                daily_nv = pd.concat([daily_nv, df], ignore_index=True)
            else:
                break
        nv_datas = pd.concat([nv_datas, daily_nv], ignore_index=True)
        k += 1
        if k == 30:
            pd.io.sql.to_sql(nv_datas, 'of_nv_data', engine_ali, index=False, schema='stocksfit', if_exists='append')
            nv_datas = pd.DataFrame()
            k = 0
    if len(nv_datas) > 0:
        pd.io.sql.to_sql(nv_datas, 'of_nv_data', engine_ali, index=False, schema='stocksfit', if_exists='append')
    engine_ali.dispose()
    if len(nv_datas) > 0:
        result_signal = True
    else:
        result_signal = False
    return nv_datas, result_signal
    

def get_quarter_date():
    """获取前一季末日期"""
    today = dt.now()
    quarter = (today.month-1)/3
    if quarter == 1:
        return dt(today.year, 3, 31)
    elif quarter == 2:
        return dt(today.year, 6, 30)
    elif quarter == 3:
        return dt(today.year, 9, 30)
    else:
        return dt(today.year-1, 12, 31)
    

def of_portfolio_update(pro=None, mode='update', new_dates=None, result_signal=True):
    """"更新公募基金持仓组合数据"""
    if pro is None:
        ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')
        pro = ts.pro_api()
    conf_a = config_Ali.configModel()
    engine_ali = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')
    today = dt.now().strftime('%Y%m%d')
    if mode == 'update':
        for end_date in tqdm(new_dates):
            print(end_date)
            fund_portfolio_data = pd.DataFrame()
            limit = 15000
            for num in tqdm(range(1000)):
                offset = num * limit
                for _ in range(3):
                    try:
                        fund_portfolio = pro.fund_portfolio(**{
                                        "ts_code": "",
                                        "ann_date": end_date,
                                        "start_date": "",
                                        "end_date": "",
                                        "period": "",
                                        "limit": limit,
                                        "offset": offset
                                    }, fields=[
                                        "ts_code",
                                        "ann_date",
                                        "end_date",
                                        "symbol",
                                        "mkv",
                                        "amount",
                                        "stk_mkv_ratio",
                                        "stk_float_ratio"
                                    ])
                    except:
                        time.sleep(3)
                    else:
                        break
                if len(fund_portfolio) > 0:
                    fund_portfolio_data = pd.concat([fund_portfolio_data, fund_portfolio], ignore_index=True)
                else:
                    break
            if len(fund_portfolio_data) > 0:
                try:
                    pd.io.sql.to_sql(fund_portfolio_data, 'of_portfolio', engine_ali,
                                     index=False, schema='stocksfit', if_exists='append')
                except:
                    time.sleep(3)
    elif mode == 'all':
        start_date = '20210630'
        Q_dates = pd.period_range(start=start_date, end=today, freq='Q').strftime('%Y%m%d')
        for end_date in Q_dates:
            fund_portfolio_data = pd.DataFrame()
            print(end_date)
            limit = 15000
            for num in tqdm(range(1000)):
                offset = num * limit
                for _ in range(3):
                    try:
                        fund_portfolio = pro.fund_portfolio(**{
                                        "ts_code": "",
                                        "ann_date": "",
                                        "start_date": "",
                                        "end_date": "",
                                        "period": end_date,
                                        "limit": limit,
                                        "offset": offset
                                    }, fields=[
                                        "ts_code",
                                        "ann_date",
                                        "end_date",
                                        "symbol",
                                        "mkv",
                                        "amount",
                                        "stk_mkv_ratio",
                                        "stk_float_ratio"
                                    ])
                    except:
                        time.sleep(3)
                    else:
                        break
                if len(fund_portfolio) > 0:
                    fund_portfolio_data = pd.concat([fund_portfolio_data, fund_portfolio], ignore_index=True)
                    time.sleep(3)
                else:
                    break
            if len(fund_portfolio_data) > 0:
                try:
                    pd.io.sql.to_sql(fund_portfolio_data, 'of_portfolio', engine_ali,
                                     index=False, schema='stocksfit', if_exists='append')
                except:
                    time.sleep(3)
    engine_ali.dispose()
    return result_signal


def collect_portfolio_indus(start_date=None, end_date=None, mode='all'):
    """设定重仓品种的申万行业，并统计行业持仓比重并写表"""
    config_ali = config_Ali.configModel()
    engine_ali = create_engine(
        'mysql+pymysql://' + config_ali.DC_DB_USER + ':' + urlquote(config_ali.DC_DB_PASS) + '@'
        + config_ali.DC_DB_URL + ':' + str(
            config_ali.DC_DB_PORT) + '/stocksfit')
    today = dt.now().strftime('%Y%m%d')
    indus_attrib = get_stock_info()[['ts_code', 'industry']]
    portfolio_indus = None
    if start_date is None and mode == 'update':
        if end_date is None:
            end_date = datetime.datetime.now()
        new_date = (pd.to_datetime(end_date) - relativedelta(months=3)).strftime('%Y%m%d')
        sql_update = """SELECT p.ts_code, MAX(p.end_date) AS end_date
                        FROM of_portfolio AS p
                        LEFT JOIN of_portfolio_indus AS pi ON p.ts_code = pi.fund_id AND p.end_date = pi.report_date
                        WHERE pi.fund_id IS NULL AND pi.report_date IS NULL and p.end_date >=%(new_date)s
                        GROUP BY p.ts_code, p.end_date ;"""
        fund_ids = pd.read_sql_query(sql=sql_update, con=engine_ali, params={'new_date': new_date})
        if len(fund_ids) > 0:
            fund_ids = tuple(fund_ids['ts_code'].unique())
            sql_port_list = """select ts_code as fund_id, ann_date, end_date as report_date, 
                                symbol, mkv+0 MarketValue, stk_mkv_ratio+0 RatioInNV
                                from stocksfit.of_portfolio 
                                where ts_code in %(fund_ids)s and end_date>=%(new_date)s """
            portfolio_list = pd.read_sql_query(sql=sql_port_list, con=engine_ali,
                                               params={'fund_ids': fund_ids, 'new_date': new_date})
            if len(portfolio_list) > 0:
                portfolio_list['industry'] = np.nan
                indus_attrib.columns = ['symbol', 'industry']
                s = indus_attrib.set_index('symbol')['industry']
                portfolio_list['industry'] = portfolio_list['symbol'].map(s).fillna(portfolio_list['industry']).astype(
                    str)
                portfolio_list = portfolio_list.query('industry!="nan"')
                portfolio_indus = portfolio_list.groupby(
                    by=['fund_id', 'report_date', 'industry'], as_index=False)[['MarketValue', 'RatioInNV']].sum()
                pd.io.sql.to_sql(portfolio_indus, 'of_portfolio_indus', engine_ali,
                                 index=False, schema='stocksfit', if_exists='append')
                print('完成持仓行业数据更新！')
        else:
            print('持仓行业数据已是最新，无需更新！')
            return
    elif mode == 'all':
        start_all = '20120101'
        year_range = pd.period_range(start_all, today, freq='Y')
        for year in year_range:
            print(year)
            start_date = year.start_time.strftime('%Y%m%d')
            end_date = year.end_time.strftime('%Y%m%d')
            sql_port_list = """select ts_code as fund_id, ann_date, end_date as report_date, 
                            symbol, mkv+0 MarketValue, stk_mkv_ratio+0 RatioInNV
                            from stocksfit.of_portfolio 
                            where end_date>=%(start_date)s and end_date<=%(end_date)s"""
            for _ in range(3):
                try:
                    portfolio_list = pd.read_sql_query(sql=sql_port_list, con=engine_ali,
                                                       params={'start_date': start_date, 'end_date': end_date})
                except:
                    time.sleep(3)
                else:
                    break
            if len(portfolio_list) > 0:
                portfolio_list['industry'] = np.nan
                indus_attrib.columns = ['symbol', 'industry']
                s = indus_attrib.set_index('symbol')['industry']
                portfolio_list['industry'] = portfolio_list['symbol'].map(s).fillna(portfolio_list['industry']).astype(str)
                portfolio_list = portfolio_list.query('industry!="nan"')
                portfolio_indus = portfolio_list.groupby(
                    by=['fund_id', 'report_date', 'industry'], as_index=False)[['MarketValue', 'RatioInNV']].sum()
                pd.io.sql.to_sql(portfolio_indus, 'of_portfolio_indus', engine_ali,
                                 index=False, schema='stocksfit', if_exists='append')
    return portfolio_indus


if __name__ == '__main__':
    data_update(end_date='2022-09-30')
    # """ 使用 result=stock_data.pivot('ts_code','trade_date') 即可将CSV文件中提取的数据按照股票代码和交易日期reindex
    #     result.loc['600000.SH']['open'] 即可获得股票的开盘价数据序列，以pd.Series格式呈现"""

    # start_all, end_all = '20120331', '20230410'
    # year_range = pd.period_range(start_all, end_all, freq='Y')
    # for year in year_range:
    #     start_date = year.start_time.strftime('%Y%m%d')
    #     end_date = year.end_time.strftime('%Y%m%d')
    #     port_indus = collect_portfolio_indus(start_date=start_date, end_date=end_date)
    #     print('finish:', year)
