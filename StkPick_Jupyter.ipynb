{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["starttime:  2025-09-02 14:09:20.519557\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["603633.SH\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [00:02<00:00,  2.93s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["计算Gap指标：\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [00:00<00:00, 17.82it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["endtime:  2025-09-02 14:12:19.642062\n", "耗时：  0:02:59.122505\n", "finish date:  2025-08-21\n"]}], "source": ["from function_ai.StkQuota_Func_V7 import stksfit_result_3\n", "stk_temp, _  = stksfit_result_3(end_date='2025-08-21', cal_mode='ADJ', stk_code='603633.SH',freqdata_source='local', storemode=False)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Turn2NowSec_PRA_UpBand", "rawType": "object", "type": "unknown"}, {"name": "Turn2NowSec_PRA_LowBand", "rawType": "object", "type": "unknown"}, {"name": "Recent3Day_PGV_MaxRollAvg", "rawType": "object", "type": "unknown"}, {"name": "Now2UpBand_PRA_Ratio", "rawType": "object", "type": "unknown"}, {"name": "SecConcave_StartDate", "rawType": "object", "type": "string"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}], "ref": "26221dcb-4c2b-497f-86fd-92a7f1b48088", "rows": [["0", "0.164", "0.081", "0.147", "0.896", "2024-09-27", "2025-08-14"]], "shape": {"columns": 6, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Turn2NowSec_PRA_UpBand</th>\n", "      <th>Turn2NowSec_PRA_LowBand</th>\n", "      <th>Recent3Day_PGV_MaxRollAvg</th>\n", "      <th>Now2UpBand_PRA_Ratio</th>\n", "      <th>SecConcave_StartDate</th>\n", "      <th>Now_SecDate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.164</td>\n", "      <td>0.081</td>\n", "      <td>0.147</td>\n", "      <td>0.896</td>\n", "      <td>2024-09-27</td>\n", "      <td>2025-08-14</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Turn2NowSec_PRA_UpBand Turn2NowSec_PRA_LowBand Recent3Day_PGV_MaxRollAvg  \\\n", "0                  0.164                   0.081                     0.147   \n", "\n", "  Now2UpBand_PRA_Ratio SecConcave_StartDate Now_SecDate  \n", "0                0.896           2024-09-27  2025-08-14  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['Turn2NowSec_PRA_UpBand', 'Turn2NowSec_PRA_LowBand', 'Recent3Day_PGV_MaxRollAvg', 'Now2UpBand_PRA_Ratio', 'SecConcave_StartDate', 'Now_SecDate']]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Recent_MinPRA_Date", "rawType": "object", "type": "string"}, {"name": "Recent_MinPRA_DownCoverDays", "rawType": "object", "type": "unknown"}, {"name": "Recent_MinPRA2Now_Days", "rawType": "object", "type": "unknown"}, {"name": "Recent_MinPRA_DownCover2PostSec_DaysProp", "rawType": "object", "type": "unknown"}, {"name": "PostSecStart_LastDays", "rawType": "object", "type": "unknown"}, {"name": "Section_StartDate", "rawType": "object", "type": "string"}, {"name": "Recent_MinPRADate2Now_RiseAvg", "rawType": "object", "type": "unknown"}, {"name": "Recent_MinPRADate2Now_RiseRatio", "rawType": "object", "type": "unknown"}, {"name": "PostSecStart_Mean2MovAvg_MaxRate", "rawType": "object", "type": "unknown"}, {"name": "PostSecStart_Mean2MovAvg_MinRate", "rawType": "object", "type": "unknown"}, {"name": "PostSecStart_BelowPre1Day_Prop", "rawType": "object", "type": "unknown"}, {"name": "PostSecStart_MedianPeakGap", "rawType": "object", "type": "unknown"}, {"name": "PostSecStart_MaxPeakGap", "rawType": "object", "type": "unknown"}], "ref": "9efc0291-8a6f-483d-b49a-320d06ad5939", "rows": [["0", "2025-08-28", "12", "1", "0.333", "36", "2025-07-10", "0", "0.0", "6.067", "-2.895", "0.216", "1.294", "3.948"]], "shape": {"columns": 13, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Recent_MinPRA_Date</th>\n", "      <th>Recent_MinPRA_DownCoverDays</th>\n", "      <th>Recent_MinPRA2Now_Days</th>\n", "      <th>Recent_MinPRA_DownCover2PostSec_DaysProp</th>\n", "      <th>PostSecStart_LastDays</th>\n", "      <th>Section_StartDate</th>\n", "      <th>Recent_MinPRADate2Now_RiseAvg</th>\n", "      <th>Recent_MinPRADate2Now_RiseRatio</th>\n", "      <th>PostSecStart_Mean2MovAvg_MaxRate</th>\n", "      <th>PostSecStart_Mean2MovAvg_MinRate</th>\n", "      <th>PostSecStart_BelowPre1Day_Prop</th>\n", "      <th>PostSecStart_MedianPeakGap</th>\n", "      <th>PostSecStart_MaxPeakGap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-08-28</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>0.333</td>\n", "      <td>36</td>\n", "      <td>2025-07-10</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>6.067</td>\n", "      <td>-2.895</td>\n", "      <td>0.216</td>\n", "      <td>1.294</td>\n", "      <td>3.948</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Recent_MinPRA_Date Recent_MinPRA_DownCoverDays Recent_MinPRA2Now_Days  \\\n", "0         2025-08-28                          12                      1   \n", "\n", "  Recent_MinPRA_DownCover2PostSec_DaysProp PostSecStart_LastDays  \\\n", "0                                    0.333                    36   \n", "\n", "  Section_StartDate Recent_MinPRADate2Now_RiseAvg  \\\n", "0        2025-07-10                             0   \n", "\n", "  Recent_MinPRADate2Now_RiseRatio PostSecStart_Mean2MovAvg_MaxRate  \\\n", "0                             0.0                            6.067   \n", "\n", "  PostSecStart_Mean2MovAvg_MinRate PostSecStart_BelowPre1Day_Prop  \\\n", "0                           -2.895                          0.216   \n", "\n", "  PostSecStart_MedianPeakGap PostSecStart_MaxPeakGap  \n", "0                      1.294                   3.948  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[[ 'Recent_MinPRA_Date',\n", "            'Recent_MinPRA_DownCoverDays',\n", "            'Recent_MinPRA2Now_Days',\n", "            'Recent_MinPRA_DownCover2PostSec_DaysProp', 'PostSecStart_LastDays', \n", "            'Section_StartDate', 'Recent_MinPRADate2Now_RiseAvg', 'Recent_MinPRADate2Now_RiseRatio',\n", "            'PostSecStart_Mean2MovAvg_MaxRate', 'PostSecStart_Mean2MovAvg_MinRate', 'PostSecStart_BelowPre1Day_Prop',\n", "            'PostSecStart_MedianPeakGap', 'PostSecStart_MaxPeakGap']]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["stk_temp_data = stk_temp[['Section_StartDate', 'Period_TurnDate', 'PostSec_PGV_TurnP_Date',\n", "            'PostSec_PGV_TurnP_AbsChange',\n", "            'PostSec_PGV_TurnP_RelaChange',\n", "            'PostSec_PGV_PostTurnP_LastDays',    \n", "            'PostSec_TO_TurnP_Date',\n", "            'PostSec_TO_TurnP_AbsChange',\n", "            'PostSec_TO_TurnP_RelaChange',\n", "            'PostSec_TO_PostTurnP_LastDays', 'PostSecStart_RiseRatio', 'PostSecStart_MaxDrop', 'NowSec_MaxPRA_Percentile_PostTurn', 'PostNowSec_MaxCls_Percentile_PostTurn',\n", "            'Now_PGV_Percentile_PostTurn', 'Recent3Day_MaxPGV_Percentile_PostTurn','Recent3Day_MaxPGV_PostTurn_Rank']]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["stk_temp_data = stk_temp[['PostSecStart_PRA_MaxRate',\n", "                             'PostSecStart_PRA_MaxRate_Date',\n", "                             'PostSecStart_VRA_MaxRate',\n", "                             'PostSecStart_VRA_MaxRate_Date',\n", "                             'Now_PRA_Rate',\n", "                             'Now_VRA_Rate',\n", "                             'Recent3Day_PRA_MaxRate',\n", "                             'Recent3Day_VRA_MaxRate',\n", "                             'Recent3Day_PRA_MaxRate_CoverDays',\n", "                             'Recent3Day_VRA_MaxRate_CoverDays',\n", "                                'PostTurn_VGVRank5_NearNowDate',\n", "                             'PostTurn_VGVRank5_NearNowDate_Days',]]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["stk_temp_data = stk_temp[[ 'Section_BottomDate', 'Section_PeakDate', 'Section_StartDate','PreNow_PeakDate', 'Now_SecDate',\n", "                           'SectionPeak_PreSecRise_LastDays',\n", "                           'SectionPeak_PreSecRise_SumRatio',\n", "                           'SectionPeak_PreSecRise_AvgRatio',\n", "                           'SectionPeak_PrePostDays_Ratio',\n", "                           'SectionPeak_PrePostSum_Ratio',\n", "                           'SectionPeak_PrePostAvg_Ratio',\n", "                           'PreNowPeak_PrePostDays_Ratio',\n", "                           'PreNowPeak_PrePostSum_Ratio',\n", "                           'PreNowPeak_PrePostAvg_Ratio', 'PostSecStart_PGV_MinRollAvg2Now_LastDays',\n", "                           'PostSecStart_Over3MovAvg_ContiDays',\n", "                           'Peak2Sec_Und3MovAvg_ContiDays',\n", "                           'PreNowPeak_PreSec_Over3MovAvg_ContiDays',\n", "                           'PreNowPeak_PreSec_Over3MovAvg_Prop',\n", "                           'PostNowSec_Over3MovAvg_ContiDays',\n", "                           'PostNowSec_Over3MovAvg_Prop',\n", "                           'PostNowSec_Over3MovAvg_MaxRate',\n", "                           'PreNowSec_PreSec_Over3MovAvg_ContiDays',\n", "                           'PreNowSec_PreSec_Over3MovAvg_Prop',\n", "                           'PreNowSec_PreSec_Over3MovAvg_MaxRate',\n", "                           'PreNowSec_PostSec_Und3MovAvg_MaxRate',]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["3144    0.2015\n", "dtype: object"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["(stk_temp['PostSecStart_MaxValleyGapValue'] + stk_temp['PostNowSec_MaxValleyGapValue'])/2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Peak_Pres_Num", "rawType": "object", "type": "string"}, {"name": "Peak_Pres_Lastdays", "rawType": "object", "type": "string"}, {"name": "Valley_Pres_Num", "rawType": "object", "type": "string"}, {"name": "Valley_Pres_Lastdays", "rawType": "object", "type": "string"}, {"name": "Peak_Pres_MeanPrice", "rawType": "object", "type": "string"}, {"name": "Peak_Pres_MeanPrice_Ratio", "rawType": "object", "type": "string"}, {"name": "Valley_Pres_MeanPrice", "rawType": "object", "type": "string"}, {"name": "Valley_Pres_MeanPrice_Ratio", "rawType": "object", "type": "string"}], "ref": "1a5a26c8-023e-413a-b0de-ce1f35562100", "rows": [["3847", "1", "83", "3", "274", "10.64", "3.704", "10.737", "4.646"]], "shape": {"columns": 8, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Peak_Pres_Num</th>\n", "      <th>Peak_Pres_Lastdays</th>\n", "      <th>Valley_Pres_Num</th>\n", "      <th>Valley_Pres_Lastdays</th>\n", "      <th>Peak_Pres_MeanPrice</th>\n", "      <th>Peak_Pres_MeanPrice_Ratio</th>\n", "      <th>Valley_Pres_MeanPrice</th>\n", "      <th>Valley_Pres_MeanPrice_Ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3847</th>\n", "      <td>1</td>\n", "      <td>83</td>\n", "      <td>3</td>\n", "      <td>274</td>\n", "      <td>10.64</td>\n", "      <td>3.704</td>\n", "      <td>10.737</td>\n", "      <td>4.646</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Peak_Pres_Num Peak_Pres_Lastdays Valley_Pres_Num Valley_Pres_Lastdays  \\\n", "3847             1                 83               3                  274   \n", "\n", "     Peak_Pres_MeanPrice Peak_Pres_MeanPrice_Ratio Valley_Pres_MeanPrice  \\\n", "3847               10.64                     3.704                10.737   \n", "\n", "     Valley_Pres_MeanPrice_Ratio  \n", "3847                       4.646  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['Peak_Pres_Num', 'Peak_Pres_Lastdays', 'Valley_Pres_Num', 'Valley_Pres_Lastdays',\n", "                           'Peak_Pres_MeanPrice', 'Peak_Pres_MeanPrice_Ratio', 'Valley_Pres_MeanPrice',\n", "                           'Valley_Pres_MeanPrice_Ratio',]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Target_Price</th>\n", "      <th>Target_Ratio</th>\n", "      <th>Bottom_Date</th>\n", "      <th>NowSec2SecStart_Ratio</th>\n", "      <th>Latest_Eff_Peak_Date</th>\n", "      <th>Now_SecDate</th>\n", "      <th>PostNowSec_Over7Num</th>\n", "      <th>PreNow_BottomDate</th>\n", "      <th>NowSec_PGV_MaxRollAvg_UpCoverDays</th>\n", "      <th>NowSec_MaxClose_UpCoverDays</th>\n", "      <th>NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio</th>\n", "      <th>NowSec_PRA2Close_CoverDays_Diff</th>\n", "      <th>SecStart_PRA2Close_CoverDays_Diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>7.7</td>\n", "      <td>36.767</td>\n", "      <td>2024-09-18</td>\n", "      <td>21.327</td>\n", "      <td>2025-02-28</td>\n", "      <td>2025-03-24</td>\n", "      <td>1</td>\n", "      <td>2025-03-24</td>\n", "      <td>6</td>\n", "      <td>80</td>\n", "      <td>2.247</td>\n", "      <td>-74</td>\n", "      <td>-11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Target_Price Target_Ratio Bottom_Date NowSec2SecStart_Ratio  \\\n", "3144          7.7       36.767  2024-09-18                21.327   \n", "\n", "     Latest_Eff_Peak_Date Now_SecDate PostNowSec_Over7Num PreNow_BottomDate  \\\n", "3144           2025-02-28  2025-03-24                   1        2025-03-24   \n", "\n", "     NowSec_PGV_MaxRollAvg_UpCoverDays NowSec_MaxClose_UpCoverDays  \\\n", "3144                                 6                          80   \n", "\n", "     NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio  \\\n", "3144                                      2.247   \n", "\n", "     NowSec_PRA2Close_CoverDays_Diff SecStart_PRA2Close_CoverDays_Diff  \n", "3144                             -74                               -11  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['Target_Price', 'Target_Ratio', 'Bottom_Date', 'NowSec2SecStart_Ratio', 'Latest_Eff_Peak_Date', 'Now_SecDate','PostNowSec_Over7Num','PreNow_BottomDate', 'NowSec_PGV_MaxRollAvg_UpCoverDays', 'NowSec_MaxClose_UpCoverDays',\n", "          'NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio', 'NowSec_PRA2Close_CoverDays_Diff' , 'SecStart_PRA2Close_CoverDays_Diff']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Now_PRA_Percentile_PostSectionPeak</th>\n", "      <th>PostNowSec_PRA_MaxRate</th>\n", "      <th>PostNowSec_PRA_MaxRate_Date</th>\n", "      <th>PostSecStart_PRA_MaxRate</th>\n", "      <th>PostSecStart_PRA_MaxRate_Date</th>\n", "      <th>Now_PRA_Rate</th>\n", "      <th>PostNowSec_PRA_MaxRate_BreachCount</th>\n", "      <th>PostSecStart_PRA_MaxRate_BreachCount</th>\n", "      <th>PostNowSec_MaxRate_PRA_Percentile</th>\n", "      <th>PostNowSec_MaxRate_Post2Pre_DiffRatio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>0.371</td>\n", "      <td>29.991</td>\n", "      <td>2025-03-25</td>\n", "      <td>100.172</td>\n", "      <td>2025-03-13</td>\n", "      <td>29.991</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>0.371</td>\n", "      <td>1.66</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Now_PRA_Percentile_PostSectionPeak PostNowSec_PRA_MaxRate  \\\n", "3144                              0.371                 29.991   \n", "\n", "     PostNowSec_PRA_MaxRate_Date PostSecStart_PRA_MaxRate  \\\n", "3144                  2025-03-25                  100.172   \n", "\n", "     PostSecStart_PRA_MaxRate_Date Now_PRA_Rate  \\\n", "3144                    2025-03-13       29.991   \n", "\n", "     PostNowSec_PRA_MaxRate_BreachCount PostSecStart_PRA_MaxRate_BreachCount  \\\n", "3144                                  0                                    5   \n", "\n", "     PostNowSec_MaxRate_PRA_Percentile PostNowSec_MaxRate_Post2Pre_DiffRatio  \n", "3144                             0.371                                  1.66  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[[\n", "                             'Now_PRA_Percentile_PostSectionPeak',\n", "                             'PostNowSec_PRA_MaxRate',\n", "                             'PostNowSec_PRA_MaxRate_Date',\n", "                             'PostSecStart_PRA_MaxRate',\n", "                             'PostSecStart_PRA_MaxRate_Date',\n", "                             'Now_PRA_Rate',\n", "                             'PostNowSec_PRA_MaxRate_BreachCount',\n", "                             'PostSecStart_PRA_MaxRate_BreachCount',\n", "                             'PostNowSec_MaxRate_PRA_Percentile',\n", "                             'PostNowSec_MaxRate_Post2Pre_DiffRatio']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Now_SecDate</th>\n", "      <th>Section_PeakDate</th>\n", "      <th>Period_TurnDate</th>\n", "      <th>NowSec_MaxPRA_Percentile_PostTurn</th>\n", "      <th>NowSec_MinPRA_Percentile_PostTurn</th>\n", "      <th>NowSec_MaxPRA_Percentile_PostSectionPeak</th>\n", "      <th>NowSec_MinPRA_Percentile_PostSectionPeak</th>\n", "      <th>SecPeak2NowSec_PRA_UpBand</th>\n", "      <th>SecPeak2NowSec_PRA_LowBand</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>2025-03-24</td>\n", "      <td>2024-11-22</td>\n", "      <td>2024-09-18</td>\n", "      <td>0.418</td>\n", "      <td>0.418</td>\n", "      <td>0.371</td>\n", "      <td>0.371</td>\n", "      <td>0.3113</td>\n", "      <td>0.039</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Now_SecDate Section_PeakDate Period_TurnDate  \\\n", "3144  2025-03-24       2024-11-22      2024-09-18   \n", "\n", "     NowSec_MaxPRA_Percentile_PostTurn NowSec_MinPRA_Percentile_PostTurn  \\\n", "3144                             0.418                             0.418   \n", "\n", "     NowSec_MaxPRA_Percentile_PostSectionPeak  \\\n", "3144                                    0.371   \n", "\n", "     NowSec_MinPRA_Percentile_PostSectionPeak SecPeak2NowSec_PRA_UpBand  \\\n", "3144                                    0.371                    0.3113   \n", "\n", "     SecPeak2NowSec_PRA_LowBand  \n", "3144                      0.039  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['Now_SecDate','Section_PeakDate','Period_TurnDate','NowSec_MaxPRA_Percentile_PostTurn',\n", "                             'NowSec_MinPRA_Percentile_PostTurn',\n", "                             'NowSec_MaxPRA_Percentile_PostSectionPeak',\n", "                             'NowSec_MinPRA_Percentile_PostSectionPeak','SecPeak2NowSec_PRA_UpBand',\n", "                             'SecPeak2NowSec_PRA_LowBand',]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PostSecStart_DailyDropRecov_Count</th>\n", "      <th>PostSecStart_DailyDropRecov_RecovRatio</th>\n", "      <th>PostSecStart_DailyDropRecov_MeanRecovDays</th>\n", "      <th>PostPreNow_MinDailyRatio_Date</th>\n", "      <th>PostPreNow_MinDailyRatio</th>\n", "      <th>PostPreNow_MinDailyRatio_Recover_Date</th>\n", "      <th>PostPreNow_MinDailyRatio_Recover_Days</th>\n", "      <th>PostSecPeak_MinDailyRatio_Date</th>\n", "      <th>PostSecPeak_MinDailyRatio</th>\n", "      <th>PostSecPeak_MinDailyRatio_Recover_Date</th>\n", "      <th>PostSecPeak_MinDailyRatio_Recover_Days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>7</td>\n", "      <td>1.0</td>\n", "      <td>2.571</td>\n", "      <td>2025-03-18</td>\n", "      <td>-2.416357</td>\n", "      <td>2025-03-25</td>\n", "      <td>5</td>\n", "      <td>2024-11-26</td>\n", "      <td>-8.633094</td>\n", "      <td>2025-03-25</td>\n", "      <td>78</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PostSecStart_DailyDropRecov_Count PostSecStart_DailyDropRecov_RecovRatio  \\\n", "3144                                 7                                    1.0   \n", "\n", "     PostSecStart_DailyDropRecov_MeanRecovDays PostPreNow_MinDailyRatio_Date  \\\n", "3144                                     2.571                    2025-03-18   \n", "\n", "     PostPreNow_MinDailyRatio PostPreNow_MinDailyRatio_Recover_Date  \\\n", "3144                -2.416357                            2025-03-25   \n", "\n", "     PostPreNow_MinDailyRatio_Recover_Days PostSecPeak_MinDailyRatio_Date  \\\n", "3144                                     5                     2024-11-26   \n", "\n", "     PostSecPeak_MinDailyRatio PostSecPeak_MinDailyRatio_Recover_Date  \\\n", "3144                 -8.633094                             2025-03-25   \n", "\n", "     PostSecPeak_MinDailyRatio_Recover_Days  \n", "3144                                     78  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['PostSecStart_DailyDropRecov_Count', 'PostSecStart_DailyDropRecov_RecovRatio',\n", "                           'PostSecStart_DailyDropRecov_MeanRecovDays', 'PostPreNow_MinDailyRatio_Date', 'PostPreNow_MinDailyRatio',\n", "                           'PostPreNow_MinDailyRatio_Recover_Date', 'PostPreNow_MinDailyRatio_Recover_Days',\n", "                           'PostSecPeak_MinDailyRatio_Date', 'PostSecPeak_MinDailyRatio',\n", "                           'PostSecPeak_MinDailyRatio_Recover_Date', 'PostSecPeak_MinDailyRatio_Recover_Days']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Section_StartDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>PostNowSec_Over7Num</th>\n", "      <th>PreNow_BottomDate</th>\n", "      <th>DownConsecutive_PGVRollAvg_DiffRatio</th>\n", "      <th>DownConsecutive_SumRatio</th>\n", "      <th>DownConsecutive2Now_LastDays</th>\n", "      <th>Now_PGV_RollAvg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>2025-01-03</td>\n", "      <td>2025-03-24</td>\n", "      <td>1</td>\n", "      <td>2025-03-24</td>\n", "      <td>0.389</td>\n", "      <td>-4.089</td>\n", "      <td>3</td>\n", "      <td>0.14</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Section_StartDate Now_SecDate PostNowSec_Over7Num PreNow_BottomDate  \\\n", "3144        2025-01-03  2025-03-24                   1        2025-03-24   \n", "\n", "     DownConsecutive_PGVRollAvg_DiffRatio DownConsecutive_SumRatio  \\\n", "3144                                0.389                   -4.089   \n", "\n", "     DownConsecutive2Now_LastDays Now_PGV_RollAvg  \n", "3144                            3            0.14  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['Section_StartDate','Now_SecDate', 'PostNowSec_Over7Num','PreNow_BottomDate', 'DownConsecutive_PGVRollAvg_DiffRatio','DownConsecutive_SumRatio','DownConsecutive2Now_LastDays','Now_PGV_RollAvg']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Section_StartDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>Section_PeakDate</th>\n", "      <th>PreNow_PeakDate</th>\n", "      <th>PostNowSec_PGV_MaxRollAvg</th>\n", "      <th>PreTurnPeak_PRV_Top3Mean</th>\n", "      <th>SectionPeak_PRV_Top3Mean</th>\n", "      <th>SectionStart_PRV_Top3Mean</th>\n", "      <th>NowSec_PRV_Top3Mean</th>\n", "      <th>PreNowSec_PRV_Top3Mean</th>\n", "      <th>PreNowPeak_PRV_Top3Mean</th>\n", "      <th>Turn_PRV_Top3Mean</th>\n", "      <th>PostTurnPeak_PRV_Top3Mean</th>\n", "      <th>PostSecPeak_PRV_Top3Mean</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>2025-01-03</td>\n", "      <td>2025-03-24</td>\n", "      <td>2024-11-22</td>\n", "      <td>2025-03-17</td>\n", "      <td>0.14</td>\n", "      <td>0.111</td>\n", "      <td>0.278</td>\n", "      <td>0.069</td>\n", "      <td>0.079</td>\n", "      <td>0.152</td>\n", "      <td>0.152</td>\n", "      <td>0.017</td>\n", "      <td>0.278</td>\n", "      <td>0.129</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Section_StartDate Now_SecDate Section_PeakDate PreNow_PeakDate  \\\n", "3144        2025-01-03  2025-03-24       2024-11-22      2025-03-17   \n", "\n", "     PostNowSec_PGV_MaxRollAvg PreTurnPeak_PRV_Top3Mean  \\\n", "3144                      0.14                    0.111   \n", "\n", "     SectionPeak_PRV_Top3Mean SectionStart_PRV_Top3Mean NowSec_PRV_Top3Mean  \\\n", "3144                    0.278                     0.069               0.079   \n", "\n", "     PreNowSec_PRV_Top3Mean PreNowPeak_PRV_Top3Mean Turn_PRV_Top3Mean  \\\n", "3144                  0.152                   0.152             0.017   \n", "\n", "     PostTurnPeak_PRV_Top3Mean PostSecPeak_PRV_Top3Mean  \n", "3144                     0.278                    0.129  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['Section_StartDate', 'Now_SecDate', 'Section_PeakDate', 'PreNow_PeakDate','PostNowSec_PGV_MaxRollAvg', 'PreTurnPeak_PRV_Top3Mean', 'SectionPeak_PRV_Top3Mean',\n", "                             'SectionStart_PRV_Top3Mean', 'NowSec_PRV_Top3Mean',\n", "                             'PreNowSec_PRV_Top3Mean', 'PreNowPeak_PRV_Top3Mean',\n", "                             'Turn_PRV_Top3Mean', 'PostTurnPeak_PRV_Top3Mean', 'PostSecPeak_PRV_Top3Mean',]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PreNow_PeakDate</th>\n", "      <th>PreNow_SecDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>PreNowSec2NowSec_MeanStdSum</th>\n", "      <th>PreNowBottom2Now_MeanStdDiff</th>\n", "      <th>PostNowSec_PGVRollAvg_CoverDrop_Diff</th>\n", "      <th>PGV_RollAvg_Recent2Previous_Change</th>\n", "      <th>NowSec_SecConcave_StartDate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>2025-03-17</td>\n", "      <td>2025-03-17</td>\n", "      <td>2025-03-24</td>\n", "      <td>-0.71</td>\n", "      <td>9.96</td>\n", "      <td>-0.171</td>\n", "      <td>0.286</td>\n", "      <td>2024-11-25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PreNow_PeakDate PreNow_SecDate Now_SecDate PreNowSec2NowSec_MeanStdSum  \\\n", "3144      2025-03-17     2025-03-17  2025-03-24                       -0.71   \n", "\n", "     PreNowBottom2Now_MeanStdDiff PostNowSec_PGVRollAvg_CoverDrop_Diff  \\\n", "3144                         9.96                               -0.171   \n", "\n", "     PGV_RollAvg_Recent2Previous_Change NowSec_SecConcave_StartDate  \n", "3144                              0.286                  2024-11-25  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['PreNow_PeakDate', 'PreNow_SecDate', 'Now_SecDate', 'PreNowSec2NowSec_MeanStdSum', 'PreNowBottom2Now_MeanStdDiff', 'PostNowSec_PGVRollAvg_CoverDrop_Diff','PGV_RollAvg_Recent2Previous_Change', 'NowSec_SecConcave_StartDate']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PreNowBottom2PostNowPeak_STrend</th>\n", "      <th>PreNowBottom2PostNowPeak_STrend_UpDev</th>\n", "      <th>PreNowBottom2PostNowPeak_STrend_LowDev</th>\n", "      <th>PostPreNowPeak_U2D_MaxTO_Eff_Date</th>\n", "      <th>PostPreNowPeak_U2D_MinTO_Eff_Date</th>\n", "      <th>PostPreNowPeak_U2D_MinTO_Eff2Now_LastDays</th>\n", "      <th>PostPreNowPeak_U2D_MinTO_Eff_CoverDays</th>\n", "      <th>PreNowSec_Turnover2Change_Rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>0</td>\n", "      <td>4.211</td>\n", "      <td>-0.823</td>\n", "      <td>2025-03-21</td>\n", "      <td>2025-03-17</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PreNowBottom2PostNowPeak_STrend PreNowBottom2PostNowPeak_STrend_UpDev  \\\n", "3144                               0                                 4.211   \n", "\n", "     PreNowBottom2PostNowPeak_STrend_LowDev PostPreNowPeak_U2D_MaxTO_Eff_Date  \\\n", "3144                                 -0.823                        2025-03-21   \n", "\n", "     PostPreNowPeak_U2D_MinTO_Eff_Date  \\\n", "3144                        2025-03-17   \n", "\n", "     PostPreNowPeak_U2D_MinTO_Eff2Now_LastDays  \\\n", "3144                                         6   \n", "\n", "     PostPreNowPeak_U2D_MinTO_Eff_CoverDays PreNowSec_Turnover2Change_Rank  \n", "3144                                      1                            1.0  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['PreNowBottom2PostNowPeak_STrend', 'PreNowBottom2PostNowPeak_STrend_UpDev', 'PreNowBottom2PostNowPeak_STrend_LowDev', 'PostPreNowPeak_U2D_MaxTO_Eff_Date', 'PostPreNowPeak_U2D_MinTO_Eff_Date',\n", "                   'PostPreNowPeak_U2D_MinTO_Eff2Now_LastDays',\n", "                   'PostPreNowPeak_U2D_MinTO_Eff_CoverDays', 'PreNowSec_Turnover2Change_Rank']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PreTurn_PeakDate</th>\n", "      <th>Period_TurnDate</th>\n", "      <th>PreNow_PeakDate</th>\n", "      <th>PostSec_MinTO_Eff_Date</th>\n", "      <th>Eff_Recent2Previous_MinChange</th>\n", "      <th>Eff_Recent2Previous_Change</th>\n", "      <th>Is_LowBound_Oscillation</th>\n", "      <th>PostPreNowPeak_MaxTO_Eff_CoverDays</th>\n", "      <th>Recent_EffPeak_ChangeRate</th>\n", "      <th>Recent_EffPeak_ChangeRate_Percentile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>2024-08-19</td>\n", "      <td>2024-09-18</td>\n", "      <td>2025-03-17</td>\n", "      <td>2025-03-19</td>\n", "      <td>-0.741</td>\n", "      <td>-0.769</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-13.397</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PreTurn_PeakDate Period_TurnDate PreNow_PeakDate PostSec_MinTO_Eff_Date  \\\n", "3144       2024-08-19      2024-09-18      2025-03-17             2025-03-19   \n", "\n", "     Eff_Recent2Previous_MinChange Eff_Recent2Previous_Change  \\\n", "3144                        -0.741                     -0.769   \n", "\n", "     Is_LowBound_Oscillation PostPreNowPeak_MaxTO_Eff_CoverDays  \\\n", "3144                       0                                  1   \n", "\n", "     Recent_EffPeak_ChangeRate Recent_EffPeak_ChangeRate_Percentile  \n", "3144                   -13.397                                  1.0  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[[ 'PreTurn_PeakDate','Period_TurnDate','PreNow_PeakDate', 'PostSec_MinTO_Eff_Date','Eff_Recent2Previous_MinChange', 'Eff_Recent2Previous_Change', 'Is_LowBound_Oscillation','PostPreNowPeak_MaxTO_Eff_CoverDays','Recent_EffPeak_ChangeRate','Recent_EffPeak_ChangeRate_Percentile']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Eff_Avg_Peak_Period</th>\n", "      <th>Eff_Avg_Valley_Period</th>\n", "      <th>Eff_Peak_Intensity</th>\n", "      <th>Eff_Valley_Intensity</th>\n", "      <th>Days_From_Last_Peak</th>\n", "      <th>Days_From_Last_Valley</th>\n", "      <th>Peak2Turn_AvgSecDays</th>\n", "      <th>Turn2Now_AvgSecDays</th>\n", "      <th>PostPreNowPeak_MaxTO_Eff_Band</th>\n", "      <th>Latest_TO_Eff_Band</th>\n", "      <th>PostPreNowPeak_U2D_MaxTO_Eff_Band</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>3.0</td>\n", "      <td>3.7</td>\n", "      <td>0.415</td>\n", "      <td>0.391</td>\n", "      <td>17</td>\n", "      <td>4</td>\n", "      <td>20.0</td>\n", "      <td>10.25</td>\n", "      <td>-0.791</td>\n", "      <td>-0.828</td>\n", "      <td>0.537</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Eff_Avg_Peak_Period Eff_Avg_Valley_Period Eff_Peak_Intensity  \\\n", "3144                 3.0                   3.7              0.415   \n", "\n", "     Eff_Valley_Intensity Days_From_Last_Peak Days_From_Last_Valley  \\\n", "3144                0.391                  17                     4   \n", "\n", "     Peak2Turn_AvgSecDays Turn2Now_AvgSecDays PostPreNowPeak_MaxTO_Eff_Band  \\\n", "3144                 20.0               10.25                        -0.791   \n", "\n", "     Latest_TO_Eff_Band PostPreNowPeak_U2D_MaxTO_Eff_Band  \n", "3144             -0.828                             0.537  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['Eff_Avg_Peak_Period', 'Eff_Avg_Valley_Period',\n", "                   'Eff_Peak_Intensity', 'Eff_Valley_Intensity',\n", "                   'Days_From_Last_Peak', 'Days_From_Last_Valley','Peak2Turn_AvgSecDays',\n", "                   'Turn2Now_AvgSecDays', 'PostPreNowPeak_MaxTO_Eff_Band', 'Latest_TO_Eff_Band', 'PostPreNowPeak_U2D_MaxTO_Eff_Band']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Section_StartDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>PostSecStart_PGV_MaxRollAvg_Date</th>\n", "      <th>Days_From_Last_Peak</th>\n", "      <th>Days_From_Last_Valley</th>\n", "      <th>PostSecMaxRollAvg_PGV_MinRollAvg_Band</th>\n", "      <th>PostSecMaxRollAvg_PGV_MinRollAvg_CoverDays</th>\n", "      <th>PostSecStart_PGV_MaxRollAvg_Band</th>\n", "      <th>PostSecStart_PGV_MaxRollAvg_CoverDays</th>\n", "      <th>PostNowSec_PGV_MaxRollAvg_Band</th>\n", "      <th>PostNowSec_PGV_MaxRollAvg_CoverDays</th>\n", "      <th>Now_PGV_RollAvg_Rank</th>\n", "      <th>PGV_RollAvg_Recent2Previous_Change</th>\n", "      <th>PGV_RollAvg_NowSec2Previous_Change</th>\n", "      <th>PGV_RollAvg_VolatilityRatio</th>\n", "      <th>Efficiency_VolatilityRatio</th>\n", "      <th>U2D_Efficiency_VolatilityRatio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>2025-01-03</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-03-17</td>\n", "      <td>17</td>\n", "      <td>4</td>\n", "      <td>0.128</td>\n", "      <td>6</td>\n", "      <td>1.768</td>\n", "      <td>63</td>\n", "      <td>1.433</td>\n", "      <td>6</td>\n", "      <td>0.844898</td>\n", "      <td>0.286</td>\n", "      <td>0</td>\n", "      <td>0.845</td>\n", "      <td>1.592</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Section_StartDate Now_SecDate PostSecStart_PGV_MaxRollAvg_Date  \\\n", "3144        2025-01-03  2025-03-24                       2025-03-17   \n", "\n", "     Days_From_Last_Peak Days_From_Last_Valley  \\\n", "3144                  17                     4   \n", "\n", "     PostSecMaxRollAvg_PGV_MinRollAvg_Band  \\\n", "3144                                 0.128   \n", "\n", "     PostSecMaxRollAvg_PGV_MinRollAvg_CoverDays  \\\n", "3144                                          6   \n", "\n", "     PostSecStart_PGV_MaxRollAvg_Band PostSecStart_PGV_MaxRollAvg_CoverDays  \\\n", "3144                            1.768                                    63   \n", "\n", "     PostNowSec_PGV_MaxRollAvg_Band PostNowSec_PGV_MaxRollAvg_CoverDays  \\\n", "3144                          1.433                                   6   \n", "\n", "     Now_PGV_RollAvg_Rank PGV_RollAvg_Recent2Previous_Change  \\\n", "3144             0.844898                              0.286   \n", "\n", "     PGV_RollAvg_NowSec2Previous_Change PGV_RollAvg_VolatilityRatio  \\\n", "3144                                  0                       0.845   \n", "\n", "     Efficiency_VolatilityRatio U2D_Efficiency_VolatilityRatio  \n", "3144                      1.592                            0.2  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['Section_StartDate','Now_SecDate', 'PostSecStart_PGV_MaxRollAvg_Date','Days_From_Last_Peak', 'Days_From_Last_Valley',\n", "                   'PostSecMaxRollAvg_PGV_MinRollAvg_Band', 'PostSecMaxRollAvg_PGV_MinRollAvg_CoverDays',\n", "                   'PostSecStart_PGV_MaxRollAvg_Band', 'PostSecStart_PGV_MaxRollAvg_CoverDays',\n", "                   'PostNowSec_PGV_MaxRollAvg_Band', 'PostNowSec_PGV_MaxRollAvg_CoverDays',\n", "                   'Now_PGV_RollAvg_Rank', \n", "                   'PGV_RollAvg_Recent2Previous_Change',\n", "                   'PGV_RollAvg_NowSec2Previous_Change',\n", "                   'PGV_RollAvg_VolatilityRatio',\n", "                   'Efficiency_VolatilityRatio', 'U2D_Efficiency_VolatilityRatio',]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Peak2Sec_Und5MovAvg_Prop</th>\n", "      <th>PostSecStart_Over5MovAvg_Prop</th>\n", "      <th>DownConsecutive2Now_LastDays</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>0.667</td>\n", "      <td>0.519</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Peak2Sec_Und5MovAvg_Prop PostSecStart_Over5MovAvg_Prop  \\\n", "3144                    0.667                         0.519   \n", "\n", "     DownConsecutive2Now_LastDays  \n", "3144                            3  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['Peak2Sec_Und5MovAvg_Prop', 'PostSecStart_Over5MovAvg_Prop', 'DownConsecutive2Now_LastDays']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PreNowPeak2NowSec_MinRatioMovAvg_Date</th>\n", "      <th>PreNowPeak2NowSec_RatioMovAvg_NowSecRank</th>\n", "      <th>PreNow_PeakDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>PreNow_BottomDate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>2025-03-20</td>\n", "      <td>4.0</td>\n", "      <td>2025-03-17</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-03-24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PreNowPeak2NowSec_MinRatioMovAvg_Date  \\\n", "3144                            2025-03-20   \n", "\n", "     PreNowPeak2NowSec_RatioMovAvg_NowSecRank PreNow_PeakDate Now_SecDate  \\\n", "3144                                      4.0      2025-03-17  2025-03-24   \n", "\n", "     PreNow_BottomDate  \n", "3144        2025-03-24  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['PreNowPeak2NowSec_MinRatioMovAvg_Date', 'PreNowPeak2NowSec_RatioMovAvg_NowSecRank', 'PreNow_PeakDate', 'Now_SecDate', 'PreNow_BottomDate']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SecValley_GapRatio</th>\n", "      <th>PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays</th>\n", "      <th>Now_SecDate</th>\n", "      <th>PreNow_BottomDate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>4.49</td>\n", "      <td>3</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-03-24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     SecValley_GapRatio PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays  \\\n", "3144               4.49                                             3   \n", "\n", "     Now_SecDate PreNow_BottomDate  \n", "3144  2025-03-24        2025-03-24  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['SecValley_GapRatio', 'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays', 'Now_SecDate', 'PreNow_BottomDate']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PostNowSec_Recover_TopOpen_Days</th>\n", "      <th>Section_StartDate</th>\n", "      <th>PostPeak_Recent_Neg4_RecovDate</th>\n", "      <th>PostPeak_Recent_Neg4_DropDate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3144</th>\n", "      <td>1</td>\n", "      <td>2025-01-03</td>\n", "      <td>2025-03-03</td>\n", "      <td>2025-02-18</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PostNowSec_Recover_TopOpen_Days Section_StartDate  \\\n", "3144                               1        2025-01-03   \n", "\n", "     PostPeak_Recent_Neg4_RecovDate PostPeak_Recent_Neg4_DropDate  \n", "3144                     2025-03-03                    2025-02-18  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['PostNowSec_Recover_TopOpen_Days', 'Section_StartDate', 'PostPeak_Recent_Neg4_RecovDate', 'PostPeak_Recent_Neg4_DropDate']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-01-17T00:34:09.585349Z", "start_time": "2025-01-17T00:28:55.397726Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试日期： 2025-01-14\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:370: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:472: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:543: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n"]}, {"ename": "ValueError", "evalue": "too many values to unpack (expected 2)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[19], line 5\u001b[0m\n\u001b[1;32m      3\u001b[0m dates \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2025-01-14\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m      4\u001b[0m \u001b[38;5;66;03m# dates = ['2025-01-08']\u001b[39;00m\n\u001b[0;32m----> 5\u001b[0m output_droprecov, output_droprecov_indus, model_droprecov, industry_summary \u001b[38;5;241m=\u001b[39m get_turnbreak_stocks(turntrack_startdate\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2024-09-18\u001b[39m\u001b[38;5;124m'\u001b[39m, \n\u001b[1;32m      6\u001b[0m                                                                                             recentdrop_startdate\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2024-12-30\u001b[39m\u001b[38;5;124m'\u001b[39m, \n\u001b[1;32m      7\u001b[0m                                                                                             end_date\u001b[38;5;241m=\u001b[39mdates,\n\u001b[1;32m      8\u001b[0m                                                                                             scope\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mall\u001b[39m\u001b[38;5;124m'\u001b[39m, \n\u001b[1;32m      9\u001b[0m                                                                                             model_adj\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m     10\u001b[0m                                                                                             store_mode\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m     11\u001b[0m                                                                                             bottom_industry\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \n\u001b[1;32m     12\u001b[0m                                                                                             index_turndate\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m)\n", "File \u001b[0;32m~/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:549\u001b[0m, in \u001b[0;36mget_turnbreak_stocks\u001b[0;34m(turntrack_startdate, recentdrop_startdate, end_date, scope, model_adj, store_mode, index_turndate, bottom_industry)\u001b[0m\n\u001b[1;32m    546\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m pd\u001b[38;5;241m.\u001b[39mDataFrame(), pd\u001b[38;5;241m.\u001b[39mDataFrame(), pd\u001b[38;5;241m.\u001b[39mDataFrame(), pd\u001b[38;5;241m.\u001b[39mDataFrame()\n\u001b[1;32m    548\u001b[0m recentdrop_startdate \u001b[38;5;241m=\u001b[39m trade_dates[trade_dates \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;28mmax\u001b[39m(end_dates)][\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m5\u001b[39m]\n\u001b[0;32m--> 549\u001b[0m Bottom_List, _ \u001b[38;5;241m=\u001b[39m cal_swindex_state(end_date\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mmax\u001b[39m(\n\u001b[1;32m    550\u001b[0m     end_dates), index_preturn_date\u001b[38;5;241m=\u001b[39mrecentdrop_startdate)\n\u001b[1;32m    552\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(output_DropRecov) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m    553\u001b[0m     output_DropRecov[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstk_min_cumret_date\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[0;31mValueError\u001b[0m: too many values to unpack (expected 2)"]}], "source": ["# 调用step1筛选品种\n", "from function_ai.StkPick_ModelFunc import get_turnbreak_stocks\n", "dates = ['2025-01-14']\n", "# dates = ['2025-01-08']\n", "output_droprecov, output_droprecov_indus, model_droprecov, industry_summary = get_turnbreak_stocks(turntrack_startdate='2024-09-18', \n", "                                                                                            recentdrop_startdate='2024-12-30', \n", "                                                                                            end_date=dates,\n", "                                                                                            scope='all', \n", "                                                                                            model_adj=False,\n", "                                                                                            store_mode=False,\n", "                                                                                            bottom_industry=None, \n", "                                                                                            index_turndate=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov2 = output_droprecov.query('industry==\"机械设备\"').sort_values(by='industry_rank_percentile', ascending=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov2 = output_droprecov[['ts_code', 'name', 'industry', 'PreNow_PeakDate', 'PreNowSec_Turnover2Change_Rank'] + output_droprecov.columns.tolist()[-8:]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov.query('ts_code==\"002031.SZ\"')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov2 = output_droprecov.query('PreSec_AvgTurnover>2').sort_values(by='PreSec_Turnover2Drop', ascending=True)[['ts_code', 'name', 'industry', 'PreSec_Turnover2Drop', 'PreSec_Turnover2Rise', 'PreSec_Turnover2Drop_Last4Rank', 'PreSec_AvgTurnover','PGV_RollAvg_VolatilityRatio',\n", "                   'Efficiency_VolatilityRatio', 'U2D_Efficiency_VolatilityRatio',]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["','.join(output_droprecov2['ts_code'].values.tolist())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov2 = output_droprecov.sort_values(by='PostPreNowPeak_MaxTO_Eff_CoverDays', ascending=False)[['ts_code', 'name', 'industry', 'Eff_Latest2Pre3Mean', 'PostPreNowPeak_MaxTO_Eff2Now_LastDays', 'PostPreNowPeak_MaxTO_Eff_Band','PostPreNowPeak_MaxTO_Eff_CoverDays']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["','.join(model_droprecov['ts_code'].values.tolist())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_droprecov2 = model_droprecov[['ts_code', 'name', 'industry', 'PreTurn_PeakDate', 'Period_TurnDate', 'Section_StartDate', 'PostPreNowPeak_MaxTO_Eff_Date', 'PostSec_MinTO_Eff_Date', 'PostPreNowPeak_MaxTO_Eff_CoverDays', 'PostPreNowPeak_U2D_MaxTO_Eff_CoverDays','Eff_Recent2Previous_MinChange','Recent_EffPeak_ChangeRate', 'Recent_EffPeak_ChangeRate_Percentile',\n", "                   'PostPreNowPeak_MaxTO_Eff_Band', 'PostSec_MinTO_Eff_Band',\n", "                   'Latest_TO_Eff_Band', 'Eff_Recent2Previous_Change',]]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ouput_droprecov2 = output_droprecov.query('PostPreNowPeak_MaxTO_Eff2Now_LastDays>0').sort_values(by='Eff_Avg_Peak_Period', ascending=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_droprecov.query('ts_code==\"300913.SZ\"')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["col = ['PreNow_PeakDate']\n", "rest_columns = [i for i in output_droprecov.columns if i not in col]\n", "output_droprecov2 = output_droprecov[col+rest_columns]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov2 = output_droprecov.sort_values(by='TrendWLS_MaxR2Width', ascending=True)[['ts_code', 'name', 'industry', 'TrendWLS_MaxR2', 'TrendWLS_MaxR2Length', 'TrendWLS_MaxR2Width']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from machine_learn.Func_MacinLn import stkpick_model_predict\n", "result_predict, result_predict_all = stkpick_model_predict(predict_date='2024-11-19', stk_list=output_droprecov['ts_code'].tolist(), model_select='XGB', model_date='2024-10-30', label_style='Label', pretreat='none')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov2 = output_droprecov.sort_values(by=['PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays', 'PreSecPeak_Sec_SumRatio'],ascending=[True, True])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["','.join(model_droprecov['ts_code'].values.tolist())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["','.join(output_droprecov['ts_code'].values.tolist())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov2 = output_droprecov[['ts_code', 'name', 'industry', 'PreSecPeak_Sec_AvgRatio', 'PreSecPeak_Sec_SumRatio', 'PreNowPeak2NowSec_RatioMovAvg_NowSecRank', 'DownConsecutive2Now_LastDays', 'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays', 'PostSecStart_Sec_Max_LastDays', 'Recent3Day_PGV_MinRollAvg', 'PostSecStart_PGV_RollAvg_LowQuntl']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov.query('ts_code==\"300898.SZ\"')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from machine_learn.Func_MacinLn import stkpick_model_predict\n", "result_predict, result_predict_all = stkpick_model_predict(predict_date='2024-11-27', stk_list=output_droprecov['ts_code'].tolist(), model_select='XGB', model_date='2024-10-30', label_style='Label')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov2 = output_droprecov[['ts_code', 'name', 'industry', 'PreNowPeak2NowSec_RatioMovAvg_NowSecRank', 'PreNowSec_LastDays', 'Now_SecDate', 'PostSecStart_Over5MovAvg_Prop']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov2 = output_droprecov.sort_values(by='PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays', ascending=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_droprecov_indus2 = output_droprecov_indus.query('industry in [\"通信\"]').copy()\n", "output_droprecov_indus2['PRA_Recent3Min2LowQuntl_Ratio'] = round(\n", "    output_droprecov_indus2['Recent3Day_PGV_MinRollAvg'] / \n", "    output_droprecov_indus2['PostSecStart_PGV_RollAvg_LowQuntl'], 3)\n", "output_droprecov_indus2 = output_droprecov_indus2.sort_values(by='PRA_Recent3Min2LowQuntl_Ratio', ascending=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["备选日期范围： 2025-01-06/2025-01-07/2025-01-08/2025-01-09/2025-01-10\n", "备选品种数量： 330\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:222: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:249: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:222: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, temp_df], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2024-12-02日期后的Bottom转折行业列表:\n", "- 医药生物 (结束日期: 2025-01-10, 下行持续天数: 262)\n", "- 非银金融 (结束日期: 2025-01-10, 下行持续天数: 46)\n", "- 有色金属 (结束日期: 2024-12-31, 下行持续天数: 177)\n", "- 农林牧渔 (结束日期: 2024-12-26, 下行持续天数: 256)\n"]}], "source": ["# 调用step2筛选品种\n", "from function_ai.StkPick_ModelFunc import track_turnbreak_stocks\n", "output_recov, output_recov_indus =track_turnbreak_stocks(index_turndates=['2025-01-06'], lag_num=4, check_date='2025-01-14')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_recov2 = output_recov[['ts_code', 'name', 'industry', 'PreNow_SecDate', 'Now_SecDate_x', 'PreNowSec_Sec_AvgTurnover_y', \n", "                              'Check_PostNowSec_AvgTurnover', 'Check_RecovDate', 'Efficiency_VolatilityRatio_x',\n", "                              'PostPreNow_UpEff_MaxTO_Eff_Band', 'PreNowSec_AvgTurnover2Drop', 'Over7Ratio_Days',\n", "                              'PreSec_Trend_Consistency', 'PreSec_Channel_Stability']].copy()\n", "output_recov2['Turnover_Diff'] = output_recov2['Check_PostNowSec_AvgTurnover'] - output_recov2['PreNowSec_Sec_AvgTurnover_y']\n", "output_recov2 = output_recov2.sort_values(by='Over7Ratio_Days', ascending=False)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'603106.SH,002354.SZ,002175.SZ,603815.SH,688010.SH,300753.SZ,688255.SH,002400.SZ,002547.SZ,300328.SZ,600148.SH,300128.SZ,300640.SZ,605178.SH,001309.SZ,301383.SZ,300257.SZ,600539.SH,300660.SZ,603305.SH,300432.SZ,600807.SH,002725.SZ,003042.SZ,688022.SH,300091.SZ,002058.SZ,603813.SH,000531.SZ,002050.SZ,300127.SZ,300423.SZ,002686.SZ,600257.SH,301123.SZ,301168.SZ,300473.SZ,002938.SZ,603379.SH,600126.SH,600641.SH,001283.SZ,301188.SZ,002526.SZ,301368.SZ,600860.SH,002892.SZ,301160.SZ,605319.SH,300449.SZ,301183.SZ,300158.SZ,688090.SH,000868.SZ,688168.SH,600259.SH,600237.SH,301356.SZ,002879.SZ,603050.SH,600722.SH,600218.SH,688717.SH,000702.SZ,301177.SZ,600241.SH,300580.SZ,603666.SH,688160.SH,002527.SZ,301322.SZ,300806.SZ,002989.SZ,688362.SH,603989.SH,300491.SZ,002896.SZ,600967.SH,600657.SH,600378.SH,603129.SH,600105.SH,600053.SH,002384.SZ,301419.SZ,300765.SZ,300225.SZ,688256.SH,002178.SZ,002162.SZ,300990.SZ,002591.SZ,603336.SH,603186.SH,603029.SH,002291.SZ,000430.SZ,600708.SH,300814.SZ,300852.SZ,002222.SZ,603188.SH,300436.SZ,002255.SZ,002195.SZ,002149.SZ,002282.SZ,002042.SZ,688696.SH,001255.SZ,688652.SH,603306.SH,300827.SZ,301076.SZ,000010.SZ,301061.SZ,603583.SH,301251.SZ,688275.SH,300866.SZ,301345.SZ,301210.SZ,300989.SZ,603163.SH,603032.SH,003002.SZ,002198.SZ,688190.SH,000603.SZ,301369.SZ,603922.SH,001872.SZ,300813.SZ,601236.SH,001359.SZ,600226.SH,000506.SZ,002052.SZ,300209.SZ,001207.SZ,000811.SZ,002171.SZ,300876.SZ,002281.SZ,002357.SZ,603416.SH,603259.SH,688220.SH,002910.SZ,000622.SZ,605128.SH,605318.SH,603312.SH,603109.SH,603843.SH,300341.SZ,002863.SZ,300115.SZ,002348.SZ,002289.SZ,002114.SZ,001358.SZ,000534.SZ,688498.SH,688077.SH,603231.SH,600963.SH,600366.SH,300135.SZ,688038.SH,605151.SH,603258.SH,002689.SZ,002553.SZ,002490.SZ,002105.SZ,002084.SZ,000514.SZ,688685.SH,603655.SH,603790.SH,600882.SH,002358.SZ,000571.SZ,300224.SZ,300391.SZ,300401.SZ,301305.SZ,002483.SZ,688087.SH,603895.SH,603191.SH,600961.SH,002842.SZ,002716.SZ,000856.SZ,301261.SZ,301036.SZ,603325.SH,603507.SH,002718.SZ,300421.SZ,002597.SZ,002506.SZ,300565.SZ,603211.SH,300617.SZ,002742.SZ,001211.SZ,301285.SZ,301236.SZ,301072.SZ,300804.SZ,300698.SZ,301213.SZ'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["','.join(output_recov2['ts_code'].values.tolist())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-01-16T01:40:40.885443Z", "start_time": "2025-01-16T01:39:45.953051Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["完成数据更新： 2025-01-23 \n", " 数据已保存到Excel文件: /Users/<USER>/PycharmProjects/AI_Stock/index_study/申万指数历史行情_1.xls\n", "完成申万行业指数数据获取： 2025-01-23\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>trade_date</th>\n", "      <th>农林牧渔</th>\n", "      <th>基础化工</th>\n", "      <th>钢铁</th>\n", "      <th>有色金属</th>\n", "      <th>电子</th>\n", "      <th>家用电器</th>\n", "      <th>食品饮料</th>\n", "      <th>纺织服饰</th>\n", "      <th>轻工制造</th>\n", "      <th>...</th>\n", "      <th>传媒</th>\n", "      <th>通信</th>\n", "      <th>银行</th>\n", "      <th>非银金融</th>\n", "      <th>汽车</th>\n", "      <th>机械设备</th>\n", "      <th>煤炭</th>\n", "      <th>石油石化</th>\n", "      <th>环保</th>\n", "      <th>美容护理</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-01-23</td>\n", "      <td>2428.32</td>\n", "      <td>3231.16</td>\n", "      <td>2014.08</td>\n", "      <td>4482.91</td>\n", "      <td>4479.34</td>\n", "      <td>8341.61</td>\n", "      <td>16830.52</td>\n", "      <td>1437.41</td>\n", "      <td>1940.21</td>\n", "      <td>...</td>\n", "      <td>623.95</td>\n", "      <td>3009.1</td>\n", "      <td>3866.82</td>\n", "      <td>1767.52</td>\n", "      <td>6436.09</td>\n", "      <td>1543.1</td>\n", "      <td>2651.48</td>\n", "      <td>2193.25</td>\n", "      <td>1594.21</td>\n", "      <td>4214.6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 32 columns</p>\n", "</div>"], "text/plain": ["   trade_date     农林牧渔     基础化工       钢铁     有色金属       电子     家用电器      食品饮料  \\\n", "0  2025-01-23  2428.32  3231.16  2014.08  4482.91  4479.34  8341.61  16830.52   \n", "\n", "      纺织服饰     轻工制造  ...      传媒      通信       银行     非银金融       汽车    机械设备  \\\n", "0  1437.41  1940.21  ...  623.95  3009.1  3866.82  1767.52  6436.09  1543.1   \n", "\n", "        煤炭     石油石化       环保    美容护理  \n", "0  2651.48  2193.25  1594.21  4214.6  \n", "\n", "[1 rows x 32 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from function_ai.swindex_crap import crap_swindex_from_web, level1_update\n", "crap = crap_swindex_from_web()\n", "level1_update()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取stkresult_3存档数据\n", "from function_ai.StkPick_Func_V7 import get_result_3\n", "result_store = get_result_3(end_date='2024-08-22')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_store.query('ts_code==\"002227.SZ\"')[['PreNowPeak2NowSec_MinRatioMovAvg', 'PreNowPeak2NowSec_MinRatioMovAvg_Date','PreNow_PeakDate']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import get_result_3\n", "result = get_result_3(end_date='2023-11-24')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_pick = result.query('PostPeak_Recent_Neg4_DropDate<=Now_SecDate & '\n", "                           'PostPeak_Recent_Neg4_DropDate>=PreNow_SecDate & '\n", "                           'PostPeak_Recent_Neg4_RecovDate>Now_SecDate & '\n", "                           'PreNowSec_LastDays>=8 & '\n", "                           'PostNowSec_LastDays<=3 & '\n", "                           'PostPeak_Recent_Neg4_RecovDate==Cal_Date').sort_values(by='PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays', ascending=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_pick_2 = result.query('PostPeak_Recent_Neg4_DropDate<=Now_SecDate & '\n", "                                                'PostPeak_Recent_Neg4_DropDate>=PreNow_SecDate & '\n", "                                                'PostPeak_Recent_Neg4_RecovDate>Now_SecDate & '\n", "                                                'PostPeak_Recent_Neg4_RecovDate==Cal_Date & '\n", "                                                'Recent3Day_PGV_MinRollAvg<=PostSecStart_PGV_RollAvg_LowQuntl & '\n", "                                                'PreNowSec_LastDays>=8 & PostNowSec_LastDays<=3 & '                                              \n", "                                                '((Section_StartDate<Now_SecDate & '\n", "                                                'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays>0) | '\n", "                                                '(Section_StartDate==Now_SecDate & '\n", "                                                '10>Peak2Sec_PGV_MinRollAvg2Sec_LastDays>0))')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_pick2 = result_pick.query('Recent3Day_PGV_MinRollAvg<=PostSecStart_PGV_RollAvg_LowQuntl')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取指定行业市值居前股票\n", "from function_ai.Func_Base import get_totalmv\n", "stock_totalmv = get_totalmv(end_date='2024-10-18',industry='农林牧渔', limit_num=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ts_list = stock_totalmv['ts_code'].tolist()\n", "from machine_learn.Func_MacinLn import stkpick_model_predict\n", "result_predict, result = stkpick_model_predict(predict_date='2024-10-18', stk_list=ts_list, model_select='XGB', model_date='2024-10-30', label_style='Label', pretreat='turn')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import get_result_3\n", "result_store = get_result_3(end_date='2024-08-22')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_store2 = result_store.query('Recent_EffPeak_ChangeRate_Percentile<0.2 & PostPreNowPeak_MaxTO_Eff_Date==Cal_Date').sort_values(by=['Recent_EffPeak_ChangeRate_Percentile', 'PostPreNowPeak_MaxTO_Eff_Band'], ascending=[True, False])[['ts_code', 'name', 'industry', 'PreTurn_PeakDate', 'Period_TurnDate', 'Section_StartDate', 'PostPreNowPeak_MaxTO_Eff_Date', 'PostSec_MinTO_Eff_Date', 'PostPreNowPeak_MaxTO_Eff_CoverDays', 'PostSec_MinTO_Eff_CoverDays','Eff_Recent2Previous_MinChange','Recent_EffPeak_ChangeRate', 'Recent_EffPeak_ChangeRate_Percentile',\n", "                   'PostPreNowPeak_MaxTO_Eff_Band', 'PostSec_MinTO_Eff_Band',\n", "                   'Latest_TO_Eff_Band', 'Eff_Recent2Previous_Change',]]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ts_list = ['002693.SZ',\n", " '002583.SZ',\n", " '300085.SZ',\n", " '000158.SZ',\n", " '300377.SZ',\n", " '300489.SZ',\n", " '300010.SZ',\n", " '002094.SZ',\n", " '002542.SZ',\n", " '300152.SZ',\n", " '603038.SH',\n", " '688656.SH',\n", " '000536.SZ',\n", " '603268.SH',\n", " '300077.SZ',\n", " '300339.SZ',\n", " '002628.SZ',\n", " '300746.SZ',\n", " '301297.SZ',\n", " '300341.SZ',\n", " '300355.SZ',\n", " '002312.SZ',\n", " '600839.SH',\n", " '002272.SZ',\n", " '603887.SH',\n", " '002685.SZ',\n", " '300561.SZ',\n", " '600622.SH',\n", " '600340.SH',\n", " '002717.SZ']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_filtered = result_store.query('ts_code in @ts_list').copy()\n", "# 条件1: 近期回补特征\n", "# 选择最近10天内出现回补且回补日期为计算日期的股票\n", "cond1 = (result_filtered.eval(\"(PostNowSec_Recover_TopOpen_Date==Cal_Date & \"\n", "                                      \"(PostPeak_Recent_Neg4_RecovDate==Cal_Date & \"\n", "                                      \"PostPeak_Recent_Neg4_DropDate<=Now_SecDate & \"\n", "                                      \"PostPeak_Recent_Neg4_RecovDays<5)) & \"\n", "                                      \"PreSec_AvgTurnover>2\"))\n", "                                \n", "# 条件2: 区间持续时间特征\n", "# 选择当前区间持续时间大于等于3天且前高到现在的最大换手距今不超过10天的股票\n", "cond2 = result_filtered.eval(\"PreNowSec_LastDays>=2 & \"\n", "                                     \"(0<PostPreNowPeak_MaxTO_Eff2Now_LastDays<10 | \"\n", "                                     \"PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays<5 | \"\n", "                                     \"Days_From_Last_Peak<5 | \"\n", "                                     \"PostPreNowPeak_MaxTO_Eff_Band<0)\")\n", "\n", "# 条件3: 有效高点特征\n", "# 选择近期有效高点变化率在50%分位数以下、有效高点强度大于0.5且前高到现在的最大换手有效带宽大于1的股票\n", "cond3 = result_filtered.eval(\"PostPreNowPeak_MaxTO_Eff_CoverDays>10 & \"\n", "                                \"Eff_Peak_Intensity>0.3 & PostPreNowPeak_U2D_MaxTO_Eff_Band>=1\")\n", "\n", "# 条件4: 有效高点变化趋势\n", "# 选择近期有效高点较前期下降或前高到现在的最大换手有效带宽大于1的股票\n", "cond4 = result_filtered.eval(\"(Eff_Recent2Previous_Change<0 | PostPreNowPeak_MaxTO_Eff_Band>1)\")\n", "                                \n", "# # 条件5: 前高到现在区间排名\n", "# # 选择前高到现在区间移动平均比值排名小于8的股票\n", "# cond5 = result_filtered.eval(\"PreNowPeak2NowSec_RatioMovAvg_NowSecRank<8\")\n", "# cond5 = result_filtered.eval(\"Now_SecDiff<=2 | Now_SecDiff==999\")\n", "            \n", "# 条件6: 均线位置\n", "# 选择区间开始后大部分时间在5日均线上方或刚开始新区间,且前高到区间大部分时间在5日均线下方的股票\n", "cond6 = result_filtered.eval(\"(PostSecStart_Over5MovAvg_Prop>0.5 | PostSecStart_RiseRatio<20) | \"\n", "                            \"Peak2Sec_Und5MovAvg_Prop>0.5\")\n", "\n", "# 合并所有条件\n", "result_filtered_pick = result_filtered[cond1 & cond2 & cond3 & cond4 & cond6]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qs_list = list(set(result_filtered['ts_code'].tolist())-set(result_filtered_pick['ts_code'].tolist()))\n", "qs_name = result_filtered.query('ts_code in @qs_list')[['ts_code', 'name', 'industry']]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# stk_temp = output_droprecov.query('ts_code==\"688369.SH\"').copy()\n", "# 条件1: 近期回补特征\n", "# 选择最近10天内出现回补且回补日期为计算日期的股票\n", "cond1 = (stk_temp.eval(\"(PostNowSec_Recover_TopOpen_Date==Cal_Date & \"\n", "                                      \"(PostPeak_Recent_Neg4_RecovDate==Cal_Date & \"\n", "                                      \"PostPeak_Recent_Neg4_DropDate<=Now_SecDate & \"\n", "                                      \"PostPeak_Recent_Neg4_RecovDays<=8)) & \"\n", "                                      \"PreNowSec_Sec_AvgTurnover>1\"))\n", "                                \n", "# 条件2: 区间持续时间特征\n", "# 选择当前区间持续时间大于等于3天且前高到现在的最大换手距今不超过10天的股票\n", "cond2 = stk_temp.eval(\"PreNowSec_LastDays>=2 & \"\n", "                                     \"(0<PostPreNowPeak_MaxTO_Eff2Now_LastDays<15 | \"\n", "                                     \"PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays<5 | \"\n", "                                     \"Days_From_Last_Peak<5 | \"\n", "                                     \"PostPreNowPeak_MaxTO_Eff_Band<0)\")\n", "# 条件3: 有效高点特征\n", "# 选择近期有效高点变化率在50%分位数以下、有效高点强度大于0.5且前高到现在的最大换手有效带宽大于1的股票\n", "cond3 = stk_temp.eval(\"PostPreNowPeak_MaxTO_Eff_CoverDays>10 & \"\n", "                                \"Eff_Peak_Intensity>0.3 & PostPreNowPeak_U2D_MaxTO_Eff_Band>=1\")\n", "\n", "# 条件4: 有效高点变化趋势\n", "# 选择近期有效高点较前期下降或前高到现在的最大换手有效带宽大于1的股票\n", "cond4 = stk_temp.eval(\"(Eff_Recent2Previous_Change<0 | PostPreNowPeak_MaxTO_Eff_Band>1)\")\n", "                                \n", "# # 条件5: 前高到现在区间排名\n", "# # 选择前高到现在区间移动平均比值排名小于8的股票\n", "# cond5 = result_filtered.eval(\"PreNowPeak2NowSec_RatioMovAvg_NowSecRank<8\")\n", "# cond5 = result_filtered.eval(\"Now_SecDiff<=2 | Now_SecDiff==999\")\n", "            \n", "# 条件6: 均线位置\n", "# 选择区间开始后大部分时间在5日均线上方或刚开始新区间,且前高到区间大部分时间在5日均线下方的股票\n", "cond6 = stk_temp.eval(\"(PostSecStart_Over5MovAvg_Prop>0.5 | PostSecStart_RiseRatio<20) | \"\n", "                            \"Peak2Sec_Und5MovAvg_Prop>0.5\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>name</th>\n", "      <th>industry</th>\n", "      <th>Now_SecDate</th>\n", "      <th>PreSec_AvgTurnover</th>\n", "      <th>PostNowSec_Recover_TopOpen_Date</th>\n", "      <th>PostPeak_Recent_Neg4_RecovDate</th>\n", "      <th>PostPeak_Recent_Neg4_RecovDays</th>\n", "      <th>PreSec_AvgTurnover</th>\n", "      <th>PreNowSec_Turnover2Change_Rank</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3643</th>\n", "      <td>002917.SZ</td>\n", "      <td>金奥博</td>\n", "      <td>基础化工</td>\n", "      <td>2025-01-02</td>\n", "      <td>1.69</td>\n", "      <td>2025-01-09</td>\n", "      <td>2025-01-09</td>\n", "      <td>6</td>\n", "      <td>1.69</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        ts_code name industry Now_SecDate PreSec_AvgTurnover  \\\n", "3643  002917.SZ  金奥博     基础化工  2025-01-02               1.69   \n", "\n", "     PostNowSec_Recover_TopOpen_Date PostPeak_Recent_Neg4_RecovDate  \\\n", "3643                      2025-01-09                     2025-01-09   \n", "\n", "     PostPeak_Recent_Neg4_RecovDays PreSec_AvgTurnover  \\\n", "3643                              6               1.69   \n", "\n", "     PreNowSec_Turnover2Change_Rank  \n", "3643                            4.0  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['ts_code', 'name', 'industry', 'Now_SecDate','PreSec_AvgTurnover', 'PostNowSec_Recover_TopOpen_Date','PostPeak_Recent_Neg4_RecovDate', 'PostPeak_Recent_Neg4_RecovDays', 'PreSec_AvgTurnover','PreNowSec_Turnover2Change_Rank']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Days_From_Last_Peak</th>\n", "      <th>Days_From_Last_Valley</th>\n", "      <th>PreNow_PeakDate</th>\n", "      <th>Section_StartDate</th>\n", "      <th>PreNow_SecDate</th>\n", "      <th>Period_TurnDate</th>\n", "      <th>PreSec_AvgTurnover</th>\n", "      <th>Now_SecDate</th>\n", "      <th>PostPeak_Recent_Neg4_RecovDate</th>\n", "      <th>PostNowSec_Recover_TopOpen_Date</th>\n", "      <th>...</th>\n", "      <th>Recent_EffPeak_ChangeRate_Percentile</th>\n", "      <th>Eff_Peak_Intensity</th>\n", "      <th>PostPreNowPeak_U2D_MaxTO_Eff_Band</th>\n", "      <th>Eff_Recent2Previous_Change</th>\n", "      <th>PostPreNowPeak_MaxTO_Eff_Band</th>\n", "      <th>PostSecStart_RiseRatio</th>\n", "      <th>Section_PeakDate</th>\n", "      <th>Section_StartDate</th>\n", "      <th>PostPeak_Recent_Neg4_RecovDays</th>\n", "      <th>PostPeak_Recent_Neg4_Recov2Now_LastDays</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1539</th>\n", "      <td>13</td>\n", "      <td>43</td>\n", "      <td>2024-11-12</td>\n", "      <td>2025-01-06</td>\n", "      <td>2024-12-09</td>\n", "      <td>2024-02-07</td>\n", "      <td>1.59</td>\n", "      <td>2025-01-06</td>\n", "      <td>2025-01-14</td>\n", "      <td>2025-01-14</td>\n", "      <td>...</td>\n", "      <td>0.023</td>\n", "      <td>0.387</td>\n", "      <td>1.708</td>\n", "      <td>-0.301</td>\n", "      <td>0.501</td>\n", "      <td>6.266</td>\n", "      <td>2024-11-12</td>\n", "      <td>2025-01-06</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 23 columns</p>\n", "</div>"], "text/plain": ["     Days_From_Last_Peak Days_From_Last_Valley PreNow_PeakDate  \\\n", "1539                  13                    43      2024-11-12   \n", "\n", "     Section_StartDate PreNow_SecDate Period_TurnDate PreSec_AvgTurnover  \\\n", "1539        2025-01-06     2024-12-09      2024-02-07               1.59   \n", "\n", "     Now_SecDate PostPeak_Recent_Neg4_RecovDate  \\\n", "1539  2025-01-06                     2025-01-14   \n", "\n", "     PostNowSec_Recover_TopOpen_Date  ...  \\\n", "1539                      2025-01-14  ...   \n", "\n", "     Recent_EffPeak_ChangeRate_Percentile Eff_Peak_Intensity  \\\n", "1539                                0.023              0.387   \n", "\n", "     PostPreNowPeak_U2D_MaxTO_Eff_Band Eff_Recent2Previous_Change  \\\n", "1539                             1.708                     -0.301   \n", "\n", "     PostPreNowPeak_MaxTO_Eff_Band PostSecStart_RiseRatio Section_PeakDate  \\\n", "1539                         0.501                  6.266       2024-11-12   \n", "\n", "     Section_StartDate PostPeak_Recent_Neg4_RecovDays  \\\n", "1539        2025-01-06                              7   \n", "\n", "     PostPeak_Recent_Neg4_Recov2Now_LastDays  \n", "1539                                       0  \n", "\n", "[1 rows x 23 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['Days_From_Last_Peak', 'Days_From_Last_Valley', 'PreNow_PeakDate', 'Section_StartDate', 'PreNow_SecDate', 'Period_TurnDate', 'PreSec_AvgTurnover', 'Now_SecDate','PostPeak_Recent_Neg4_RecovDate','PostNowSec_Recover_TopOpen_Date', 'PostPreNowPeak_MaxTO_Eff2Now_LastDays', 'PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays','PreNowSec_LastDays', 'Recent_EffPeak_ChangeRate_Percentile', 'Eff_Peak_Intensity', 'PostPreNowPeak_U2D_MaxTO_Eff_Band', 'Eff_Recent2Previous_Change', 'PostPreNowPeak_MaxTO_Eff_Band', 'PostSecStart_RiseRatio','Section_PeakDate', 'Section_StartDate','PostPeak_Recent_Neg4_RecovDays', 'PostPeak_Recent_Neg4_Recov2Now_LastDays']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PostPreNowPeak_MaxTO_Eff2Now_LastDays</th>\n", "      <th>PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1539</th>\n", "      <td>13</td>\n", "      <td>13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PostPreNowPeak_MaxTO_Eff2Now_LastDays  \\\n", "1539                                    13   \n", "\n", "     PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays  \n", "1539                                        13  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp[['PostPreNowPeak_MaxTO_Eff2Now_LastDays', 'PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import get_result_3\n", "result_store = get_result_3(end_date='2025-01-24')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 针对bottom_industry左侧筛选逻辑\n", "bottom_industry = ['医药生物', '农林牧渔', '有色金属']\n", "result_store_indus_pick = result_store.query('industry in @bottom_industry & '\n", "                                             'PostPreNowPeak_U2D_MinTO_Eff_Date>PostPreNowPeak_U2D_MaxTO_Eff_Date & '\n", "                                             'PostPreNowPeak_U2D_MinTO_Eff2Now_LastDays<=5').sort_values(\n", "                                                 by='PostPreNowPeak_U2D_MinTO_Eff_CoverDays', ascending=False\n", "                                             )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_store2 = result_store.query('Eff_Latest2Pre3Mean<1').sort_values(by='Eff_Latest2Pre3Mean', ascending=True)[['ts_code', 'name', 'industry', 'Eff_Latest2Pre3Mean', 'Days_From_Last_Peak']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_indus = result_store.query('industry==\"有色金属\"').sort_values(by='PostNowSec_SumRatio', ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:232: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:262: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:232: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, temp_df], ignore_index=True)\n"]}], "source": ["from function_ai.StkPick_ModelFunc import cal_stock_industry_strength\n", "relative_return = cal_stock_industry_strength(stk_code='603928.SH', start_date='2024-12-09', end_date='2025-01-14')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkQuota_Func_V7 import cal_industry_stocks_strength\n", "industry_stocks_strength = cal_industry_stocks_strength(industry_name='食品饮料', start_date='2024-07-24', end_date='2024-09-24')\n", "industry_stocks_strength2 = industry_stocks_strength.query('bottom_date>=\"2024-09-18\" & bottom_date<=\"2024-09-20\"').sort_values(by='bottom_return', ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算section状态\n", "from function_ai.Func_Base import section_stat\n", "section_rise, section_drop, day_list = section_stat(stk_code='002821.SZ', start_date='2024-03-18', end_date='2025-01-21')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试日期： 2025-05-29\n", "行业: 钢铁, 回撤转折日期: 2025-05-29, 转折日期: 2025-03-13, 转折回升幅度: 0.00, 下行天数: 180, 下行幅度: -18.73\n", "行业: 医药生物, 回撤转折日期: 2025-05-14, 转折日期: 2025-04-01, 转折回升幅度: 5.09, 下行天数: 272, 下行幅度: -28.59\n", "行业: 农林牧渔, 回撤转折日期: 2025-05-12, 转折日期: 2025-04-08, 转折回升幅度: 1.47, 下行天数: 290, 下行幅度: -29.81\n", "行业: 有色金属, 回撤转折日期: 2025-04-09, 转折日期: 2025-03-25, 转折回升幅度: 3.82, 下行天数: 177, 下行幅度: -26.87\n", "行业: 机械设备, 回撤转折日期: 2025-04-08, 转折日期: 2025-03-10, 转折回升幅度: 7.68, 下行天数: 27, 下行幅度: -20.36\n", "行业: 传媒, 回撤转折日期: 2025-04-08, 转折日期: 2025-02-14, 转折回升幅度: 6.93, 下行天数: 266, 下行幅度: -44.48\n", "行业: 轻工制造, 回撤转折日期: 2025-04-08, 转折日期: 2024-12-13, 转折回升幅度: 7.68, 下行天数: 146, 下行幅度: -29.49\n", "行业: 纺织服饰, 回撤转折日期: 2025-02-21, 转折日期: 2024-12-16, 转折回升幅度: 9.70, 下行天数: 172, 下行幅度: -33.10\n", "处理股票 600358.SH 时出错: '2025-05-23'\n", "处理股票 300280.SZ 时出错: single positional indexer is out-of-bounds\n", "处理股票 002750.SZ 时出错: single positional indexer is out-of-bounds\n", "处理股票 301033.SZ 时出错: '2025-05-23'\n", "处理股票 000878.SZ 时出错: '2025-05-23'\n", "处理股票 000584.SZ 时出错: '2025-05-23'\n", "处理股票 300307.SZ 时出错: '2025-05-23'\n", "行业排序结果:\n", "1. 机械设备: 71.13, 涨停数/总数:4/519\n", "2. 医药生物: 53.87, 涨停数/总数:7/468\n", "3. 轻工制造: 50.97, 涨停数/总数:2/153\n", "4. 纺织服饰: 42.88, 涨停数/总数:1/104\n", "5. 有色金属: 26.67, 涨停数/总数:0/130\n", "6. 传媒: 16.68, 涨停数/总数:3/127\n", "7. 农林牧渔: 14.16, 涨停数/总数:0/101\n", "8. 钢铁: 0.00, 涨停数/总数:0/45\n", "2025-05-29 筛选数据存储 \n", "存储PullStart股票条目： 980 \n", "存储TurnBreak股票条目： 195\n", "存储成功\n", "PullStart股票行业分布：\n", "           count\n", "industry       \n", "机械设备        335\n", "医药生物        206\n", "传媒          104\n", "轻工制造         97\n", "农林牧渔         78\n", "有色金属         70\n", "纺织服饰         64\n", "钢铁           26\n"]}], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "# trade_date = get_trade_date(start_date='2025-05-26', end_date='2025-06-05')\n", "# trade_date = trade_date[::-1]\n", "trade_date = ['2025-05-28']\n", "for date in trade_date:\n", "    # industry_list = ['房地产', '纺织服饰', '建筑装饰', '建筑材料', '轻工制造', '家用电器', '交通运输', '公用事业', \n", "                    #  '煤炭', '农林牧渔']\n", "    trend_industry_list = ['传媒','有色金属', '轻工制造','农林牧渔','传媒','纺织服饰', '机械设备', '医药生物']\n", "    recent_industry_list = ['钢铁']\n", "    # recent_industry_list = ['非银金融', '家用电器','建筑材料']\n", "    # industry_list = None\n", "    # '2025-03-24', '2025-03-10', '2025-02-28', '2025-02-18', \n", "    end_date, trend_startdate = date, '2025-02-21'\n", "    recent_turndate = '2025-05-23'\n", "    result_df_head, result_df_turn, pull_start_list, result_df_recentindus = track_pullstart_stocks(\n", "        end_date=end_date, trend_startdate=trend_startdate,\n", "        recent_turndate=recent_turndate,\n", "        rise_stop_signal=False,\n", "        industry_list=trend_industry_list,\n", "        limit_num=80, store_mode=True,\n", "        recent_indus=recent_industry_list,\n", "        recentindus_calstartdate='2025-03-18')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["industry_list = list(set(recent_industry_list + trend_industry_list))\n", "pull_start_list_core = pull_start_list.query('industry in @industry_list & Now_PRA_Rate>=50 & PostNowSec_PRA_MaxRate_BreachCount==1 & Peak_Pres_Num>=2'\n", "                                             ).sort_values(by=['PostSecStart_PGV_Max2Mean_Ratio'], ascending=[False]).copy()\n", "if 'pullstart_sort_position' in pull_start_list_core.columns:\n", "            cols = pull_start_list_core.columns.tolist()\n", "            cols.remove('pullstart_sort_position')\n", "            pull_start_list_core = pull_start_list_core[['pullstart_sort_position'] + cols]\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["pull_start_list2 = pull_start_list.query('Peak_Pres_Num>=2')"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"['匠心家居', '凯恩股份', '帝欧家居', '舒华体育', '力诺药包', '金牌家居', '哈尔斯', '梦天家居', '山鹰国际', '浙江正特']\""]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["indus = \"轻工制造\"\n", "str(pull_start_list.query('industry==@indus')['name'].iloc[:10].tolist())"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"['中国中车', '汇川技术', '三一重工', '徐工机械', '恒立液压', '中联重科', '时代电气', '中国通号', '豪迈科技', '华工科技']\""]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["str(result_df_head.query('industry==@indus')['name'].iloc[:10].tolist())"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["['002424.SZ',\n", " '603168.SH',\n", " '600080.SH',\n", " '301063.SZ',\n", " '688056.SH',\n", " '301151.SZ',\n", " '600232.SH',\n", " '301303.SZ',\n", " '300567.SZ',\n", " '301188.SZ',\n", " '002853.SZ',\n", " '300921.SZ',\n", " '000886.SZ',\n", " '600692.SH']"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_core['ts_code'].values.tolist()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "now_overpre1day", "rawType": "int64", "type": "integer"}, {"name": "recentbottom_downcoverdays", "rawType": "int64", "type": "integer"}, {"name": "postcumret_consecutive_over7num", "rawType": "int64", "type": "integer"}, {"name": "nowsec_recent_diff", "rawType": "int64", "type": "integer"}, {"name": "recent2bottom_days", "rawType": "int64", "type": "integer"}, {"name": "postnowsec_risestop_num", "rawType": "int64", "type": "integer"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PGV_Max2Mean_Ratio", "rawType": "float64", "type": "float"}, {"name": "Peak_Pres_Num", "rawType": "float64", "type": "float"}], "ref": "4b56390b-6af8-4c50-a679-e983b8d89ccf", "rows": [["859", "0", "6", "0", "21", "0", "0", "2025-04-28", "2.978", "5.0"]], "shape": {"columns": 9, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>now_overpre1day</th>\n", "      <th>recentbottom_downcoverdays</th>\n", "      <th>postcumret_consecutive_over7num</th>\n", "      <th>nowsec_recent_diff</th>\n", "      <th>recent2bottom_days</th>\n", "      <th>postnowsec_risestop_num</th>\n", "      <th>Now_SecDate</th>\n", "      <th>PostSecStart_PGV_Max2Mean_Ratio</th>\n", "      <th>Peak_Pres_Num</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>859</th>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>21</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2025-04-28</td>\n", "      <td>2.978</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     now_overpre1day  recentbottom_downcoverdays  \\\n", "859                0                           6   \n", "\n", "     postcumret_consecutive_over7num  nowsec_recent_diff  recent2bottom_days  \\\n", "859                                0                  21                   0   \n", "\n", "     postnowsec_risestop_num Now_SecDate  PostSecStart_PGV_Max2Mean_Ratio  \\\n", "859                        0  2025-04-28                            2.978   \n", "\n", "     Peak_Pres_Num  \n", "859            5.0  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp = result_df_head.query('ts_code==\"600980.SH\"')\n", "stk_temp[['now_overpre1day',\n", "            'recentbottom_downcoverdays',\n", "            'postcumret_consecutive_over7num',\n", "            'nowsec_recent_diff', 'recent2bottom_days',\n", "            'postnowsec_risestop_num', 'Now_SecDate', 'PostSecStart_PGV_Max2Mean_Ratio', 'Peak_Pres_Num']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combin_list = list(set(result_df_turn['ts_code'].values.tolist() + result_df_recentindus['ts_code'].values.tolist()))\n", "result_df_combin = result_df_head.query('ts_code in @combin_list')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pull_start_list2 = pull_start_list[['name', 'recent_bottom_date', 'nowsec_recent_diff','Now_SecDate', 'recent2bottom_days']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_df_turn2 = result_df_turn[['ts_code', 'name', 'industry', 'PreNowPeak2Now_LastDays', \n", "                                  'PreNowPeak2Now_SumRatio', 'PreNow_PeakDate', 'Now_SecDate'\n", "                                  ]].sort_values(\n", "                                      by=['industry', 'PreNowPeak2Now_LastDays', 'PreNowPeak2Now_SumRatio'],\n", "                                      ascending=[True, True, False]\n", "                                  )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_df_sorted = result_df_head.sort_values(\n", "        by=['postcumret_now2avgturnover_ratio',\n", "            'PostNowSec_Max2PreNowPeak_PRV_Diff'], ascending=[True, False])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_turn_list = result_df_turn['ts_code'].values.tolist()\n", "result_df_maxrate = result_df_head.query('ts_code in @df_turn_list')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pull_tslist = pull_start_list.query('industry==\"纺织服饰\"')['ts_code'].values.tolist()\n", "result_df_recentindus2 = result_df_recentindus.query('ts_code in @pull_tslist')[['name', 'PostSecStart_SumRatio', 'PostSecStart_Over7Num']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_df_turn_indus = result_df_head.query('industry==\"机械设备\" & Now_SecDate==\"2025-02-28\" & NowSec_PRA_FirstBreach_Date!=\"-\"').sort_values(by='PostSecStart_SumRatio', ascending=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>secstart_minret_diff</th>\n", "      <th>Now_SecDate</th>\n", "      <th>postcumret_consecutive_over7num</th>\n", "      <th>Section_StartDate</th>\n", "      <th>MinCumRet_Date</th>\n", "      <th>NowSec_LowBand_CoverDays</th>\n", "      <th>NowSec2SecStart_Ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1024</th>\n", "      <td>0</td>\n", "      <td>2025-02-18</td>\n", "      <td>3</td>\n", "      <td>2025-01-06</td>\n", "      <td>2025-01-06</td>\n", "      <td>5.0</td>\n", "      <td>19.35</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      secstart_minret_diff Now_SecDate  postcumret_consecutive_over7num  \\\n", "1024                     0  2025-02-18                                3   \n", "\n", "     Section_StartDate MinCumRet_Date  NowSec_LowBand_CoverDays  \\\n", "1024        2025-01-06     2025-01-06                       5.0   \n", "\n", "      NowSec2SecStart_Ratio  \n", "1024                  19.35  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp = result_df_head.query('ts_code==\"002639.SZ\"')\n", "stk_temp[['secstart_minret_diff', 'Now_SecDate', 'postcumret_consecutive_over7num',\n", "                                         'Section_StartDate', 'MinCumRet_Date',\n", "                                         'NowSec_LowBand_CoverDays','NowSec2SecStart_Ratio']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>name</th>\n", "      <th>industry</th>\n", "      <th>cum_rel_return</th>\n", "      <th>relative_bottom_return</th>\n", "      <th>cumret2now_return</th>\n", "      <th>relative_bottom_days</th>\n", "      <th>relative_bottom_date</th>\n", "      <th>MinCumRet_Date</th>\n", "      <th>relative_bottom_max_diff</th>\n", "      <th>...</th>\n", "      <th>PostTurn_Peak2Turn_PRV_Diff</th>\n", "      <th>PostSecStart_Peak2Start_PRV_Diff</th>\n", "      <th>secstart_diff</th>\n", "      <th>nowsec_recent_diff</th>\n", "      <th>nowsec_former_diff</th>\n", "      <th>relative_bottom_date_diff</th>\n", "      <th>nowsec_minret_diff</th>\n", "      <th>secstart_minret_diff</th>\n", "      <th>PostNowSec_AvgRatio_Pre</th>\n", "      <th>In_TurnBreak</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1024</th>\n", "      <td>002639.SZ</td>\n", "      <td>雪人股份</td>\n", "      <td>机械设备</td>\n", "      <td>14.89</td>\n", "      <td>18.03</td>\n", "      <td>15.23</td>\n", "      <td>10</td>\n", "      <td>2025-03-11</td>\n", "      <td>2025-01-06</td>\n", "      <td>-40.43</td>\n", "      <td>...</td>\n", "      <td>11.136</td>\n", "      <td>2.291</td>\n", "      <td>4</td>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>46</td>\n", "      <td>25</td>\n", "      <td>0</td>\n", "      <td>0.306</td>\n", "      <td>99</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 885 columns</p>\n", "</div>"], "text/plain": ["        ts_code  name industry  cum_rel_return  relative_bottom_return  \\\n", "1024  002639.SZ  雪人股份     机械设备           14.89                   18.03   \n", "\n", "      cumret2now_return  relative_bottom_days relative_bottom_date  \\\n", "1024              15.23                    10           2025-03-11   \n", "\n", "     MinCumRet_Date  relative_bottom_max_diff  ...  \\\n", "1024     2025-01-06                    -40.43  ...   \n", "\n", "      PostTurn_Peak2Turn_PRV_Diff  PostSecStart_Peak2Start_PRV_Diff  \\\n", "1024                       11.136                             2.291   \n", "\n", "      secstart_diff  nowsec_recent_diff  nowsec_former_diff  \\\n", "1024              4                  24                   0   \n", "\n", "      relative_bottom_date_diff nowsec_minret_diff  secstart_minret_diff  \\\n", "1024                         46                 25                     0   \n", "\n", "      PostNowSec_AvgRatio_Pre  In_TurnBreak  \n", "1024                    0.306            99  \n", "\n", "[1 rows x 885 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["cumret_threshold = -10\n", "stk_temp.query(\n", "    # 'postcumret_consecutive_over7num<3 & '\n", "            'rise_stop_flag==1 & '\n", "            # 'now_daily_ratio>5 & '\n", "            # 'Now_Over3Mean==\"True\" & '\n", "            # 'NowSec_PGV_MaxRollAvg_UpCoverDays>40 & '\n", "            'PostNowSec_Max2PreNowPeak_PRV_Diff>0.8 & '\n", "            'post_lateturn_over5num<=2'\n", "           )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>nowsec_former_diff</th>\n", "      <th>post_lateturn_over5num</th>\n", "      <th>recent5day_over7num</th>\n", "      <th>recent5day_risestop_num</th>\n", "      <th>PostNowSec_Over7Num</th>\n", "      <th>PostNowSec_Max2PreNowPeak_PRV_Diff</th>\n", "      <th>PostNowSec_MaxTurnover</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>918</th>\n", "      <td>3</td>\n", "      <td>4.0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>4.0</td>\n", "      <td>4.605</td>\n", "      <td>4.264</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     nowsec_former_diff  post_lateturn_over5num  recent5day_over7num  \\\n", "918                   3                     4.0                    3   \n", "\n", "     recent5day_risestop_num  PostNowSec_Over7Num  \\\n", "918                        3                  4.0   \n", "\n", "     PostNowSec_Max2PreNowPeak_PRV_Diff  PostNowSec_MaxTurnover  \n", "918                               4.605                   4.264  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp = result_df_head.query('ts_code==\"600403.SH\"')\n", "stk_temp[['nowsec_former_diff', 'post_lateturn_over5num', 'recent5day_over7num', 'recent5day_risestop_num','PostNowSec_Over7Num', 'PostNowSec_Max2PreNowPeak_PRV_Diff', 'PostNowSec_MaxTurnover']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pull_start_list2 = pull_start_list[['ts_code', 'name', 'industry', \n", "                                    'Target_Price', 'Target_Ratio',\n", "                                    'recent5day_maxsumratio',\n", "                                    'SecStart_PRA_Breach_Count',\n", "                                    'NowSec_PRA_Breach_Count',\n", "                                    'NowSec_PRA_LastBreach_ContinuousDays',\n", "                                    'SecStart_PRA_LastBreach_ContinuousDays',\n", "                                    'Now_SecDate', 'Period_TurnDate', 'Section_PeakDate',\n", "                                    'NowSec_PRA2Close_CoverDays_Diff', \n", "                                    'NowSec_PGV_MaxRollAvg_UpCoverDays', 'NowSec_MaxClose_UpCoverDays',\n", "                                    'NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio',\n", "                                    'Now_PRA_Percentile_PostSectionPeak',\n", "                                    'NowSec_MaxPRA_Percentile_PostSectionPeak',\n", "                                    'NowSec_MaxPRA_Percentile_PostTurn',\n", "                                    'PostNowSec_PRA_MaxRate',\n", "                                    'PostNowSec_PRA_MaxRate_Date',\n", "                                    'PostSecStart_PRA_MaxRate',\n", "                                    'PostSecStart_PRA_MaxRate_Date',\n", "                                    'Now_PRA_Rate',\n", "                                    'PostNowSec_PRA_MaxRate_BreachCount',\n", "                                    'PostSecStart_PRA_MaxRate_BreachCount',\n", "                                    'PostNowSec_MaxRate_PRA_Percentile',\n", "                                    'PostNowSec_MaxRate2Now_LastDays',\n", "                                    'PostNowSec_LastDays',\n", "                                    'SecPeak2NowSec_PRA_UpBand',\n", "                                    'SecPeak2NowSec_PRA_LowBand',\n", "                                    'Turn2NowSec_PRA_UpBand',\n", "                                    'Turn2NowSec_PRA_LowBand',\n", "                                    'NowSec_PGV_MaxRollAvg_UpCoverDays',\n", "                                    'PostNowSec_Max2PreNowPeak_PRV_Diff',\n", "                                    'PostNowSec_MaxRate_Post2Pre_DiffRatio',\n", "                                    'SecStart_PRA_FirstBreach_Date',\n", "                                    'SecStart_FirstBreach2Now_LastDays',\n", "                                    'NowSec_PRA_FirstBreach_Date',\n", "                                    'NowSec_FirstBreach2Now_LastDays',]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indus_list = ['煤炭', '机械设备', '有色金属']\n", "result_df2 = result_df.query('industry in @indus_list')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_df_turn2 = result_df_turn.query('industry==\"非银金融\"')[['ts_code', 'name', 'industry', \n", "                                    'Now_SecDate', 'Period_TurnDate', 'Section_PeakDate',\n", "                                    'NowSec_PRA2Close_CoverDays_Diff',\n", "                                    'NowSec_PGV_MaxRollAvg_UpCoverDays',\n", "                                    'NowSec_MaxClose_UpCoverDays',\n", "                                    'NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio',\n", "                                    'cumret2now_return',\n", "                                    'Now_PRA_Percentile_PostSectionPeak',\n", "                                    'NowSec_MaxPRA_Percentile_PostSectionPeak',\n", "                                    'NowSec_MaxPRA_Percentile_PostTurn',\n", "                                    'PostNowSec_PRA_MaxRate',\n", "                                    'PostNowSec_PRA_MaxRate_Date',\n", "                                    'PostSecStart_PRA_MaxRate',\n", "                                    'PostSecStart_PRA_MaxRate_Date',\n", "                                    'Now_PRA_Rate',\n", "                                    'PostNowSec_PRA_MaxRate_BreachCount',\n", "                                    'PostSecStart_PRA_MaxRate_BreachCount',\n", "                                    'PostNowSec_MaxRate_PRA_Percentile',\n", "                                    'PostNowSec_MaxRate2Now_LastDays',\n", "                                    'PostNowSec_LastDays',\n", "                                    'SecPeak2NowSec_PRA_UpBand',\n", "                                    'SecPeak2NowSec_PRA_LowBand',\n", "                                    'Turn2NowSec_PRA_UpBand',\n", "                                    'Turn2NowSec_PRA_LowBand',\n", "                                    'NowSec_PGV_MaxRollAvg_UpCoverDays',\n", "                                    'PostNowSec_Max2PreNowPeak_PRV_Diff',\n", "                                    'PostNowSec_MaxRate_Post2Pre_DiffRatio',\n", "                                    'SecStart_PRA_FirstBreach_Date',\n", "                                    'SecStart_FirstBreach2Now_LastDays',\n", "                                    'NowSec_PRA_FirstBreach_Date',\n", "                                    'NowSec_FirstBreach2Now_LastDays']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_df_indus = result_df.query('industry==\"煤炭\"').sort_values(by='NowSec_PRA2Close_CoverDays_Diff', ascending=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>relative_bottom_date</th>\n", "      <th>PostSecStart_PeakDate</th>\n", "      <th>MinCumRet_Date</th>\n", "      <th>secstart_minret_diff</th>\n", "      <th>nowsec_minret_diff</th>\n", "      <th>NowSec_LowBand_CoverDays</th>\n", "      <th>NowSec2SecStart_Ratio</th>\n", "      <th>Now_SecDate</th>\n", "      <th>Section_StartDate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1016</th>\n", "      <td>2025-01-03</td>\n", "      <td>2025-03-19</td>\n", "      <td>2025-03-06</td>\n", "      <td>33</td>\n", "      <td>12</td>\n", "      <td>7.0</td>\n", "      <td>36.23</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-01-10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     relative_bottom_date PostSecStart_PeakDate MinCumRet_Date  \\\n", "1016           2025-01-03            2025-03-19     2025-03-06   \n", "\n", "      secstart_minret_diff  nowsec_minret_diff  NowSec_LowBand_CoverDays  \\\n", "1016                    33                  12                       7.0   \n", "\n", "      NowSec2SecStart_Ratio Now_SecDate Section_StartDate  \n", "1016                  36.23  2025-03-24        2025-01-10  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp = result_df.query('ts_code==\"600403.SH\"')\n", "stk_temp[['relative_bottom_date','PostSecStart_PeakDate', 'MinCumRet_Date', 'secstart_minret_diff', 'nowsec_minret_diff', 'NowSec_LowBand_CoverDays','NowSec2SecStart_Ratio', 'Now_SecDate', 'Section_StartDate']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["['600981.SH', '301558.SZ', '603090.SH', '600421.SH', '603082.SH', '002712.SZ']"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list['ts_code'].values.tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py:428: FutureWarning: DataFrame.applymap has been deprecated. Use DataFrame.map instead.\n", "  is_string = df.applymap(lambda x: isinstance(x, str))\n"]}], "source": ["from machine_learn.Func_MacinLn import stkpick_model_predict\n", "result_predict, result_predict_all = stkpick_model_predict(predict_date='2025-03-25', stk_list=pull_start_list['ts_code'].tolist(), model_select='XGB', model_date='2024-07-08', label_style='Ratio', pretreat='none')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stk_temp = industry_stocks_strength.query('ts_code==\"600611.SH\"')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>stock_name</th>\n", "      <th>industry</th>\n", "      <th>cum_rel_return</th>\n", "      <th>bottom_return</th>\n", "      <th>bottom_days</th>\n", "      <th>bottom_date</th>\n", "      <th>bottom_max_diff</th>\n", "      <th>postbottom_over7num</th>\n", "      <th>consecutive_over7num</th>\n", "      <th>...</th>\n", "      <th>BreakPreNowPeak_Ratio</th>\n", "      <th>PostTurn_RiseRatio</th>\n", "      <th>PostSecStart_SumRatio</th>\n", "      <th>DownConsecutive2Now_LastDays</th>\n", "      <th>PostNowSec_LastDays</th>\n", "      <th>PostNowSec_SumRatio</th>\n", "      <th>secstart_diff</th>\n", "      <th>nowsec_diff</th>\n", "      <th>nowsec_minret_diff</th>\n", "      <th>PostNowSec_AvgRatio_Pre</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>600611.SH</td>\n", "      <td>大众交通</td>\n", "      <td>交通运输</td>\n", "      <td>19.29</td>\n", "      <td>23.32</td>\n", "      <td>22</td>\n", "      <td>2024-06-06</td>\n", "      <td>-6.62</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>20.677</td>\n", "      <td>18.45</td>\n", "      <td>None</td>\n", "      <td>23.0</td>\n", "      <td>18.45</td>\n", "      <td>21</td>\n", "      <td>21</td>\n", "      <td>6.0</td>\n", "      <td>0.438</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 53 columns</p>\n", "</div>"], "text/plain": ["     ts_code stock_name industry  cum_rel_return  bottom_return  bottom_days  \\\n", "4  600611.SH       大众交通     交通运输           19.29          23.32           22   \n", "\n", "  bottom_date  bottom_max_diff  postbottom_over7num  consecutive_over7num  \\\n", "4  2024-06-06            -6.62                    1                     1   \n", "\n", "   ...  BreakPreNowPeak_Ratio  PostTurn_RiseRatio  PostSecStart_SumRatio  \\\n", "4  ...                   None              20.677                  18.45   \n", "\n", "   DownConsecutive2Now_LastDays PostNowSec_LastDays  PostNowSec_SumRatio  \\\n", "4                          None                23.0                18.45   \n", "\n", "   secstart_diff  nowsec_diff  nowsec_minret_diff  PostNowSec_AvgRatio_Pre  \n", "4             21           21                 6.0                    0.438  \n", "\n", "[1 rows x 53 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp.query( \n", "            '(recent3day_mindailyratio<0 | postbottom_over7num==1)')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pull_start_list2 = pull_start_list.query('PostNowSec_Max2PreNowPeak_PRV_Diff>1')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pull_start_list = industry_stocks_strength.query('(postbottom_over7num==1 & Now_DayRatio>7)')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>stock_name</th>\n", "      <th>industry</th>\n", "      <th>cum_rel_return</th>\n", "      <th>bottom_return</th>\n", "      <th>bottom_days</th>\n", "      <th>bottom_date</th>\n", "      <th>bottom_max_diff</th>\n", "      <th>MinCumRet_Date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>315</th>\n", "      <td>000017.SZ</td>\n", "      <td>深中华A</td>\n", "      <td>纺织服饰</td>\n", "      <td>1.26</td>\n", "      <td>10.23</td>\n", "      <td>171</td>\n", "      <td>2023-04-25</td>\n", "      <td>-17.15</td>\n", "      <td>2023-12-28</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       ts_code stock_name industry  cum_rel_return  bottom_return  \\\n", "315  000017.SZ       深中华A     纺织服饰            1.26          10.23   \n", "\n", "     bottom_days bottom_date  bottom_max_diff MinCumRet_Date  \n", "315          171  2023-04-25           -17.15     2023-12-28  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["industry_stocks_strength.query('ts_code==\"000017.SZ\"')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stk_list = ['601658.SH','601328.SH', '000002.SZ', '600463.SH']\n", "result_pick = result_store.query('ts_code in @stk_list')\n", "result_pick2 = result_pick[['name','PreNowBottom2PostNowPeak_STrend', 'PreNowBottom2PostNowPeak_STrend_UpDev', 'PreNowBottom2PostNowPeak_STrend_LowDev', 'PostPreNowPeak_U2D_MaxTO_Eff_Date', 'PostPreNowPeak_U2D_MinTO_Eff_Date',\n", "                   'PostPreNowPeak_U2D_MinTO_Eff2Now_LastDays',\n", "                   'PostPreNowPeak_U2D_MinTO_Eff_CoverDays', 'PreNowSec_Turnover2Change_Rank',\n", "                   'PreTurn_PeakDate','Period_TurnDate','PreNow_PeakDate', 'PostSec_MinTO_Eff_Date','Eff_Recent2Previous_MinChange', 'Eff_Recent2Previous_Change', 'Is_LowBound_Oscillation','PostPreNowPeak_MaxTO_Eff_CoverDays','Recent_EffPeak_ChangeRate','Recent_EffPeak_ChangeRate_Percentile']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_ModelFunc import cal_target_pgv\n", "in_target_pgv, out_target_pgv = cal_target_pgv(stk_list=['601918.SH', '600121.SH','600490.SH'], check_date='2025-03-14')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "double_check_pullstart_stocks() got an unexpected keyword argument 'index_turndate'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[1], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mfunction_ai\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mStkPick_ModelFunc\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m double_check_pullstart_stocks\n\u001b[1;32m      2\u001b[0m industry_list \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m国防军工\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m商贸零售\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m----> 3\u001b[0m double_check_result \u001b[38;5;241m=\u001b[39m double_check_pullstart_stocks(double_checkdate\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2025-02-26\u001b[39m\u001b[38;5;124m'\u001b[39m, start_date\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2025-02-20\u001b[39m\u001b[38;5;124m'\u001b[39m, \n\u001b[1;32m      4\u001b[0m                                                     end_date\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2025-02-26\u001b[39m\u001b[38;5;124m'\u001b[39m, index_turndate\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2025-02-21\u001b[39m\u001b[38;5;124m'\u001b[39m, \n\u001b[1;32m      5\u001b[0m                                                     class_type\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, industry_list\u001b[38;5;241m=\u001b[39mindustry_list)\n", "\u001b[0;31mTypeError\u001b[0m: double_check_pullstart_stocks() got an unexpected keyword argument 'index_turndate'"]}], "source": ["from function_ai.StkPick_ModelFunc import double_check_pullstart_stocks\n", "industry_list = ['国防军工', '商贸零售']\n", "double_check_result = double_check_pullstart_stocks(double_checkdate='2025-02-26', start_date='2025-02-20', \n", "                                                    end_date='2025-02-26',\n", "                                                    class_type=None, industry_list=industry_list)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试日期： 2025-06-09\n", "行业: 医药生物, 回撤转折日期: 2025-06-06, 转折日期: 2025-06-04, 转折回升幅度: 1.67, 下行天数: 272, 下行幅度: -29.01\n", "行业: 钢铁, 回撤转折日期: 2025-06-03, 转折日期: 2025-03-13, 转折回升幅度: 0.76, 下行天数: 180, 下行幅度: -18.68\n", "行业: 非银金融, 回撤转折日期: 2025-04-16, 转折日期: 2025-05-14, 转折回升幅度: 3.96, 下行天数: 107, 下行幅度: -19.26\n", "行业: 家用电器, 回撤转折日期: 2025-04-14, 转折日期: 2025-01-09, 转折回升幅度: 3.02, 下行天数: 29, 下行幅度: -15.56\n", "行业: 汽车, 回撤转折日期: 2025-04-09, 转折日期: 2025-03-20, 转折回升幅度: 7.68, 下行天数: 50, 下行幅度: -22.47\n", "行业: 机械设备, 回撤转折日期: 2025-04-08, 转折日期: 2025-03-10, 转折回升幅度: 6.40, 下行天数: 27, 下行幅度: -20.20\n", "行业: 计算机, 回撤转折日期: 2025-04-08, 转折日期: 2025-03-06, 转折回升幅度: 5.14, 下行天数: 290, 下行幅度: -39.91\n", "行业: 轻工制造, 回撤转折日期: 2025-04-08, 转折日期: 2024-12-13, 转折回升幅度: 8.63, 下行天数: 146, 下行幅度: -29.24\n", "处理股票 000403.SZ 时出错: single positional indexer is out-of-bounds\n", "处理股票 002750.SZ 时出错: '2025-06-03'\n", "处理股票 002826.SZ 时出错: '2025-06-03'\n", "处理股票 301033.SZ 时出错: '2025-06-03'\n", "处理股票 000584.SZ 时出错: '2025-06-03'\n", "处理股票 300307.SZ 时出错: '2025-06-03'\n", "处理股票 600960.SH 时出错: '2025-06-03'\n", "处理股票 603023.SH 时出错: '2025-06-03'\n", "处理股票 603306.SH 时出错: '2025-06-03'\n", "处理股票 603003.SH 时出错: '2025-06-03'\n", "处理股票 603019.SH 时出错: '2025-06-03'\n", "处理股票 300691.SZ 时出错: '2025-06-03'\n", "处理股票 603226.SH 时出错: '2025-06-03'\n", "处理股票 000627.SZ 时出错: '2025-06-03'\n", "行业排序结果:\n", "1. 机械设备: 67.10, 涨停数/总数:5/519\n", "2. 轻工制造: 64.05, 涨停数/总数:6/152\n", "3. 汽车: 56.70, 涨停数/总数:2/263\n", "4. 计算机: 46.74, 涨停数/总数:0/331\n", "5. 家用电器: 27.23, 涨停数/总数:1/95\n", "6. 医药生物: 22.30, 涨停数/总数:14/466\n", "7. 非银金融: 11.70, 涨停数/总数:3/82\n", "8. 钢铁: 2.21, 涨停数/总数:1/45\n", "查询数据时发生错误: (pymysql.err.ProgrammingError) (1064, \"You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 2\")\n", "[SQL: select * from stocksfit.stkpick_pullstart \n", "                                    where Check_Date='2025-06-09')\n", "                                    ]\n", "(Background on this error at: https://sqlalche.me/e/14/f405)\n", "2025-06-09 筛选数据存储 \n", "存储PullStart股票条目： 655 \n", "存储TurnBreak股票条目： 221\n", "存储成功\n", "PullStart股票行业分布：\n", "           count\n", "industry       \n", "机械设备        261\n", "汽车          135\n", "计算机          82\n", "轻工制造         50\n", "家用电器         48\n", "医药生物         32\n", "钢铁           25\n", "非银金融         22\n"]}], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "trade_date = ['2025-06-09']\n", "for date in trade_date:\n", "    # industry_list = ['房地产', '纺织服饰', '建筑装饰', '建筑材料', '轻工制造', '家用电器', '交通运输', '公用事业', \n", "                    #  '煤炭', '农林牧渔']\n", "    trend_industry_list = ['轻工制造','汽车','机械设备', '计算机','非银金融','家用电器']\n", "    # recent_industry_list = ['房地产', '商贸零售']\n", "    recent_industry_list = ['医药生物','钢铁']\n", "    # industry_list = None\n", "    # '2025-03-24', '2025-03-10', '2025-02-28', '2025-02-18', \n", "    end_date, trend_startdate = date, '2025-02-18'\n", "    recent_turndate = '2025-06-03'\n", "    result_df_head, result_df_turn, pull_start_list, result_df_recentindus = track_pullstart_stocks(\n", "        end_date=end_date, trend_startdate=trend_startdate,\n", "        recent_turndate=recent_turndate,\n", "        industry_list=trend_industry_list,\n", "        limit_num=80, store_mode=True,\n", "        recent_indus=recent_industry_list,\n", "        recentindus_calstartdate='2025-02-05',\n", "        rise_stop_signal=False)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["industry_list = ['医药生物','汽车','轻工制造','机械设备','非银金融']\n", "pull_start_list_core = pull_start_list.query('industry in @industry_list & Now_PRA_Rate>=50 & PostNowSec_PRA_MaxRate_BreachCount==1'\n", "                                             ).sort_values(by=['industry', 'PostSecStart_PGV_Max2Mean_Ratio'], ascending=[True, False])\n", "if 'pullstart_sort_position' in pull_start_list_core.columns:\n", "            cols = pull_start_list_core.columns.tolist()\n", "            cols.remove('pullstart_sort_position')\n", "            pull_start_list_core = pull_start_list_core[['pullstart_sort_position'] + cols]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ts_code", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "industry", "rawType": "category", "type": "unknown"}, {"name": "SecConcave_RatioBand", "rawType": "float64", "type": "float"}, {"name": "SecConcave_LastDays", "rawType": "float64", "type": "float"}, {"name": "SecConcave_AftBottom_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_RiseRatio", "rawType": "float64", "type": "float"}, {"name": "Section_StartDate", "rawType": "object", "type": "string"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "Period_TurnDate", "rawType": "object", "type": "string"}, {"name": "Section_PeakDate", "rawType": "object", "type": "string"}, {"name": "Peak2Sec_LastDays", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PGV_MinRollAvg_Date", "rawType": "object", "type": "string"}, {"name": "PostSecPeak_MaxTO_Eff_Date", "rawType": "object", "type": "string"}, {"name": "PostSecPeak_PGV_MinRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Sec_MaxDrop_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Sec_MaxDrop_SumRatio", "rawType": "float64", "type": "float"}, {"name": "Target_Ratio", "rawType": "float64", "type": "float"}, {"name": "SecStart_PRA_Breach_Count", "rawType": "float64", "type": "float"}, {"name": "NowSec_PRA_Breach_Count", "rawType": "float64", "type": "float"}, {"name": "NowSec_PRA_LastBreach_ContinuousDays", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_LastDays", "rawType": "float64", "type": "float"}, {"name": "Now_PGV_RollAvg_CoverDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PRA_MaxRate", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PRA_MaxRate_Date", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PRA_MaxRate", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PRA_MaxRate_Date", "rawType": "object", "type": "string"}, {"name": "NowSec_PRA2Close_CoverDays_Diff", "rawType": "float64", "type": "float"}, {"name": "NowSec_PGV_MaxRollAvg_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxClose_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio", "rawType": "float64", "type": "float"}, {"name": "Now_PRA_Percentile_PostSectionPeak", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxPRA_Percentile_PostSectionPeak", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxPRA_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "Now_PRA_Rate", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PRA_MaxRate_BreachCount", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PRA_MaxRate_BreachCount", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxRate_PRA_Percentile", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxRate2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "SecPeak2NowSec_PRA_UpBand", "rawType": "float64", "type": "float"}, {"name": "SecPeak2NowSec_PRA_LowBand", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_UpBand", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_LowBand", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_Max2PreNowPeak_PRV_Diff", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxRate_Post2Pre_DiffRatio", "rawType": "float64", "type": "float"}, {"name": "SecStart_PRA_FirstBreach_Date", "rawType": "object", "type": "string"}, {"name": "NowSec_PRA_FirstBreach_Date", "rawType": "object", "type": "string"}, {"name": "cum_rel_return", "rawType": "float64", "type": "float"}, {"name": "relative_bottom_return", "rawType": "float64", "type": "float"}, {"name": "cumret2now_return", "rawType": "float64", "type": "float"}, {"name": "relative_bottom_days", "rawType": "int64", "type": "integer"}, {"name": "relative_bottom_date", "rawType": "object", "type": "string"}, {"name": "MinCumRet_Date", "rawType": "object", "type": "string"}, {"name": "relative_bottom_max_diff", "rawType": "float64", "type": "float"}, {"name": "post_lateturn_over5num", "rawType": "int64", "type": "integer"}, {"name": "postcumret_consecutive_over7num", "rawType": "int64", "type": "integer"}, {"name": "postnowsec_risestop_num", "rawType": "int64", "type": "integer"}, {"name": "recent5day_maxsumratio", "rawType": "float64", "type": "float"}, {"name": "postcumret_pre3day_sumratio", "rawType": "float64", "type": "float"}, {"name": "postcumret_max_mov3day_sumratio", "rawType": "float64", "type": "float"}, {"name": "postcumret_maxidx", "rawType": "object", "type": "string"}, {"name": "postcumret_now_turnover_rank", "rawType": "float64", "type": "float"}, {"name": "postcumret_now2avgturnover_ratio", "rawType": "float64", "type": "float"}, {"name": "mincumret2sec_sumratio", "rawType": "float64", "type": "float"}, {"name": "postsec2pre1date_sumratio", "rawType": "float64", "type": "float"}, {"name": "stk_checkperiod_sumratio", "rawType": "float64", "type": "float"}, {"name": "postnowsec2postsec_riseavgratio_diffratio", "rawType": "float64", "type": "float"}, {"name": "peak2recentbottom_lastdays", "rawType": "float64", "type": "float"}, {"name": "peak2recentbottom_sumratio", "rawType": "float64", "type": "float"}, {"name": "peak2recentbottom_avgratio", "rawType": "float64", "type": "float"}, {"name": "secstart2peak_avgratio", "rawType": "float64", "type": "float"}, {"name": "recent_bottom_date", "rawType": "object", "type": "string"}, {"name": "recent2bottom_days", "rawType": "int64", "type": "integer"}, {"name": "recentbottom_und3min", "rawType": "object", "type": "string"}, {"name": "recentbottom_downcoverdays", "rawType": "int64", "type": "integer"}, {"name": "now_daily_ratio", "rawType": "float64", "type": "float"}, {"name": "now_high_ratio", "rawType": "float64", "type": "float"}, {"name": "rise_stop_flag", "rawType": "int64", "type": "integer"}, {"name": "now_overpre1day", "rawType": "int64", "type": "integer"}, {"name": "nowsec_recentturn_diffratio", "rawType": "float64", "type": "float"}, {"name": "PostSec_MinTO_Eff_Date", "rawType": "object", "type": "string"}, {"name": "Period_Valley_GapRatio", "rawType": "float64", "type": "float"}, {"name": "Now_PostSecPeak_VGV_MaxCoverDays", "rawType": "float64", "type": "float"}, {"name": "CoverRatioBand_10", "rawType": "float64", "type": "float"}, {"name": "MinRollAvg_Truncated_Diff", "rawType": "float64", "type": "float"}, {"name": "Now_PGV_RollAvg", "rawType": "float64", "type": "float"}, {"name": "Pre_PreNow_Sec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PGV_RollAvg_VolRange", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_DropSecOver10Days_StartDate", "rawType": "object", "type": "string"}, {"name": "PostTurn2Peak_LastDays", "rawType": "float64", "type": "float"}, {"name": "Now_Spring_Date", "rawType": "object", "type": "string"}, {"name": "PreNow_Rise_MaxContiSum", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_NowSec2MaxRate_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostPeak_Recent_Neg4_RecovDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Recent_Neg4_DropDate", "rawType": "object", "type": "string"}, {"name": "Turn_1stTODate_LastDays", "rawType": "float64", "type": "float"}, {"name": "CoverDays_Aft_PostturnMaxDate", "rawType": "object", "type": "string"}, {"name": "PostSecStart_DailyDropRecov_RecovRatio", "rawType": "float64", "type": "float"}, {"name": "PreSec_Turnover2Rise", "rawType": "float64", "type": "float"}, {"name": "TurnConcave_LastDays", "rawType": "float64", "type": "float"}, {"name": "Post_SecStart_MaxHLRatio", "rawType": "float64", "type": "float"}, {"name": "Recent_MeanRatio", "rawType": "float64", "type": "float"}, {"name": "Narrow_E2N_MaxReturn", "rawType": "float64", "type": "float"}, {"name": "PostSec_Max2PreShock_Ratio", "rawType": "float64", "type": "float"}, {"name": "PreTurn_Period_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "PreSec_MaxTurnover", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_CumSum2IdxRatio_MaxDate", "rawType": "object", "type": "string"}, {"name": "Valley_Pres_Lastdays", "rawType": "float64", "type": "float"}, {"name": "Now_Vol_Trend", "rawType": "object", "type": "string"}, {"name": "PreNowSec_PGV_MeanRollAvg", "rawType": "float64", "type": "float"}, {"name": "Stop_Lose_Price", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Sec_TrendRatio_Diff", "rawType": "float64", "type": "float"}, {"name": "Concave_Break_Days2Now", "rawType": "float64", "type": "float"}, {"name": "PreSec_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_RiseSecOver10Days_StartDate", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PeakGap_LowQuntl", "rawType": "float64", "type": "float"}, {"name": "SecStart_PGV_MaxRollAvg_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MinPeakGap", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_Turnover2Change", "rawType": "float64", "type": "float"}, {"name": "SecStart_PRA2Close_CoverDays_Diff", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_PGV_MinRollAvg2MaxRatio", "rawType": "float64", "type": "float"}, {"name": "PostPeak_Gap2NowDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PGV_Max2Mean_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak2Now_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Sec_Min_SumRatio", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PeakGapValue_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "PostBottom_RiseRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_RollAvg_LowQuntl", "rawType": "float64", "type": "float"}, {"name": "PostPreNowBottom_SlowTrend_Deviation", "rawType": "float64", "type": "float"}, {"name": "PreSecMaxRollAvg_PGV_MeanRollAvg", "rawType": "float64", "type": "float"}, {"name": "Now_Und3Mean", "rawType": "object", "type": "string"}, {"name": "Section_Target_Price", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MedianValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "DownConsecutive_PGVRollAvg_DiffRatio", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_BottomDate", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PeakGap_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_VGV_MinRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Drop2Rise_SecMaxDaysDiv", "rawType": "float64", "type": "float"}, {"name": "NowSec_Recent3DValley_Over_HighQuntl_Num", "rawType": "object", "type": "string"}, {"name": "PreSec_DDR_Quntl", "rawType": "float64", "type": "float"}, {"name": "PostPeak_Recent_Neg4_RecovDate", "rawType": "object", "type": "string"}, {"name": "Sec2Now_PeakDate", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_Peak2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "id", "rawType": "int64", "type": "integer"}, {"name": "PostNowSec_UpConsecutive_MaxLastDays", "rawType": "float64", "type": "float"}, {"name": "PreNow_Rise_BMA_Prop", "rawType": "float64", "type": "float"}, {"name": "PostPreNowBottom_LastDays", "rawType": "float64", "type": "float"}, {"name": "Pre_PreNow_Sec_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MedianValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MedianPeakGap", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxTurnover_Date", "rawType": "object", "type": "string"}, {"name": "PostPreNowPeak_CumSum2IdxRatio_MinDate", "rawType": "object", "type": "string"}, {"name": "PostNowSec_BelowPre1Day_MeanRatio", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_U2D_MaxTO_Eff_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Now_RiseSec_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_BreakRatio", "rawType": "float64", "type": "float"}, {"name": "NowSec_PGV_DownCoverPeriod_Min2Max_DiffRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Recent_Neg4_Recov2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_PeakGapValue_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "PostTurn_BfPeak_MaxDrop", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2Now_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_BelowPre1Day_Prop", "rawType": "float64", "type": "float"}, {"name": "Recent2Day_MeanPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "PreNow_MeanHLRatio", "rawType": "float64", "type": "float"}, {"name": "SecConcave_PGV_MinRollAvg_Date", "rawType": "object", "type": "string"}, {"name": "PostSecMaxRollAvg_PGV_MinRollAvg2Now_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2Now_OverPre1Day_Days", "rawType": "float64", "type": "float"}, {"name": "PreSec_StartDate", "rawType": "object", "type": "string"}, {"name": "Peak3Day_MaxPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "Shrink_Date", "rawType": "object", "type": "string"}, {"name": "PreNowBottom2PostNowPeak_STrend_UpDev", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_MinValleyGap", "rawType": "float64", "type": "float"}, {"name": "Now_PostNowSec_PGV_Desc_Rank", "rawType": "float64", "type": "float"}, {"name": "SecConcave_BfBottom_LastDays", "rawType": "float64", "type": "float"}, {"name": "TurnDiff_Period_AvgTurnover_Div", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_UpConsecutive_Num", "rawType": "float64", "type": "float"}, {"name": "PGV_RollAvg_NowSec2Previous_Change", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_COR_Und2Poxn", "rawType": "float64", "type": "float"}, {"name": "PGV_Now2Pre3Days_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PRA2Close_CoverDays_Diff", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxValleyGap", "rawType": "float64", "type": "float"}, {"name": "PostSec_SOSDate", "rawType": "object", "type": "string"}, {"name": "PostSec_MinTO_Eff", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_COR_Mean", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_DropDayRatio_Min", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MinValleyGap", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_COR_Std", "rawType": "float64", "type": "float"}, {"name": "PostPeak_Recent_Neg4_DropDate", "rawType": "object", "type": "string"}, {"name": "Now_PostSecStart_VGV_RollAvg_Desc_Rank", "rawType": "float64", "type": "float"}, {"name": "UpEff_Latest2Pre3Mean", "rawType": "float64", "type": "float"}, {"name": "TargetRatio_High", "rawType": "object", "type": "string"}, {"name": "area", "rawType": "object", "type": "string"}, {"name": "NowSec_SecConcave_StartDate", "rawType": "object", "type": "string"}, {"name": "PostPeak_Recent_Neg4_Recov2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "CoverDays_BreakDate_RatioBand", "rawType": "float64", "type": "float"}, {"name": "PreNow_PeakDate", "rawType": "object", "type": "string"}, {"name": "PostSecStart_RiseSecOver10Days_EndDate", "rawType": "object", "type": "string"}, {"name": "PostSecPeak_MinDailyRatio", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_ContiOver7Days", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_MaxValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "PostSecWLS_Slope", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MedianPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "Eff_Recent2Previous_Change", "rawType": "float64", "type": "float"}, {"name": "SOS_Date", "rawType": "object", "type": "string"}, {"name": "PostSecMaxRollAvg_MinPGV", "rawType": "float64", "type": "float"}, {"name": "PreTurn_PeakDate", "rawType": "object", "type": "string"}, {"name": "PostTurn_MaxContiSum", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Peak2Now_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PeakGap_LowQuntl", "rawType": "float64", "type": "float"}, {"name": "Now_PostSecPeak_VGV_Desc_Rank", "rawType": "float64", "type": "float"}, {"name": "PGV_RollAvg_Recent2Previous_Change", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2Now_OverPre1Day_Prop", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2NowSec_SlowTrend_Deviation", "rawType": "float64", "type": "float"}, {"name": "PostPreNow_MinDailyRatio_Date", "rawType": "object", "type": "string"}, {"name": "Now_FallSec_Turnover2Drop", "rawType": "float64", "type": "float"}, {"name": "Now_PostPreNowBottom_PGV_RollAvg_Asc_Rank", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MedianPeakGap", "rawType": "float64", "type": "float"}, {"name": "PostPreNowBottom_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "Recent_Mean_ClsOpenRatio", "rawType": "float64", "type": "float"}, {"name": "PrePeak_Over4_Date", "rawType": "object", "type": "string"}, {"name": "PostSecWLS_Deviation", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_DailyDropRecov_MeanRecovDays", "rawType": "float64", "type": "float"}, {"name": "PreSecWLS_R2", "rawType": "float64", "type": "float"}, {"name": "Sec_IndexDiff_Signal", "rawType": "object", "type": "string"}, {"name": "PostSecStart_MovAvg_BiasMean", "rawType": "float64", "type": "float"}, {"name": "Post_PreSecPeak_RecovRatio", "rawType": "float64", "type": "float"}, {"name": "Recent5D_BfPreNow_Volab_Num", "rawType": "float64", "type": "float"}, {"name": "Post_PreSecPeak_MeanDepresDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_RiseRatio", "rawType": "float64", "type": "float"}, {"name": "SecAvgRatio_PreSec2PostSecPeakDrop_Ratio", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "Now_PostSecPeak_VGV_RollAvg_MinCoverDays", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_BelowMinDaily", "rawType": "float64", "type": "float"}, {"name": "DownConsecutive_Start_PGVRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak2Now_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_PGV_MaxRollAvg2Min_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PreTurn_LastPeriod_SecNum", "rawType": "float64", "type": "float"}, {"name": "SecStart_MinClose_DownCoverDays", "rawType": "float64", "type": "float"}, {"name": "Total_Share", "rawType": "float64", "type": "float"}, {"name": "Days_From_Last_Peak", "rawType": "float64", "type": "float"}, {"name": "Post2Pre_PGV_MinRollAvg_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Recent_Neg4_Threshold", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_SecRiseState", "rawType": "float64", "type": "float"}, {"name": "Now_PostTurn_SecDropState", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_MaxPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_ValleyGap_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PGV_MinRollAvg_Date", "rawType": "object", "type": "string"}, {"name": "PostPreNowBottom_UpConsecutive_Num", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_ValleyGapValue_LowQuntl", "rawType": "float64", "type": "float"}, {"name": "NowSec_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MovAvg_BiasStd", "rawType": "float64", "type": "float"}, {"name": "PreSecPeak_Sec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "Now_Period_StartDate", "rawType": "object", "type": "string"}, {"name": "PostSecPeak_DropTrend_Prop", "rawType": "float64", "type": "float"}, {"name": "Now_PostSecPeak_PGV_RollAvg_MinCoverDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_Recover_TopOpen_Date", "rawType": "object", "type": "string"}, {"name": "PreSecPeak_Sec_StartDate", "rawType": "object", "type": "string"}, {"name": "PostSecPeak_MinValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2Now_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PeakGapValue_LowQuntl", "rawType": "float64", "type": "float"}, {"name": "Section_Target_Ratio", "rawType": "float64", "type": "float"}, {"name": "Now_Recent3Mean", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxPGVDate2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Long_Trend", "rawType": "object", "type": "string"}, {"name": "PostSecPeak_StepBack_GapRatio", "rawType": "float64", "type": "float"}, {"name": "PreSec_Adverse2Trend", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Sec_Max_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_U2D_MinTO_Eff_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Now_Over5Min", "rawType": "object", "type": "string"}, {"name": "PostSecMaxRollAvg_DownConsecutive_MaxLastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PeakDate", "rawType": "object", "type": "string"}, {"name": "Now_TO_Eff", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_DropSec_Num", "rawType": "float64", "type": "float"}, {"name": "NowSec_SecConcave_LastDays", "rawType": "float64", "type": "float"}, {"name": "PreTurn_Period_GapRatio", "rawType": "object", "type": "string"}, {"name": "NowSec_PGV_MinRollAvg_DownCoverDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxPeakGap", "rawType": "float64", "type": "float"}, {"name": "PreTurn_LastSec_ExtreRatio", "rawType": "float64", "type": "float"}, {"name": "PostPeak_GapLastDays", "rawType": "float64", "type": "float"}, {"name": "DownConsecutive2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "TurnDiff_Period_AvgRatio_Div", "rawType": "float64", "type": "float"}, {"name": "Now_Period_Lastdays", "rawType": "float64", "type": "float"}, {"name": "Days_From_Last_Valley", "rawType": "float64", "type": "float"}, {"name": "NowSec_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "SecDiff_Sec_LastDays_Div", "rawType": "float64", "type": "float"}, {"name": "Peak_Pres_Lastdays", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_StepBack_LastDays", "rawType": "float64", "type": "float"}, {"name": "PreNow_Rise_MaxContiDays", "rawType": "float64", "type": "float"}, {"name": "Period_Break_Date", "rawType": "object", "type": "string"}, {"name": "TurnDiff_Recoved_Sec_LastDays_Div", "rawType": "float64", "type": "float"}, {"name": "Now_StepBack_State", "rawType": "object", "type": "string"}, {"name": "PostSec_MinTO_Eff_CoverDays", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_PGV_MeanRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_UpConsecutive_MaxLastDays", "rawType": "float64", "type": "float"}, {"name": "PreSec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "PreTurn_Period_Lastdays", "rawType": "float64", "type": "float"}, {"name": "Now_PostSec_SecRise_ClsDiff", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_RiseSecNum", "rawType": "float64", "type": "float"}, {"name": "Narrow_E2N_Days", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_DownConsecutive_Num", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PGV_MinRollAvg2Sec_LastDays", "rawType": "float64", "type": "float"}, {"name": "PreNow_Rise_Recent_ClsOpenRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_MaxDrop", "rawType": "float64", "type": "float"}, {"name": "LongTrend_PeriodNum_Rise", "rawType": "float64", "type": "float"}, {"name": "PreTurnPeak_Sec_StartDate", "rawType": "object", "type": "string"}, {"name": "PreSecPeak_Sec_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MinDailyRatio_Date", "rawType": "object", "type": "string"}, {"name": "PostTurn_Sec_Avg_LastDays", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_MaxValleyGap", "rawType": "float64", "type": "float"}, {"name": "Now_PostSecStart_PGV_RollAvg_Desc_Rank", "rawType": "float64", "type": "float"}, {"name": "PostNow2PreNow_PGV_MeanRollAvg_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Sec_Max_ExtreRatio", "rawType": "float64", "type": "float"}, {"name": "Bottom2Now_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Recent_Neg4_RecovDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_BelowPre1Day_MeanRatio", "rawType": "float64", "type": "float"}, {"name": "TurnDiff_Sec_LastDays_Div", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_MaxValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "Now_Turnover", "rawType": "float64", "type": "float"}, {"name": "PostPreNow_MaxUpEff_Date", "rawType": "object", "type": "string"}, {"name": "PostSec_1stSec_BreakRatio", "rawType": "float64", "type": "float"}, {"name": "LastDrop_ClsOpenRatio_Avg", "rawType": "float64", "type": "float"}, {"name": "Is_More_Volatile", "rawType": "float64", "type": "float"}, {"name": "LastDrop_COR_Ratio", "rawType": "float64", "type": "float"}, {"name": "Turn_1stTODate_Ratio", "rawType": "float64", "type": "float"}, {"name": "Peak2Turn_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Reg_Sum2Std", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxContiAvg", "rawType": "float64", "type": "float"}, {"name": "PostPeak_Recent_Neg4_Threshold", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_Channel_Stability", "rawType": "float64", "type": "float"}, {"name": "Section_PostPeak_MaxDrop", "rawType": "float64", "type": "float"}, {"name": "PreNow_Rise_MaxContiAvg", "rawType": "float64", "type": "float"}, {"name": "CoverDaysDiff_20", "rawType": "float64", "type": "float"}, {"name": "Pre_PreNow_Sec_ExtreRatio", "rawType": "float64", "type": "float"}, {"name": "Peak2Turn_AvgSecDays", "rawType": "float64", "type": "float"}, {"name": "Pre_SecStart_MaxHLRatio", "rawType": "float64", "type": "float"}, {"name": "PreNow_BottomDate", "rawType": "object", "type": "string"}, {"name": "Pre_PreNowSec_Und2ContiDays", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_StepBackDate", "rawType": "object", "type": "string"}, {"name": "PostPreNowPeak_MaxTO_Eff2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Peak2Turn_COR_Und2Poxn", "rawType": "float64", "type": "float"}, {"name": "PreNow_SecDate", "rawType": "object", "type": "string"}, {"name": "PostSecStart_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "PreSecPeak_Sec_LastDays", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_Und5MovAvg_Prop", "rawType": "float64", "type": "float"}, {"name": "PreTurn_Period_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_1stSec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "SecStart_1stTODate2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Eff_Valley_Intensity", "rawType": "float64", "type": "float"}, {"name": "Sec2PreNow_Volab_ProP", "rawType": "float64", "type": "float"}, {"name": "PostPreNowBottomWLS_Deviation", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PreNow2Now_Volab_ProP", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_RiseTrend_Prop", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "Post_SecPeak_RecovCount", "rawType": "float64", "type": "float"}, {"name": "Now_VGV_RollAvg", "rawType": "float64", "type": "float"}, {"name": "Now_PeakGapValue", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_Reg_R2", "rawType": "float64", "type": "float"}, {"name": "PeakN2Y_2Turn_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_COR_Und2Poxn", "rawType": "float64", "type": "float"}, {"name": "PostTurn_MaxContiAvg", "rawType": "float64", "type": "float"}, {"name": "Now_Turnover2Rise_State", "rawType": "float64", "type": "float"}, {"name": "Peak2SecWLS_Deviation", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "PreTurn_Turnover2Rise", "rawType": "float64", "type": "float"}, {"name": "Convex_Break_DropDate", "rawType": "object", "type": "string"}, {"name": "PGVRollAvg_Now2PreSecLowQuntl_Ratio", "rawType": "float64", "type": "float"}, {"name": "Peak_Pres_MeanPrice_Ratio", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_COR_Und2Poxn", "rawType": "float64", "type": "float"}, {"name": "PreNowSec2NowSec_MeanStdSum", "rawType": "float64", "type": "float"}, {"name": "Peak_Pres_Num", "rawType": "float64", "type": "float"}, {"name": "Period_Trend", "rawType": "object", "type": "string"}, {"name": "PostSecStart_MaxDrop_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_ShockDate", "rawType": "object", "type": "string"}, {"name": "PostSecMaxRollAvg_PGV_PostMin_MaxRollAvg2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_Adverse2Trend", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Reg_R2", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_MedianValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "PostPreNowBottom_DownConsecutive_MaxLastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxContiDrop", "rawType": "float64", "type": "float"}, {"name": "Recent_HLRatio_Chg", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "NowSec_SecConcave_TO_Sum", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_HighOver7Num", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_MinPGV2MaxRatio", "rawType": "float64", "type": "float"}, {"name": "PostPreSecPeak_DownConsecutive_Num", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_Und2ContiDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Reg_Lastdays", "rawType": "float64", "type": "float"}, {"name": "Shock_Date", "rawType": "object", "type": "string"}, {"name": "PostSecStart_ValleyGap_LowQuntl", "rawType": "float64", "type": "float"}, {"name": "Eff_Peak_Intensity", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostSec_Peak_PGV_RollAvg_Desc_Rank", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_PGV_MeanRollAvg", "rawType": "float64", "type": "float"}, {"name": "Break_PreSec", "rawType": "object", "type": "string"}, {"name": "SecDiff_Sec_AvgRatio_Div", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "Now_ValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "Now_VGV_RollAvg_CoverDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_COR_Und2Poxn", "rawType": "float64", "type": "float"}, {"name": "Shock_Date_Last", "rawType": "object", "type": "string"}, {"name": "PostSecBottom_MaxValleyGap", "rawType": "object", "type": "string"}, {"name": "PostPreNowPeak_DropTrend_Prop", "rawType": "float64", "type": "float"}, {"name": "Turn_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "Latest_Eff_Peak_Date", "rawType": "object", "type": "string"}, {"name": "PostPreNowBottom_DownConsecutive_Num", "rawType": "float64", "type": "float"}, {"name": "PostSec_RiseSec10Days_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PGV_MeanRollAvg", "rawType": "float64", "type": "float"}, {"name": "PreSec_Reg_R2", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PGV_MinRollAvg2MeanRatio", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_PRA_SectionStart_Rank", "rawType": "float64", "type": "float"}, {"name": "PreTurnPeak_Sec_MaxPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "PreTurn_LastSec_Lastdays", "rawType": "float64", "type": "float"}, {"name": "SecAvgRatio_PreNowSec2PostSecDrop_Ratio", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2NowSec_LastDays", "rawType": "float64", "type": "float"}, {"name": "SecStart2Now_STrend", "rawType": "float64", "type": "float"}, {"name": "Peak2Turn_COR_Mean", "rawType": "float64", "type": "float"}, {"name": "PostTurn_MedianValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "UpConsecutive_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Sec_Max_SumRatio", "rawType": "float64", "type": "float"}, {"name": "Sec_IndexDiff_StartDate", "rawType": "object", "type": "string"}, {"name": "Efficiency_VolatilityRatio", "rawType": "float64", "type": "float"}, {"name": "Peak2SecWLS_R2", "rawType": "float64", "type": "float"}, {"name": "PostTurn_COR_Std", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxPRA_Percentile_PostSectionPeak", "rawType": "float64", "type": "float"}, {"name": "Now_Turnover_Signal", "rawType": "float64", "type": "float"}, {"name": "Latest_TO_Eff_Band", "rawType": "float64", "type": "float"}, {"name": "PostTurn_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "SecStart_1stTODate_Ratio", "rawType": "float64", "type": "float"}, {"name": "Now_LowBand_CoverDays", "rawType": "int64", "type": "integer"}, {"name": "Eff_Avg_Valley_Period", "rawType": "float64", "type": "float"}, {"name": "Now_PGVRollAvg_DownCount", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PGV_MaxRollAvg_Quarter", "rawType": "float64", "type": "float"}, {"name": "Now_PGVRollAvg_UpCount", "rawType": "float64", "type": "float"}, {"name": "NowSec_FirstBreach2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Over5MovAvg_Prop", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_TO_Over10Num", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_DropSec10Days_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "Now_Period_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_BMA_Proportion", "rawType": "float64", "type": "float"}, {"name": "PGVRollAvg_Now2PostSecHighQuntl_Ratio", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PeakGap_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "PostTurn_MaxContiDays", "rawType": "float64", "type": "float"}, {"name": "PreSecWLS_Slope", "rawType": "float64", "type": "float"}, {"name": "PostPeak_Recent_Neg4_DropDate_Close", "rawType": "float64", "type": "float"}, {"name": "TurnDiff_Period_LastDays_Div", "rawType": "float64", "type": "float"}, {"name": "PreNow_Rise_Recent_MaxRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MaxTO_Eff", "rawType": "float64", "type": "float"}, {"name": "Narrow_E2N_MinReturn", "rawType": "float64", "type": "float"}, {"name": "PostTurn_1stSec_SumRatio", "rawType": "float64", "type": "float"}, {"name": "SecStart_PRA_LastBreach_ContinuousDays", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_VGV_MinRollAvg2MaxRatio", "rawType": "float64", "type": "float"}, {"name": "PreSec_Turnover2Drop_State", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PGV_MinRollAvg2MaxRatio", "rawType": "float64", "type": "float"}, {"name": "GapRatio2Sec", "rawType": "float64", "type": "float"}, {"name": "Now_PGV_RollAvg_Rank", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_MinPeakGap_Date", "rawType": "object", "type": "string"}, {"name": "Peak2Sec_OverPre1Day_Days", "rawType": "float64", "type": "float"}, {"name": "PreNowBottom2Now_MeanStdDiff", "rawType": "float64", "type": "float"}, {"name": "SecValley_GapRatio", "rawType": "float64", "type": "float"}, {"name": "PreTurn_Period_R2", "rawType": "float64", "type": "float"}, {"name": "PostTurn_RiseAvg", "rawType": "float64", "type": "float"}, {"name": "PostTurn_SOSDate", "rawType": "object", "type": "string"}, {"name": "PreTurn_LastSec_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MaxRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Over7Num", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Reg_R2", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_ValleyGapValue_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "Recent2Day_MeanValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxVGV", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MaxVGV2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PGV_MaxRollAvg_Date", "rawType": "object", "type": "string"}, {"name": "PostSecStart_MaxVGV_Date", "rawType": "object", "type": "string"}, {"name": "PostSecPeak_SOSDate", "rawType": "object", "type": "string"}, {"name": "BreakShock_Date", "rawType": "object", "type": "string"}, {"name": "Now_Period_SumRatio", "rawType": "float64", "type": "float"}, {"name": "Valley_Pres_MeanPrice", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_MaxPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "Turn2Peak_MaxContiDropDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_Over7Num", "rawType": "float64", "type": "float"}, {"name": "SecConcave_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostPreNow_MinDailyRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_MedianPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_Recover_TopOpen_Open", "rawType": "float64", "type": "float"}, {"name": "SecConcave_StartDate", "rawType": "object", "type": "string"}, {"name": "PostNowSecWLS_Slope", "rawType": "float64", "type": "float"}, {"name": "Turn_Target_Price", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PGVRollAvg_CoverDrop_DiffRatio", "rawType": "float64", "type": "float"}, {"name": "Peak2Turn_MaxContiRise", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_ExtreRatio", "rawType": "float64", "type": "float"}, {"name": "CoverDays_Bf_PostturnMaxDate", "rawType": "object", "type": "string"}, {"name": "PostSecStart_Und2ContiDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_StepBackDate", "rawType": "object", "type": "string"}, {"name": "Now_RiseSec_Turnover2Rise", "rawType": "float64", "type": "float"}, {"name": "PGVRollAvg_Now2PostSecLowQuntl_Ratio", "rawType": "float64", "type": "float"}, {"name": "BfMinRollAvg_PGVRollAvg_DownCount", "rawType": "float64", "type": "float"}, {"name": "PostSec_SecDrop_MaxClsDiff", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_OverPre1Day_Prop", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MinQuntlRatio", "rawType": "float64", "type": "float"}, {"name": "NowSec_Und2ContiDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxPRA_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "Now_MaxSum", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Adverse2Trend", "rawType": "float64", "type": "float"}, {"name": "SecStart_MaxClose_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MedianPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_MedianPeakGap", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_PGV_MinRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_MaxTO_Eff_Band", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_DownConsecutive_Num", "rawType": "float64", "type": "float"}, {"name": "PreNow_Rise_BMA_Days", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2PreNowSec_SlowTrend_Deviation", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "PreTurn_ShockDate", "rawType": "object", "type": "string"}, {"name": "PreSecPeak_Sec_MaxPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "PreTurn_MaxTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MaxRollAvg_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Now_PostNowSec_PGV_MaxCoverDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_Rise_BMA_Days", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_MinPGV2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "UndVolab_LastDays", "rawType": "float64", "type": "float"}, {"name": "Peak2SecWLS_Slope", "rawType": "float64", "type": "float"}, {"name": "PreSecMaxRollAvg_PGV_MaxRollAvg2MinRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_1stSec_Lastdays", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_U2D_MinTO_Eff_Date", "rawType": "object", "type": "string"}, {"name": "Now_ValleyGap", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_COR_Std", "rawType": "float64", "type": "float"}, {"name": "Now_PostSec_SecDropState", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxPeakGap", "rawType": "float64", "type": "float"}, {"name": "PostBottom_Lastdays", "rawType": "float64", "type": "float"}, {"name": "PostSec_StepBack_GapRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSec_StepBack_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MedianValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "Now_PostSec_SecRiseState", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_PGV_MinRollAvg_Date", "rawType": "object", "type": "string"}, {"name": "PostSec_TO_Eff_Min2Max_Ratio", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2NowSec_MinRatioMovAvg", "rawType": "float64", "type": "float"}, {"name": "UpConsecutive_Start_PGVRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MaxPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_MaxValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "Target_Price", "rawType": "float64", "type": "float"}, {"name": "Recent5D_AftPreNow_Volab_Num", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2Now_MaxTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MinDailyRatio_Recover_Date", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PGV_MeanRollAvg", "rawType": "float64", "type": "float"}, {"name": "Is_LowBound_Oscillation", "rawType": "float64", "type": "float"}, {"name": "U2D_Efficiency_VolatilityRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_MaxTurnover", "rawType": "float64", "type": "float"}, {"name": "Now_Recent3Min", "rawType": "float64", "type": "float"}, {"name": "SecConcave_BfBottom_TO_Sum", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_AvgTurnover2Drop", "rawType": "float64", "type": "float"}, {"name": "Cal_Date", "rawType": "object", "type": "string"}, {"name": "PostSecStart_VGV_NowRollAvg2MinRatio", "rawType": "float64", "type": "float"}, {"name": "PostMinRollAvg_RiseTrend_Prop", "rawType": "float64", "type": "float"}, {"name": "PreSec_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Sec_Max_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "Period_Break_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostPreNow_MaxUpEff2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_Recover_TopOpen_Close", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_STrend", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxContiDropDays", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MinPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_AvgTurnover2Rise", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PGV_MeanRollAvg", "rawType": "float64", "type": "float"}, {"name": "Narrow_E2N_Return", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_Over_StartCls_State", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PeakGapValue_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PGV_RollAvg_LowQuntl", "rawType": "float64", "type": "float"}, {"name": "Break_D100_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MinDailyRatio_Recover_Days", "rawType": "float64", "type": "float"}, {"name": "PostPreNowBottomWLS_R2", "rawType": "float64", "type": "float"}, {"name": "PostSec_MaxRollAvg_PeakDate_Diff", "rawType": "float64", "type": "float"}, {"name": "Now_Turnover2Ratio", "rawType": "float64", "type": "float"}, {"name": "PostSec2PreSec_PGV_MaxRollAvg_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MaxValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_DropDayRatio_Quntl", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak2Now_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "Now_PostSec_SecRise_Portion", "rawType": "float64", "type": "float"}, {"name": "PreTurn_LastSec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxDrop", "rawType": "float64", "type": "float"}, {"name": "PostTurn_MaxValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "Now_Turnover2Drop_State", "rawType": "float64", "type": "float"}, {"name": "PGV_Post_MinRollAvg_MaxVGV_CoverDays", "rawType": "float64", "type": "float"}, {"name": "PostPreNow_MinDailyRatio_Recover_Date", "rawType": "object", "type": "string"}, {"name": "PostNowSec_SlowTrend_Deviation", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_RollAvg_VolRange", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2NowSec_MinRatioMovAvg_Date", "rawType": "object", "type": "string"}, {"name": "Recent3Day_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "Recent_MaxRatio", "rawType": "float64", "type": "float"}, {"name": "SecConcave_PGV_MinRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_COR_Mean", "rawType": "float64", "type": "float"}, {"name": "SecStart_PRA_LastBreach_Date", "rawType": "object", "type": "string"}, {"name": "SecStart_PGV_UpCoverPeriod_Max2Min_DiffRatio", "rawType": "float64", "type": "float"}, {"name": "PostPeak_GapCovDays", "rawType": "float64", "type": "float"}, {"name": "Recent_EffPeak_ChangeRate_Percentile", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_U2D_MaxTO_Eff", "rawType": "float64", "type": "float"}, {"name": "Peak2Turn_COR_Std", "rawType": "float64", "type": "float"}, {"name": "SectionStart_Position", "rawType": "float64", "type": "float"}, {"name": "PreSec_Turnover2Drop_Last4Rank", "rawType": "float64", "type": "float"}, {"name": "PreSec_Turnover2Drop", "rawType": "float64", "type": "float"}, {"name": "PostBottom_MaxDrop", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_TO_Eff_Max2Min_Ratio", "rawType": "float64", "type": "float"}, {"name": "Turn2Peak_MaxContiDrop", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_PGV_MinRollAvg_CoverDays", "rawType": "float64", "type": "float"}, {"name": "PostSecWLS_R2", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Drop2Rise_SecSumDaysDiv", "rawType": "float64", "type": "float"}, {"name": "SecStart_PGV_DownCoverPeriod_Min2Max_DiffRatio", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_MaxContiRiseDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxValleyGap", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_Sec_MaxTurnover", "rawType": "float64", "type": "float"}, {"name": "RiseSec_SectionStable2NowSec_Days", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_BelowPre1Day_Prop", "rawType": "float64", "type": "float"}, {"name": "Now_Over3Mean", "rawType": "object", "type": "string"}, {"name": "SecStart_PGV_MinRollAvg_DownCoverDays", "rawType": "float64", "type": "float"}, {"name": "Now_PostSecStart_PGV_RollAvg_Asc_Rank", "rawType": "float64", "type": "float"}, {"name": "MinRollAvg_NowSec_Diff", "rawType": "float64", "type": "float"}, {"name": "Now_DayRatio", "rawType": "float64", "type": "float"}, {"name": "Latest_EffPeak2NowSec_Diff", "rawType": "float64", "type": "float"}, {"name": "PostPreSecPeak_DownConsecutive_MaxLastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MinRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_Recover_TopOpen_Days", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MaxRollAvg2MeanRatio", "rawType": "float64", "type": "float"}, {"name": "SectionPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PostSec_StepBackDate", "rawType": "object", "type": "string"}, {"name": "NowSec_MinPRA_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_BelowPre1Day_Days", "rawType": "float64", "type": "float"}, {"name": "Pre_PostSecPeak_Sec_SumTO", "rawType": "float64", "type": "float"}, {"name": "Break_DownSecutiveStart_First2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_COR_Mean", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Avg_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_DownConsecutive_MaxLastDays", "rawType": "float64", "type": "float"}, {"name": "PreTurn_Period_Sum2Std", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Und2SumDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_HighOver7Num", "rawType": "float64", "type": "float"}, {"name": "PreSecWLS_Deviation", "rawType": "float64", "type": "float"}, {"name": "Is_Expanding", "rawType": "float64", "type": "float"}, {"name": "Now_PostSecPeak_VGV_RollAvg_Asc_Rank", "rawType": "float64", "type": "float"}, {"name": "Latest_UpEff_Peak2Now_Days", "rawType": "float64", "type": "float"}, {"name": "Bottom_Date", "rawType": "object", "type": "string"}, {"name": "Break_UpSecutiveStart_First2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Now_State", "rawType": "object", "type": "string"}, {"name": "Narrow_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_ValleyGapValue_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "PreSec_SecDropDays_QuntlRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_TO_Over10Num", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_Trend_Consistency", "rawType": "float64", "type": "float"}, {"name": "Recent4P_MaxLastDays", "rawType": "float64", "type": "float"}, {"name": "PostPreNow_MinDailyRatio_Recover_Days", "rawType": "float64", "type": "float"}, {"name": "Eff_Recent2Previous_<PERSON><PERSON><PERSON>e", "rawType": "float64", "type": "float"}, {"name": "PGV_RollAvg_VolatilityRatio", "rawType": "float64", "type": "float"}, {"name": "NowSec_BreakSecPeak_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_Now2Mean_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_U2D_MinTO_Eff", "rawType": "float64", "type": "float"}, {"name": "UpConsecutive2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Sec_DropNum", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_Sec_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "PreSec_Sec_OMA_Days", "rawType": "float64", "type": "float"}, {"name": "UpConsecutive_PGVRollAvg_DiffRatio", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_MaxTO_Eff", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_MaxTO_Eff_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Recent9Day_MaxPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_PGV_RollAvg_VolRange", "rawType": "float64", "type": "float"}, {"name": "Post_PreSecPeak_Over35Count", "rawType": "float64", "type": "float"}, {"name": "Now_PostSecPeak_PGV_RollAvg_Asc_Rank", "rawType": "float64", "type": "float"}, {"name": "Post_SecPeak_MeanRecovDays", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_VGV_NowRollAvg2MaxRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PGV_NowRollAvg2MaxRatio", "rawType": "float64", "type": "float"}, {"name": "PreSec_Und2ContiDays", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "Now_PostSecStart_VGV_MaxCoverDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxContiDays", "rawType": "float64", "type": "float"}, {"name": "Pre3Date_PeakGapValue", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PeakDate_Diff", "rawType": "float64", "type": "float"}, {"name": "Now_Turnover_Quntl", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MinPeakGap", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MaxRollAvg_Band", "rawType": "float64", "type": "float"}, {"name": "PostPreNowBottomWLS_Slope", "rawType": "float64", "type": "float"}, {"name": "Peak2Turn_MaxContiRiseDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_VGV_Max2Mean_Ratio", "rawType": "float64", "type": "float"}, {"name": "Concave_Break_Date", "rawType": "object", "type": "string"}, {"name": "SecConcave_TO_Sum", "rawType": "float64", "type": "float"}, {"name": "Recent_EffPeak_ChangeRate", "rawType": "float64", "type": "float"}, {"name": "NowSec_MinPRA_Percentile_PostSectionPeak", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_BelowPre1Day_Days", "rawType": "float64", "type": "float"}, {"name": "PreSec_Reg_Sum2Std", "rawType": "float64", "type": "float"}, {"name": "NowSec2SecStart_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxTurnover", "rawType": "float64", "type": "float"}, {"name": "BreakPreNowSec_Ratio", "rawType": "float64", "type": "float"}, {"name": "Pre_PreNowSec_Adverse2Trend", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Sec_Min_ExtreRatio", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PeakDate", "rawType": "object", "type": "string"}, {"name": "PostPreNow_MinDailyRatio_Recover_State", "rawType": "float64", "type": "float"}, {"name": "BreakShockLast_Date", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PGV_Max2Mean_Ratio", "rawType": "float64", "type": "float"}, {"name": "PreSec_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "NowSec_MinClose_DownCoverDays", "rawType": "float64", "type": "float"}, {"name": "PostBottom_TO_Over10Num", "rawType": "float64", "type": "float"}, {"name": "PostPreNow_UpEff_MaxTO_Eff_Band", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxPGV", "rawType": "float64", "type": "float"}, {"name": "Total_MV", "rawType": "float64", "type": "float"}, {"name": "SecConcave_AftBottom_TO_Sum", "rawType": "float64", "type": "float"}, {"name": "Now_Turnover_Rank", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_OverPre1Day_MeanRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Recent_Neg4_RecovDate", "rawType": "object", "type": "string"}, {"name": "PostSecStart_MaxPGV_Date", "rawType": "object", "type": "string"}, {"name": "Now_Und3Min", "rawType": "object", "type": "string"}, {"name": "NowSec_LastBreach2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Post_SecPeak_Und4Count", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_PRA_LastBreach_Date", "rawType": "object", "type": "string"}, {"name": "Peak2Turn_Reg_R2", "rawType": "float64", "type": "float"}, {"name": "PreTurnPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_COR_Und2Poxn", "rawType": "float64", "type": "float"}, {"name": "PostTurn_1stTODate2Top_Ratio", "rawType": "float64", "type": "float"}, {"name": "Post_PreSecPeak_DepresCount", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "Now_Over3Max", "rawType": "object", "type": "string"}, {"name": "PreNowBottom2PostNowPeak_STrend", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MaxRollAvg2MinRatio", "rawType": "float64", "type": "float"}, {"name": "Post_PreSecPeak_RecovCount", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Recent_Neg4_DropRatio", "rawType": "float64", "type": "float"}, {"name": "Valley_Pres_Num", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_VGV_MaxRollAvg2MinRatio", "rawType": "float64", "type": "float"}, {"name": "Turn_Period_GapRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_StepBack_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_COR_Std", "rawType": "float64", "type": "float"}, {"name": "Long_PeakDate", "rawType": "object", "type": "string"}, {"name": "PreTurn_Turnover2Drop", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_Und2ContiDays", "rawType": "float64", "type": "float"}, {"name": "SecStart_FirstBreach2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Recent_HLR_Date", "rawType": "object", "type": "string"}, {"name": "Peak_Pres_MeanPrice", "rawType": "float64", "type": "float"}, {"name": "PreSecMaxRollAvg_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostBottom_PeriodNum", "rawType": "float64", "type": "float"}, {"name": "PreNowBottom2PostNowPeak_STrend_LowDev", "rawType": "float64", "type": "float"}, {"name": "Post_SecPeak_RecovRatio", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_Und2SumDays", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak2Now_SumTO", "rawType": "float64", "type": "float"}, {"name": "Now_PostTurn_SecRiseState", "rawType": "float64", "type": "float"}, {"name": "PostNowSecWLS_R2", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_PGV_RollAvg_Now2Min_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Sec_RiseNum", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_MaxContiRise", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_DropSecOver10Days_EndDate", "rawType": "object", "type": "string"}, {"name": "PostNowSec_PGV_Now2Mean_Ratio", "rawType": "float64", "type": "float"}, {"name": "PreNow2PostSec_PGV_MeanRollAvg_Ratio", "rawType": "float64", "type": "float"}, {"name": "Eff_Latest2Pre3Mean", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PostTurn_PeakDate", "rawType": "object", "type": "string"}, {"name": "PostPreNowPeak_U2D_MinTO_Eff2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_COR_Mean", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_SecDropDays_QuntlRatio", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_MinValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "PostSec_Peak_PGV_RollAvg", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MedianPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_UpConsecutive_Num", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_MaxPeakGap", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_Support_Breach_CostDays", "rawType": "float64", "type": "float"}, {"name": "PostSecMax2Peak_Ratio", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2Now_OverPre1Day_MeanRatio", "rawType": "float64", "type": "float"}, {"name": "Pre_PreNow_Sec_Over7Num", "rawType": "float64", "type": "float"}, {"name": "PostNowSecWLS_Deviation", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PGV_MaxRollAvg_CoverDays", "rawType": "float64", "type": "float"}, {"name": "CoverRatioBand_20", "rawType": "float64", "type": "float"}, {"name": "DownConsecutive_SumRatio", "rawType": "float64", "type": "float"}, {"name": "SectionStart_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PostNow2PostSec_PGV_MeanRollAvg_Ratio", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_MedianValleyGap", "rawType": "float64", "type": "float"}, {"name": "Post_PreSecPeak_DepresRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MinDailyRatio_Recover_State", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_Turnover2Change_Rank", "rawType": "float64", "type": "float"}, {"name": "Now_Shock_Date", "rawType": "object", "type": "string"}, {"name": "PostNowSec_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxDailyRatio", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxPeakGapValue_Date", "rawType": "object", "type": "string"}, {"name": "PostTurn_Peak2Now_SumRatio", "rawType": "float64", "type": "float"}, {"name": "AftPreTurnPeak_PGV_MinRollAvg2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Peak2Turn_Reg_Sum2Std", "rawType": "float64", "type": "float"}, {"name": "Is_Bottom_Reversal", "rawType": "float64", "type": "float"}, {"name": "DropSec_SectionStable2NowSec_Days", "rawType": "float64", "type": "float"}, {"name": "Now_Turnover_MinGap", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_MinPeakGap", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_Reg_Sum2Std", "rawType": "float64", "type": "float"}, {"name": "PreNow_MHLR_Over4Num", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_ValleyGap_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "DownConsecutive_Last2Break_LastDays", "rawType": "float64", "type": "float"}, {"name": "PreTurn_Turnover2Drop_Last4Rank", "rawType": "float64", "type": "float"}, {"name": "PostPreNowBottom_UpConsecutive_MaxLastDays", "rawType": "float64", "type": "float"}, {"name": "Now_RiseSec_Turnover2Drop", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PGV_MinRollAvg2Now_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_SecDropAvg_QuntlRatio", "rawType": "float64", "type": "float"}, {"name": "PreSec_SecDropAvg_QuntlRatio", "rawType": "float64", "type": "float"}, {"name": "UpConsecutive_Last2Break_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_NowRollAvg2MinRatio", "rawType": "float64", "type": "float"}, {"name": "BreakPreNowPeak_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxVGVDate2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_COR_Std", "rawType": "float64", "type": "float"}, {"name": "NowSec_Recent3D_MaxValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PGVRollAvg_CoverDrop_Diff", "rawType": "float64", "type": "float"}, {"name": "TurnDiff_Sec_Recov_Position", "rawType": "float64", "type": "float"}, {"name": "PostTurn_1stSec_ExtreRatio", "rawType": "float64", "type": "float"}, {"name": "Now_HighLow_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Over7Num", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Peak2Now_Lastdays", "rawType": "float64", "type": "float"}, {"name": "LongTrend_PeriodNum_Drop", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PRV_Top3Mean_CoverDays", "rawType": "float64", "type": "float"}, {"name": "PreSec_ShockDate", "rawType": "object", "type": "string"}, {"name": "Peak2Sec_PGV_MinRollAvg2Sec_SumRatio", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_MinPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "SecPeakConcave_CoverDays", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Reg_Sum2Std", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_1stSec_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxContiSum", "rawType": "float64", "type": "float"}, {"name": "SecDiff_Sec_Recov_Position", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_MaxPeakGapValue", "rawType": "float64", "type": "float"}, {"name": "Now_Period_R2", "rawType": "float64", "type": "float"}, {"name": "Pre_PreNow_Sec_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostPreNowBottom2PreNowSec_SlowTrend_Deviation", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_PGV_MeanRollAvg_TruncatedValue", "rawType": "float64", "type": "float"}, {"name": "TurnDiff_Recoved_Sec_Num", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MinDailyRatio", "rawType": "float64", "type": "float"}, {"name": "SecConcave_PGV_MinRollAvg2MeanRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_DailyDropRecov_Count", "rawType": "float64", "type": "float"}, {"name": "PostBottom_MaxContiSum", "rawType": "float64", "type": "float"}, {"name": "CoverDaysDiff_10", "rawType": "float64", "type": "float"}, {"name": "Eff_Avg_Peak_Period", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Sec_Max_SumRatio", "rawType": "float64", "type": "float"}, {"name": "Break_TurnShock", "rawType": "object", "type": "string"}, {"name": "PreNowSec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_Rise_BMA_Prop", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_ExtreRatio", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2NowSec_RatioMovAvg_NowSecRank", "rawType": "float64", "type": "float"}, {"name": "AftPreTurnPeak_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_PGV_MinRollAvg_Band", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_Adverse2Trend", "rawType": "float64", "type": "float"}, {"name": "NowSec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "Turn2Now_AvgSecDays", "rawType": "float64", "type": "float"}, {"name": "PostPeak_Recent_Neg4_DropRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurnPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PostSec_MinTO_Eff2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_MaxTO_Eff_Date", "rawType": "object", "type": "string"}, {"name": "BfRise_Turnover_Threshold", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_U2D_MaxTO_Eff_Band", "rawType": "float64", "type": "float"}, {"name": "PostBottom_MaxTurnover", "rawType": "float64", "type": "float"}, {"name": "Turn_Target_Ratio", "rawType": "float64", "type": "float"}, {"name": "Sec_IndexDiff_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MinDaily2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Peak2Turn_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_ValleyGapValue_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_MaxValleyGap", "rawType": "float64", "type": "float"}, {"name": "Spring_Date", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "Latst2Now_DDRQ_Days", "rawType": "float64", "type": "float"}, {"name": "NowSec_LowBand_CoverDays", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Reg_EndDate", "rawType": "object", "type": "string"}, {"name": "PostSecStart_MeanClose", "rawType": "float64", "type": "float"}, {"name": "PreSec_DDR_Min", "rawType": "float64", "type": "float"}, {"name": "SecDiff_Sec_AvgTurnover_Div", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_SectionStable_Num", "rawType": "float64", "type": "float"}, {"name": "PostSecPeak_MaxTO_Eff2Now_LastDays", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_Sec_Max_LastDays", "rawType": "float64", "type": "float"}, {"name": "Recent9Day_MaxValleyGapValue", "rawType": "float64", "type": "float"}, {"name": "Valley_Pres_MeanPrice_Ratio", "rawType": "float64", "type": "float"}, {"name": "Post_PreSecPeak_Und4Count", "rawType": "float64", "type": "float"}, {"name": "PreNow_Rise_Recent_MeanRatio", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PGV_MaxRollAvg_Band", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak2Now_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "PostSec_MinTO_Eff_Band", "rawType": "float64", "type": "float"}, {"name": "PreNowSec_MaxClsOpenRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecBottom_MaxPeakGap", "rawType": "object", "type": "string"}, {"name": "PostPreNowPeak_TO_Eff_Max2Min_Ratio", "rawType": "float64", "type": "float"}, {"name": "PreTurn_LastSec_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PostTurn_COR_Mean", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_VGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Drop2Rise_SecMaxSumDiv", "rawType": "float64", "type": "float"}, {"name": "PostPreNowPeak_U2D_MaxTO_Eff_Date", "rawType": "object", "type": "string"}, {"name": "Recent3Day_PRA_NowSec_Rank", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "Bf_SecStart_5D_MinRatio", "rawType": "float64", "type": "float"}, {"name": "Latest_Eff_Peak2Now_Days", "rawType": "float64", "type": "float"}, {"name": "PreSec_Sec_OMA_Prop", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_PGV_MeanRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_MaxRollAvg_Date", "rawType": "object", "type": "string"}, {"name": "Return_Risk_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostPeak_Gap2Peak_Ratio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_PGV_RollAvg_HighQuntl", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_SumRatio", "rawType": "float64", "type": "float"}, {"name": "PreSec_ExtreRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MedianValleyGap", "rawType": "float64", "type": "float"}, {"name": "AftPreTurnPeak_PGV_MinRollAvg2MaxRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart2PreNowSec_Beta", "rawType": "float64", "type": "float"}, {"name": "PostTurn_StepBack_GapRatio", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "PostTurn_Period_RiseNum", "rawType": "float64", "type": "float"}, {"name": "Peak2Turn_LastDays", "rawType": "float64", "type": "float"}, {"name": "Now_PeakGap", "rawType": "float64", "type": "float"}, {"name": "Post_PreSecPeak_MeanRecovDays", "rawType": "float64", "type": "float"}, {"name": "SecStart_LastBreach2Now_LastDays", "rawType": "object", "type": "string"}, {"name": "Latest_UpEff_Peak_Date", "rawType": "object", "type": "string"}, {"name": "date_diff", "rawType": "int64", "type": "integer"}, {"name": "sort_position", "rawType": "int64", "type": "integer"}, {"name": "Turn_Turnover_Diff", "rawType": "float64", "type": "float"}, {"name": "NowSec2Turn_PRV_MeanDiff", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Max2SecPeak_PRV_Diff", "rawType": "float64", "type": "float"}, {"name": "SecStart2SecPeak_PRV_Diff", "rawType": "float64", "type": "float"}, {"name": "NowSec2PreNowPeak_PRV_Diff", "rawType": "float64", "type": "float"}, {"name": "SecConcave_AvgRatioBand", "rawType": "float64", "type": "float"}, {"name": "SecConcave_AftB_LastDays_Propotion", "rawType": "float64", "type": "float"}, {"name": "nowsec_recent_diff", "rawType": "int64", "type": "integer"}, {"name": "secstart_recent_diff", "rawType": "int64", "type": "integer"}, {"name": "relative_bottom_date_diff", "rawType": "int64", "type": "integer"}, {"name": "nowsec_minret_diff", "rawType": "int64", "type": "integer"}, {"name": "secstart_minret_diff", "rawType": "int64", "type": "integer"}, {"name": "PostNowSec_AvgRatio_Pre", "rawType": "float64", "type": "float"}, {"name": "In_TurnBreak", "rawType": "int64", "type": "integer"}], "ref": "9a9ac2da-dd56-4293-8bf3-433db319ee22", "rows": [["325", "002940.SZ", "昂利康", "医药生物", "32.044", "228.0", "198.0", "34.0", "21.29", "2025-04-08", "2025-04-28", "2025-04-08", "2025-04-02", "3.0", "-13.287", "2025-05-13", "2025-04-22", "12.0", "3.0", "-4.3", "19.548", "5.0", "2.0", "0.0", "3.0", "20.0", "2.0", "117.581", "2025-05-21", "117.581", "2025-05-21", "-87.0", "22.0", "109.0", "6.0", "0.59", "0.979", "0.979", "15.977", "1.0", "4.0", "0.893", "6.0", "0.441", "0.1033", "0.441", "0.1033", "1.048", "2.021", "2025-04-23", "2025-05-22", "-21.14", "8.22", "5.93", "31", "2025-04-11", "2025-05-14", "-32.07", "0", "0", "0", "1.83", "1.85", "5.16", "2025-05-27", "2.0", "0.737", "7.74", "16.61", "-0.595", "-0.059", "2.0", "-2.74", "-1.37", "0.626", "2025-05-23", "0", "False", "3", "4.01", "6.22", "0", "0", "5.99", "2025-05-22", "4.289", "1.0", "46.851", "-1.0", "0.303", "1.136", "0.027", "2025-02-10", "34.0", "-", "10.02", "14.0", "11.0", null, "28.0", "1", "1.0", "0.627", "110.0", "10.306", "2.59", "4.011", null, "-0.946", "3.483", "2025-05-29", "609.0", "扩大", "0.343", "12.12", "4.414", "233.0", "3.0", "2025-04-28", "0.505", "157.0", "0.463", "1.873", "48.0", "0.164", "11.0", "2.574", "0.0", "-4.3", "0.252", "32.044", "0.072", "0.187", "1.553", "0.238", "False", "16.2", "0.134", "0.283", "2025-04-28", "3.023", "10.0", "0.15", null, "-1.244", "2025-04-22", "2025-04-23", "0.072", "0.0", "18386092", "5.0", "0.25", "20.0", "12.5", "0.13", "1.495", "2025-05-21", "2025-04-28", "0.615", "32.0", "20.0", "-1.053", "0.16", null, "0.375", "-4.3", "3.195", "0.229", "0.342", "3.921", "2025-03-21", "11.572", "0.0", "2025-04-02", "0.539", "-", "1.553", "0.423", "2.0", "30.0", "1.162", "3.0", null, "0.636", "1.065", "-91.0", "2.026", "-", "1.363", "3.333", "-2.897", "0.335", "1.954", "2025-04-07", "17.0", "0.915", null, "浙江", "2024-12-13", "24.0", "21.29", "2025-04-23", "2025-05-29", "-10.01", "0.0", "0.297", "0.464", "0.203", "-0.541", "2025-04-22", "0.063", "2024-12-11", "10.02", "0.0", "1.154", "18.0", "0.32", "0.0", "0.812", "2025-04-25", "1.873", "17.0", "1.497", "0.072", "2.21", "2025-04-23", "9.059", "1.5", "0.0", "Rise", "2.136", "1.0", "2.0", "0.0", "21.29", "1.0", "0.283", "6.0", "1.0", "0.2557", "4.773", "-2.461", "6.0", "165.0", "20172.8", "12.0", "0.699", null, "-4.301", "1.0", "1.0", "0.539", "3.001", "2025-04-08", "3.0", "0.069", "20.0", "1.778", "0.633", "2025-01-03", "1.0", "1.0", "2025-05-16", "2025-04-28", "0.105", "-4.301", "0.066", "7.713", "14.603", "6.0", "上行", "-1.0", "0.333", "20.0", "18.0", "True", "4.0", "2025-05-29", "1.959", "0.0", "109.0", null, "44.0", "5.873", "-10.01", "13.0", "12.0", "0.187", "94.0", "5.0", "0.182", "3.667", "123.0", "0.0", "3.0", "2024-05-08", "7.25", "-", "125.0", "0.343", "12.0", "5.0", "-4.429", "16.0", "1.55", "2.0", "1.0", "2.0", "0.0", "3.19", "0.0", "3.0", "2025-04-28", "12.659", "2025-04-07", "15.5", "3.534", "11.0", "0.644", "4.574", "26.493", null, "1.055", "2.75", "0.072", "0.444", "4.773", "2025-04-23", "6.742", "2.36", "1.0", "3.839", "17.742", "-18.421", "2.02", "3.34", "13.42", "0.0", "-13.29", "3.34", "83.0", "4.574", "25.67", "7.204", "2025-04-28", "5.0", "-", "23.0", "0.829", "2025-04-23", "2.173", "20.0", "0.0", "-15.132", "1.136", "6.0", "0.323", "0.167", "6.731", "11.0", "0.208", "0.118", "0.441", "0.0", "0.137", "0.539", "1.0", "496.0", "0.714", "3.34", "0.0", "0.0", "0.444", "-234.559", "2025-04-25", "1.462", "2.726", "0.667", "-1.45", "2.0", "上行", "0.0", "-", "0.983", "0.381", "0.81", "0.206", "4.0", "-4.3", "1.814", "0.297", "177.375", "2.0", "0.073", "1.0", "1.0", "33.0", "2025-03-21", "0.478", "0.348", "0.441", "11.0", "0.243", "True", "0.256", "-4.429", "0.105", "1.0", "0.611", "2025-04-08", null, "1.0", "0.191", "2025-05-13", "1.0", "0.4337", "0.229", "1.0", "0.787", "13.0", "0.861", "4.0", "1.0", "3.0", "0.0", "1.099", "0.134", "3.573", "12.659", "2025-05-29", "1.47", "0.0", "1.167", "1.0", "2.294", "-0.493", "2.23", "17.742", "0", "3.4", "1.0", "0.13", "2.0", "5.0", "0.571", "1.0", "0.0657", "0.176", "0.31", "1.117", "1.82", "3.0", "0.0", "12.77", "5.875", "10.02", "3.911", "0.0", "12.5", "0.0", "0.287", "2.0", "0.164", "-1.852", "0.822", "2025-05-13", "0.0", "0.04", "7.661", "0.211", "0.626", "2025-05-20", "2.91", "22.0", "0.0", "0.81", "0.251", "0.159", "0.861", "0.444", "4.0", "2025-05-23", "2025-04-08", "-", "2025-04-22", "16.589", "15.714", "0.222", "3.0", "0.0", "0.062", "-2.75", "0.198", "13.81", "2024-06-18", "0.455", "18.0", "0.984", "8.663", "4.011", "110", "11.0", "-", "3.71", "1.619", "4.0", "0.6", "0.0", "-1.5", "15.0", "1.0", "21.29", "0.143", "109.0", null, "1.496", "12.0", "0.206", "0.0", "3.0", null, "0.633", "2024-12-17", "0.861", "6.83", "135.0", "3.0", "4.0", "12.0", "14.0", "0.0", "4.282", "11.0", "2025-05-20", "0.727", "1.259", "1.0", "6.62", "5.873", "198.0", "-1.0", "34.0", "0.0", null, "1.0", "2025-05-13", "0.348", "-1.45039", "0.1633", "0.539", "0.212", "17.98", "1.0", "4.633", "2025-04-29", "0.255", "0.0", "0.607", "6.62", "14.37", "47.198", "1.099", "2025-05-29", "1.889", "0.583", "-13.287", "1.136", "21.998", "23.0", "13.44", "0.0", "3.0", "0.539", "0.187", "0.221", "4.011", "True", "0.411", "0.207", "5.175", "16.0", "0.916", "22.0", "1.19", "1.558", "0.105", "-1.18", "0.0", "0.587", "-3.32", "0.0", "0.444", "0.0", "20.0", "2025-05-16", "10.0", "0.084", "2025-04-28", "0.314", "4.01", "45.0", "1.77", "2025-05-23", "7.15", "2.0", "0.612", "1.398", "1.046", "0.082", "1.0", "0.627", "-21.019", "2.87", "-4.3", "32.0", "0.967", "0.097", "0.16", "0.0", "3.534", "0.19", "4.633", "90.0", "0.238", "True", "44.0", "25.0", "8.0", "4.011", "8.0", "4.0", "12.0", "11.0", "1.73", "0.383", "-", "-0.093", "5.0", "45.477", "6.0", "1.964", "15.5", "0.0", "1.119", "21.0", "1.0", "0.0", "1.0", "15.0", "25.0", "2024-07-30", null, null, "6.0", "0.224", "0.249", "1.0", "0.0", "94.0", "12.0", "-0.403", "0.722", "5.175", "1.18", "-1.041", "4.0", "1.0", "3.195", "0.0", "2.656", "3.44", "1.0", "0.861", "0.086", "2.0", "17.0", "0.0", "0.542", "0.686", "2.0", "0.072", "1.0", "3.0", "0.224", "6.0", "0.629", "0.463", "2.264", "0.455", "23.0", "1.964", "2024-06-12", "408.733", "16.018", "-0.093", "8.0", "13.771", "7.66", "6.62", "7.814", "0.091", "-2.75", "2025-05-29", "0.0", "2025-04-14", "2.756", "2.91", "0.441", "25.0", "14.0", "-0.179", "0.861", "303399.0", "363.366", "22.0", "0.0", null, "2025-05-21", "False", "4.0", "0.0", "0.414", "0.0", "2025-05-23", "0.211", "0.255", "0.0", "3.014", "0.0", "0.35", "True", "0.0", "6.1", "1.0", null, "4.0", "5.567", "-8.689", "0.0", "1.429", "2024-05-23", "-234.559", "2.0", "23.0", "2025-05-29", "15.45", "0.103", "5.0", "-4.871", "1.0", "2.0", "0.0", "1.0", "0.916", "4.187", "2.0", "0.0", "2025-02-28", "1.237", "1.445", "1.065", "0.414", "2025-05-29", "7.0", "2.36", "0.249", "0.095", "0.303", "0.198", "5.0", "1.648", "-1.0", "-4.92", "0.0", "0.0", "6.731", "18.0", "60.209", "-1.821", "0.191", "0.867", "0.979", "0.0", "0.0", "1.0", "-", "2.25", "4.574", "2025-05-21", "0.0", "45.0", "1.748", "0.0", "74.0", "-1.5594", "0.463", "1.26", "10.0", "1.908", "6.0", "2.0", "5.0", "3.71", "11.572", "1.343", "4.16", "100.0", "4.187", "7.814", "34.0", null, "0.212", "-0.007", "0.816", "4.574", "7.113", "0.0", "0.0", "2.0", "18.0", "-", "0.0", "0.145", "70.0", "2.02", "23.0", "1.905", "10.02", "1.389", "12.659", "0.861", "0.248", "11.0", null, "-1.0", "2.0", "-2.75", "0.347", "4.0", "26.21", "8.0", "4.0", "12.659", "-", "-1.434", "0.19", "0.608", "-2.75", "14.0", "0.062", "-0.727", "0.5", "0.632959", "17.5", "-10.007", "0.163", "0.35", "5.0", "2025-04-23", "3.483", "1.453", "6.83", "19.681", "10.829", "35.0", "-0.239", "0.403", "2.026", "2025-04-08", "0.441", "2.0", "5.0", "2025-05-28", "13.52", "-10.007", "0.655", "0.0", "24.0", "3.0", "0.297", "4.483", "1.0", "3.24", "2.207", "-1.434", "-0.775", "3.333", null, "2.524", "-13.29", "1.493", "0.405", "0.34", "2025-05-14", "7.0", "0.261", null, "12.0", "0.0", "0.292", "2025-04-24", "1.007", "-18.808", "0.271", "21.29", "-10.01", "0.997", "0.14", "1.17175", "-5.918", "0.434", "1.0", "76.0", "3.669", "10.0", null, "2025-04-21", "20", "88", "-4.6", "0.953", "1.151", "0.499", "0.44", "0.141", "0.868", "16", "30", "20", "9", "23", "0.455", "99"]], "shape": {"columns": 932, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>name</th>\n", "      <th>industry</th>\n", "      <th>SecConcave_RatioBand</th>\n", "      <th>SecConcave_LastDays</th>\n", "      <th>SecConcave_AftBottom_LastDays</th>\n", "      <th>PostSecStart_LastDays</th>\n", "      <th>PostSecStart_RiseRatio</th>\n", "      <th>Section_StartDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>...</th>\n", "      <th>NowSec2PreNowPeak_PRV_Diff</th>\n", "      <th>SecConcave_AvgRatioBand</th>\n", "      <th>SecConcave_AftB_LastDays_Propotion</th>\n", "      <th>nowsec_recent_diff</th>\n", "      <th>secstart_recent_diff</th>\n", "      <th>relative_bottom_date_diff</th>\n", "      <th>nowsec_minret_diff</th>\n", "      <th>secstart_minret_diff</th>\n", "      <th>PostNowSec_AvgRatio_Pre</th>\n", "      <th>In_TurnBreak</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>325</th>\n", "      <td>002940.SZ</td>\n", "      <td>昂利康</td>\n", "      <td>医药生物</td>\n", "      <td>32.044</td>\n", "      <td>228.0</td>\n", "      <td>198.0</td>\n", "      <td>34.0</td>\n", "      <td>21.29</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-04-28</td>\n", "      <td>...</td>\n", "      <td>0.44</td>\n", "      <td>0.141</td>\n", "      <td>0.868</td>\n", "      <td>16</td>\n", "      <td>30</td>\n", "      <td>20</td>\n", "      <td>9</td>\n", "      <td>23</td>\n", "      <td>0.455</td>\n", "      <td>99</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 932 columns</p>\n", "</div>"], "text/plain": ["       ts_code name industry  SecConcave_RatioBand  SecConcave_LastDays  \\\n", "325  002940.SZ  昂利康     医药生物                32.044                228.0   \n", "\n", "     SecConcave_AftBottom_LastDays  PostSecStart_LastDays  \\\n", "325                          198.0                   34.0   \n", "\n", "     PostSecStart_RiseRatio Section_StartDate Now_SecDate  ...  \\\n", "325                   21.29        2025-04-08  2025-04-28  ...   \n", "\n", "    NowSec2PreNowPeak_PRV_Diff SecConcave_AvgRatioBand  \\\n", "325                       0.44                   0.141   \n", "\n", "     SecConcave_AftB_LastDays_Propotion  nowsec_recent_diff  \\\n", "325                               0.868                  16   \n", "\n", "    secstart_recent_diff relative_bottom_date_diff  nowsec_minret_diff  \\\n", "325                   30                        20                   9   \n", "\n", "     secstart_minret_diff  PostNowSec_AvgRatio_Pre  In_TurnBreak  \n", "325                    23                    0.455            99  \n", "\n", "[1 rows x 932 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp = result_df_head.query('ts_code==\"002940.SZ\"')\n", "stk_temp[['now_overpre1day',\n", "            'recentbottom_downcoverdays',\n", "            'postcumret_consecutive_over7num',\n", "            'nowsec_recent_diff', 'recent2bottom_days',\n", "            'postnowsec_risestop_num', 'Now_SecDate', 'PostSecStart_PGV_Max2Mean_Ratio']]\n", "stk_temp.query('(now_overpre1day<=1 | '\n", "            'Cal_Date==@recent_turndate) & '\n", "            # 'recentbottom_und3min==\"True\" & '\n", "            'recentbottom_downcoverdays>=3 & '\n", "            'postcumret_consecutive_over7num<=2 &'\n", "            '(nowsec_recent_diff<=1'\n", "            ' | (nowsec_recent_diff<5 & nowsec_recentturn_diffratio<2) | '\n", "            'recent2bottom_days<=1) & '\n", "            'postnowsec_risestop_num<=3')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pull_start_list2 = pull_start_list[['ts_code', 'name', 'industry', 'PostSecStart_LastDays', 'PostSecStart_SumRatio', 'PostSecStart_Over7Num']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import get_result_3\n", "result_store = get_result_3(end_date='2025-04-10')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["industry_list = ['电力设备']\n", "now_secdate = '2025-04-07'\n", "result_store_indus = result_store.query('industry in @industry_list & Now_SecDate>=@now_secdate').sort_values(by=['PreNowPeak2Now_LastDays', 'PreNowPeak2Now_SumRatio'], ascending=[True, False])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["industry_list = ['煤炭', '公用事业', '石油石化', '有色金属', '医药生物', '家用电器','机械设备','计算机','基础化工']\n", "result_store_pick = result_store.query('industry in @industry_list & Now_SecDate==Cal_Date & PreNowSec_LastDays<=5'\n", "                                       ).sort_values(by=['industry', 'PreNowSec_AvgRatio'], ascending=[True, False])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Bottom_Date</th>\n", "      <th>Period_TurnDate</th>\n", "      <th>Section_StartDate</th>\n", "      <th>Target_Price</th>\n", "      <th>Turn_Target_Price</th>\n", "      <th>Section_Target_Price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3840</th>\n", "      <td>2022-04-26</td>\n", "      <td>2024-09-04</td>\n", "      <td>2025-01-03</td>\n", "      <td>15.91</td>\n", "      <td>8.0</td>\n", "      <td>8.6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Bottom_Date Period_TurnDate Section_StartDate  Target_Price  \\\n", "3840  2022-04-26      2024-09-04        2025-01-03         15.91   \n", "\n", "      Turn_Target_Price  Section_Target_Price  \n", "3840                8.0                   8.6  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ts_code = '300084.SZ'\n", "result_store.query('ts_code==@ts_code')[['Bottom_Date', 'Period_TurnDate', 'Section_StartDate', 'Target_Price', 'Turn_Target_Price', 'Section_Target_Price']]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_store['SecMax2SectionPeak_Ratio'] = round(result_store['PostSecStart_PGV_MaxRollAvg']/result_store['SectionPeak_PRV_Top3Mean'], 2)\n", "result_store_check = result_store.query('SecMax2SectionPeak_Ratio>1').copy().sort_values(by=['PostSecStart_PGV_MaxRollAvg_Date', 'SecMax2SectionPeak_Ratio'], ascending=[False, True])\n", "result_store_check2 = result_store_check[['ts_code', 'name', 'industry', 'Section_PeakDate', 'Section_StartDate', 'PostSecStart_PGV_MaxRollAvg_Date', 'SecMax2SectionPeak_Ratio']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["industry_list = ['公用事业', '煤炭', '石油石化', '有色金属' ,'交通运输', '食品饮料']\n", "result_store_check = result_store.query('PostNowSec_PRA_MaxRate>50 & '\n", "                                        'PostNowSec_PRA_MaxRate_Date==\"2025-04-08\" & '\n", "                                        # 'Now_SecDate==\"2025-01-27\" & '\n", "                                        # 'industry in @industry_list & '\n", "                                        'PostNowSec_PRA_MaxRate_BreachCount==1 & '\n", "                                        'PreNowPeak2Now_LastDays>10'\n", "                                        # 'NowSec_PGV_MaxRollAvg_UpCoverDays>30'\n", "                                        # 'NowSec_MaxPRA_Percentile_PostSectionPeak<1'\n", "                                        ).sort_values(by=['PreNowPeak2Now_AvgRatio', \n", "                                                          'PostSecStart_MaxPRA_Percentile_PostSectionPeak'], \n", "                                                      ascending=[False, False])\n", "result_store_check2 = result_store_check[['ts_code', 'name', 'industry', 'NowSec_PGV_MaxRollAvg_UpCoverDays',\n", "                                    'Section_StartDate', 'Now_SecDate', 'Period_TurnDate', 'Section_PeakDate',\n", "                                    'Now_PRA_Percentile_PostSectionPeak',\n", "                                    'NowSec_MaxPRA_Percentile_PostSectionPeak',\n", "                                    'NowSec_MaxPRA_Percentile_PostTurn',\n", "                                    'PostSecStart_MaxPRA_Percentile_PostSectionPeak',\n", "                                    'PostSecStart_MaxPRA_Percentile_PostTurn',\n", "                                    'PostNowSec_PRA_MaxRate',\n", "                                    'PostNowSec_PRA_MaxRate_Date',\n", "                                    'PostSecStart_PRA_MaxRate',\n", "                                    'PostSecStart_PRA_MaxRate_Date',\n", "                                    'Now_PRA_Rate',\n", "                                    'Now_PGV_RollAvg',\n", "                                    'PostNowSec_PRA_MaxRate_BreachCount',\n", "                                    'PostSecStart_PRA_MaxRate_BreachCount',\n", "                                    'PostNowSec_MaxRate_PRA_Percentile',\n", "                                    'PostNowSec_MaxRate2Now_LastDays',\n", "                                    'PostNowSec_LastDays',\n", "                                    'SecPeak2NowSec_PRA_UpBand',\n", "                                    'SecPeak2NowSec_PRA_LowBand',\n", "                                    'Turn2NowSec_PRA_UpBand',\n", "                                    'Turn2NowSec_PRA_LowBand',\n", "                                    'NowSec_PGV_MaxRollAvg_UpCoverDays',\n", "                                    'NowSec_BreakSecPeak_Ratio',\n", "                                     'SecStart_PRA_FirstBreach_Date',\n", "                                    'SecStart_FirstBreach2Now_LastDays',\n", "                                    'NowSec_PRA_FirstBreach_Date',\n", "                                    'NowSec_FirstBreach2Now_LastDays',\n", "                                    'PostNowSec_MaxRate_Post2Pre_DiffRatio',]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stk_temp = result_store.query('ts_code==\"000571.SZ\"')[['Now_SecDate', 'Section_StartDate', 'NowSec_PGV_MaxRollAvg_UpCoverDays', 'PostSecStart_PRA_MaxRate_Date', 'PostSecStart_PRA_MaxRate', 'PostNowSec_PRA_MaxRate_Date', 'PostNowSec_PRA_MaxRate']]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试日期： 2025-03-04\n", "行业: 机械设备, 回撤转折日期: 2025-03-03, 转折日期: 2025-02-26, 转折回升幅度: 1.91, 下行天数: 27, 下行幅度: -19.85\n", "行业: 有色金属, 回撤转折日期: 2025-02-27, 转折日期: 2025-01-13, 转折回升幅度: 3.19, 下行天数: 177, 下行幅度: -25.00\n", "行业: 建筑材料, 回撤转折日期: 2025-02-21, 转折日期: 2025-02-28, 转折回升幅度: 3.23, 下行天数: 478, 下行幅度: -34.42\n", "行业: 房地产, 回撤转折日期: 2025-02-21, 转折日期: 2024-11-07, 转折回升幅度: 3.39, 下行天数: 157, 下行幅度: -35.02\n", "行业: 国防军工, 回撤转折日期: 2025-02-18, 转折日期: 2024-11-11, 转折回升幅度: 5.28, 下行天数: 150, 下行幅度: -22.66\n", "行业: 钢铁, 回撤转折日期: 2025-01-21, 转折日期: 2024-12-11, 转折回升幅度: 7.19, 下行天数: 379, 下行幅度: -17.93\n", "处理股票 600114.SH 时出错: single positional indexer is out-of-bounds\n", "行业排序结果:\n", "1. 国防军工: 16.10, 涨停数/总数:5/136\n", "2. 机械设备: 12.76, 涨停数/总数:15/520\n", "3. 钢铁: 11.39, 涨停数/总数:0/45\n", "4. 有色金属: 8.62, 涨停数/总数:2/131\n", "5. 房地产: 7.31, 涨停数/总数:1/103\n", "6. 建筑材料: 3.26, 涨停数/总数:0/71\n", "2025-03-04 筛选数据存储 \n", "存储PullStart股票条目： 687 \n", "存储TurnBreak股票条目： 156\n", "存储成功\n", "PullStart股票行业分布：\n", "           count\n", "industry       \n", "机械设备        400\n", "国防军工        120\n", "有色金属         91\n", "房地产          34\n", "建筑材料         32\n", "钢铁           10\n"]}], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "# trade_date = get_trade_date(start_date='2025-02-05', end_date='2025-03-06')\n", "# trade_date = trade_date[::-1]\n", "trade_date = ['2025-03-04']\n", "for date in trade_date:\n", "    trend_industry_list = ['建筑材料','房地产', '国防军工','钢铁','机械设备']\n", "    recent_industry_list = ['有色金属']\n", "    end_date, trend_startdate = date, '2025-01-06'\n", "    recent_turndate = '2025-02-28'\n", "    result_df_head, result_df_turn, pull_start_list, result_df_recentindus = track_pullstart_stocks(\n", "        end_date=end_date, trend_startdate=trend_startdate,\n", "        recent_turndate=recent_turndate,\n", "        rise_stop_signal=False,\n", "        industry_list=trend_industry_list,\n", "        limit_num=80, store_mode=True,\n", "        recent_indus=recent_industry_list,\n", "        recentindus_calstartdate='2025-01-06')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Section_StartDate", "rawType": "object", "type": "string"}, {"name": "Section_PeakDate", "rawType": "object", "type": "string"}, {"name": "Peak2Sec_PGV_MinRollAvg2Sec_LastDays", "rawType": "float64", "type": "float"}, {"name": "Peak2Sec_PGV_MinRollAvg", "rawType": "float64", "type": "float"}, {"name": "SectionPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "87506e52-8170-4fc4-8341-6183698c1ef6", "rows": [["1262", "2024-06-06", "2024-05-20", "0.0", "0.073", "0.225"]], "shape": {"columns": 5, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Section_StartDate</th>\n", "      <th>Section_PeakDate</th>\n", "      <th>Peak2Sec_PGV_MinRollAvg2Sec_LastDays</th>\n", "      <th>Peak2Sec_PGV_MinRollAvg</th>\n", "      <th>SectionPeak_PRV_Top3Mean</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1262</th>\n", "      <td>2024-06-06</td>\n", "      <td>2024-05-20</td>\n", "      <td>0.0</td>\n", "      <td>0.073</td>\n", "      <td>0.225</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Section_StartDate Section_PeakDate  Peak2Sec_PGV_MinRollAvg2Sec_LastDays  \\\n", "1262        2024-06-06       2024-05-20                                   0.0   \n", "\n", "      Peak2Sec_PGV_MinRollAvg  SectionPeak_PRV_Top3Mean  \n", "1262                    0.073                     0.225  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp = pull_start_list.query('ts_code==\"603536.SH\"')\n", "stk_temp[['Section_StartDate','Section_PeakDate', 'Peak2Sec_PGV_MinRollAvg2Sec_LastDays', 'Peak2Sec_PGV_MinRollAvg', 'SectionPeak_PRV_Top3Mean',]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pull_start_list2 = pull_start_list.sort_values(by=['industry', 'peak2recentbottom_avgratio'], ascending=[True, False])[['name', 'industry', 'Now_PRA_Rate']]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 2}