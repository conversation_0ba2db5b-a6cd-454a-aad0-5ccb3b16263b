{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["已连接到 base (Python 3.11.5)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from machine_learn.Func_MacinLn import *"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集天数： 12\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2025-03-29 07:56:14,670] A new study created in memory with name: no-name-3f9da500-8a1a-4483-8c8e-254c39af9954\n", "[I 2025-03-29 07:56:53,189] Trial 0 finished with value: 0.351702320544999 and parameters: {'n_estimators': 281, 'max_depth': 7, 'learning_rate': 0.17981352159004418, 'subsample': 0.8955866724588346, 'colsample_bytree': 0.763551137743662, 'gamma': 0.17696638350149602, 'min_child_weight': 1, 'reg_alpha': 0.1172220410318105, 'reg_lambda': 4.148108148316348}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 07:57:06,897] Trial 1 finished with value: 0.11070846982976452 and parameters: {'n_estimators': 148, 'max_depth': 5, 'learning_rate': 0.05520452550077023, 'subsample': 0.9293710017946895, 'colsample_bytree': 0.6458031989885892, 'gamma': 0.28150778797700754, 'min_child_weight': 2, 'reg_alpha': 0.2225870095494648, 'reg_lambda': 1.671980114706023}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 07:57:47,579] Trial 2 finished with value: 0.3246249188959472 and parameters: {'n_estimators': 279, 'max_depth': 10, 'learning_rate': 0.19208208034081856, 'subsample': 0.7153758634896404, 'colsample_bytree': 0.9510102618357356, 'gamma': 0.3727353040927938, 'min_child_weight': 1, 'reg_alpha': 0.4747572389271186, 'reg_lambda': 4.566188595281574}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 07:58:14,569] Trial 3 finished with value: 0.3333847472971425 and parameters: {'n_estimators': 275, 'max_depth': 7, 'learning_rate': 0.1959312197855131, 'subsample': 0.9757567191290696, 'colsample_bytree': 0.8155695882084482, 'gamma': 0.42020712925510156, 'min_child_weight': 1, 'reg_alpha': 0.17500223764183864, 'reg_lambda': 3.237452365261726}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 07:58:57,283] Trial 4 finished with value: 0.28398831869593666 and parameters: {'n_estimators': 203, 'max_depth': 9, 'learning_rate': 0.05058907046526267, 'subsample': 0.9379833935252258, 'colsample_bytree': 0.7017972588541109, 'gamma': 0.366743431559872, 'min_child_weight': 1, 'reg_alpha': 0.18595422623873636, 'reg_lambda': 1.0239037642059952}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 07:59:19,644] Trial 5 finished with value: 0.13523268031990718 and parameters: {'n_estimators': 152, 'max_depth': 7, 'learning_rate': 0.034936905392922143, 'subsample': 0.70457889052774, 'colsample_bytree': 0.7491496431390604, 'gamma': 0.4137676888450515, 'min_child_weight': 2, 'reg_alpha': 0.17673360160148804, 'reg_lambda': 3.3428806005960623}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 07:59:43,286] Trial 6 finished with value: 0.10005851863210546 and parameters: {'n_estimators': 115, 'max_depth': 8, 'learning_rate': 0.02191820611207041, 'subsample': 0.9203104764088953, 'colsample_bytree': 0.8176859758318689, 'gamma': 0.48314435278588885, 'min_child_weight': 2, 'reg_alpha': 0.31141742932465966, 'reg_lambda': 2.240220382326146}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:00:22,945] Trial 7 finished with value: 0.08428608517578416 and parameters: {'n_estimators': 276, 'max_depth': 7, 'learning_rate': 0.011796765182759458, 'subsample': 0.7078551389945557, 'colsample_bytree': 0.6281421551192284, 'gamma': 0.20909282277515706, 'min_child_weight': 2, 'reg_alpha': 0.24909694851074016, 'reg_lambda': 4.595835617575513}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:00:57,958] Trial 8 finished with value: 0.32982555940327857 and parameters: {'n_estimators': 174, 'max_depth': 10, 'learning_rate': 0.12936479619580407, 'subsample': 0.9368902338100868, 'colsample_bytree': 0.619417409099117, 'gamma': 0.06975065154850252, 'min_child_weight': 2, 'reg_alpha': 0.24110458948301855, 'reg_lambda': 1.599225466199249}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:01:15,891] Trial 9 finished with value: 0.235644740906327 and parameters: {'n_estimators': 106, 'max_depth': 8, 'learning_rate': 0.09004198734385174, 'subsample': 0.8663589536130532, 'colsample_bytree': 0.6717119129047958, 'gamma': 0.2793746913650999, 'min_child_weight': 2, 'reg_alpha': 0.16153704463239954, 'reg_lambda': 3.3395001973244507}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:01:32,276] Trial 10 finished with value: 0.21558259366806012 and parameters: {'n_estimators': 231, 'max_depth': 4, 'learning_rate': 0.15130560787252167, 'subsample': 0.8171198939785378, 'colsample_bytree': 0.9063327881007504, 'gamma': 0.005641750418666047, 'min_child_weight': 3, 'reg_alpha': 8.765912618549643e-05, 'reg_lambda': 4.036893596616049}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:02:06,512] Trial 11 finished with value: 0.34977555969718926 and parameters: {'n_estimators': 297, 'max_depth': 6, 'learning_rate': 0.19488058845889836, 'subsample': 0.994854407116134, 'colsample_bytree': 0.8260499267766404, 'gamma': 0.15191151974105763, 'min_child_weight': 1, 'reg_alpha': 0.05963358404949695, 'reg_lambda': 3.8341603053059066}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:02:35,070] Trial 12 finished with value: 0.30624410377256184 and parameters: {'n_estimators': 299, 'max_depth': 5, 'learning_rate': 0.15937969312106556, 'subsample': 0.9930193503546275, 'colsample_bytree': 0.8726406941559715, 'gamma': 0.15536351661287628, 'min_child_weight': 1, 'reg_alpha': 0.04055939562298222, 'reg_lambda': 4.010887311824498}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:03:04,991] Trial 13 finished with value: 0.3267723586648744 and parameters: {'n_estimators': 244, 'max_depth': 6, 'learning_rate': 0.16966746154871498, 'subsample': 0.8413865014908616, 'colsample_bytree': 0.7569559162838939, 'gamma': 0.1282051844992973, 'min_child_weight': 1, 'reg_alpha': 0.07583584438372729, 'reg_lambda': 3.958004720567346}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:03:33,893] Trial 14 finished with value: 0.300071033165719 and parameters: {'n_estimators': 241, 'max_depth': 6, 'learning_rate': 0.11983724881460547, 'subsample': 0.7924333381075663, 'colsample_bytree': 0.8634922213602904, 'gamma': 0.19509231376353808, 'min_child_weight': 3, 'reg_alpha': 0.11227321736382762, 'reg_lambda': 4.954928577145131}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:04:00,003] Trial 15 finished with value: 0.32740403142252855 and parameters: {'n_estimators': 214, 'max_depth': 6, 'learning_rate': 0.17443919453344325, 'subsample': 0.8805350471191555, 'colsample_bytree': 0.7556252769775805, 'gamma': 0.09075023152947859, 'min_child_weight': 1, 'reg_alpha': 0.3452072301321839, 'reg_lambda': 2.582353058299243}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:04:56,518] Trial 16 finished with value: 0.3249667594341668 and parameters: {'n_estimators': 300, 'max_depth': 8, 'learning_rate': 0.08894378315865005, 'subsample': 0.89164644422153, 'colsample_bytree': 0.967173697595513, 'gamma': 0.2506182962716538, 'min_child_weight': 1, 'reg_alpha': 0.10517822608601504, 'reg_lambda': 3.751399732173841}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:05:16,442] Trial 17 finished with value: 0.23160010010498902 and parameters: {'n_estimators': 261, 'max_depth': 4, 'learning_rate': 0.1406123909259791, 'subsample': 0.7596213918467145, 'colsample_bytree': 0.7090765368645143, 'gamma': 0.033638032104997106, 'min_child_weight': 1, 'reg_alpha': 0.013620352090719628, 'reg_lambda': 2.803413243481895}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:05:40,175] Trial 18 finished with value: 0.30455466750029764 and parameters: {'n_estimators': 254, 'max_depth': 5, 'learning_rate': 0.17896052510651494, 'subsample': 0.9692145441788421, 'colsample_bytree': 0.7887131568950099, 'gamma': 0.1645704266985363, 'min_child_weight': 3, 'reg_alpha': 0.07437541908101451, 'reg_lambda': 4.4475517718884525}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:06:07,578] Trial 19 finished with value: 0.2717077385591008 and parameters: {'n_estimators': 226, 'max_depth': 6, 'learning_rate': 0.1022628452262941, 'subsample': 0.9998927971632577, 'colsample_bytree': 0.8471141926281892, 'gamma': 0.10636558458475193, 'min_child_weight': 1, 'reg_alpha': 0.33070645162824147, 'reg_lambda': 3.5480224435861185}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:06:44,286] Trial 20 finished with value: 0.3370512055953992 and parameters: {'n_estimators': 284, 'max_depth': 9, 'learning_rate': 0.19695818660339234, 'subsample': 0.8993097186514181, 'colsample_bytree': 0.9175216725690747, 'gamma': 0.31736078168771986, 'min_child_weight': 2, 'reg_alpha': 0.13105708405978977, 'reg_lambda': 4.985437361876212}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:07:19,468] Trial 21 finished with value: 0.334167485962163 and parameters: {'n_estimators': 280, 'max_depth': 9, 'learning_rate': 0.19454362078112886, 'subsample': 0.891388805697721, 'colsample_bytree': 0.9208193488907944, 'gamma': 0.34069338505172675, 'min_child_weight': 2, 'reg_alpha': 0.136371971304653, 'reg_lambda': 4.895748403646729}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:08:00,791] Trial 22 finished with value: 0.33611551816775304 and parameters: {'n_estimators': 300, 'max_depth': 9, 'learning_rate': 0.1993967914648285, 'subsample': 0.845156728151101, 'colsample_bytree': 0.9984451262629044, 'gamma': 0.2187256995061297, 'min_child_weight': 1, 'reg_alpha': 0.05541767560075671, 'reg_lambda': 4.12262522855052}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:08:37,841] Trial 23 finished with value: 0.33851413099573585 and parameters: {'n_estimators': 263, 'max_depth': 8, 'learning_rate': 0.1783921871756114, 'subsample': 0.9078412333844283, 'colsample_bytree': 0.898796595354447, 'gamma': 0.3145273746214983, 'min_child_weight': 3, 'reg_alpha': 0.11776458190637211, 'reg_lambda': 4.379418915773163}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:09:12,453] Trial 24 finished with value: 0.3369778951185407 and parameters: {'n_estimators': 259, 'max_depth': 7, 'learning_rate': 0.15683731831902586, 'subsample': 0.9538394575521101, 'colsample_bytree': 0.7843814520435947, 'gamma': 0.2491621955258661, 'min_child_weight': 3, 'reg_alpha': 0.09004653029020017, 'reg_lambda': 4.446495387008257}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:09:52,571] Trial 25 finished with value: 0.34915713527201153 and parameters: {'n_estimators': 266, 'max_depth': 8, 'learning_rate': 0.18243995587579606, 'subsample': 0.911617190972833, 'colsample_bytree': 0.844047894524297, 'gamma': 0.16424986015117832, 'min_child_weight': 3, 'reg_alpha': 0.03679968330480325, 'reg_lambda': 3.7136786890112736}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:10:23,391] Trial 26 finished with value: 0.31729038811621463 and parameters: {'n_estimators': 285, 'max_depth': 6, 'learning_rate': 0.1375540663541551, 'subsample': 0.9613547060053055, 'colsample_bytree': 0.8416613636695512, 'gamma': 0.15859594188394033, 'min_child_weight': 3, 'reg_alpha': 0.02778610621513269, 'reg_lambda': 3.591993974781823}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:10:52,933] Trial 27 finished with value: 0.33717951125864537 and parameters: {'n_estimators': 191, 'max_depth': 8, 'learning_rate': 0.16590430528576008, 'subsample': 0.868298321854379, 'colsample_bytree': 0.7347747591320138, 'gamma': 0.12946513270018695, 'min_child_weight': 2, 'reg_alpha': 0.0507894571261504, 'reg_lambda': 2.956919200770675}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:11:30,491] Trial 28 finished with value: 0.3380852637784026 and parameters: {'n_estimators': 244, 'max_depth': 7, 'learning_rate': 0.17996917008112298, 'subsample': 0.8026389204359096, 'colsample_bytree': 0.8032376370192383, 'gamma': 0.06303331942614554, 'min_child_weight': 1, 'reg_alpha': 0.0042621953777441934, 'reg_lambda': 3.669232260576267}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:11:57,776] Trial 29 finished with value: 0.19504081450487928 and parameters: {'n_estimators': 270, 'max_depth': 5, 'learning_rate': 0.07008977670632299, 'subsample': 0.9167726299121917, 'colsample_bytree': 0.7778467503894682, 'gamma': 0.19238918890038786, 'min_child_weight': 3, 'reg_alpha': 0.2846400305990024, 'reg_lambda': 2.417181211322794}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:12:27,680] Trial 30 finished with value: 0.31512520453253057 and parameters: {'n_estimators': 223, 'max_depth': 6, 'learning_rate': 0.14630049741577872, 'subsample': 0.9422900136832741, 'colsample_bytree': 0.8345433432594829, 'gamma': 0.2460436345408099, 'min_child_weight': 1, 'reg_alpha': 0.20454309869520715, 'reg_lambda': 4.166816178053661}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:13:04,388] Trial 31 finished with value: 0.34439523281504797 and parameters: {'n_estimators': 257, 'max_depth': 8, 'learning_rate': 0.18176356041026281, 'subsample': 0.9093189727582203, 'colsample_bytree': 0.8896824858179077, 'gamma': 0.3011988881576037, 'min_child_weight': 3, 'reg_alpha': 0.13155236979953894, 'reg_lambda': 4.296295336920825}. Best is trial 0 with value: 0.351702320544999.\n", "[I 2025-03-29 08:13:43,033] Trial 32 finished with value: 0.3518923954841102 and parameters: {'n_estimators': 289, 'max_depth': 7, 'learning_rate': 0.17932551071018224, 'subsample': 0.8682496039898672, 'colsample_bytree': 0.8675587454275608, 'gamma': 0.2809458002136637, 'min_child_weight': 3, 'reg_alpha': 0.13767787907503054, 'reg_lambda': 3.8115709220610112}. Best is trial 32 with value: 0.3518923954841102.\n", "[I 2025-03-29 08:14:26,675] Trial 33 finished with value: 0.35272458798651285 and parameters: {'n_estimators': 291, 'max_depth': 7, 'learning_rate': 0.16412746977089324, 'subsample': 0.8648845168383059, 'colsample_bytree': 0.8200876941694155, 'gamma': 0.17680751505821146, 'min_child_weight': 3, 'reg_alpha': 0.42084642591460425, 'reg_lambda': 3.8393884083931766}. Best is trial 33 with value: 0.35272458798651285.\n", "[I 2025-03-29 08:15:06,590] Trial 34 finished with value: 0.3538257571530947 and parameters: {'n_estimators': 290, 'max_depth': 7, 'learning_rate': 0.1887062731299481, 'subsample': 0.8212134603689221, 'colsample_bytree': 0.8181318492543548, 'gamma': 0.22776738274769276, 'min_child_weight': 3, 'reg_alpha': 0.48807389844619353, 'reg_lambda': 3.1360587142638385}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:15:46,077] Trial 35 finished with value: 0.3354250235093432 and parameters: {'n_estimators': 291, 'max_depth': 7, 'learning_rate': 0.16006787307015993, 'subsample': 0.8186563985184371, 'colsample_bytree': 0.718248625390105, 'gamma': 0.2755295956450342, 'min_child_weight': 3, 'reg_alpha': 0.4949715301641301, 'reg_lambda': 3.1936908465747025}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:16:27,351] Trial 36 finished with value: 0.33379608409118305 and parameters: {'n_estimators': 286, 'max_depth': 7, 'learning_rate': 0.11988687194514921, 'subsample': 0.749659754392407, 'colsample_bytree': 0.8726685251182679, 'gamma': 0.22266147113473037, 'min_child_weight': 3, 'reg_alpha': 0.42742571061088985, 'reg_lambda': 3.097982126564976}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:16:48,700] Trial 37 finished with value: 0.3056709054623068 and parameters: {'n_estimators': 142, 'max_depth': 7, 'learning_rate': 0.16706477838085387, 'subsample': 0.8596104014484963, 'colsample_bytree': 0.8064718239539076, 'gamma': 0.39299710553305145, 'min_child_weight': 3, 'reg_alpha': 0.43317062040339904, 'reg_lambda': 3.4118128283679963}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:17:26,060] Trial 38 finished with value: 0.32768406485546625 and parameters: {'n_estimators': 272, 'max_depth': 7, 'learning_rate': 0.128519423700471, 'subsample': 0.8321595071416652, 'colsample_bytree': 0.6658528969791128, 'gamma': 0.4643368501100098, 'min_child_weight': 3, 'reg_alpha': 0.4397152500649221, 'reg_lambda': 2.024445330598671}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:18:03,483] Trial 39 finished with value: 0.3472006006808634 and parameters: {'n_estimators': 279, 'max_depth': 7, 'learning_rate': 0.18373593902879323, 'subsample': 0.7777821599802363, 'colsample_bytree': 0.945999039690366, 'gamma': 0.3512695513100676, 'min_child_weight': 2, 'reg_alpha': 0.3930029743669056, 'reg_lambda': 2.859077433837353}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:18:43,158] Trial 40 finished with value: 0.34935018874344353 and parameters: {'n_estimators': 247, 'max_depth': 8, 'learning_rate': 0.18830794595751998, 'subsample': 0.8780760621853139, 'colsample_bytree': 0.7708684438513732, 'gamma': 0.19644376494692511, 'min_child_weight': 2, 'reg_alpha': 0.37341250157558403, 'reg_lambda': 4.714658519224254}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:19:18,496] Trial 41 finished with value: 0.34717827702954507 and parameters: {'n_estimators': 293, 'max_depth': 6, 'learning_rate': 0.1896161707376461, 'subsample': 0.8291330518946592, 'colsample_bytree': 0.816413213448378, 'gamma': 0.18010126562409112, 'min_child_weight': 3, 'reg_alpha': 0.46672812503157673, 'reg_lambda': 3.880360261420632}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:19:59,020] Trial 42 finished with value: 0.3447462978326336 and parameters: {'n_estimators': 287, 'max_depth': 7, 'learning_rate': 0.16660409615107155, 'subsample': 0.8597708833433654, 'colsample_bytree': 0.8208012958992268, 'gamma': 0.12231501685835316, 'min_child_weight': 3, 'reg_alpha': 0.21121747368068922, 'reg_lambda': 3.3132360900552036}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:20:32,529] Trial 43 finished with value: 0.339273407873843 and parameters: {'n_estimators': 276, 'max_depth': 6, 'learning_rate': 0.1995045397970112, 'subsample': 0.9819541421756067, 'colsample_bytree': 0.8621539961484989, 'gamma': 0.22282644895986778, 'min_child_weight': 1, 'reg_alpha': 0.2872564058702588, 'reg_lambda': 3.860223449956774}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:21:12,011] Trial 44 finished with value: 0.33542221984104637 and parameters: {'n_estimators': 293, 'max_depth': 7, 'learning_rate': 0.15156421045645005, 'subsample': 0.8064737085528091, 'colsample_bytree': 0.7321140760662214, 'gamma': 0.26487423053887704, 'min_child_weight': 3, 'reg_alpha': 0.16201679891632842, 'reg_lambda': 3.4760492828748695}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:21:45,290] Trial 45 finished with value: 0.3348998061581115 and parameters: {'n_estimators': 273, 'max_depth': 6, 'learning_rate': 0.17299798162986252, 'subsample': 0.9321447021431094, 'colsample_bytree': 0.795999877206124, 'gamma': 0.1368583499351809, 'min_child_weight': 2, 'reg_alpha': 0.472490426701224, 'reg_lambda': 4.191140467723117}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:22:03,212] Trial 46 finished with value: 0.2672322034770373 and parameters: {'n_estimators': 171, 'max_depth': 5, 'learning_rate': 0.19066688800609974, 'subsample': 0.839664696048526, 'colsample_bytree': 0.823194379583438, 'gamma': 0.23168335824372865, 'min_child_weight': 2, 'reg_alpha': 0.4021594609294961, 'reg_lambda': 4.71016473639074}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:22:39,871] Trial 47 finished with value: 0.22767662843238012 and parameters: {'n_estimators': 295, 'max_depth': 6, 'learning_rate': 0.0503885231793688, 'subsample': 0.8766646255148458, 'colsample_bytree': 0.7684241210435345, 'gamma': 0.09059983289807685, 'min_child_weight': 1, 'reg_alpha': 0.18665973780093317, 'reg_lambda': 3.82310274217915}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:22:59,928] Trial 48 finished with value: 0.29897940772121984 and parameters: {'n_estimators': 132, 'max_depth': 7, 'learning_rate': 0.15723892183195293, 'subsample': 0.7338431799395618, 'colsample_bytree': 0.8837949585491296, 'gamma': 0.29007698201983634, 'min_child_weight': 2, 'reg_alpha': 0.23275817514462974, 'reg_lambda': 1.636789893769739}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:23:45,353] Trial 49 finished with value: 0.3430069020385096 and parameters: {'n_estimators': 234, 'max_depth': 10, 'learning_rate': 0.17155316335071075, 'subsample': 0.7815052786489056, 'colsample_bytree': 0.8597670103414279, 'gamma': 0.14195905444909546, 'min_child_weight': 3, 'reg_alpha': 0.0673351832409012, 'reg_lambda': 1.4193779147389378}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:24:25,371] Trial 50 finished with value: 0.34431603607697703 and parameters: {'n_estimators': 267, 'max_depth': 7, 'learning_rate': 0.1855398644959548, 'subsample': 0.8561338578266432, 'colsample_bytree': 0.68641239474807, 'gamma': 0.18155572702106731, 'min_child_weight': 1, 'reg_alpha': 0.49596355791512914, 'reg_lambda': 2.6727553572572056}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:25:03,688] Trial 51 finished with value: 0.338986164438903 and parameters: {'n_estimators': 250, 'max_depth': 8, 'learning_rate': 0.1932571135086105, 'subsample': 0.8783398319531177, 'colsample_bytree': 0.7707771570155882, 'gamma': 0.20515460769935726, 'min_child_weight': 2, 'reg_alpha': 0.3745174946145654, 'reg_lambda': 4.78849634220447}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:25:45,603] Trial 52 finished with value: 0.33754880309746055 and parameters: {'n_estimators': 284, 'max_depth': 8, 'learning_rate': 0.1919737913216775, 'subsample': 0.8506892901229343, 'colsample_bytree': 0.7459535478116823, 'gamma': 0.18447146297188266, 'min_child_weight': 1, 'reg_alpha': 0.34994915544670285, 'reg_lambda': 4.627608156779926}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:26:19,552] Trial 53 finished with value: 0.33694555897162043 and parameters: {'n_estimators': 204, 'max_depth': 8, 'learning_rate': 0.18751228108985674, 'subsample': 0.8964831487490976, 'colsample_bytree': 0.8307094286184442, 'gamma': 0.24111688634003997, 'min_child_weight': 2, 'reg_alpha': 0.4587098958209201, 'reg_lambda': 4.010254865718834}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:27:04,694] Trial 54 finished with value: 0.34789625511097516 and parameters: {'n_estimators': 300, 'max_depth': 9, 'learning_rate': 0.17571317262301014, 'subsample': 0.884148077681559, 'colsample_bytree': 0.8007709416642733, 'gamma': 0.20881917050559187, 'min_child_weight': 3, 'reg_alpha': 0.390910393278315, 'reg_lambda': 4.497437281725074}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:27:44,091] Trial 55 finished with value: 0.3430033061670837 and parameters: {'n_estimators': 250, 'max_depth': 7, 'learning_rate': 0.16401150584400664, 'subsample': 0.8701352890247902, 'colsample_bytree': 0.7650860738065423, 'gamma': 0.2663653943063071, 'min_child_weight': 1, 'reg_alpha': 0.41789570654958225, 'reg_lambda': 4.280378987060314}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:28:29,649] Trial 56 finished with value: 0.3516637396588092 and parameters: {'n_estimators': 279, 'max_depth': 8, 'learning_rate': 0.17479795164813133, 'subsample': 0.8191952459814738, 'colsample_bytree': 0.8494683423612424, 'gamma': 0.10587802080069277, 'min_child_weight': 3, 'reg_alpha': 0.36718349969061526, 'reg_lambda': 3.576037631437258}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:29:09,923] Trial 57 finished with value: 0.3327897557645693 and parameters: {'n_estimators': 278, 'max_depth': 7, 'learning_rate': 0.143589101310195, 'subsample': 0.8201367188289047, 'colsample_bytree': 0.8586616795165983, 'gamma': 0.053626282502629796, 'min_child_weight': 3, 'reg_alpha': 0.09739912241153552, 'reg_lambda': 3.1237868069636434}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:29:45,145] Trial 58 finished with value: 0.3351912520826641 and parameters: {'n_estimators': 292, 'max_depth': 6, 'learning_rate': 0.15349498904224868, 'subsample': 0.8029246406960393, 'colsample_bytree': 0.8485045149733266, 'gamma': 0.11064103622099976, 'min_child_weight': 3, 'reg_alpha': 0.4493301838332737, 'reg_lambda': 3.570527738294238}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:30:31,064] Trial 59 finished with value: 0.3465362265222955 and parameters: {'n_estimators': 281, 'max_depth': 8, 'learning_rate': 0.1723438508550049, 'subsample': 0.8350369019130652, 'colsample_bytree': 0.8787102252838469, 'gamma': 0.08285143196327727, 'min_child_weight': 3, 'reg_alpha': 0.14482640548303483, 'reg_lambda': 3.3216986749032262}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:31:03,115] Trial 60 finished with value: 0.3089875170557433 and parameters: {'n_estimators': 263, 'max_depth': 6, 'learning_rate': 0.1332865269703623, 'subsample': 0.9497667492085385, 'colsample_bytree': 0.9107815168485422, 'gamma': 0.14677553219396308, 'min_child_weight': 3, 'reg_alpha': 0.28828144023892394, 'reg_lambda': 3.6946816027080076}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:31:49,228] Trial 61 finished with value: 0.3399016499123014 and parameters: {'n_estimators': 290, 'max_depth': 9, 'learning_rate': 0.18614290617672544, 'subsample': 0.8484525265409628, 'colsample_bytree': 0.7868338206571196, 'gamma': 0.17691949522657743, 'min_child_weight': 3, 'reg_alpha': 0.36912645689867346, 'reg_lambda': 3.9366985613793863}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:32:31,204] Trial 62 finished with value: 0.34167620905370616 and parameters: {'n_estimators': 271, 'max_depth': 8, 'learning_rate': 0.17591618937223982, 'subsample': 0.9245059890223596, 'colsample_bytree': 0.8090398985622228, 'gamma': 0.16515239356599964, 'min_child_weight': 1, 'reg_alpha': 0.4126491396556342, 'reg_lambda': 4.029916736037121}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:33:14,140] Trial 63 finished with value: 0.34373143123038935 and parameters: {'n_estimators': 281, 'max_depth': 8, 'learning_rate': 0.1997803796555515, 'subsample': 0.8880489952551825, 'colsample_bytree': 0.8309489596685138, 'gamma': 0.11022942271282501, 'min_child_weight': 3, 'reg_alpha': 0.30858832523851976, 'reg_lambda': 3.4983353275641567}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:33:58,083] Trial 64 finished with value: 0.3434121910952304 and parameters: {'n_estimators': 300, 'max_depth': 8, 'learning_rate': 0.180855422245899, 'subsample': 0.8249549119017635, 'colsample_bytree': 0.7431600577843493, 'gamma': 0.20184377057518266, 'min_child_weight': 2, 'reg_alpha': 0.36909877976798033, 'reg_lambda': 4.311314511391614}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:34:34,410] Trial 65 finished with value: 0.32710252304493714 and parameters: {'n_estimators': 254, 'max_depth': 7, 'learning_rate': 0.16170631178171574, 'subsample': 0.8099936003005793, 'colsample_bytree': 0.7935289510511014, 'gamma': 0.02559105752818014, 'min_child_weight': 3, 'reg_alpha': 0.3407782486302253, 'reg_lambda': 4.128448337174247}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:35:18,296] Trial 66 finished with value: 0.3391164174276387 and parameters: {'n_estimators': 287, 'max_depth': 9, 'learning_rate': 0.19301353006531838, 'subsample': 0.8999066260133983, 'colsample_bytree': 0.8507115989837393, 'gamma': 0.15723420212953543, 'min_child_weight': 1, 'reg_alpha': 0.08297634754984465, 'reg_lambda': 3.814156292410077}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:35:52,000] Trial 67 finished with value: 0.2996961800997732 and parameters: {'n_estimators': 237, 'max_depth': 7, 'learning_rate': 0.09333772961886717, 'subsample': 0.8641260771176762, 'colsample_bytree': 0.7806962806173967, 'gamma': 0.32533123006625286, 'min_child_weight': 3, 'reg_alpha': 0.31925990700235185, 'reg_lambda': 2.9922735269416085}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:36:17,984] Trial 68 finished with value: 0.31573089647693503 and parameters: {'n_estimators': 275, 'max_depth': 5, 'learning_rate': 0.1779616939710128, 'subsample': 0.9820109112780109, 'colsample_bytree': 0.8365826558796059, 'gamma': 0.25553484633365353, 'min_child_weight': 3, 'reg_alpha': 0.27013888816802756, 'reg_lambda': 3.6133817776258272}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:37:09,449] Trial 69 finished with value: 0.16513049053665266 and parameters: {'n_estimators': 266, 'max_depth': 8, 'learning_rate': 0.020729861756013865, 'subsample': 0.7932414044414505, 'colsample_bytree': 0.8942350115828743, 'gamma': 0.23277660349619375, 'min_child_weight': 2, 'reg_alpha': 0.11355216401568212, 'reg_lambda': 3.254770466085466}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:37:51,156] Trial 70 finished with value: 0.3373701053689348 and parameters: {'n_estimators': 295, 'max_depth': 7, 'learning_rate': 0.16910779210305238, 'subsample': 0.8452662749394334, 'colsample_bytree': 0.8136463474523765, 'gamma': 0.2909955953782631, 'min_child_weight': 1, 'reg_alpha': 0.4780553797348415, 'reg_lambda': 3.410490134607962}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:38:33,031] Trial 71 finished with value: 0.3525552709183503 and parameters: {'n_estimators': 268, 'max_depth': 8, 'learning_rate': 0.18562142356209946, 'subsample': 0.9116581030931125, 'colsample_bytree': 0.9311761515019779, 'gamma': 0.16388168617202764, 'min_child_weight': 3, 'reg_alpha': 0.04090547003715912, 'reg_lambda': 3.724693061919665}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:39:24,538] Trial 72 finished with value: 0.35252086913928976 and parameters: {'n_estimators': 286, 'max_depth': 9, 'learning_rate': 0.1865295830981026, 'subsample': 0.872595271365026, 'colsample_bytree': 0.9326128058516476, 'gamma': 0.12323892809489459, 'min_child_weight': 3, 'reg_alpha': 0.02369768297123976, 'reg_lambda': 3.7579720275940622}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:40:15,554] Trial 73 finished with value: 0.34465740693610064 and parameters: {'n_estimators': 286, 'max_depth': 9, 'learning_rate': 0.18328840676770963, 'subsample': 0.8708682743364891, 'colsample_bytree': 0.9459206393633235, 'gamma': 0.100202768650825, 'min_child_weight': 3, 'reg_alpha': 0.02183259304226899, 'reg_lambda': 3.7123899385440806}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:41:06,931] Trial 74 finished with value: 0.3517668338893607 and parameters: {'n_estimators': 295, 'max_depth': 10, 'learning_rate': 0.1940867473425904, 'subsample': 0.8537713695806941, 'colsample_bytree': 0.9283001819341381, 'gamma': 0.1283459697880845, 'min_child_weight': 3, 'reg_alpha': 0.061301407750949555, 'reg_lambda': 3.7939516836016396}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:41:56,393] Trial 75 finished with value: 0.3527625657588054 and parameters: {'n_estimators': 280, 'max_depth': 9, 'learning_rate': 0.17747690506411912, 'subsample': 0.9079664352207153, 'colsample_bytree': 0.9647482589272025, 'gamma': 0.06678361136492461, 'min_child_weight': 3, 'reg_alpha': 0.036427884237807566, 'reg_lambda': 4.009800415471913}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:42:50,968] Trial 76 finished with value: 0.35295257500726945 and parameters: {'n_estimators': 270, 'max_depth': 10, 'learning_rate': 0.19582357477874313, 'subsample': 0.9028947909673442, 'colsample_bytree': 0.9651856138691722, 'gamma': 0.0427960587870409, 'min_child_weight': 3, 'reg_alpha': 0.050813689560954264, 'reg_lambda': 4.077328292628763}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:43:45,009] Trial 77 finished with value: 0.35182971151555464 and parameters: {'n_estimators': 290, 'max_depth': 10, 'learning_rate': 0.19607528497527857, 'subsample': 0.90275841616112, 'colsample_bytree': 0.97255244376531, 'gamma': 0.019212035836456226, 'min_child_weight': 3, 'reg_alpha': 0.043424889775796595, 'reg_lambda': 4.077736963687414}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:44:36,783] Trial 78 finished with value: 0.3511890261670165 and parameters: {'n_estimators': 272, 'max_depth': 10, 'learning_rate': 0.19998418211860453, 'subsample': 0.9069224562645387, 'colsample_bytree': 0.9776383605340322, 'gamma': 0.002621260970237467, 'min_child_weight': 3, 'reg_alpha': 0.04176845102491919, 'reg_lambda': 4.0898026492999335}. Best is trial 34 with value: 0.3538257571530947.\n", "[I 2025-03-29 08:45:30,263] Trial 79 finished with value: 0.3471895337261514 and parameters: {'n_estimators': 287, 'max_depth': 10, 'learning_rate': 0.18725329330594018, 'subsample': 0.9224271242121104, 'colsample_bytree': 0.9604849729197041, 'gamma': 0.01970082790570799, 'min_child_weight': 3, 'reg_alpha': 0.016260737682456547, 'reg_lambda': 4.238361770901353}. Best is trial 34 with value: 0.3538257571530947.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Best Parameters: {'n_estimators': 290, 'max_depth': 7, 'learning_rate': 0.1887062731299481, 'subsample': 0.8212134603689221, 'colsample_bytree': 0.8181318492543548, 'gamma': 0.22776738274769276, 'min_child_weight': 3, 'reg_alpha': 0.48807389844619353, 'reg_lambda': 3.1360587142638385}\n", "Best Score: 0.3538257571530947\n", "Evaluation Accuracy: 0.9588331963845522\n", "Scores: [0.85554643 0.95513558 0.96450288 0.96663928 0.96548891 0.96138044\n", " 0.95710059 0.95167653 0.94543064 0.94197896]\n", "Mean: 0.9464880227891417\n", "Standard deviation: 0.031336204334526045\n", "XGB训练模型已存储至： XGB_model(Ratio_none_0708).pkl\n"]}], "source": ["result_train = stkpick_model_train(start_date='2024-05-20', \n", "                                   end_date='2024-07-10',\n", "                                   label_style='<PERSON><PERSON>',\n", "                                   model_select='XGB',\n", "                                   pretreat='none',\n", "                                   lag_num=5)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集天数： 53\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2024-12-22 12:33:07,443] A new study created in memory with name: no-name-c5058163-3f6d-49bb-b659-e835976fa2bb\n", "[I 2024-12-22 12:34:57,673] Trial 0 finished with value: 0.6964343423462243 and parameters: {'n_estimators': 360, 'max_depth': 7, 'learning_rate': 0.2226204144935528, 'subsample': 0.9382541971368488, 'colsample_bytree': 0.6515696778502623, 'gamma': 0.07487756254266621, 'min_child_weight': 1, 'reg_alpha': 0.46373236942518803, 'reg_lambda': 1.6243756734840225}. Best is trial 0 with value: 0.6964343423462243.\n", "[I 2024-12-22 12:36:11,347] Trial 1 finished with value: 0.6858953642859708 and parameters: {'n_estimators': 283, 'max_depth': 6, 'learning_rate': 0.22783873085321069, 'subsample': 0.93145894124939, 'colsample_bytree': 0.8421996474857121, 'gamma': 0.27169478396506375, 'min_child_weight': 1, 'reg_alpha': 0.6172788678035572, 'reg_lambda': 1.6497919929622893}. Best is trial 0 with value: 0.6964343423462243.\n", "[I 2024-12-22 12:39:02,049] Trial 2 finished with value: 0.699935365722288 and parameters: {'n_estimators': 365, 'max_depth': 9, 'learning_rate': 0.1562413062417932, 'subsample': 0.8631808177737761, 'colsample_bytree': 0.6225174922449304, 'gamma': 0.0340785617305095, 'min_child_weight': 4, 'reg_alpha': 0.7300937919378403, 'reg_lambda': 1.2156852135455232}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:43:07,376] Trial 3 finished with value: 0.695087794893892 and parameters: {'n_estimators': 287, 'max_depth': 8, 'learning_rate': 0.24470712173786063, 'subsample': 0.9022906150575677, 'colsample_bytree': 0.805890811694537, 'gamma': 0.07086229431558713, 'min_child_weight': 5, 'reg_alpha': 0.2934916384772206, 'reg_lambda': 1.7968786382512834}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:44:45,719] Trial 4 finished with value: 0.6904556716578693 and parameters: {'n_estimators': 277, 'max_depth': 9, 'learning_rate': 0.182820284046528, 'subsample': 0.9583990307348706, 'colsample_bytree': 0.9193894368455002, 'gamma': 0.27630615746050513, 'min_child_weight': 1, 'reg_alpha': 0.31881066386570145, 'reg_lambda': 1.336927112329907}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:46:46,103] Trial 5 finished with value: 0.6926640094796941 and parameters: {'n_estimators': 381, 'max_depth': 7, 'learning_rate': 0.1811851529224329, 'subsample': 0.8553258624388507, 'colsample_bytree': 0.7990239807150693, 'gamma': 0.24667131175698156, 'min_child_weight': 1, 'reg_alpha': 0.6716624823735596, 'reg_lambda': 1.421448995735942}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:48:44,767] Trial 6 finished with value: 0.6944055441847103 and parameters: {'n_estimators': 390, 'max_depth': 7, 'learning_rate': 0.17331304796435648, 'subsample': 0.8052653356382778, 'colsample_bytree': 0.7027620489674686, 'gamma': 0.00877310856570368, 'min_child_weight': 5, 'reg_alpha': 0.4213587196768528, 'reg_lambda': 1.7388903045094302}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:51:24,389] Trial 7 finished with value: 0.6949980250637365 and parameters: {'n_estimators': 288, 'max_depth': 9, 'learning_rate': 0.15614609429090567, 'subsample': 0.8603921337003924, 'colsample_bytree': 0.9262561438723925, 'gamma': 0.11947674824493158, 'min_child_weight': 2, 'reg_alpha': 0.5247060915708369, 'reg_lambda': 1.5936079458333676}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:53:09,193] Trial 8 finished with value: 0.6911917842651442 and parameters: {'n_estimators': 368, 'max_depth': 9, 'learning_rate': 0.23240173983801068, 'subsample': 0.9517783455565424, 'colsample_bytree': 0.9802469633109074, 'gamma': 0.21470509783627384, 'min_child_weight': 1, 'reg_alpha': 0.24945190578421594, 'reg_lambda': 1.7189275480845843}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:54:20,842] Trial 9 finished with value: 0.6764874860856763 and parameters: {'n_estimators': 207, 'max_depth': 7, 'learning_rate': 0.10943265574458583, 'subsample': 0.8629165964974708, 'colsample_bytree': 0.8681056033510215, 'gamma': 0.2255649779137107, 'min_child_weight': 3, 'reg_alpha': 0.3290748699995717, 'reg_lambda': 1.3276849772801715}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:56:29,525] Trial 10 finished with value: 0.6944953140148659 and parameters: {'n_estimators': 334, 'max_depth': 8, 'learning_rate': 0.1225308374638773, 'subsample': 0.9932014239412693, 'colsample_bytree': 0.6129189917057052, 'gamma': 0.1418186848116905, 'min_child_weight': 4, 'reg_alpha': 0.8841657206329618, 'reg_lambda': 1.0872339919448462}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:57:45,150] Trial 11 finished with value: 0.6856081008294733 and parameters: {'n_estimators': 331, 'max_depth': 6, 'learning_rate': 0.20612300508186304, 'subsample': 0.9015479476512174, 'colsample_bytree': 0.6043267387609413, 'gamma': 0.014221615873663246, 'min_child_weight': 3, 'reg_alpha': 0.05102779130594015, 'reg_lambda': 1.069733811223704}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:00:09,789] Trial 12 finished with value: 0.6945312219469281 and parameters: {'n_estimators': 344, 'max_depth': 8, 'learning_rate': 0.13839025974612715, 'subsample': 0.8101299666194082, 'colsample_bytree': 0.6939131733223599, 'gamma': 0.07837765029714244, 'min_child_weight': 4, 'reg_alpha': 0.7890408789200856, 'reg_lambda': 1.9717374491796091}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:02:08,582] Trial 13 finished with value: 0.6945132679808971 and parameters: {'n_estimators': 357, 'max_depth': 7, 'learning_rate': 0.20476024926775216, 'subsample': 0.9273691865282778, 'colsample_bytree': 0.7164512112344051, 'gamma': 0.06186106709150792, 'min_child_weight': 4, 'reg_alpha': 0.9776389509203265, 'reg_lambda': 1.2108066285141552}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:04:55,836] Trial 14 finished with value: 0.6966318359725663 and parameters: {'n_estimators': 400, 'max_depth': 8, 'learning_rate': 0.1497348276889373, 'subsample': 0.8380956804821335, 'colsample_bytree': 0.648738512101859, 'gamma': 0.10481005703732871, 'min_child_weight': 2, 'reg_alpha': 0.7125768622971533, 'reg_lambda': 1.5009389253275067}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:07:39,918] Trial 15 finished with value: 0.6945312219469281 and parameters: {'n_estimators': 390, 'max_depth': 9, 'learning_rate': 0.1452737614692306, 'subsample': 0.838088793428815, 'colsample_bytree': 0.7540587785031824, 'gamma': 0.18242967376671657, 'min_child_weight': 2, 'reg_alpha': 0.7560135653681959, 'reg_lambda': 1.4897456307335364}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:09:57,364] Trial 16 finished with value: 0.694154188660275 and parameters: {'n_estimators': 314, 'max_depth': 8, 'learning_rate': 0.1607528202314196, 'subsample': 0.8269547267144034, 'colsample_bytree': 0.6511400489820123, 'gamma': 0.11259506583048798, 'min_child_weight': 2, 'reg_alpha': 0.6520756987982171, 'reg_lambda': 1.2383163834894297}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:12:18,295] Trial 17 finished with value: 0.6948005314373945 and parameters: {'n_estimators': 255, 'max_depth': 9, 'learning_rate': 0.13137379997900414, 'subsample': 0.8843785786394728, 'colsample_bytree': 0.6560205670666653, 'gamma': 0.04552260698190478, 'min_child_weight': 3, 'reg_alpha': 0.8293586231998331, 'reg_lambda': 1.1614116074696532}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:15:06,943] Trial 18 finished with value: 0.6945850838450213 and parameters: {'n_estimators': 400, 'max_depth': 8, 'learning_rate': 0.10318302921719963, 'subsample': 0.8776123416498145, 'colsample_bytree': 0.759847864218374, 'gamma': 0.10575310833121747, 'min_child_weight': 4, 'reg_alpha': 0.9728716231179708, 'reg_lambda': 1.4110121684174233}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:17:25,115] Trial 19 finished with value: 0.6946209917770836 and parameters: {'n_estimators': 311, 'max_depth': 9, 'learning_rate': 0.15666841545934945, 'subsample': 0.8341266384892538, 'colsample_bytree': 0.7393566429644737, 'gamma': 0.16989118004702167, 'min_child_weight': 2, 'reg_alpha': 0.550087446127974, 'reg_lambda': 1.5216478292577769}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:19:07,100] Trial 20 finished with value: 0.6940644188301195 and parameters: {'n_estimators': 246, 'max_depth': 8, 'learning_rate': 0.19904615801450729, 'subsample': 0.846519348192308, 'colsample_bytree': 0.670909851388734, 'gamma': 0.03650603508346971, 'min_child_weight': 3, 'reg_alpha': 0.6925585828820764, 'reg_lambda': 1.0217079198404622}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:21:02,808] Trial 21 finished with value: 0.6953391504183274 and parameters: {'n_estimators': 367, 'max_depth': 7, 'learning_rate': 0.16949123025866544, 'subsample': 0.9165639177857169, 'colsample_bytree': 0.630518532785455, 'gamma': 0.09451610034388296, 'min_child_weight': 2, 'reg_alpha': 0.45538569818188857, 'reg_lambda': 1.5661176261230767}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:22:33,110] Trial 22 finished with value: 0.6876907608890805 and parameters: {'n_estimators': 354, 'max_depth': 6, 'learning_rate': 0.19114600118311756, 'subsample': 0.8801256544291542, 'colsample_bytree': 0.6697200866492512, 'gamma': 0.037832096614600935, 'min_child_weight': 1, 'reg_alpha': 0.5775037614833183, 'reg_lambda': 1.9049965242285372}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:24:20,188] Trial 23 finished with value: 0.6958598154332293 and parameters: {'n_estimators': 376, 'max_depth': 7, 'learning_rate': 0.2162108396623835, 'subsample': 0.965515210778701, 'colsample_bytree': 0.6380126355092246, 'gamma': 0.1398260873998254, 'min_child_weight': 2, 'reg_alpha': 0.7480741576697898, 'reg_lambda': 1.4591765403237602}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:26:19,938] Trial 24 finished with value: 0.6942080505583683 and parameters: {'n_estimators': 327, 'max_depth': 8, 'learning_rate': 0.14346790823733216, 'subsample': 0.8169655423960038, 'colsample_bytree': 0.600231631041479, 'gamma': 0.09183282122460949, 'min_child_weight': 3, 'reg_alpha': 0.15205849347998474, 'reg_lambda': 1.6528365040548982}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:28:13,841] Trial 25 finished with value: 0.6898272828467809 and parameters: {'n_estimators': 398, 'max_depth': 7, 'learning_rate': 0.12248440890712858, 'subsample': 0.987001756140719, 'colsample_bytree': 0.6757063753897109, 'gamma': 0.05229948897380451, 'min_child_weight': 5, 'reg_alpha': 0.4507731994394024, 'reg_lambda': 1.310738209238572}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:30:59,159] Trial 26 finished with value: 0.6977270279004633 and parameters: {'n_estimators': 353, 'max_depth': 8, 'learning_rate': 0.16422572620102305, 'subsample': 0.9421339111205961, 'colsample_bytree': 0.7291779280173059, 'gamma': 0.002126002391467069, 'min_child_weight': 1, 'reg_alpha': 0.8589968953702525, 'reg_lambda': 1.8306810360814487}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:33:23,921] Trial 27 finished with value: 0.6983374627455206 and parameters: {'n_estimators': 347, 'max_depth': 8, 'learning_rate': 0.167553412234359, 'subsample': 0.8679079074499524, 'colsample_bytree': 0.7273055026998037, 'gamma': 0.0037904198278414097, 'min_child_weight': 4, 'reg_alpha': 0.9009186030855807, 'reg_lambda': 1.8777086797465774}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:36:17,219] Trial 28 finished with value: 0.6988401737943911 and parameters: {'n_estimators': 343, 'max_depth': 9, 'learning_rate': 0.16708452318473158, 'subsample': 0.9158970386756309, 'colsample_bytree': 0.7284496386652011, 'gamma': 0.002824690940660681, 'min_child_weight': 4, 'reg_alpha': 0.8990978247530178, 'reg_lambda': 1.8734403025047466}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:38:55,580] Trial 29 finished with value: 0.6973320406477791 and parameters: {'n_estimators': 313, 'max_depth': 9, 'learning_rate': 0.18450145185898195, 'subsample': 0.9138601780781155, 'colsample_bytree': 0.7766428195643705, 'gamma': 0.024630118209961128, 'min_child_weight': 4, 'reg_alpha': 0.9217489881127978, 'reg_lambda': 1.9052671652919142}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:41:52,819] Trial 30 finished with value: 0.6991094832848578 and parameters: {'n_estimators': 342, 'max_depth': 9, 'learning_rate': 0.17250948297140534, 'subsample': 0.8857892412330469, 'colsample_bytree': 0.8082047452222483, 'gamma': 0.0003096754484130451, 'min_child_weight': 4, 'reg_alpha': 0.9229578634379162, 'reg_lambda': 1.9973283239466362}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:44:50,436] Trial 31 finished with value: 0.6985529103378937 and parameters: {'n_estimators': 344, 'max_depth': 9, 'learning_rate': 0.1733253000575388, 'subsample': 0.8911978242706663, 'colsample_bytree': 0.8397386232319756, 'gamma': 0.0015639685637776901, 'min_child_weight': 4, 'reg_alpha': 0.8192248091356431, 'reg_lambda': 1.980736976516331}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:47:34,270] Trial 32 finished with value: 0.697709073934432 and parameters: {'n_estimators': 338, 'max_depth': 9, 'learning_rate': 0.176538192382235, 'subsample': 0.8912096589155852, 'colsample_bytree': 0.8495372422637604, 'gamma': 0.025082018143083917, 'min_child_weight': 5, 'reg_alpha': 0.8036884849493154, 'reg_lambda': 1.9970923921779704}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:50:15,270] Trial 33 finished with value: 0.6979065675607741 and parameters: {'n_estimators': 318, 'max_depth': 9, 'learning_rate': 0.19298095327736733, 'subsample': 0.897353131371015, 'colsample_bytree': 0.8268500906496331, 'gamma': 0.026123308719463903, 'min_child_weight': 4, 'reg_alpha': 0.9965614029673182, 'reg_lambda': 1.9513452044009059}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:52:48,722] Trial 34 finished with value: 0.6970088692592192 and parameters: {'n_estimators': 297, 'max_depth': 9, 'learning_rate': 0.1530032390837206, 'subsample': 0.9169427209955965, 'colsample_bytree': 0.8817176014098931, 'gamma': 0.0005328667823237725, 'min_child_weight': 5, 'reg_alpha': 0.9322943438630589, 'reg_lambda': 1.8065479478457165}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:55:27,805] Trial 35 finished with value: 0.697116593055406 and parameters: {'n_estimators': 367, 'max_depth': 9, 'learning_rate': 0.18699301694235476, 'subsample': 0.8737710101925196, 'colsample_bytree': 0.8008085269164813, 'gamma': 0.06261373662405166, 'min_child_weight': 4, 'reg_alpha': 0.8497613106334851, 'reg_lambda': 1.7530898264334749}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:58:13,454] Trial 36 finished with value: 0.69767316600237 and parameters: {'n_estimators': 323, 'max_depth': 9, 'learning_rate': 0.17070008352990987, 'subsample': 0.9073741410276617, 'colsample_bytree': 0.8983081015253288, 'gamma': 0.018696598974817742, 'min_child_weight': 4, 'reg_alpha': 0.6332606237555911, 'reg_lambda': 1.8468116273477233}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:01:03,540] Trial 37 finished with value: 0.69767316600237 and parameters: {'n_estimators': 375, 'max_depth': 9, 'learning_rate': 0.17933233862703582, 'subsample': 0.9295493073722909, 'colsample_bytree': 0.8284102823743893, 'gamma': 0.042487719168289154, 'min_child_weight': 5, 'reg_alpha': 0.7630383972308306, 'reg_lambda': 1.9328920665406069}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:04:17,530] Trial 38 finished with value: 0.6975295342741211 and parameters: {'n_estimators': 341, 'max_depth': 9, 'learning_rate': 0.13549703979335576, 'subsample': 0.8515650473541099, 'colsample_bytree': 0.779278115286496, 'gamma': 0.076945735378592, 'min_child_weight': 3, 'reg_alpha': 0.9470939882544727, 'reg_lambda': 1.7001268724446834}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:06:56,442] Trial 39 finished with value: 0.6973499946138103 and parameters: {'n_estimators': 296, 'max_depth': 9, 'learning_rate': 0.16419227753642712, 'subsample': 0.8931582845300036, 'colsample_bytree': 0.9559198535587811, 'gamma': 0.02919372069026845, 'min_child_weight': 5, 'reg_alpha': 0.8305936742787001, 'reg_lambda': 1.7847953833321226}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:09:39,284] Trial 40 finished with value: 0.6941900965923372 and parameters: {'n_estimators': 364, 'max_depth': 9, 'learning_rate': 0.2425297250191757, 'subsample': 0.8872733070406776, 'colsample_bytree': 0.8178713020020999, 'gamma': 0.05171871547072761, 'min_child_weight': 4, 'reg_alpha': 0.8843013450215457, 'reg_lambda': 1.8683677115966724}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:16:17,527] Trial 41 finished with value: 0.6979424754928364 and parameters: {'n_estimators': 348, 'max_depth': 9, 'learning_rate': 0.16912641534746745, 'subsample': 0.8672055181789114, 'colsample_bytree': 0.8541882388801835, 'gamma': 0.001537970106450164, 'min_child_weight': 4, 'reg_alpha': 0.8879219279711739, 'reg_lambda': 1.996675793219151}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:19:35,980] Trial 42 finished with value: 0.6981220151531473 and parameters: {'n_estimators': 385, 'max_depth': 9, 'learning_rate': 0.17539898956560368, 'subsample': 0.8597227968411232, 'colsample_bytree': 0.7874190819033543, 'gamma': 0.012284558433638074, 'min_child_weight': 4, 'reg_alpha': 0.9137423676285752, 'reg_lambda': 1.878709076477902}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:22:01,712] Trial 43 finished with value: 0.6937951093396532 and parameters: {'n_estimators': 348, 'max_depth': 8, 'learning_rate': 0.1591951396978074, 'subsample': 0.8705103693890852, 'colsample_bytree': 0.6993189825643849, 'gamma': 0.01313885899049818, 'min_child_weight': 4, 'reg_alpha': 0.7230217236557506, 'reg_lambda': 1.935560964356229}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:25:06,751] Trial 44 finished with value: 0.6988940356924843 and parameters: {'n_estimators': 338, 'max_depth': 9, 'learning_rate': 0.15008503119031924, 'subsample': 0.9045831100173811, 'colsample_bytree': 0.7561575010440758, 'gamma': 0.013611938733463854, 'min_child_weight': 3, 'reg_alpha': 0.814083424008452, 'reg_lambda': 1.6559031412788126}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:27:25,784] Trial 45 finished with value: 0.6921612984308233 and parameters: {'n_estimators': 335, 'max_depth': 9, 'learning_rate': 0.14721569824570893, 'subsample': 0.9055571348875833, 'colsample_bytree': 0.7614536733990732, 'gamma': 0.2999693181362393, 'min_child_weight': 3, 'reg_alpha': 0.8062448391085492, 'reg_lambda': 1.6424957381476286}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:29:52,467] Trial 46 finished with value: 0.6957700456030738 and parameters: {'n_estimators': 273, 'max_depth': 9, 'learning_rate': 0.1510735563897887, 'subsample': 0.9399257617923131, 'colsample_bytree': 0.7979037448731183, 'gamma': 0.06213522636519753, 'min_child_weight': 3, 'reg_alpha': 0.674934179641244, 'reg_lambda': 1.6910317783587843}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:32:30,895] Trial 47 finished with value: 0.6958598154332293 and parameters: {'n_estimators': 305, 'max_depth': 9, 'learning_rate': 0.1811465529115801, 'subsample': 0.9115563751109556, 'colsample_bytree': 0.8714665678776917, 'gamma': 0.0348802705868958, 'min_child_weight': 3, 'reg_alpha': 0.35606290861879586, 'reg_lambda': 1.3811582968451397}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:35:46,137] Trial 48 finished with value: 0.6986606341340802 and parameters: {'n_estimators': 360, 'max_depth': 9, 'learning_rate': 0.12809465975062173, 'subsample': 0.9202393637465351, 'colsample_bytree': 0.9128040541447489, 'gamma': 0.017153936010032005, 'min_child_weight': 4, 'reg_alpha': 0.6033682758259424, 'reg_lambda': 1.7860880911403014}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:38:56,217] Trial 49 finished with value: 0.6978167977306186 and parameters: {'n_estimators': 360, 'max_depth': 9, 'learning_rate': 0.12005949930946709, 'subsample': 0.9245658237308364, 'colsample_bytree': 0.9805916472612614, 'gamma': 0.05188540180961789, 'min_child_weight': 5, 'reg_alpha': 0.5992254928575689, 'reg_lambda': 1.7801895678599078}. Best is trial 2 with value: 0.699935365722288.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Best Parameters: {'n_estimators': 365, 'max_depth': 9, 'learning_rate': 0.1562413062417932, 'subsample': 0.8631808177737761, 'colsample_bytree': 0.6225174922449304, 'gamma': 0.0340785617305095, 'min_child_weight': 4, 'reg_alpha': 0.7300937919378403, 'reg_lambda': 1.2156852135455232}\n", "Best Score: 0.699935365722288\n", "Evaluation Accuracy: 0.7229443447037702\n", "交叉验证报错： Found input variables with inconsistent numbers of samples: [69623, 55698]\n", "XGB训练模型已存储至： XGB_model(Label_recov_1030).pkl\n"]}], "source": ["result_train = stkpick_model_train(start_date='2024-02-05', end_date='2024-10-30',  model_select='XGB', label_style='Label', pretreat='recov', lag_num=30)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py:428: FutureWarning: DataFrame.applymap has been deprecated. Use DataFrame.map instead.\n", "  is_string = df.applymap(lambda x: isinstance(x, str))\n"]}, {"ename": "ValueError", "evalue": "Feature shape mismatch, expected: 656, got 658", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mfunction_ai\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mFunc_Base\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m get_stock_info\n\u001b[1;32m      3\u001b[0m stk_list \u001b[38;5;241m=\u001b[39m get_stock_info()[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mts_code\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mtolist()\n\u001b[0;32m----> 4\u001b[0m result_check, result_predict \u001b[38;5;241m=\u001b[39m stkpick_model_predict(predict_date\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2024-08-20\u001b[39m\u001b[38;5;124m'\u001b[39m, stk_list\u001b[38;5;241m=\u001b[39mstk_list, model_select\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mXGB\u001b[39m\u001b[38;5;124m'\u001b[39m, label_style\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mLabel\u001b[39m\u001b[38;5;124m'\u001b[39m, pretreat\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mnone\u001b[39m\u001b[38;5;124m'\u001b[39m, model_date\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2024-10-30\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m~/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py:782\u001b[0m, in \u001b[0;36mstkpick_model_predict\u001b[0;34m(predict_date, stk_list, label_style, model_select, model_date, store_mode, pretreat, lag_num, industry)\u001b[0m\n\u001b[1;32m    779\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m无符合PreTreat条件的待选品种\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m    780\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m--> 782\u001b[0m result \u001b[38;5;241m=\u001b[39m clf_rf(origin_data, model_select\u001b[38;5;241m=\u001b[39mmodel_select,\n\u001b[1;32m    783\u001b[0m                 model_style\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpredict\u001b[39m\u001b[38;5;124m'\u001b[39m, label_style\u001b[38;5;241m=\u001b[39mlabel_style, model_date\u001b[38;5;241m=\u001b[39mmodel_date,\n\u001b[1;32m    784\u001b[0m                 treat_style\u001b[38;5;241m=\u001b[39mpretreat, lag_num\u001b[38;5;241m=\u001b[39mlag_num)\n\u001b[1;32m    786\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mLabel\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMean_Label\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStrength_Label\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m    787\u001b[0m     sort_label \u001b[38;5;241m=\u001b[39m rank_label[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py:711\u001b[0m, in \u001b[0;36mclf_rf\u001b[0;34m(result, model_select, model_style, label_style, model_date, treat_style, lag_num)\u001b[0m\n\u001b[1;32m    708\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(pkl_filename, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mrb\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m file:\n\u001b[1;32m    709\u001b[0m     clf_pred \u001b[38;5;241m=\u001b[39m pickle\u001b[38;5;241m.\u001b[39mload(file)\n\u001b[0;32m--> 711\u001b[0m y_label \u001b[38;5;241m=\u001b[39m clf_pred\u001b[38;5;241m.\u001b[39mpredict(X_train_origin)\n\u001b[1;32m    712\u001b[0m y_pro \u001b[38;5;241m=\u001b[39m clf_pred\u001b[38;5;241m.\u001b[39mpredict_proba(X_train_origin)\n\u001b[1;32m    713\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mLabel\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMean_Label\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStrength_Label\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.11/site-packages/xgboost/sklearn.py:1565\u001b[0m, in \u001b[0;36mXGBClassifier.predict\u001b[0;34m(self, X, output_margin, validate_features, base_margin, iteration_range)\u001b[0m\n\u001b[1;32m   1556\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpredict\u001b[39m(\n\u001b[1;32m   1557\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1558\u001b[0m     X: ArrayLike,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1562\u001b[0m     iteration_range: Optional[IterationRange] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1563\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ArrayLike:\n\u001b[1;32m   1564\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m config_context(verbosity\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mverbosity):\n\u001b[0;32m-> 1565\u001b[0m         class_probs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mpredict(\n\u001b[1;32m   1566\u001b[0m             X\u001b[38;5;241m=\u001b[39mX,\n\u001b[1;32m   1567\u001b[0m             output_margin\u001b[38;5;241m=\u001b[39moutput_margin,\n\u001b[1;32m   1568\u001b[0m             validate_features\u001b[38;5;241m=\u001b[39mvalidate_features,\n\u001b[1;32m   1569\u001b[0m             base_margin\u001b[38;5;241m=\u001b[39mbase_margin,\n\u001b[1;32m   1570\u001b[0m             iteration_range\u001b[38;5;241m=\u001b[39miteration_range,\n\u001b[1;32m   1571\u001b[0m         )\n\u001b[1;32m   1572\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m output_margin:\n\u001b[1;32m   1573\u001b[0m             \u001b[38;5;66;03m# If output_margin is active, simply return the scores\u001b[39;00m\n\u001b[1;32m   1574\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m class_probs\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.11/site-packages/xgboost/sklearn.py:1186\u001b[0m, in \u001b[0;36mXGBModel.predict\u001b[0;34m(self, X, output_margin, validate_features, base_margin, iteration_range)\u001b[0m\n\u001b[1;32m   1184\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_can_use_inplace_predict():\n\u001b[1;32m   1185\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1186\u001b[0m         predts \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_booster()\u001b[38;5;241m.\u001b[39minplace_predict(\n\u001b[1;32m   1187\u001b[0m             data\u001b[38;5;241m=\u001b[39mX,\n\u001b[1;32m   1188\u001b[0m             iteration_range\u001b[38;5;241m=\u001b[39miteration_range,\n\u001b[1;32m   1189\u001b[0m             predict_type\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmargin\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m output_margin \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   1190\u001b[0m             missing\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmissing,\n\u001b[1;32m   1191\u001b[0m             base_margin\u001b[38;5;241m=\u001b[39mbase_margin,\n\u001b[1;32m   1192\u001b[0m             validate_features\u001b[38;5;241m=\u001b[39mvalidate_features,\n\u001b[1;32m   1193\u001b[0m         )\n\u001b[1;32m   1194\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m _is_cupy_alike(predts):\n\u001b[1;32m   1195\u001b[0m             \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mcupy\u001b[39;00m  \u001b[38;5;66;03m# pylint: disable=import-error\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.11/site-packages/xgboost/core.py:2520\u001b[0m, in \u001b[0;36mBooster.inplace_predict\u001b[0;34m(self, data, iteration_range, predict_type, missing, validate_features, base_margin, strict_shape)\u001b[0m\n\u001b[1;32m   2516\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\n\u001b[1;32m   2517\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m`shape` attribute is required when `validate_features` is True.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   2518\u001b[0m         )\n\u001b[1;32m   2519\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(data\u001b[38;5;241m.\u001b[39mshape) \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m1\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnum_features() \u001b[38;5;241m!=\u001b[39m data\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m1\u001b[39m]:\n\u001b[0;32m-> 2520\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   2521\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFeature shape mismatch, expected: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnum_features()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   2522\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgot \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m1\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   2523\u001b[0m         )\n\u001b[1;32m   2525\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m _is_np_array_like(data):\n\u001b[1;32m   2526\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdata\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _ensure_np_dtype\n", "\u001b[0;31mValueError\u001b[0m: Feature shape mismatch, expected: 656, got 658"]}], "source": ["from machine_learn.Func_MacinLn import *\n", "from function_ai.Func_Base import get_stock_info\n", "stk_list = get_stock_info()['ts_code'].tolist()\n", "result_check, result_predict = stkpick_model_predict(predict_date='2024-08-20', stk_list=stk_list, model_select='XGB', label_style='Label', pretreat='none', model_date='2024-10-30')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}