{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["已连接到 base (Python 3.11.5)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["from function_ai.Func_Base import get_stock_data\n", "import pandas as pd\n", "stk_code = '603686.SH'\n", "start_date, end_date = '2024-07-08', '2024-10-18'"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["stk_data = get_stock_data(stk_code=stk_code, start_date=start_date, end_date=end_date)\n", "stk_data = stk_data.set_index('trade_date')\n", "stk_data['mean_price'] = (stk_data['close'] + stk_data['open'])/2\n", "stk_data['mean_ratio'] = stk_data['mean_price'].pct_change()*100"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["from function_ai.Func_Base import section_stat\n", "section_rise, section_drop, day_list = section_stat(stk_code=stk_code, start_date=start_date, end_date=end_date)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["section_all = pd.concat([section_rise, section_drop], join='inner', ignore_index=True).sort_values(by='start_date', ascending=True)\n", "section_all = section_all.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "check_data = pd.DataFrame()\n", "for index in section_all.index:\n", "    check_start = section_all.loc[index, 'start_date']\n", "    check_end = section_all.loc[index, 'end_date']\n", "    if section_all.loc[index, 'sumratio'] > 0:\n", "        reverse_std = stk_data.loc[check_start:check_end].iloc[1:].query('mean_ratio < 0')['mean_ratio'].std() \\\n", "                      if len(stk_data.loc[check_start:check_end].iloc[1:].query('mean_ratio < 0')) > 1 else 0\n", "    else:\n", "        reverse_std = stk_data.loc[check_start:check_end].iloc[1:].query('mean_ratio > 0')['mean_ratio'].std() \\\n", "                      if len(stk_data.loc[check_start:check_end].iloc[1:].query('mean_ratio > 0')) > 1 else 0\n", "    temp = pd.DataFrame({'start_date': check_start, 'end_date': check_end, 'last_days':section_all.loc[index, 'lastdays'], 'avgratio': round(stk_data.loc[check_start:check_end,'mean_ratio'].mean(),3), 'avgstd': round(stk_data.loc[check_start:check_end, 'mean_ratio'].std(),3), 'reverse_std': reverse_std}, index=range(0,1))\n", "    check_data = pd.concat([check_data, temp], ignore_index=True)\n"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["trade_date\n", "2024-10-11   -3.105935\n", "2024-10-14    0.457928\n", "2024-10-15   -1.766382\n", "2024-10-16   -2.378190\n", "2024-10-17    0.475342\n", "2024-10-18    0.650503\n", "Name: mean_ratio, dtype: float64"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_data.loc['2024-10-11':'2024-10-21', 'mean_ratio']"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}