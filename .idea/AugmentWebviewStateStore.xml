<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>