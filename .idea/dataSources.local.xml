<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="PY-251.25410.122">
    <data-source name="StockCloud" uuid="bb40719d-dc6e-467d-903e-fe00fd61040d">
      <database-info product="MySQL" version="8.0.25" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-java-8.0.25 (Revision: 08be9e9b4cba6aa115f9b27b215887af40b159e0)" dbms="MYSQL" exact-version="8.0.25" exact-driver-version="8.0">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <secret-storage>master_key</secret-storage>
      <user-name>sql_user</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="@" />
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>