import numpy as np
import pandas as pd
from function_ai.Func_Base import get_stock_data, get_stock_info, get_trade_date
from function_ai.swindex_funcs import get_swindex_data

def calculate_beta(stock_returns, index_returns):
    """计算单只股票相对行业指数的beta系数
    
    参数:
    stock_returns: 股票日收益率序列
    index_returns: 行业指数日收益率序列
    
    返回:
    beta: beta系数
    """
    import numpy as np
    # 对齐数据
    common_dates = stock_returns.index.intersection(index_returns.index)
    if len(common_dates) < 2:  # 至少需要2个数据点才能计算beta
        return np.nan
        
    # 按日期排序
    common_dates = sorted(common_dates)
    stock_returns = stock_returns[common_dates]
    index_returns = index_returns[common_dates]
    
    # 移除缺失值
    valid_mask = ~(np.isnan(stock_returns) | np.isnan(index_returns))
    stock_returns = stock_returns[valid_mask]
    index_returns = index_returns[valid_mask]
    
    if len(stock_returns) < 2:  # 再次检查有效数据点
        return np.nan
        
    # 计算beta
    try:
        covariance = np.cov(stock_returns, index_returns)[0,1]
        index_variance = np.var(index_returns)
        beta = covariance / index_variance if index_variance != 0 else np.nan
        return beta
    except:
        return np.nan

def get_industry_beta(industry, start_date, end_date):
    """计算行业内所有股票相对行业指数的beta系数
    
    参数:
    industry: 行业名称
    start_date: 起始日期
    end_date: 结束日期
    
    返回:
    beta_df: 包含股票代码、名称和beta系数的DataFrame
    """
    # 获取申万行业指数数据
    index_data = get_swindex_data(start_date=start_date, end_date=end_date)
    if industry not in index_data.columns:
        raise KeyError(f"未找到行业 {industry} 的指数数据")
        
    # 获取行业指数收益率序列,确保按日期排序
    index_data = index_data.sort_index()
    index_returns = index_data[industry].pct_change().dropna()
    
    # 获取行业内股票列表
    stock_info = get_stock_info()
    if 'industry' not in stock_info.columns:
        raise KeyError(f"stock_info中缺少'industry'列。当前列包含:{stock_info.columns.tolist()}")
        
    industry_stocks = stock_info[stock_info['industry'] == industry]['ts_code'].tolist()
    if not industry_stocks:
        raise ValueError(f"行业 {industry} 中未找到股票")
    
    # 获取股票数据并计算收益率,确保按日期排序
    stock_data = get_stock_data(stk_code=industry_stocks, start_date=start_date, end_date=end_date)
    stock_data = stock_data.sort_values(['ts_code', 'trade_date'])
    stock_data['return'] = stock_data.groupby('ts_code')['close'].pct_change()
    
    # 计算每只股票的beta系数
    beta_list = []
    for ts_code in industry_stocks:
        stock_returns = stock_data[stock_data['ts_code'] == ts_code].set_index('trade_date')['return']
        if len(stock_returns) > 0:  # 确保有数据
            beta = calculate_beta(stock_returns, index_returns)
            beta_list.append({
                'ts_code': ts_code,
                'name': stock_info[stock_info['ts_code'] == ts_code]['name'].iloc[0],
                'beta': beta
            })
    
    # 转换为DataFrame
    beta_df = pd.DataFrame(beta_list)
    # 移除beta为nan的记录
    beta_df = beta_df.dropna(subset=['beta'])
    
    return beta_df

def get_beta_stats(industry, start_date, end_date):
    """获取行业beta系数的统计信息和后续收益率
    
    参数:
    industry: 行业名称
    start_date: 起始日期
    end_date: 结束日期
    
    返回:
    stats_dict: 包含统计信息的字典
    result_df: 包含个股beta和后续收益的DataFrame
    """
    beta_df = get_industry_beta(industry, start_date, end_date)
    
    if len(beta_df) == 0:
        raise ValueError(f"行业 {industry} 在指定时间段内无有效beta数据")

    
    # 获取后续20个交易日数据计算最大收益率,确保按日期排序
    # 获取end_date后20个交易日的日期
    trade_dates = get_trade_date(start_date=end_date)
    end_date_20d = trade_dates[20] if len(trade_dates) > 20 else trade_dates[-1]
    
    stock_data = get_stock_data(stk_code=beta_df['ts_code'].tolist(),
                               start_date=end_date,
                               end_date=end_date_20d)
    stock_data = stock_data.sort_values(['ts_code', 'trade_date'])
    stock_data['base_close'] = stock_data.groupby('ts_code')['close'].transform('first')
    stock_data['return_ratio'] = (stock_data['close'] / stock_data['base_close'] - 1) * 100
    max_returns = stock_data.groupby('ts_code')['return_ratio'].agg('max').reset_index()
    
    # 合并beta和收益率数据
    result_df = pd.merge(beta_df, max_returns, on='ts_code', how='left')
    result_df = result_df.rename(columns={'return_ratio': 'max_return_20d'})
    result_df = result_df.sort_values('beta', ascending=True)
    
    # 计算beta与后20交易日收益率的相关系数
    corr = result_df['beta'].corr(result_df['max_return_20d'])
    
    stats_dict = {
        'industry': industry,
        'period': f"{start_date} to {end_date}",
        'stock_count': len(beta_df),
        'avg_beta': beta_df['beta'].mean(),
        'median_beta': beta_df['beta'].median(),
        'max_beta': beta_df['beta'].max(),
        'min_beta': beta_df['beta'].min(),
        'beta_std': beta_df['beta'].std(),
        'beta_return_corr': corr
    }
    
    return stats_dict, result_df
