""" 函数汇总 """

from dateutil import parser
import pdb
import time
from pathlib import Path

import numpy as np
import pandas as pd
from sqlalchemy import create_engine
from tqdm import tqdm

import os
import sys

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from function_ai.trend_func import peak_valley_turn, trend_judge


import tushare as ts

ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')


def get_trade_date(start_date=None, end_date=None, enginesignal=None, loc=None):
    """获取指定起止日期范围内的交易日期"""
    if enginesignal is None:
        import config.config_Ali as config
        conf = config.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')
    else:
        engine = enginesignal
    sql = """select distinct trade_date from stocksfit.index_data order by trade_date ASC"""
    for _ in range(3):
        try:
            trade_date = pd.read_sql_query(sql, con=engine)
        except:
            time.sleep(1)
        else:
            break
    if start_date is not None:
        trade_date = trade_date.query('trade_date>=@start_date')
    if end_date is not None:
        trade_date = trade_date.query('trade_date<=@end_date')
    if loc is not None:
        trade_date = trade_date.values[loc].tolist()[-1]
    else:
        trade_date = trade_date['trade_date'].values
    return trade_date


def get_stock_data(stk_code=None, start_date=None, end_date=None, enginesignal=None):
    """从数据库获取股票行情数据"""
    if enginesignal is None:
        import config.config_Ali as config
        conf = config.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')
    else:
        engine = enginesignal

    sql = """select distinct trade_date from stocksfit.stock_data group by trade_date"""
    for _ in range(3):
        try:
            trade_date = pd.read_sql_query(sql, con=engine)
        except:
            time.sleep(3)
        else:
            break
    if start_date is None:
        start_date = trade_date['trade_date'].min()
    if end_date is None:
        end_date = trade_date['trade_date'].max()
    if pd.to_datetime(end_date, format='%Y-%m-%d') < pd.to_datetime(start_date, format='%Y-%m-%d') \
            or pd.to_datetime(end_date, format='%Y-%m-%d') \
            < pd.to_datetime(trade_date['trade_date'].min(), format='%Y-%m-%d') \
            or pd.to_datetime(start_date, format='%Y-%m-%d') \
            > pd.to_datetime(trade_date['trade_date'].max(), format='%Y-%m-%d'):
        print('所选时间区间无股票数据!')
        return None
    if isinstance(stk_code, str):
        stk_code = [stk_code]
    elif isinstance(stk_code, np.ndarray):
        stk_code = stk_code.tolist()

    stock_data = pd.DataFrame()
    if stk_code is None:
        sql = f"""select
                    ts_code, trade_date, close, open, high, low, pre_close, vol, turnover_rate as turnover, adj_factor, 
                    total_share, total_mv
                 from stocksfit.stock_data
                 where trade_date between '{start_date}' and '{end_date}'
              """
        for _ in range(3):
            try:
                stock_data = pd.read_sql_query(sql=sql, con=engine)
            except Exception as e:
                time.sleep(3)
            else:
                break
    else:
        ts_codes = ','.join(["'%s'" % item for item in stk_code])
        sql = f"""select 
                    ts_code, trade_date, close, open, high, low, pre_close, vol, turnover_rate as turnover, adj_factor,
                    total_share, total_mv
                 from stocksfit.stock_data
                 where trade_date between '{start_date}' and '{end_date}' and ts_code in ({ts_codes})
              """
        for _ in range(3):
            try:
                stock_data = pd.read_sql_query(sql=sql, con=engine)
            except:
                time.sleep(3)
            else:
                break
    if len(stock_data) == 0:
        return []
    stock_data_last = stock_data.groupby('ts_code').last()
    stock_data['newadj'] = stock_data['ts_code'].apply(
        lambda fn: stock_data_last.loc[fn, 'adj_factor'])
    stock_data['close'] = stock_data['close'] * \
                          stock_data['adj_factor'] / stock_data['newadj']
    stock_data['open'] = stock_data['open'] * \
                         stock_data['adj_factor'] / stock_data['newadj']
    stock_data['high'] = stock_data['high'] * \
                         stock_data['adj_factor'] / stock_data['newadj']
    stock_data['low'] = stock_data['low'] * \
                        stock_data['adj_factor'] / stock_data['newadj']
    stock_data['pre_close'] = stock_data['pre_close'] * \
                              stock_data['adj_factor'] / stock_data['newadj']
    stock_data['pct_chg'] = (stock_data['close'] / stock_data['pre_close'] - 1) * 100
    if enginesignal is None:
        engine.dispose()
    stock_data = stock_data.sort_values(
        ['trade_date', 'ts_code'], ascending=[True, True])
    stock_data = stock_data.drop_duplicates(
        subset=['ts_code', 'trade_date'], keep='last')
    return stock_data


def get_index_data(stk_code=None, start_date=None, end_date=None):
    """获取指数行情数据"""
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')

    sql = """select MIN(trade_date) as mindate, MAX(trade_date) as maxdate from stocksfit.index_data"""
    for _ in range(3):
        try:
            trade_date = pd.read_sql_query(sql, con=engine)
        except:
            time.sleep(2)
        else:
            break
    if start_date is None:
        start_date = trade_date['mindate'].iloc[-1]
    if end_date is None:
        end_date = trade_date['maxdate'].iloc[-1]
    if stk_code is None:
        sql = f"""select 
                    ts_code, trade_date, 
                    cast(close as float(4)) as 'close', 
                    cast(open as float(4)) as 'open',
                    cast(high as float(4)) as 'high',
                    cast(low as float(4)) as 'low',
                    cast(pre_close as float(4)) as 'pre_close',
                    cast(vol as float(4)) as 'vol',
                    cast(amount as float(4)) as 'amount'
                from stocksfit.index_data 
                where trade_date between '{start_date}' and '{end_date}'
                order by ts_code, trade_date ASC """
    else:
        sql = f"""select 
                ts_code, trade_date, 
                cast(close as float(4)) as 'close', 
                cast(open as float(4)) as 'open',
                cast(high as float(4)) as 'high',
                cast(low as float(4)) as 'low',
                cast(pre_close as float(4)) as 'pre_close',
                cast(vol as float(4)) as 'vol',
                cast(amount as float(4)) as 'amount'
                from stocksfit.index_data 
                where trade_date between '{start_date}' and '{end_date}' and ts_code='{stk_code}'
                order by ts_code, trade_date ASC """
    index_data = pd.read_sql_query(sql, con=engine)
    engine.dispose()
    index_data = index_data.drop_duplicates(
        subset=['ts_code', 'trade_date'], keep='last')
    index_data = index_data.sort_values(
        ['trade_date', 'ts_code'], ascending=[True, True])
    return index_data


def get_stock_info(industry=None):
    """从数据库获取股票基本信息"""
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    try:
        if industry is not None:
            sql = f"""select * from stocksfit.stockInfo where industry='{industry}'"""
            for _ in range(3):
                try:
                    stock_info = pd.read_sql_query(sql, con=engine)
                except Exception as e:
                    print(f"Error:{e}")
                    time.sleep(3)
                else:
                    break
        else:
            sql = """select * from stocksfit.stockInfo"""
            for _ in range(3):
                try:
                    stock_info = pd.read_sql_query(sql, con=engine)
                except Exception as e:
                    print(f"Error:{e}")
                    time.sleep(3)
                else:
                    break
        stock_info = stock_info.drop_duplicates(
            subset=['ts_code'], keep='first')
    finally:
        engine.dispose()
    return stock_info


def coef_regress(stkclose, mode='coef'):
    """ 函数coef_regress，计算R平方、斜率、截距和斜率截距乘积 """
    # if isinstance(stkclose, pd.Series):
    #     stkclose = stkclose.values
    stkclose = stkclose.dropna().values
    y = (stkclose[1:] / stkclose[0] - 1) * 100  # 区间价格cumsum变化幅度
    if len(y) >= 3:
        ybar = np.mean(y)
        day_count = range(0, len(y))
        coeff1 = np.polyfit(day_count, y, 1)
        yhat1 = np.polyval(coeff1, day_count)
        coeff2 = np.polyfit(day_count, y, 2)
        yhat2 = np.polyval(coeff2, day_count)
        if np.sum((y - ybar) ** 2) != 0:
            r_squared1 = np.sum((yhat1 - ybar) ** 2) / np.sum((y - ybar) ** 2)
            r_squared2 = np.sum((yhat2 - ybar) ** 2) / np.sum((y - ybar) ** 2)
        else:
            r_squared1 = 0
            r_squared2 = 0
        if r_squared1 > r_squared2:
            r_squared = r_squared1
            slope = coeff1[0]
            intercept = coeff1[1]
            si_pro = coeff1[0] * coeff1[1]
        else:
            r_squared = r_squared2
            slope = coeff2[0]
            intercept = coeff2[1]
            si_pro = coeff2[0] * coeff2[1]
        y_adj = y - yhat1
        if len(y_adj[y_adj < 0]) > 0 \
                and (y_adj.max() - y_adj.min()) * slope != 0:
            err = (y_adj.max() - y_adj.min()) / \
                  (slope * np.sqrt(1 + 1 / slope ** 2))
        # elif len(y_adj[y_adj > 0]) > 0 and stkclose[-1] < stkclose[0] \
        #         and (y_adj.max() - y_adj.min()) * slope != 0:
        #     err = (y_adj.max() - y_adj.min()) / \
        #         (slope * np.sqrt(1 + 1 / slope ** 2))
        else:
            err = 0
        if mode == 'coef':
            return [r_squared, slope, intercept, si_pro]
        else:
            return [r_squared, slope, intercept, si_pro, abs(err)]
    else:
        return [0, 0, 0, 0] if mode == 'coef' else [0, 0, 0, 0, 0]


def cr_trend_data(stk_code, stk_close, sfit_date, mode='Stock'):
    """函数 cr_trend_data 获取波峰波谷状态和趋势状态的数据
    如果函数输入的波峰波谷数据中有对应股票的数据，即return该数据，否则调用函数测算，并存储至相应文件"""
    tradedate = stk_close.index
    crthr_turn = peak_valley_turn(stk_close[stk_close.index <= sfit_date].values,
                                  tradedate[tradedate <= sfit_date], mode=mode)
    if not isinstance(crthr_turn, pd.DataFrame) or len(crthr_turn) == 0:
        crthr_turn = []
    trendstat = trend_judge(
        stk_close[stk_close.index <= sfit_date], crthr_turn)
    if not isinstance(trendstat, pd.DataFrame) or len(trendstat) == 0:
        trendstat = []
    return {'crthr_turn': crthr_turn, 'trendstat': trendstat}


def period_stat(stk_code='', mode='ADJ', stk_close=None, stk_open=None, turnover=None, end_date=None, style='Stock'):
    """函数 period_stat 识别股票波峰波谷区间的运行状态，并展示
       输入参数stk_name为股票简称 """
    # from trend_func import peak_valley_turn as pvt
    """ 根据输入股票简称，获取股票代码及股票开盘和收盘数据 """
    if stk_code != '' and style == 'Stock':
        # stk_list = pd.read_csv('dataset/stockList.csv')
        stk_list = get_stock_info()
        stk_code = stk_list[stk_list['ts_code'] == stk_code]['ts_code'].tolist()[
            0]
        attemps = 3
        while len(stk_code) == 0 and attemps > 0:
            stk_code = input('未查询到股票代码，请重新输入：')
            stk_code = stk_list[stk_list['ts_code'].values == stk_code]['ts_code'].tolist()[
                0]
            attemps -= 1
        if attemps == 0:
            raise ValueError('未查询到股票代码，请重新运行程序！')
        stk_data = get_stock_data(stk_code=stk_code, end_date=end_date).set_index(
            'trade_date', drop=False)
        stk_close = stk_data['close']
        stk_open = stk_data['open']
        turnover = stk_data['turnover']
        """ 构建波峰波谷序列 """
    elif stk_code != '' and style == 'Index':
        index_data = get_index_data(stk_code=stk_code)
        if len(index_data) > 0:
            index_data = index_data.set_index('trade_date', drop=False)
            stk_close = index_data['close']
            stk_open = index_data['open']
        else:
            print('未查询到指数行情数据！')
            return
    if end_date is not None:
        stk_close = stk_close[:end_date]
        stk_open = stk_open[:end_date]
    stk_ratio = (stk_close / stk_close.shift(1) - 1) * 100
    tradedate = stk_close.index
    crthr_turn = peak_valley_turn(stk_close, tradedate.values, mode=style)
    if len(crthr_turn) < 2:
        return [], []
    crthr_turn = crthr_turn.set_index(['date'])
    tradedate = stk_close.index
    crthrperiod = crthr_turn[['valley', 'peak']].max(axis=1)
    periodchang = pd.DataFrame({'start_date': crthrperiod.index[:-1], 'end_date': crthrperiod.index[1:]},
                               index=range(0, len(crthrperiod) - 1))
    periodchang['sum_ratio'] = (
            100 * crthrperiod.diff()[1:] / crthrperiod.shift(1)[1:]).values

    avg_ratios = []
    last_days = []
    close_open_ratio = []
    std_bias = []
    r_squared = []
    slope = []
    intercept = []
    si_pro = []
    avg_turnover = []
    max_turnover = []
    rise_avgturnover = []
    drop_avgturnover = []
    for pnum in periodchang.index:
        pnum_startdate = periodchang.loc[pnum, 'start_date']
        pnum_enddate = periodchang.loc[pnum, 'end_date']
        last_day = len(tradedate[(tradedate >= pnum_startdate) &
                                 (tradedate <= pnum_enddate)]) - 1
        avg_ratios.append(periodchang.loc[pnum, 'sum_ratio'] / last_day)
        if turnover is not None:
            avg_a = turnover.loc[pnum_startdate:pnum_enddate].mean()
            max_a = turnover.loc[pnum_startdate:pnum_enddate].max()
            avg_turnover.append(round(avg_a, 2))
            max_turnover.append(round(max_a, 2))
            stk_ratio_temp = stk_ratio.loc[pnum_startdate:pnum_enddate]
            rise_index = stk_ratio_temp[stk_ratio_temp > 0].index
            drop_index = stk_ratio_temp[stk_ratio_temp < 0].index
            rise_avgturnover.append(round(turnover.loc[rise_index].sum() / stk_ratio_temp.loc[rise_index].sum()
                                          if stk_ratio_temp.loc[rise_index].sum() != 0 else 0, 3))
            drop_avgturnover.append(round(abs(turnover.loc[drop_index].sum() / stk_ratio_temp.loc[drop_index].sum())
                                          if stk_ratio_temp.loc[drop_index].sum() != 0 else 0, 3))
        last_days.append(last_day)
        period_close = stk_close[pnum_startdate: pnum_enddate]
        period_open = stk_open[pnum_startdate: pnum_enddate]
        close_open_ratio.append(
            np.mean(abs(period_close / period_open - 1)) * 100)

        # 调用子函数完成线性拟合测试
        coef_data = coef_regress(period_close, mode='err')
        r_squared.append(coef_data[0])
        slope.append(coef_data[1])
        intercept.append(coef_data[2])
        si_pro.append(coef_data[3])
        std_bias.append(coef_data[4])
    periodchang['avg_ratio'] = avg_ratios
    periodchang['last_days'] = last_days
    periodchang['close_open_ratio'] = close_open_ratio
    periodchang['std_bias'] = abs(periodchang['sum_ratio'] / std_bias)
    periodchang['r_squared'] = r_squared
    periodchang['slope'] = slope
    periodchang['intercept'] = intercept
    periodchang['si_pro'] = si_pro
    if turnover is not None:
        periodchang['avg_turnover'] = avg_turnover
        periodchang['max_turnover'] = max_turnover
        periodchang['turnover2rise'] = rise_avgturnover
        periodchang['turnover2drop'] = drop_avgturnover
    if mode != 'ADJ':
        trend_stat = trend_judge(stk_close, crthr_turn)
        return periodchang, trend_stat
    else:
        periodchang_ADJ = periodchang[:1].copy()
        pnum = 1
        while pnum <= len(periodchang) - 1:
            # pnum_startdate = periodchang.at[pnum, 'start_date']
            pnum_enddate = periodchang.at[pnum, 'end_date']
            pnum_sum_ratio = periodchang.at[pnum, 'sum_ratio']
            pnum_last_days = periodchang.at[pnum, 'last_days']
            pnum_minus_one_end_date = periodchang.at[pnum - 1, 'end_date']
            last_index = periodchang_ADJ.index[-1]
            if (pnum < len(periodchang) - 1
                and pnum_sum_ratio > 0
                and (pnum_last_days <= 3
                     or 10 > pnum_sum_ratio)
                and stk_close[periodchang.at[pnum + 1, 'end_date']] <=
                stk_close[pnum_minus_one_end_date]) \
                    or (pnum < len(periodchang) - 1
                        and pnum_sum_ratio < 0
                        and (pnum_last_days < 5
                             or -10 < pnum_sum_ratio)
                        and stk_close[periodchang.at[pnum + 1, 'end_date']] * 1.03 >
                        stk_close[pnum_minus_one_end_date]):
                pnum_plus_one_end_date = periodchang.at[pnum + 1, 'end_date']
                end_date = pnum_plus_one_end_date
                periodchang_ADJ.at[last_index,
                'end_date'] = end_date
                periodchang_ADJ.at[last_index, 'last_days'] = \
                    periodchang_ADJ.at[last_index, 'last_days'] + \
                    periodchang['last_days'].iloc[pnum + 1] + \
                    periodchang['last_days'].iloc[pnum]
                periodchang_ADJ.at[last_index, 'sum_ratio'] = \
                    (stk_close[pnum_plus_one_end_date] /
                     stk_close[periodchang_ADJ.at[last_index, 'start_date']] - 1) * 100
                periodchang_ADJ.at[last_index, 'avg_ratio'] = \
                    periodchang_ADJ.at[last_index, 'sum_ratio'] / \
                    periodchang_ADJ.at[last_index, 'last_days']
                if turnover is not None:
                    periodchang_ADJ.at[last_index, 'avg_turnover'] = \
                        round(turnover.loc[periodchang_ADJ['start_date'].iloc[-1]:periodchang_ADJ['end_date'].iloc[-1]
                              ].mean(), 2)
                    periodchang_ADJ.at[last_index, 'max_turnover'] = \
                        round(turnover.loc[periodchang_ADJ['start_date'].iloc[-1]:periodchang_ADJ['end_date'].iloc[-1]
                              ].max(), 2)
                    stk_ratio_temp = stk_ratio.loc[
                                     periodchang_ADJ['start_date'].iloc[-1]:periodchang_ADJ['end_date'].iloc[-1]]
                    rise_index = stk_ratio_temp[stk_ratio_temp > 0].index
                    drop_index = stk_ratio_temp[stk_ratio_temp < 0].index
                    periodchang_ADJ.loc[last_index, 'turnover2rise'] = round(
                        turnover.loc[rise_index].sum() / stk_ratio_temp.loc[rise_index].sum(), 3)
                    periodchang_ADJ.loc[last_index, 'turnover2drop'] = round(
                        abs(turnover.loc[drop_index].sum() / stk_ratio_temp.loc[drop_index].sum()), 3)
                period_close = stk_close[periodchang_ADJ.at[last_index, 'start_date']:
                                         pnum_plus_one_end_date]
                period_open = stk_open[periodchang_ADJ.at[last_index, 'start_date']:
                                       pnum_plus_one_end_date]
                periodchang_ADJ.at[last_index, 'close_open_ratio'] = np.mean(
                    abs(period_close / period_open - 1)) * 100

                coef_data = coef_regress(period_close, mode='err')
                periodchang_ADJ.at[last_index,
                'r_squared'] = coef_data[0]
                periodchang_ADJ.at[last_index,
                'slope'] = coef_data[1]
                periodchang_ADJ.at[last_index,
                'intercept'] = coef_data[2]
                periodchang_ADJ.at[last_index,
                'si_pro'] = coef_data[3]
                periodchang_ADJ.at[last_index, 'std_bias'] = abs(
                    periodchang_ADJ.at[last_index, 'sum_ratio'].copy() / coef_data[4]) \
                    if coef_data[4] != 0 else 0
                pnum += 2
            elif pnum == len(periodchang) - 1 \
                    and pnum_last_days < 5 \
                    and ((periodchang.at[pnum, 'avg_ratio'] >= 0
                          and stk_close[pnum_enddate] <=
                          (stk_close[pnum_minus_one_end_date] +
                           stk_close[periodchang.at[pnum - 1, 'start_date']]) / 2)
                         or (periodchang.at[pnum, 'avg_ratio'] < 0
                             and stk_close[pnum_enddate] >
                             (stk_close[pnum_minus_one_end_date] +
                              stk_close[periodchang.at[pnum - 1, 'start_date']]) / 2)):
                end_date = pnum_enddate
                periodchang_ADJ.at[last_index, 'end_date'] = end_date
                periodchang_ADJ.at[last_index, 'last_days'] = \
                    periodchang_ADJ.at[last_index,
                    'last_days'] + pnum_last_days
                periodchang_ADJ.at[last_index, 'sum_ratio'] = \
                    (stk_close[pnum_enddate] /
                     stk_close[periodchang_ADJ.at[last_index, 'start_date']] - 1) * 100
                periodchang_ADJ.at[last_index, 'avg_ratio'] = \
                    periodchang_ADJ.at[last_index, 'sum_ratio'] / \
                    periodchang_ADJ.at[last_index, 'last_days']
                period_close = stk_close[periodchang_ADJ.at[last_index, 'start_date']:
                                         pnum_enddate]
                period_open = stk_open[periodchang_ADJ.at[last_index, 'start_date']:
                                       pnum_enddate]
                periodchang_ADJ.at[last_index, 'close_open_ratio'] = np.mean(
                    abs(period_close / period_open - 1)) * 100
                if turnover is not None:
                    periodchang_ADJ.at[last_index, 'avg_turnover'] = \
                        round(turnover.loc[periodchang_ADJ['start_date'].iloc[-1]:pnum_enddate
                              ].mean(), 2)
                    periodchang_ADJ.at[last_index, 'max_turnover'] = \
                        round(turnover.loc[periodchang_ADJ['start_date'].iloc[-1]:pnum_enddate
                              ].max(), 2)
                    stk_ratio_temp = stk_ratio.loc[
                                     periodchang_ADJ['start_date'].iloc[-1]:pnum_enddate]
                    rise_index = stk_ratio_temp[stk_ratio_temp > 0].index
                    drop_index = stk_ratio_temp[stk_ratio_temp < 0].index
                    periodchang_ADJ.loc[last_index, 'turnover2rise'] = round(
                        turnover.loc[rise_index].sum() / stk_ratio_temp.loc[rise_index].sum(), 3)
                    periodchang_ADJ.loc[last_index, 'turnover2drop'] = round(
                        abs(turnover.loc[drop_index].sum() / stk_ratio_temp.loc[drop_index].sum()), 3)

                coef_data = coef_regress(period_close, mode='err')
                periodchang_ADJ.at[last_index, 'r_squared'] = coef_data[0]
                periodchang_ADJ.at[last_index, 'slope'] = coef_data[1]
                periodchang_ADJ.at[last_index, 'intercept'] = coef_data[2]
                periodchang_ADJ.at[last_index, 'si_pro'] = coef_data[3]
                periodchang_ADJ.at[last_index, 'std_bias'] = abs(
                    periodchang_ADJ.at[last_index, 'sum_ratio'].copy() / coef_data[4]) \
                    if coef_data[4] != 0 else 0
                pnum += 1
            elif periodchang_ADJ.at[last_index, 'end_date'] != pnum_enddate:
                periodchang_ADJ.loc[len(periodchang_ADJ)
                ] = periodchang.loc[pnum]
                pnum += 1
            else:
                pnum += 1
        if periodchang_ADJ['end_date'].iloc[-1] != periodchang['end_date'].iloc[-1]:
            periodchang_ADJ.loc[len(periodchang_ADJ)] = periodchang.iloc[-1]
        if len(periodchang_ADJ) >= 2 \
                and (periodchang_ADJ['sum_ratio'].iloc[-1] * periodchang_ADJ['sum_ratio'].iloc[-2] > 0
                     or periodchang_ADJ['sum_ratio'].iloc[-1] == 0):
            periodchang_ADJ.loc[periodchang_ADJ.index[-2],
            'end_date'] = periodchang_ADJ['end_date'].iloc[-1]
            periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'last_days'] = \
                periodchang_ADJ['last_days'].iloc[-1] + \
                periodchang_ADJ['last_days'].iloc[-2]
            periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'sum_ratio'] = (stk_close[
                                                                               periodchang_ADJ['end_date'].iloc[-2]] /
                                                                           stk_close[periodchang_ADJ['start_date'].iloc[
                                                                               -2]] - 1) * 100
            periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'avg_ratio'] = \
                periodchang_ADJ['sum_ratio'].iloc[-2] / \
                periodchang_ADJ['last_days'].iloc[-2]
            if turnover is not None:
                periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'avg_turnover'] = \
                    round(turnover.loc[periodchang_ADJ['start_date'].iloc[-2]:periodchang_ADJ['end_date'].iloc[-2]
                          ].mean(), 2)
                periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'max_turnover'] = \
                    round(turnover.loc[periodchang_ADJ['start_date'].iloc[-2]:periodchang_ADJ['end_date'].iloc[-2]
                          ].max(), 2)
                stk_ratio_temp = stk_ratio.loc[
                                 periodchang_ADJ['start_date'].iloc[-2]:periodchang_ADJ['end_date'].iloc[-2]]
                rise_index = stk_ratio_temp[stk_ratio_temp > 0].index
                drop_index = stk_ratio_temp[stk_ratio_temp < 0].index
                periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'turnover2rise'] = round(
                    turnover.loc[rise_index].sum() / stk_ratio_temp.loc[rise_index].sum(), 3)
                periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'turnover2drop'] = round(
                    abs(turnover.loc[drop_index].sum() / stk_ratio_temp.loc[drop_index].sum()), 3)

            periodchang_ADJ = periodchang_ADJ.drop(
                len(periodchang_ADJ) - 1, axis=0)
        crthr = pd.DataFrame(periodchang_ADJ['end_date'])
        crthr['valley'] = periodchang_ADJ.apply(
            lambda fn: stk_close.loc[fn['end_date']] if fn['sum_ratio'] < 0 else np.nan, axis=1)
        crthr['peak'] = periodchang_ADJ.apply(
            lambda fn: stk_close.loc[fn['end_date']] if fn['sum_ratio'] > 0 else np.nan, axis=1)
        crthr_turn_adj = pd.DataFrame(
            np.insert(crthr.values, 0,
                      values=[periodchang_ADJ['start_date'].iloc[0],
                              stk_close.loc[periodchang_ADJ['start_date'].iloc[0]], np.nan], axis=0)) \
            if periodchang['sum_ratio'].iloc[0] > 0 \
            else pd.DataFrame(
            np.insert(crthr.values, 0, values=[periodchang_ADJ['start_date'].iloc[0],
                                               np.nan, stk_close.loc[periodchang_ADJ['start_date'].iloc[0]]], axis=0))
        crthr_turn_adj.columns = ['date', 'valley', 'peak']
        crthr_turn_adj['valley'] = crthr_turn_adj['valley'].astype('float64')
        crthr_turn_adj['peak'] = crthr_turn_adj['peak'].astype('float64')
        crthr_turn_adj = crthr_turn_adj.set_index(['date'])
        trend_stat = trend_judge(stk_close, crthr_turn_adj)
        return periodchang_ADJ, trend_stat


def indus_cal(result, cal_days=120, indusNum=8):
    """函数indus_cal,用于统计筛选结果的行业分布状况
       输入参数result为筛选结果结构数据，cal_days为筛选结果距离转折点间隔天数有效值，indusNum为选择行业排列位数"""
    """依据距离转折点天数，筛选符合条件的result数据"""
    valid_name_condition = result['name'] != 'name'
    if cal_days != 0:
        result_a = result[((pd.to_datetime(result.loc[:, 'Sfit_Date_First']) -
                            pd.to_datetime(result.loc[:, 'Turn_Date'])
                            ).astype('timedelta64[D]').astype(int) < cal_days)]
        result_a = result_a[valid_name_condition]
    else:
        result_a = result[valid_name_condition].copy()
    """设定强势行业筛选排名位数"""
    # 测算每个转折点对应强势行业indus_stat
    # result = pd.read_csv('res_data/sfitResult.csv')
    result_a = result_a.drop_duplicates(subset='name', keep='last')
    result_a = result_a.sort_values(
        ['Turn_Date', 'Sfit_Date_First'], ascending=False)
    indus = result_a.groupby(['Turn_Date', 'industry'])[
        'industry'].count().unstack()
    indus_stat = pd.DataFrame()
    for date in indus.index:
        induslen = len(indus.loc[date].dropna())
        indusone = indus.loc[date].dropna().sort_values(
            ascending=False)[:min(indusNum, induslen)]
        indus_d = pd.Series(indusone.index.tolist(), name=date,
                            index=range(0, len(indusone.index)))
        indus_stat = indus_stat.append(indus_d, ignore_index=False)
    return indus_stat


def indus_bar(result, turn_date=None):
    if turn_date is not None:
        result = result.query('Turn_Date >= @turn_date')
    result = result.drop_duplicates(subset='ts_code', keep='first')
    import matplotlib.pyplot as plt
    import matplotlib
    fig, ax = plt.subplots()
    matplotlib.rcParams['font.sans-serif'] = ['KaiTi']
    width = 0.35
    result_count = result.groupby(['Turn_Date', 'industry']).count()
    for turn_date in result['Turn_Date'].unique():
        rects = ax.bar(result_count.loc[turn_date].index,
                       result_count.loc[turn_date].name.values, width, label=turn_date)
    ax.set_ylabel('股票数量')
    ax.set_title('筛选结果行业分布')
    ax.legend(result['Turn_Date'].unique())
    for tick in ax.get_xticklabels():
        tick.set_rotation(90)
    plt.show()
    return


def indus_curve(result, start_date, end_date, indusNum=10):
    """indus_curve函数，指定起始日期和结束日期，绘制行业内突破股票数量变化曲线图，indusNum设定结束日期对应股票数量排序的行业数量"""
    import matplotlib.pyplot as plt
    import matplotlib
    result = result.drop_duplicates(subset='name', keep='last')
    result = result[result['name'] != 'name']
    result_a = result.query(
        '@end_date >= Sfit_Date_First >= @start_date').copy()
    result_a['num'] = 1
    indus = result_a.groupby(['industry', 'Sfit_Date_First'])[
        'num'].sum().unstack()
    indus = indus.fillna(value=0)
    indus = indus.cumsum(axis=1)
    indus = indus.sort_values(indus.columns[-1], ascending=False)
    indus = indus.iloc[:indusNum]
    indus = indus.T
    fig, ax = plt.subplots()
    matplotlib.rcParams['font.sans-serif'] = ['KaiTi']
    for num in indus.columns:
        ax.plot(indus.index, indus.loc[:, num])
    ax.legend(indus.columns)
    ax.set_ylabel('股票数量')
    ax.set_title('突破品种行业统计变化')
    for tick in ax.get_xticklabels():
        tick.set_rotation(45)
    plt.show()
    return


def retracement_from_high(stockclose, mode=1):
    """计算股票序列最高价之后，相对最高价的最大跌幅"""
    if len(stockclose) == 0:
        return np.nan, np.nan
    if mode == 1:
        index_j = np.argmax(np.maximum.accumulate(
            stockclose.values) - stockclose.values)
        index_i = np.argmax(stockclose.iloc[:index_j]) if len(
            stockclose.iloc[:index_j]) > 0 else index_j
        max_drop = round((stockclose.iloc[index_j] / stockclose.iloc[index_i] - 1) * 100, 2)
        maxdrop_lastdays = index_j - index_i
    else:
        max_drop = (stockclose.iloc[stockclose.values.argmax():].values.min() / stockclose.values.max() - 1) * 100
        maxdrop_end = stockclose.iloc[stockclose.values.argmax():].idxmin()
        maxdrop_start = stockclose.idxmax()
        maxdrop_lastdays = len(stockclose.loc[maxdrop_start:maxdrop_end]) - 1
    return round(max_drop, 3), maxdrop_lastdays


def retracement_for_swindex(index_ratio):
    """申万行业指数相对中证500指数涨跌幅曲线的最大回撤计算"""
    index_mindate = index_ratio.iloc[int(len(index_ratio) / 2):].idxmin()
    start_maxdate = index_ratio.loc[:index_mindate].idxmax()
    # if len(index_ratio.loc[:index_mindate]) > len(index_ratio)/2:
    index_ratio = index_ratio.loc[start_maxdate:].copy()
    index_end = np.argmax(np.maximum.accumulate(
        index_ratio.values) - index_ratio.values)
    index_start = np.argmax(index_ratio.iloc[:index_end]) if len(
        index_ratio.iloc[:index_end]) > 0 else index_end
    return round(index_ratio.iloc[index_end] - index_ratio.iloc[index_start], 2), \
        index_ratio.index[index_start], index_ratio.index[index_end]


def maxrise_for_swindex(index_ratio):
    """计算申万行业指数相对中证500指数涨跌幅曲线的最大涨幅"""
    index_end = np.argmin(np.minimum.accumulate(
        index_ratio.values) - index_ratio.values)
    index_start = np.argmin(index_ratio.iloc[:index_end]) if len(
        index_ratio.iloc[:index_end]) > 0 else index_end
    return round(index_ratio.iloc[index_end] - index_ratio.iloc[index_start], 2), \
        index_ratio.index[index_start], index_ratio.index[index_end]


def turn_for_swindex(index_ratio, zz_data):
    """计算得到相对涨跌幅序列的阶段低点位置"""
    max_list = pd.DataFrame(
        columns=['maxnum', 'maxdate'], index=index_ratio.index)
    for index in index_ratio.index:
        max_list.loc[index, 'maxnum'] = index_ratio.loc[:index].max()
        max_list.loc[index, 'maxdate'] = index_ratio.loc[:index].idxmax()
    max_count = max_list.groupby('maxdate', as_index=False).count()
    peak_list = max_count.query('maxnum>=10')['maxdate'].values.tolist()
    peak_list.sort()
    if len(peak_list) == 0 and index_ratio.max() - index_ratio.iloc[0] > 1:
        peak_list = [index_ratio.idxmax()]
    if len(peak_list) == 0:
        return None, None, None, None, None
    if index_ratio.loc[peak_list[-1]:].max() > index_ratio.loc[peak_list[-1]]:
        peak_list.append(index_ratio.loc[peak_list[-1]:].idxmax())
    diff_turn = []
    diff_style = []
    for num in range(0, len(peak_list)):
        if num != len(peak_list) - 1:
            diff_turn.append(peak_list[num])
            diff_style.append('peak')
            diff_turn.append(
                index_ratio.loc[peak_list[num]:peak_list[num + 1]].idxmin())
            diff_style.append('bottom')
        else:
            diff_turn.append(peak_list[num])
            diff_style.append('peak')
            last_min = index_ratio.loc[peak_list[num]:].idxmin()
            diff_turn.append(last_min)
            diff_style.append('bottom')
            if index_ratio.loc[last_min:].max() > index_ratio.loc[peak_list[num]]:
                diff_turn.append(index_ratio.loc[last_min:].idxmax())
                diff_style.append('peak')
    diff_turns = pd.DataFrame(
        {'turndate': diff_turn, 'turnstyle': diff_style}, index=range(0, len(diff_turn)))
    diff_turns['startdate'] = diff_turns['turndate'].shift(1)
    diff_turns.loc[0, 'startdate'] = index_ratio.index[0]
    diff_turns['idx_ratio'] = diff_turns.apply(
        lambda fn: (zz_data.loc[fn['turndate'], 'close'] / zz_data.loc[fn['startdate'], 'close'] - 1) * 100, axis=1)
    diff_turns['sw_gap'] = diff_turns.apply(
        lambda fn: index_ratio.loc[fn['turndate']] - index_ratio.loc[fn['startdate']], axis=1)
    diff_turns['sw_gap_avg'] = diff_turns.apply(
        lambda fn: fn['sw_gap'] / len(zz_data.loc[fn['startdate']:fn['turndate']]), axis=1)
    recent_rise = diff_turns.query('turnstyle=="peak"')['sw_gap_avg'].iloc[-1] / \
                  diff_turns.query('turnstyle=="peak"')['sw_gap_avg'].iloc[:-1].mean() \
        if len(diff_turns.query('turnstyle=="peak"')) > 1 else 0
    if diff_turns['turnstyle'].iloc[-1] == 'bottom':
        # last_drop_index = diff_turns.query('turnstyle=="bottom"')['sw_gap'].index[-1]
        recent_drop = round(
            abs(diff_turns['sw_gap'].iloc[-1]) / diff_turns['sw_gap'].iloc[-2], 3)
        Avg_Ratio = round(
            abs(diff_turns['sw_gap_avg'].iloc[-1]) / diff_turns['sw_gap_avg'].iloc[-2], 3)
    else:
        recent_drop = 0
        Avg_Ratio = 0
    return diff_turns, \
        round(diff_turns.query('turnstyle=="peak"')['idx_ratio'].sum(), 3), \
        round(diff_turns.query('turnstyle=="bottom"')['idx_ratio'].sum(), 3), \
        round(recent_rise, 3), \
        round(recent_drop, 3)


def period_volatility(stockclose, stockopen, length):
    """判定是否存在窄幅波动走势，基础天数为length。输出为窄幅持续天数"""
    width_ratio = 0.03
    period_ratio = 0.03
    stockclose = stockclose.iloc[::-1]
    stockopen = stockopen.iloc[::-1]
    first_date = 0
    lpnum = 0
    for num in range(0, len(stockclose)):
        lp = length
        while num + lp + 1 < len(stockclose) \
                and np.max([stockclose.iloc[num:num + lp].max(), stockopen.iloc[num:num + lp].max()]) / np.min(
            [stockclose.iloc[num:num + lp].min(), stockopen.iloc[num:num + lp].min()]) - 1 < width_ratio \
                and abs(stockclose.iloc[num + lp] / np.min(stockclose.iloc[num:num + lp]) - 1) < period_ratio \
                and np.max(
            abs(stockclose.iloc[num:num + lp] / stockclose.iloc[num + 1:num + lp + 1] - 1)) < period_ratio \
                and abs(np.polyfit(range(1, lp), stockclose.iloc[num + lp - 1:num:-1], 2)[0]) < 0.05:
            lp += 1
        if lp > length:
            first_date = stockclose.index[num]
            lpnum = lp
            break
    return lpnum, first_date


def compare_vol(ts_code, sdate='2021-03-12'):
    """测算股票波幅变化比率序列"""
    # stock_data = pd.read_csv('dataset/stockdata.csv')
    stk_data = get_stock_data(stk_code=ts_code, start_date=sdate).set_index(
        'trade_date', drop=True)
    # stk_data = stock_data.query('ts_code==@ts_code & trade_date>=@sdate')
    # stk_data = stk_data.set_index('trade_date', drop=True)
    # stk_data = stk_data.sort_index()
    stock_close = stk_data['close']
    stock_ratio = (stock_close / stock_close.shift(1) - 1) * 100
    stock_ratio = stock_ratio.dropna()
    stock_com = abs(stock_ratio) / \
                abs(stock_ratio).rolling(window=5).mean().shift(1)
    return stock_com


def curv_cal(stkcode, start_date, end_date, dim, mode='code'):
    """计算指定区间股票开高收低均价走势的曲率，dim为拟合阶数"""
    if mode == 'code':
        stk_data = get_stock_data(
            stk_code=stkcode, start_date=start_date, end_date=end_date).set_index('trade_date', drop=False)
    else:
        stk_data = stkcode
    stk_price = stk_data['amount'] / stk_data['vol']
    stk_ratio = (stk_price / stk_price.iloc[0] - 1) * 100
    stk_ratio = stk_ratio - stk_ratio.mean()
    if len(stk_ratio) >= 8:
        coef = np.polyfit(range(1, len(stk_ratio) + 1), stk_ratio.values, dim)
        if mode == 'code':
            import matplotlib.pyplot as plt
            y_hat = np.polyval(coef, range(1, len(stk_ratio) + 1))
            fig, ax = plt.subplots()
            ax.plot(stk_ratio)
            ax.plot(y_hat)
        y = np.poly1d(coef)
        y_1 = y.deriv(1)
        y_2 = y.deriv(2)
        x = range(1, len(stk_ratio) + 1)
        curv = (y_2(x) / ((1 + y_1(x) ** 2) ** (3 / 2))) * 100
        curv = pd.Series(curv, index=stk_ratio.index)
        return curv
    else:
        return None


def break_state_count(stk_close):
    """统计区间内股价突破前三天最高价的次数及单次持续天数"""
    stk_ratio = stk_close / stk_close.rolling(window=3).max().shift(1) - 1
    ratio_state = stk_ratio.apply(lambda x: 1 if x > 0 else 0)
    ratio_state = pd.DataFrame({'ratio': ratio_state}, index=ratio_state.index)
    pos, = np.where(np.diff(ratio_state.ratio))
    start, end = np.insert(pos + 1, 0, 0), np.append(pos, len(ratio_state) - 1)
    ratio_state_count = pd.DataFrame({'start': list(ratio_state.index[start]),
                                      'end': list(ratio_state.index[end]),
                                      'count': end - start + 1,
                                      'value': ratio_state['ratio'].iloc[start]
                                      })
    return ratio_state_count


def cal_overlap_ratio(stk_code, start_date=None, end_date=None):
    """计算指定区间内股票stk_code与其对应指数（上证指数或创业板指数）
        超出平均跌幅的重合度比例，据此判定股票是否强于市场指数"""
    if stk_code[-2:] == 'SH':
        idx_data = get_index_data(
            stk_code='000001.SH', start_date=start_date, end_date=end_date).set_index('trade_date', drop=False)
    elif stk_code[-2:] == 'SZ':
        idx_data = get_index_data(
            stk_code='399006.SZ', start_date=start_date, end_date=end_date).set_index('trade_date', drop=False)
    else:
        print('stk_code错误')
        return
    stk_data = get_stock_data(
        stk_code=stk_code, start_date=start_date, end_date=end_date).set_index('trade_date', drop=False)
    index_ratio = (idx_data['close'] / idx_data['close'].shift(1) - 1) * 100
    stock_ratio = (stk_data['close'] / stk_data['close'].shift(1) - 1) * 100
    # 计算低于均值重合度
    index_ratio_late = index_ratio[index_ratio < 0]
    index_beyond_avg_date = index_ratio_late.index[index_ratio_late <= index_ratio_late.mean(
    )]
    stk_ratio_late = stock_ratio[stock_ratio < 0]
    stk_beyond_avg_date = stk_ratio_late.index[stk_ratio_late <= stk_ratio_late.mean(
    )]
    if len(stk_beyond_avg_date) > 0:
        x = len(set(stk_beyond_avg_date) - set(index_beyond_avg_date))
        underlap_ratio = format(
            (1 - (x / len(stk_beyond_avg_date))) * 100, '.2f')
    else:
        underlap_ratio = 'NAN'
    # 计算高于均值重合度
    index_ratio_late = index_ratio[index_ratio > 0]
    index_beyond_avg_date = index_ratio_late.index[index_ratio_late >= index_ratio_late.mean(
    )]
    stk_ratio_late = stock_ratio[stock_ratio > 0]
    stk_beyond_avg_date = stk_ratio_late.index[stk_ratio_late >= stk_ratio_late.mean(
    )]
    if len(stk_beyond_avg_date) > 0:
        x = len(set(stk_beyond_avg_date) - set(index_beyond_avg_date))
        overlap_ratio = format(
            (1 - (x / len(stk_beyond_avg_date))) * 100, '.2f')
    else:
        overlap_ratio = 'NAN'
    return '下跌重合：' + underlap_ratio + '% / 上涨重合： ' + overlap_ratio + '%'


def section_stat(stk_data=None, stk_code='', start_date=None, end_date=None, mode='ADJ', index_data=None,
                 index_mode=None):
    """计算指定区间内每日趋势状态，判定标准为收盘价是否高于前一日mean(开盘，收盘)
        依据涨跌计算结果，统计区间内日间状态，例如涨天数与跌天数比对，涨连续天数等
        mode设定：'ADJ'调整算法，合并期间小区间，'basic'基础算法，不调整
        输出结果：
            [区间上涨合计天数，区间下跌合计天数]
            [区间最大连续上涨天数，区间最大连续下跌天数]
            dataframe 每日价格及涨跌状态
    """
    from itertools import groupby
    if stk_code != '':
        stk_data = get_stock_data(
            stk_code=stk_code, start_date=start_date, end_date=end_date).set_index('trade_date')
        price_data = stk_data[['close', 'open', 'high', 'low',
                               'pre_close', 'turnover']].copy()
        price_data.columns = ['stk_close', 'stk_open', 'high', 'low', 'pre_close', 'turnover']
    else:
        # price_data = pd.DataFrame({'stk_close': stk_close, 'stk_open': stk_open}, index=stk_close.index)
        price_data = stk_data[['close', 'open', 'high', 'low', 'pre_close', 'turnover']].copy()
        price_data.columns = ['stk_close', 'stk_open', 'high', 'low', 'pre_close', 'turnover']
        if start_date is not None and end_date is not None:
            price_data = price_data.loc[start_date:end_date]
    if index_mode is not None:
        if index_data is None:
            index_data = get_index_data(stk_code='000001.SH', start_date=price_data.index[0],
                                        end_date=price_data.index[-1]).set_index('trade_date')
        elif len(index_data) > len(price_data):
            index_data = index_data.loc[price_data.index].copy()
    # price_data['amount'] = price_data['amount'] / 100000
    price_data['stk_mean'] = price_data[['stk_close', 'stk_open', 'high', 'low']].mean(axis=1)
    price_data['stk_mean_shift'] = price_data['stk_mean'].shift(1)
    price_data['stk_close_shift'] = price_data['stk_close'].shift(1)
    price_data['stk_rollmax'] = price_data['stk_close'].rolling(
        window=3, closed='left').max()
    price_data['stk_rollmin'] = price_data['stk_close'].rolling(
        window=3, closed='left').min()
    price_data['stk_rollmax_shift'] = price_data['stk_rollmax'].shift(1)
    price_data['stk_rollmin_shift'] = price_data['stk_rollmin'].shift(1)
    # price_data['stk_overmean'] = price_data.apply(lambda x: 'True' if x['stk_close'] > x['stk_rollmean'] else '-', axis=1)
    price_data['clsopen_ratio'] = abs(
        price_data['stk_close'] / price_data['stk_open'] - 1) * 100
    price_data['day_state'] = price_data.apply(
        lambda x: '涨' if x['stk_close'] >= x['stk_close_shift'] else '跌', axis=1)
    price_data['day_ratio'] = (price_data['stk_close'] / price_data['stk_close'].shift(1) - 1) * 100
    price_data['abs_dayratio'] = abs(price_data['day_ratio'])
    price_data['max_ratio'] = price_data[[
        'abs_dayratio', 'clsopen_ratio']].max(axis=1)
    price_data = price_data.fillna(value=0)
    day_state = price_data.copy()
    first_state = '涨'
    second_state = '跌'
    # if pickstyle is None:
    #     if price_data['stk_close'].iloc[-1] > price_data['stk_close'].iloc[0]:
    #         first_state = '涨'
    #         second_state = '跌'
    #     else:
    #         first_state = '跌'
    #         second_state = '涨'
    # else:
    #     if pickstyle == '涨':
    #         first_state = '涨'
    #         second_state = '跌'
    #     else:
    #         first_state = '跌'
    #         second_state = '涨'
    state_loc, = np.where(price_data['day_state'] == first_state)
    first_avgratio = []
    first_sumratio = []
    cls_diff = []
    extre_ratio = []
    clsopen_ratio = []
    und2_contidays = []
    limit_num = 2
    avg_turnover = []
    max_turnover = []
    turnover2rise = []
    turnover2drop = []
    adverse2trend = []
    first_lastdays = []
    start_date = []
    end_date = []
    last_list = []

    def fun(x):
        return x[1] - x[0]

    for _, group in groupby(enumerate(state_loc), fun):
        find_list = [j for i, j in group]
        find_list_max = find_list[price_data['stk_close'].iloc[find_list].argmax(
        )]
        tred = bool(len(last_list) == 0
                    or (first_state == '涨'
                        and price_data['stk_close'].iloc[find_list[-1]] >= price_data[['stk_close', 'stk_open']].iloc[
                            last_list[0]].min()
                        and price_data['stk_close'].iloc[find_list_max] > price_data['stk_close'].iloc[max(0,
                                                                                                           find_list_max - 3):find_list_max].mean()
                        and price_data['stk_close'].iloc[find_list].min() >= price_data[['stk_close', 'stk_open']].iloc[
                            last_list[0]].min()
                        and price_data['stk_close'].iloc[find_list].mean() > price_data['stk_close'].iloc[
                            last_list].mean())
                    or (len(find_list) == 1
                        and len(price_data) - find_list[-1] <= 1
                        and ((first_state == '跌' and price_data['stk_close'].iloc[find_list[-1]] <=
                              price_data[['stk_close', 'stk_open']].iloc[last_list[0]].max())
                             or (first_state == '涨' and price_data['stk_close'].iloc[find_list[-1]] >=
                                 price_data[['stk_close', 'stk_open']].iloc[last_list[0]].min()))))
        if mode == 'ADJ' and len(find_list) >= 1 and len(last_list) >= 2 and find_list[0] - last_list[-1] <= 5 \
                and (price_data['stk_close'].iloc[last_list[-1]:find_list[0]].min() /
                     price_data['stk_close'].iloc[last_list].max() - 1) * 100 > -3 \
                and price_data['stk_close'].iloc[find_list].max() > \
                (price_data['stk_close'].iloc[last_list].max() * 3 +
                 price_data['stk_close'].iloc[last_list[-1]:find_list[0]].min()) / 4 \
                and bool(tred):
            # if price_data['stk_close'].iloc[find_list[-1]] > price_data['stk_close'].iloc[last_list[0]]:
            end_price = price_data['stk_close'].iloc[find_list].max()
            #     end_num = price_data['stk_close'].iloc[find_list].argmax() + 1
            # else:
            #     end_price = price_data['stk_close'].iloc[find_list].min()
            #     end_num = price_data['stk_close'].iloc[find_list].argmin() + 1
            find_maxdate = price_data['stk_close'].iloc[find_list].idxmax()
            end_date[-1] = price_data.loc[start_date[-1]:find_maxdate, 'stk_close'].idxmax()
            max_loc = price_data.index.get_loc(end_date[-1])
            first_lastdays[-1] = len(price_data.loc[start_date[-1]
                                                    :end_date[-1]]) - 1
            first_avgratio[-1] = (end_price / min(price_data.loc[start_date[-1], 'stk_close'],
                                                  price_data.loc[start_date[-1], 'pre_close']) - 1
                                  ) * 100 / first_lastdays[-1]
            first_sumratio[-1] = (end_price / min(price_data.loc[start_date[-1], 'stk_close'],
                                                  price_data.loc[start_date[-1], 'pre_close']) - 1) * 100
            cls_diff[-1] = abs(end_price - min(price_data.loc[start_date[-1], 'stk_close'],
                                               price_data.loc[start_date[-1], 'pre_close']))
            sec_startdate = price_data.loc[start_date[-1]:].index[1] \
                if len(price_data.loc[start_date[-1]:]) > 1 else start_date[-1]
            extre_ratio[-1] = price_data['day_ratio'].loc[sec_startdate:end_date[-1]].max() \
                if find_list[-1] < len(price_data) - 1 \
                else price_data['day_ratio'].loc[sec_startdate:].max()
            avg_turnover[-1] = round(price_data['turnover'].loc[sec_startdate:end_date[-1]].mean(), 2) \
                if find_list[-1] < len(price_data) - 1 \
                else round(price_data['turnover'].loc[sec_startdate:].mean(), 2)
            max_turnover[-1] = round(price_data['turnover'].loc[sec_startdate:end_date[-1]].max(), 2) \
                if find_list[-1] < len(price_data) - 1 \
                else round(price_data['turnover'].loc[sec_startdate:].max(), 2)
            rise_index = price_data.loc[sec_startdate:end_date[-1]
                         ].query('day_ratio>0').index
            drop_index = price_data.loc[sec_startdate:end_date[-1]
                         ].query('day_ratio<0').index
            turnover2rise[-1] = round(
                price_data.loc[rise_index, 'turnover'].sum(
                ) / price_data.loc[rise_index, 'day_ratio'].sum()
                if len(rise_index) > 0 and price_data.loc[rise_index, 'day_ratio'].sum() != 0
                else 0, 3)
            turnover2drop[-1] = round(
                abs(price_data.loc[drop_index, 'turnover'].sum(
                ) / price_data.loc[drop_index, 'day_ratio'].sum())
                if len(drop_index) > 0 and price_data.loc[drop_index, 'day_ratio'].sum() != 0
                else 0, 3)
            # adverse2trend[-1] = round(
            #     abs(price_data.loc[drop_index, 'day_ratio'].sum()) /
            #     abs(price_data.loc[rise_index, 'day_ratio'].sum())
            #     if price_data.loc[rise_index, 'day_ratio'].sum() != 0 else 1, 3)
            adverse2trend[-1] = round(
                len(price_data.loc[sec_startdate:end_date[-1]].query('stk_close<stk_rollmin_shift')) /
                len(price_data.loc[sec_startdate:end_date[-1]])
                if len(price_data.loc[sec_startdate:end_date[-1]]) > 0 else 0, 3)
            clsopen_ratio[-1] = round(price_data['clsopen_ratio'].loc[start_date[-1]:end_date[-1]].mean(), 2) \
                if find_list[-1] < len(price_data) - 1 \
                else round(price_data['clsopen_ratio'].loc[start_date[-1]:].mean(), 2)
            und2_contidays[-1] = int(
                count_und_num(compare_data=price_data.loc[start_date[-1]:end_date[-1], 'max_ratio'],
                              limit=limit_num)) \
                if find_list[-1] < len(price_data) - 1 \
                else int(
                count_und_num(compare_data=price_data.loc[start_date[-1]:, 'max_ratio'],
                              limit=limit_num))
            price_data.loc[price_data.index[last_list[-1]
                                            :(max_loc + 1)], 'day_state'] = first_state
            # last_list = find_list.copy()
            last_list = range(last_list[0], max_loc + 1)
        elif (len(find_list) >= 2
              and (len(last_list) == 0
                   or ((price_data['stk_close'].iloc[find_list].max() /
                        price_data['stk_close'].iloc[last_list[-1]:find_list[-1]].min() - 1) * 100 > 5
                       or price_data['stk_close'].iloc[last_list].max() <= price_data['stk_close'].iloc[
                           find_list].max()))) \
                or (find_list[-1] == len(price_data) - 1):
            # if price_data['stk_close'].iloc[find_list[-1]] > price_data['stk_close'].iloc[find_list[0]]:
            end_price = price_data['stk_close'].iloc[find_list].max()
            #     end_num = price_data['stk_close'].iloc[find_list].argmax() + 1
            # else:
            #     end_price = price_data['stk_close'].iloc[find_list].min()
            #     end_num = price_data['stk_close'].iloc[find_list].argmin() + 1
            findstart_date = price_data.index[find_list[0]]
            findstart_adj = price_data.loc[end_date[-1]:findstart_date, 'stk_close'].idxmin() \
                if len(end_date) > 0 \
                else price_data.loc[:findstart_date, 'stk_close'].idxmin()
            findend_adj = price_data['stk_close'].iloc[find_list].idxmax() \
                if find_list[-1] != len(price_data) - 1 else price_data.index[-1]
            max_loc = price_data.index.get_loc(findend_adj)
            findstart_num = price_data.index.get_loc(findstart_adj)
            first_lastdays.append(
                len(price_data.loc[findstart_adj:findend_adj]) - 1)
            first_avgratio.append(
                round((end_price / min(price_data.loc[findstart_adj, 'stk_close'],
                                 price_data.loc[findstart_adj, 'pre_close']) - 1
                 ) * 100 / first_lastdays[-1], 3) if first_lastdays[-1] > 0
                else round((end_price / min(price_data.loc[findstart_adj, 'stk_close'],
                                      price_data.loc[findstart_adj, 'pre_close']) - 1) * 100, 3))
            first_sumratio.append(round((end_price / min(price_data.loc[findstart_adj, 'stk_close'],
                                                   price_data.loc[findstart_adj, 'pre_close']) - 1) * 100, 3))
            cls_diff.append(abs(end_price - min(price_data.loc[findstart_adj, 'stk_close'],
                                                price_data.loc[findstart_adj, 'pre_close'])))
            sec_startdate = price_data.loc[findstart_adj:].index[1] \
                if len(price_data.loc[findstart_adj:findend_adj]) > 1 else findstart_adj
            extre_ratio.append(price_data.loc[sec_startdate:findend_adj, 'day_ratio'].max()
                               if find_list[-1] + 1 < len(price_data) - 1
                               else price_data.loc[sec_startdate:, 'day_ratio'].max())
            avg_turnover.append(round(price_data.loc[sec_startdate:findend_adj, 'turnover'].mean(), 3)
                                if find_list[-1] + 1 < len(price_data) - 1
                                else round(price_data.loc[sec_startdate:, 'turnover'].mean(), 3))
            max_turnover.append(round(price_data.loc[sec_startdate:findend_adj, 'turnover'].max(), 3)
                                if find_list[-1] + 1 < len(price_data) - 1
                                else round(price_data.loc[sec_startdate:, 'turnover'].max(), 3))
            rise_index = price_data.loc[sec_startdate:findend_adj].query(
                'day_ratio>0').index
            drop_index = price_data.loc[sec_startdate:findend_adj].iloc[1:].query(
                'day_ratio<0').index
            turnover2rise.append(round(
                price_data.loc[rise_index, 'turnover'].sum(
                ) / price_data.loc[rise_index, 'day_ratio'].sum()
                if len(rise_index) > 0 and price_data.loc[rise_index, 'day_ratio'].sum() != 0
                else 0, 3))
            turnover2drop.append(round(
                abs(price_data.loc[drop_index, 'turnover'].sum(
                ) / price_data.loc[drop_index, 'day_ratio'].sum())
                if len(drop_index) > 0 and price_data.loc[drop_index, 'day_ratio'].sum() != 0
                else 0, 3))
            # adverse2trend.append(
            #     round(abs(price_data.loc[drop_index, 'day_ratio'].sum()) /
            #           abs(price_data.loc[rise_index, 'day_ratio'].sum())
            #           if price_data.loc[rise_index, 'day_ratio'].sum() != 0 else 1, 3))
            adverse2trend.append(
                round(len(price_data.loc[sec_startdate:findend_adj].query('stk_close<stk_rollmin_shift')) /
                      len(price_data.loc[sec_startdate:findend_adj])
                      if len(price_data.loc[sec_startdate:findend_adj]) > 0 else 0, 3))
            clsopen_ratio.append(round(price_data['clsopen_ratio'].iloc[findstart_num:max_loc + 1].mean(), 3)
                                 if find_list[-1] + 1 < len(price_data) - 1
                                 else round(price_data['clsopen_ratio'].iloc[findstart_num:].mean(), 3))
            und2days_temp = int(
                count_und_num(compare_data=price_data.loc[findstart_adj:findend_adj, 'max_ratio'],
                              limit=limit_num)) \
                if find_list[-1] + 1 < len(price_data) - 1 \
                else int(count_und_num(compare_data=price_data['max_ratio'].loc[findstart_adj:],
                                       limit=limit_num))
            und2_contidays.append(und2days_temp)
            # print(extre_ratio[-1])
            start_date.append(findstart_adj)
            end_date.append(findend_adj)
            last_list = [n for n in find_list if n <= max_loc]
    period_count = pd.DataFrame({'start_date': start_date,
                                 'end_date': end_date,
                                 'lastdays': first_lastdays,
                                 'avgratio': first_avgratio,
                                 'sumratio': first_sumratio,
                                 'extre_ratio': extre_ratio,
                                 'avg_turnover': avg_turnover,
                                 'max_turnover': max_turnover,
                                 'clsopen_ratio': clsopen_ratio,
                                 'turnover2rise': turnover2rise,
                                 'turnover2drop': turnover2drop,
                                 'adverse2trend_sum': adverse2trend,
                                 'und2_contidays': und2_contidays,
                                 'cls_diff': cls_diff},
                                index=range(0, len(first_lastdays)))
    zhang_MAX = max(len(list(g)) for k, g in groupby(price_data['day_state'])
                    if k == '涨') if len(price_data.query('day_state=="涨"')) != 0 else 0
    die_MAX = max(len(list(g)) for k, g in groupby(price_data['day_state'])
                  if k == '跌') if len(price_data.query('day_state=="跌"')) != 0 else 0
    consist_count = pd.Series({'涨MAX': zhang_MAX,
                               '跌MAX': die_MAX,
                               })
    period_count = period_count.query('lastdays>2 | sumratio>=7')
    period_count = period_count.reset_index()

    if index_mode is not None and len(period_count) > 0:
        if len(period_count) > 1:
            period_count['index_sum'] = period_count.apply(
                lambda fn: (index_data.loc[fn['end_date'], 'close'] /
                            index_data.loc[fn['start_date'], 'pre_close'] - 1) * 100, axis=1)
        else:
            period_count['index_sum'] = (index_data.loc[period_count['end_date'].iloc[-1], 'close'] /
                                         index_data.loc[period_count['start_date'].iloc[-1], 'pre_close'] - 1) * 100
        period_count['index_diff'] = period_count['sumratio'] - \
                                     period_count['index_sum']

    # if len(period_count) > 0:
    #     period_count = period_count.query('lastdays>2').copy()
    #     period_count = period_count.reset_index(drop=True)

    start_date = []
    end_date = []
    lastdays = []
    avgratio = []
    sumratio = []
    cls_diff = []
    extre_ratio = []
    avg_turnover = []
    max_turnover = []
    turnover2rise = []
    turnover2drop = []
    adverse2trend = []
    clsopen_ratio = []
    und2_contidays = []
    if len(period_count) > 0:
        for index in period_count.index:
            if index == period_count.index[0] and len(price_data.loc[:period_count.loc[index, 'start_date']]) > 1:
                start_date.append(price_data.index[0])
                end_date.append(
                    price_data.loc[:period_count.loc[index, 'start_date']].index[-1])
                lastdays.append(len(price_data.loc[:end_date[-1]]))
                sumratio.append(round((price_data.loc[end_date[-1], 'stk_close'
                                       ] / price_data['stk_close'].iloc[0] - 1) * 100, 2))
                cls_diff.append(abs(price_data.loc[start_date[-1]:end_date[-1], 'stk_close'].min() -
                                    price_data.loc[:end_date[-1], 'stk_close'].max()))
                avgratio.append(round(sumratio[-1] / lastdays[-1], 2))
                extre_ratio.append(
                    round(price_data.loc[:end_date[-1], 'day_ratio'].min(), 2))
                avg_turnover.append(
                    round(price_data.loc[:end_date[-1], 'turnover'].mean(), 2))
                max_turnover.append(
                    round(price_data.loc[:end_date[-1], 'turnover'].max(), 2))
                rise_index = price_data.loc[:end_date[-1]
                             ].query('day_ratio>0').index
                drop_index = price_data.loc[:end_date[-1]
                             ].query('day_ratio<0').index
                turnover2rise.append(
                    round(price_data.loc[rise_index, 'turnover'].sum() /
                          price_data.loc[rise_index, 'day_ratio'].sum()
                          if len(rise_index) > 0
                             and price_data.loc[rise_index, 'day_ratio'].sum() != 0 else 0, 3))
                turnover2drop.append(
                    round(abs(price_data.loc[drop_index, 'turnover'].sum() /
                              price_data.loc[drop_index, 'day_ratio'].sum())
                          if len(drop_index) > 0
                             and price_data.loc[drop_index, 'day_ratio'].sum() != 0 else 0, 3))
                # adverse2trend.append(round(
                #     abs(price_data.loc[rise_index, 'day_ratio'].sum()) /
                #     abs(price_data.loc[drop_index, 'day_ratio'].sum())
                #     if price_data.loc[drop_index, 'day_ratio'].sum() != 0 else 1, 3))
                adverse2trend.append(
                    round(len(price_data.loc[:end_date[-1]].query('stk_close>stk_rollmax_shift')) /
                          len(price_data.loc[:end_date[-1]])
                          if len(price_data.loc[:end_date[-1]]) > 0 else 0, 3))
                clsopen_ratio.append(
                    round(price_data.loc[:end_date[-1], 'clsopen_ratio'].mean(), 2))
                und2days_temp = int(
                    count_und_num(compare_data=price_data.loc[:end_date[-1], 'max_ratio'],
                                  limit=limit_num))
                und2_contidays.append(und2days_temp)
            elif period_count.index[-1] >= index > period_count.index[0]:
                start_date.append(
                    price_data.loc[period_count.loc[:index - 1, 'end_date'].iloc[-1]:].index[0])
                end_date.append(
                    price_data.loc[:period_count.loc[index, 'start_date']].index[-1])
                lastdays.append(
                    len(price_data.loc[start_date[-1]:end_date[-1]]) - 1)
                sumratio.append(
                    round((price_data.loc[end_date[-1], 'stk_close'] /
                           price_data.loc[period_count.loc[:index - 1, 'end_date'].iloc[-1], 'stk_close'] - 1) * 100,
                          2))
                cls_diff.append(abs(price_data.loc[start_date[-1]:end_date[-1], 'stk_close'].min() -
                                    max(price_data.loc[start_date[-1], 'stk_close'],
                                        price_data.loc[start_date[-1], 'pre_close'])))
                avgratio.append(
                    round(sumratio[-1] / lastdays[-1] if lastdays[-1] > 0 else sumratio[-1], 2))
                extre_ratio.append(round(price_data.loc[start_date[-1]:end_date[-1], 'day_ratio'].min(), 2)
                                   if end_date[-1] < price_data.index[-1]
                                   else round(price_data.loc[start_date[-1]:, 'day_ratio'].min(), 2))
                avg_turnover.append(round(price_data.loc[start_date[-1]:end_date[-1], 'turnover'].mean(), 2)
                                    if end_date[-1] < price_data.index[-1]
                                    else round(price_data.loc[start_date[-1]:, 'turnover'].mean(), 2))
                max_turnover.append(round(price_data.loc[start_date[-1]:end_date[-1], 'turnover'].max(), 2)
                                    if end_date[-1] < price_data.index[-1]
                                    else round(price_data.loc[start_date[-1]:, 'turnover'].max(), 2))
                sec_startdate = price_data.loc[start_date[-1]:].index[1] \
                    if len(price_data.loc[start_date[-1]:]) > 1 else start_date[-1]
                rise_index = price_data.loc[sec_startdate: end_date[-1]].query('day_ratio>0').index \
                    if end_date[-1] < price_data.index[-1] \
                    else price_data.loc[sec_startdate:].query('day_ratio>0').index
                drop_index = price_data.loc[sec_startdate: end_date[-1]].query('day_ratio<0').index \
                    if end_date[-1] < price_data.index[-1] \
                    else price_data.loc[sec_startdate:].query('day_ratio<0').index
                turnover2rise.append(
                    round(price_data.loc[rise_index, 'turnover'].sum() /
                          price_data.loc[rise_index, 'day_ratio'].sum()
                          if len(rise_index) > 0
                             and price_data.loc[rise_index, 'day_ratio'].sum() != 0 else 0, 3))
                turnover2drop.append(
                    round(abs(price_data.loc[drop_index, 'turnover'].sum() /
                              price_data.loc[drop_index, 'day_ratio'].sum())
                          if len(drop_index) > 0 and price_data.loc[drop_index, 'day_ratio'].sum() != 0 else 0, 3))
                # adverse2trend.append(
                #     round(abs(price_data.loc[rise_index, 'day_ratio'].sum()) /
                #           abs(price_data.loc[drop_index, 'day_ratio'].sum())
                #           if price_data.loc[drop_index, 'day_ratio'].sum() != 0 else 1, 3))
                adverse2trend.append(
                    round(len(price_data.loc[sec_startdate: end_date[-1]].query('stk_close>stk_rollmax_shift')) /
                          len(price_data.loc[sec_startdate: end_date[-1]])
                          if len(price_data.loc[sec_startdate: end_date[-1]]) > 0 else 0, 3))
                clsopen_ratio.append(round(price_data.loc[start_date[-1]:end_date[-1], 'clsopen_ratio'].mean(), 2)
                                     if end_date[-1] < price_data.index[-1]
                                     else round(price_data.loc[start_date[-1]:, 'clsopen_ratio'].mean(), 2))
                und2days_temp = int(
                    count_und_num(compare_data=price_data.loc[start_date[-1]:end_date[-1], 'max_ratio'],
                                  limit=limit_num)) \
                    if end_date[-1] < price_data.index[-1] \
                    else int(count_und_num(compare_data=price_data.loc[start_date[-1]:, 'max_ratio'],
                                           limit=limit_num))
                und2_contidays.append(und2days_temp)
                if period_count.index[-1] == index and len(price_data.loc[period_count.loc[index, 'end_date']:]) > 1:
                    start_date.append(
                        price_data.loc[period_count.loc[index, 'end_date']:].index[0])
                    end_date.append(price_data.index[-1])
                    lastdays.append(
                        len(price_data.loc[start_date[-1]:end_date[-1]]))
                    sumratio.append(round(
                        (price_data.loc[end_date[-1], 'stk_close'] /
                         max(price_data.loc[period_count.loc[index, 'end_date'], 'stk_close'],
                             price_data.loc[period_count.loc[index, 'end_date'], 'pre_close']) - 1) * 100, 2))
                    cls_diff.append(abs(price_data.loc[start_date[-1]:end_date[-1], 'stk_close'].min() -
                                        max(price_data.loc[start_date[-1], 'stk_close'],
                                            price_data.loc[start_date[-1], 'pre_close'])))
                    avgratio.append(round(sumratio[-1] / lastdays[-1], 2))
                    extre_ratio.append(round(price_data.loc[start_date[-1]:end_date[-1], 'day_ratio'].min(), 2)
                                       if end_date[-1] < price_data.index[-1]
                                       else round(price_data.loc[start_date[-1]:, 'day_ratio'].min(), 2))
                    avg_turnover.append(round(price_data.loc[start_date[-1]:end_date[-1], 'turnover'].mean(), 2)
                                        if end_date[-1] < price_data.index[-1]
                                        else round(price_data.loc[start_date[-1]:, 'turnover'].mean(), 2))
                    max_turnover.append(round(price_data.loc[start_date[-1]:end_date[-1], 'turnover'].max(), 2)
                                        if end_date[-1] < price_data.index[-1]
                                        else round(price_data.loc[start_date[-1]:, 'turnover'].max(), 2))
                    sec_startdate = price_data.loc[start_date[-1]:].index[1] \
                        if len(price_data.loc[start_date[-1]:]) > 1 else start_date[-1]
                    rise_index = price_data.loc[sec_startdate: end_date[-1]].query('day_ratio>0').index \
                        if end_date[-1] < price_data.index[-1] \
                        else price_data.loc[sec_startdate:].query('day_ratio>0').index
                    drop_index = price_data.loc[sec_startdate: end_date[-1]].query('day_ratio<0').index \
                        if end_date[-1] < price_data.index[-1] \
                        else price_data.loc[sec_startdate:].query('day_ratio<0').index
                    turnover2rise.append(
                        round(price_data.loc[rise_index, 'turnover'].sum() /
                              price_data.loc[rise_index, 'day_ratio'].sum()
                              if len(rise_index) > 0
                                 and price_data.loc[rise_index, 'day_ratio'].sum() != 0 else 0, 3))
                    turnover2drop.append(
                        round(abs(price_data.loc[drop_index, 'turnover'].sum() /
                                  price_data.loc[drop_index, 'day_ratio'].sum())
                              if len(drop_index) > 0
                                 and price_data.loc[drop_index, 'day_ratio'].sum() != 0 else 0, 3))
                    # adverse2trend.append(
                    #     round(abs(price_data.loc[rise_index, 'day_ratio'].sum()) /
                    #           abs(price_data.loc[drop_index, 'day_ratio'].sum())
                    #               if price_data.loc[drop_index, 'day_ratio'].sum() != 0 else 1, 3))
                    adverse2trend.append(
                        round(len(price_data.loc[sec_startdate: end_date[-1]].query('stk_close>stk_rollmax_shift')) /
                              len(price_data.loc[sec_startdate: end_date[-1]])
                              if len(price_data.loc[sec_startdate: end_date[-1]]) > 0 else 0, 3))
                    clsopen_ratio.append(round(price_data.loc[start_date[-1]:end_date[-1], 'clsopen_ratio'].mean(), 2)
                                         if end_date[-1] < price_data.index[-1]
                                         else round(price_data.loc[start_date[-1]:, 'clsopen_ratio'].mean(), 2))
                    und2days_temp = int(
                        count_und_num(compare_data=price_data.loc[start_date[-1]:end_date[-1], 'max_ratio'],
                                      limit=limit_num)) \
                        if end_date[-1] < price_data.index[-1] \
                        else int(count_und_num(compare_data=price_data.loc[start_date[-1]:, 'max_ratio'],
                                               limit=limit_num))
                    und2_contidays.append(und2days_temp)
            elif period_count.index[-1] == index and len(price_data.loc[period_count.loc[index, 'end_date']:]) > 1:
                start_date.append(
                    price_data.loc[period_count.loc[index, 'end_date']:].index[0])
                end_date.append(price_data.index[-1])
                lastdays.append(
                    len(price_data.loc[start_date[-1]:end_date[-1]]))
                sumratio.append(round(
                    (price_data.loc[end_date[-1], 'stk_close'] /
                     max(price_data.loc[period_count.loc[index, 'end_date'], 'stk_close'],
                         price_data.loc[period_count.loc[index, 'end_date'], 'pre_close']) - 1) * 100, 2))
                cls_diff.append(abs(price_data.loc[start_date[-1]:end_date[-1], 'stk_close'].min() -
                                    max(price_data.loc[start_date[-1], 'stk_close'],
                                        price_data.loc[start_date[-1], 'pre_close'])))
                avgratio.append(round(sumratio[-1] / lastdays[-1], 2))
                extre_ratio.append(round(price_data.loc[start_date[-1]:end_date[-1], 'day_ratio'].min(), 2)
                                   if end_date[-1] < price_data.index[-1]
                                   else round(price_data.loc[start_date[-1]:, 'day_ratio'].min(), 2))
                avg_turnover.append(round(price_data.loc[start_date[-1]:end_date[-1], 'turnover'].mean(), 2)
                                    if end_date[-1] < price_data.index[-1]
                                    else round(price_data.loc[start_date[-1]:, 'turnover'].mean(), 2))
                max_turnover.append(round(price_data.loc[start_date[-1]:end_date[-1], 'turnover'].max(), 2)
                                    if end_date[-1] < price_data.index[-1]
                                    else round(price_data.loc[start_date[-1]:, 'turnover'].max(), 2))
                sec_startdate = price_data.loc[start_date[-1]:].index[1] \
                    if len(price_data.loc[start_date[-1]:]) > 1 else start_date[-1]
                rise_index = price_data.loc[sec_startdate: end_date[-1]].query('day_ratio>0').index \
                    if end_date[-1] < price_data.index[-1] \
                    else price_data.loc[sec_startdate:].query('day_ratio>0').index
                drop_index = price_data.loc[sec_startdate: end_date[-1]].query('day_ratio<0').index \
                    if end_date[-1] < price_data.index[-1] \
                    else price_data.loc[sec_startdate:].query('day_ratio<0').index
                turnover2rise.append(
                    round(price_data.loc[rise_index, 'turnover'].sum() /
                          price_data.loc[rise_index, 'day_ratio'].sum()
                          if len(rise_index) > 0
                             and price_data.loc[rise_index, 'day_ratio'].sum() != 0 else 0, 3))
                turnover2drop.append(
                    round(abs(price_data.loc[drop_index, 'turnover'].sum() /
                              price_data.loc[drop_index, 'day_ratio'].sum())
                          if len(drop_index) > 0
                             and price_data.loc[drop_index, 'day_ratio'].sum() != 0 else 0, 3))
                # adverse2trend.append(
                #     round(abs(price_data.loc[rise_index, 'day_ratio'].sum()) /
                #           abs(price_data.loc[drop_index, 'day_ratio'].sum())
                #           if price_data.loc[drop_index, 'day_ratio'].sum() != 0 else 1, 3))
                adverse2trend.append(
                    round(len(price_data.loc[sec_startdate: end_date[-1]].query('stk_close>stk_rollmax_shift')) /
                          len(price_data.loc[sec_startdate: end_date[-1]])
                          if len(price_data.loc[sec_startdate: end_date[-1]]) > 0 else 0, 3))
                clsopen_ratio.append(round(price_data.loc[start_date[-1]:end_date[-1], 'clsopen_ratio'].mean(), 2)
                                     if end_date[-1] < price_data.index[-1]
                                     else round(price_data.loc[start_date[-1]:, 'clsopen_ratio'].mean(), 2))
                und2days_temp = int(
                    count_und_num(compare_data=price_data.loc[start_date[-1]:end_date[-1], 'max_ratio'],
                                  limit=limit_num)) \
                    if end_date[-1] < price_data.index[-1] \
                    else int(count_und_num(compare_data=price_data.loc[start_date[-1]:, 'max_ratio'],
                                           limit=limit_num))
                und2_contidays.append(und2days_temp)
        section_drop = pd.DataFrame({'start_date': start_date,
                                     'end_date': end_date,
                                     'lastdays': lastdays,
                                     'avgratio': avgratio,
                                     'sumratio': sumratio,
                                     'extre_ratio': extre_ratio,
                                     'avg_turnover': avg_turnover,
                                     'max_turnover': max_turnover,
                                     'clsopen_ratio': clsopen_ratio,
                                     'turnover2rise': turnover2rise,
                                     'turnover2drop': turnover2drop,
                                     'adverse2trend_sum': adverse2trend,
                                     'und2_contidays': und2_contidays,
                                     'cls_diff': cls_diff},
                                    index=range(0, len(lastdays)))
    else:
        start_date = price_data.index[0]
        end_date = price_data.index[-1]
        lastdays = len(price_data)
        sumratio = round((price_data.loc[end_date, 'stk_close'] /
                          price_data.loc[start_date, 'stk_close'] - 1) * 100, 2)
        cls_diff = abs(price_data.loc[end_date, 'stk_close'] -
                       price_data.loc[start_date:end_date, 'stk_close'].max())
        avgratio = round(sumratio / lastdays, 2)
        extre_ratio = round(price_data['day_ratio'].min(), 2)
        avg_turnover = round(price_data['turnover'].mean(), 2)
        max_turnover = round(price_data['turnover'].max(), 2)
        rise_index = price_data.query('day_ratio>0').index[1:] \
            if len(price_data.query('day_ratio>0')) > 1 else price_data.query('day_ratio>0').index
        drop_index = price_data.query('day_ratio<0').index[1:] \
            if len(price_data.query('day_ratio<0')) > 1 else price_data.query('day_ratio<0').index
        turnover2rise.append(
            round(price_data.loc[rise_index, 'turnover'].sum() /
                  price_data.loc[rise_index, 'day_ratio'].sum()
                  if len(rise_index) > 0
                     and price_data.loc[rise_index, 'day_ratio'].sum() != 0 else 0, 3))
        turnover2drop.append(
            round(abs(price_data.loc[drop_index, 'turnover'].sum() /
                      price_data.loc[drop_index, 'day_ratio'].sum())
                  if len(drop_index) > 0
                     and price_data.loc[drop_index, 'day_ratio'].sum() != 0 else 0, 3))
        # adverse2trend.append(
        #     round(abs(price_data.loc[rise_index, 'day_ratio'].sum()) /
        #           abs(price_data.loc[drop_index, 'day_ratio'].sum())
        #               if price_data.loc[drop_index, 'day_ratio'].sum() != 0 else 1, 3))
        adverse2trend.append(
            round(len(price_data.query('stk_close>stk_rollmax_shift')) /
                  len(price_data) if len(price_data) > 0 else 0, 3))

        clsopen_ratio = round(price_data['clsopen_ratio'].mean(), 2)
        und2days_temp = int(
            count_und_num(compare_data=price_data['max_ratio'],
                          limit=limit_num))
        und2_contidays.append(und2days_temp)
        section_drop = pd.DataFrame({'start_date': start_date,
                                     'end_date': end_date,
                                     'lastdays': lastdays,
                                     'avgratio': avgratio,
                                     'sumratio': sumratio,
                                     'extre_ratio': extre_ratio,
                                     'avg_turnover': avg_turnover,
                                     'max_turnover': max_turnover,
                                     'clsopen_ratio': clsopen_ratio,
                                     'turnover2rise': turnover2rise,
                                     'turnover2drop': turnover2drop,
                                     'adverse2trend_sum': adverse2trend,
                                     'und2_contidays': und2_contidays,
                                     'cls_diff': cls_diff},
                                    index=range(0, 1))
    if index_mode is not None:
        if len(section_drop) > 0:
            section_drop['index_sum'] = section_drop.apply(
                lambda fn: (index_data.loc[fn['end_date'], 'close'] /
                            index_data.loc[fn['start_date'], 'pre_close'] - 1) * 100, axis=1)
            section_drop['index_diff'] = section_drop['sumratio'] - \
                                         section_drop['index_sum']
        else:
            section_drop['index_sum'] = None
            section_drop['index_diff'] = None
    section_drop = section_drop.query('avgratio!=0 & sumratio!=0')
    return period_count, section_drop, price_data

def count_und_num(compare_data, limit, mode=2):
    """统计日开收盘幅度连续低于limit的天数"""
    if mode == 1:
        co_loc, = np.where(compare_data > limit)
        co_loc = np.insert(co_loc, 0, 0)
        co_loc = np.append(co_loc, [len(compare_data) - 1])
        co_df = pd.DataFrame({'co_loc': co_loc}, index=range(0, len(co_loc)))
        co_df['diff'] = co_df.co_loc.diff()
        return max(0, co_df['diff'].max() - 1) if pd.notnull(co_df['diff'].max()) else 0
    else:
        co_loc, = np.where(compare_data < limit)
        return len(co_loc)


def collect_indexturns(pickdate, price='close', mode='valley'):
    """测算pickdate后不同指数的转折点，
       参数price设定选择开盘、收盘、最高、最低哪种价格进行计算
       参数mode设定为all不分波峰波谷，valley取波谷，peak取波峰"""
    # index_data = pd.read_csv('dataset/indexdata.csv')
    index_data = get_index_data()
    index_codes = index_data['ts_code'].unique()
    print('指数代码包括：', index_codes)
    break_turn_dates = pd.DataFrame()
    for index_code in index_codes:
        idx_data = index_data.query('ts_code==@index_code')[[
            'trade_date', price]].sort_values('trade_date', ascending=True).set_index('trade_date', drop=False)
        idx_crthr = peak_valley_turn(
            idx_data[price].values, idx_data.index.to_list(), mode='Index')
        idx_crthr_pick = idx_crthr.loc[idx_crthr['date'] >= pickdate].copy()
        idx_crthr_pick['state'] = idx_crthr_pick.apply(lambda x: 'valley' if pd.notnull(x['valley']) else 'peak',
                                                       axis=1)
        idx_crthr_pick['ts_code'] = index_code
        break_turn_dates = pd.concat([break_turn_dates, idx_crthr_pick[['date', 'state', 'ts_code']]],
                                     ignore_index=True)
    break_turn_dates = break_turn_dates.drop_duplicates(
        subset='date', keep='last')
    break_turn_dates = break_turn_dates.sort_values('date', ascending=True)
    if mode == 'valley':
        break_turn_dates = break_turn_dates.query('state=="valley"')
    elif mode == 'peak':
        break_turn_dates = break_turn_dates.query('state=="peak"')
    return break_turn_dates


def code_2_txt(result):
    filename = '../codes.txt'
    f = open(filename, 'w', encoding='utf-8')
    ts_codes = result['ts_code'].apply(lambda fn: fn[0:6])
    lines = ts_codes.values.tolist()
    for line in lines:
        f.write(line + '\n')
    f.close()
    print('代码已写入codes.txt文件！')


def cal_target_price(turn_date, stk_data, periodchang, mode=None):
    """计算目标价格，periodchang可使用trendstat序列"""
    stk_data['max_CO'] = stk_data.apply(
        lambda fn: max(fn['close'], fn['open']), axis=1)
    stk_high = stk_data['high']
    stk_low = stk_data['low']
    stk_close = stk_data['close']
    # turn_date = result_loc['Turn_Date']
    # 获取Turn_Date前下行区间起始日期序列
    if 'sum_ratio' in periodchang.columns:
        PreTurn_PeriodStartDate_Drop = periodchang.query(
            'start_date<@turn_date & sum_ratio<0')['start_date'].values
    else:
        PreTurn_PeriodStartDate_Drop = periodchang.query(
            'start_date<@turn_date & state=="下行"')['start_date'].values
    if len(PreTurn_PeriodStartDate_Drop) == 0 and mode:
        return 0, 0, 0
    elif len(PreTurn_PeriodStartDate_Drop) == 0:
        return 0, 0
    stk_Peak = stk_data.loc[PreTurn_PeriodStartDate_Drop, 'max_CO'].copy()
    stk_Peak = stk_Peak.reindex(stk_Peak.index[::-1])

    # 获取Turn_Date后最高价及对应日期
    # PostTurn_period = periodchang.query('end_date>@turn_date & sum_ratio>0')
    # if len(PostTurn_period) > 0:
    #     PostTurn_Maxhigh = stk_high.loc[turn_date:PostTurn_period['end_date'].iloc[0]].max()
    #     PostTurn_MaxDate = stk_high.loc[turn_date:PostTurn_period['end_date'].iloc[0]].idxmax()
    if len(stk_high.loc[turn_date:]) > 0:
        PostTurn_Maxhigh = stk_high.loc[turn_date:].max()
        PostTurn_MaxDate = stk_high.loc[turn_date:].idxmax()
    elif mode:
        return 0, 0, 0
    else:
        return 0, 0
    # 计算上轨价格及倍数
    if all(stk_Peak <= PostTurn_Maxhigh):
        up_level_value = stk_Peak.max()
        up_level_date = stk_Peak.idxmax()
        turn_value = min(stk_data.loc[up_level_date:, 'close'].min(
        ), stk_data.loc[up_level_date:, 'open'].min())
        if stk_low.loc[PostTurn_MaxDate:].min() > up_level_value:
            multi = 2
        else:
            multi = 1
    elif stk_Peak.iloc[0] <= PostTurn_Maxhigh:
        num = 0
        up_level_value = stk_Peak.iloc[0]
        up_level_date = stk_Peak.index[0]
        turn_value = min(stk_data.loc[up_level_date:, 'close'].min(
        ), stk_data.loc[up_level_date:, 'open'].min())
        while num < len(stk_Peak) and stk_Peak.iloc[num] <= PostTurn_Maxhigh:
            num += 1
            if stk_Peak.iloc[num] > up_level_value:
                up_level_value = stk_Peak.iloc[num]
                up_level_date = stk_Peak.index[num]
                turn_value = min(stk_data.loc[up_level_date:, 'close'].min(),
                                 stk_data.loc[up_level_date:, 'open'].min())
        # over_level = stk_close[turn_date:].loc[stk_close[turn_date:] > up_level_value]

        if stk_low.loc[PostTurn_MaxDate:].min() > up_level_value and PostTurn_Maxhigh > (
                up_level_value * 2 - turn_value) * 1.1:
            multi = 2
        else:
            multi = 1
    else:
        up_level_value = stk_Peak.iloc[0]
        up_level_date = stk_Peak.index[0]
        turn_value = min(stk_data.loc[up_level_date:, 'close'].min(
        ), stk_data.loc[up_level_date:, 'open'].min())
        multi = 1

    # 计算目标价格
    target_price = up_level_value + multi * (up_level_value - turn_value)

    if mode is not None:
        press_price = '-'
        if len(stk_Peak.loc[up_level_date:][(max(up_level_value, PostTurn_Maxhigh) <
                                             stk_Peak.loc[up_level_date:])
                                            & (stk_Peak.loc[up_level_date:] <
                                               target_price)]) > 0:
            press_temp = stk_Peak.loc[up_level_date:][
                (max(up_level_value, PostTurn_Maxhigh) < stk_Peak.loc[up_level_date:]) & (
                        stk_Peak.loc[up_level_date:] < target_price)].unique().tolist()
            press_temp.sort()
            press_price = '/'.join([str(round(i, 2)) for i in press_temp])
        return round(target_price, 2), round(up_level_value, 2), press_price
    else:
        return round(target_price, 2), round(up_level_value, 2)


def stk_count(start_date, end_date, num=50, ratiolevel=50):
    """计算指定起止日期内的区间涨幅前10%强势品种地域分布
       入参：start_date起始日期， end_date结束日期
       出参：areacount地域分布，induscount行业分布, stock_pick强势股票列表"""
    # from function_ai.Function_ai import get_stock_info, get_stock_data
    import numpy as np
    import pandas as pd
    import datetime
    stock_info = get_stock_info()
    data_startdate = (pd.Timestamp(start_date) +
                      datetime.timedelta(days=-7)).strftime('%Y-%m-%d')
    stock_data = get_stock_data(start_date=data_startdate, end_date=end_date)
    stock_data = stock_data.sort_values(
        ['trade_date', 'ts_code'], ascending=[True, True])
    print('已获取股票行情数据')
    stock_info['Ratio'] = stock_info['ts_code'].apply(
        lambda fn: round((stock_data[stock_data['ts_code'] == fn
                                     ]['close'].max() / stock_data[stock_data['ts_code'] == fn]['close'].iloc[
                              0] - 1) * 100, 2)
        if len(stock_data[(stock_data['ts_code'] == fn) & (stock_data['trade_date'] <= start_date)]) > 0
           and len(stock_data[(stock_data['ts_code'] == fn) & (stock_data['trade_date'] > start_date)]) > 0
           and stock_data[(stock_data['ts_code'] == fn) & (stock_data['trade_date'] <= start_date)]['close'].mean() >=
           stock_data[(stock_data['ts_code'] == fn) & (
                   stock_data['trade_date'] >= start_date)]['close'].iloc[0]
        else np.nan)
    stock_info['Rank'] = stock_info['Ratio'].rank(ascending=False)
    # stock_info = stock_info.dropna(subset=['Ratio'], axis=0)
    stock_pick = stock_info[(stock_info['Rank'] <= num)
                            & (stock_info['Ratio'] >= ratiolevel)]
    stock_pick = stock_pick.sort_values('Ratio', ascending=False)
    areacount = stock_pick['ts_code'].groupby(stock_pick['area']).count()
    areacount = areacount.sort_values(ascending=False)
    induscount = stock_pick['ts_code'].groupby(stock_pick['industry']).count()
    induscount = induscount.sort_values(ascending=False)
    print('all done!')
    return areacount, induscount, stock_pick


# def collect_indusnum(start_date=None, end_date=None, turn_date=None, mode='result3', industry=''):
#     """从resultindus数据库获取指定行业统计数据列表
#         mode参数设定：
#             break 获取resultbreak数据库内指定时间周期内的行业筛选股票数量，如industry非空，则获取指定行业的数据序列
#             stkpick 获取stk_results_3数据库内指定时间周期内的行业赛结果数据，如industry非空，则获取指定行业的数据序列
#     """
#     import pandas as pd
#     if start_date is not None:
#         index_data = get_index_data(start_date=start_date, end_date=end_date)
#         trade_dates = index_data['trade_date'].unique()
#     else:
#         trade_dates = [end_date]
#     if turn_date is None:
#         turn_date = start_date
#     Test_Data = pd.DataFrame()
#     indusfile = Path(
#         '/Users/<USER>/PycharmProjects/AI_Stock/dataset/全部A股行业归属.xlsx')
#     stock_info = pd.read_excel(indusfile)
#     indus_num1 = stock_info['ts_code'].groupby(
#         stock_info['industry_one']).count()
#     indus_num2 = stock_info['ts_code'].groupby(
#         stock_info['industry_two']).count()
#     indus_num = pd.DataFrame(pd.concat([indus_num1, indus_num2]))
#     indus_num.columns = ['Indus_Num']
#     if mode == 'result3':
#         from function_ai.StkQuota_Func_V7 import get_result_3
#         for trade_date in trade_dates:
#             ResultPick = get_result_3(end_date=trade_date, turn_date=turn_date)
#             if len(ResultPick) == 0:
#                 continue
#             ResultIndus = ResultPick['ts_code'].groupby(
#                 ResultPick['industry']).count()
#             ResultIndus = pd.DataFrame(ResultIndus)
#             ResultIndus = ResultIndus.rename(columns={'ts_code': 'Same_Num'})
#             ResultIndus = pd.merge(
#                 ResultIndus, indus_num, how='left', left_index=True, right_index=True)
#             ResultIndus['ComRatio'] = ResultIndus['Same_Num'] / \
#                                       ResultIndus['Indus_Num']
#             ResultIndus['Rank'] = ResultIndus['Same_Num'].rank(ascending=False)
#             ResultIndus['List_Date'] = trade_date
#             ResultIndus = ResultIndus.reset_index()
#             ResultIndus = ResultIndus.rename(columns={'index': 'industry'})
#             if industry != '':
#                 Test_Data = pd.concat([Test_Data, ResultIndus.query(
#                     'industry==@industry')], ignore_index=True)
#             else:
#                 Test_Data = pd.concat(
#                     [Test_Data, ResultIndus], ignore_index=True)
#     elif mode == 'result2':
#         from function_ai.stkpick_funcs_old.StkPick_Func_2_1 import sfitprocess_2_1
#         for trade_date in trade_dates:
#             ResultBreak = sfitprocess_2_1(
#                 end_date=trade_date, turn_dates=turn_date)
#             if len(ResultBreak) == 0:
#                 continue
#             ResultIndus = ResultBreak['ts_code'].groupby(
#                 ResultBreak['industry']).count()
#             ResultIndus = pd.DataFrame(ResultIndus)
#             ResultIndus = ResultIndus.rename(columns={'ts_code': 'Same_Num'})
#             ResultIndus = pd.merge(
#                 ResultIndus, indus_num, how='left', left_index=True, right_index=True)
#             ResultIndus['ComRatio'] = ResultIndus['Same_Num'] / \
#                                       ResultIndus['Indus_Num']
#             ResultIndus['Rank'] = ResultIndus['Same_Num'].rank(ascending=False)
#             ResultIndus['List_Date'] = trade_date
#             ResultIndus = ResultIndus.reset_index()
#             ResultIndus = ResultIndus.rename(columns={'index': 'industry'})
#             if industry != '':
#                 Test_Data = pd.concat([Test_Data, ResultIndus.query(
#                     'industry==@industry')], ignore_index=True)
#             else:
#                 Test_Data = pd.concat(
#                     [Test_Data, ResultIndus], ignore_index=True)
#     Test_Data = Test_Data.sort_values(
#         ['List_Date', 'Same_Num'], ascending=[False, False])
#     return Test_Data


def cal_period_ratio(start_date=None, end_date=None, mode='Max', stk_list=None):
    """计算指定区间内股票收益率排序，股票上行趋势转折点与起始日期接近"""
    from function_ai.StkPick_Func_V7 import get_result_3
    if start_date is None or end_date is None:
        print('需录入起止日期参数！')
        return
    # import os
    from multiprocessing import Pool
    p = Pool(7)
    if stk_list is None and mode.lower() != 'max':
        stock_info = get_result_3(end_date=end_date)
        if len(stock_info) == 0:
            print('缺失日期', end_date, '的Result数据')
        Result = stock_info[['ts_code', 'name', 'industry',
                             'Section_StartDate', 'Now_SecDate']].copy()
        ts_codes = Result['ts_code'].unique()
    elif stk_list is not None and mode.lower() != 'max':
        ts_codes = stk_list['ts_code'].unique()
        Result = stk_list[['ts_code', 'name', 'industry',
                           'Section_StartDate', 'Now_SecDate']].copy()
    else:
        stock_info = get_stock_info()
        if stk_list is None:
            Result = stock_info[['ts_code', 'name', 'industry', 'list_date']].copy()
            Result['listed_lastdays'] = Result.apply(
                lambda fn: (pd.Timestamp(end_date) - pd.Timestamp(fn['list_date'])).days, axis=1)
            Result = Result[Result['listed_lastdays'] > 100]
        else:
            if isinstance(stk_list, str):
                stk_list = [stk_list]
            Result = stock_info.query('ts_code in @stk_list')[['ts_code', 'name', 'industry']].copy()
        ts_codes = Result['ts_code'].unique()
    if mode.upper() == 'P' or mode.upper() == 'S' or mode.upper() == 'NS':
        startdate_adj = (pd.to_datetime(
            start_date, format='%Y-%m-%d') - pd.Timedelta(days=1000)).strftime('%Y-%m-%d')
        stock_data = get_stock_data(
            start_date=startdate_adj, end_date=end_date)
        Result['Turn_Date'] = None
        Result['Turn_Diff'] = None
        Result['Max_SumRatio'] = None
        Result['AvgRatio'] = None

        Results = pd.DataFrame()
        pool_data_list = []
        if mode.upper() == 'P':
            for index in tqdm(Result.index):
                ts_code = Result.loc[index, 'ts_code']
                stk_data = stock_data.query('ts_code==@ts_code')
                pool_data_list.append(p.apply_async(func=period_ratio_P,
                                                    args=(
                                                        Result.loc[index, :].copy(), stk_data, start_date)))
        elif mode.upper() == 'S':
            for index in tqdm(Result.index):
                ts_code = Result.loc[index, 'ts_code']
                stk_data = stock_data.query('ts_code==@ts_code')
                Section_StartDate = Result.loc[index, 'Section_StartDate']
                pool_data_list.append(p.apply_async(func=period_ratio_S,
                                                    args=(
                                                        Result.loc[index, :].copy(
                                                        ), stk_data, Section_StartDate,
                                                        start_date, end_date)))
        elif mode.upper() == 'NS':
            for index in tqdm(Result.index):
                ts_code = Result.loc[index, 'ts_code']
                stk_data = stock_data.query('ts_code==@ts_code')
                Section_StartDate = Result.loc[index, 'Now_SecDate']
                pool_data_list.append(p.apply_async(func=period_ratio_S,
                                                    args=(
                                                        Result.loc[index, :].copy(
                                                        ), stk_data, Section_StartDate,
                                                        start_date, end_date)))
        p.close()
        p.join()
        for pool_data in pool_data_list:
            if len(Results) == 0:
                Results = pd.DataFrame(pool_data.get()).T
            else:
                Results = pd.concat(
                    [Results, pd.DataFrame(pool_data.get()).T], axis=0)
        # Results = Result.copy()
        Results = Results[Result.columns].copy()
        Results = Results.sort_values('Max_SumRatio', ascending=False)
        Results = Results.reset_index()
    else:
        stock_data = get_stock_data(start_date=start_date, end_date=end_date)
        if mode.lower() == 'min':
            end_close = stock_data[['ts_code', 'close']].groupby(
                by='ts_code', as_index=False).min()
        elif mode.lower() == 'max':
            end_close = stock_data[['ts_code', 'close']].groupby(
                by='ts_code', as_index=False).max()
        else:
            end_close = stock_data[['ts_code', 'close']].groupby(
                by='ts_code', as_index=False).last()
        end_close = end_close.rename(columns={'close': 'end_close'})
        start_close = stock_data[['ts_code', 'close']].groupby(
            by='ts_code', as_index=False).first()
        ratio_list = pd.merge(start_close, end_close,
                              on=['ts_code'], how='left')
        ratio_list['Max_SumRatio'] = round(
            (ratio_list['end_close'] / ratio_list['close'] - 1) * 100, 2)
        Results = pd.merge(
            Result, ratio_list[['ts_code', 'Max_SumRatio']], on='ts_code', how='left')
        Results = Results.sort_values('Max_SumRatio', ascending=False) \
            if mode == 'Max' else Results.sort_values('Max_SumRatio', ascending=True)
    return Results


def period_ratio_P(result_loc, stk_data, start_date):
    if len(stk_data) > 100:
        stk_data = stk_data.set_index('trade_date', drop=False)
        periodchang, trendstat = period_stat(
            stk_close=stk_data['close'], stk_open=stk_data['open'])

        period_startdate = periodchang.query('sum_ratio>0')['start_date'].iloc[-1] \
            if len(periodchang.query('sum_ratio>0')) > 0 else None
        result_loc['Turn_Date'] = period_startdate
        if period_startdate is not None:
            result_loc['Turn_Diff'] = len(stk_data.loc[period_startdate:start_date]) \
                if period_startdate < start_date \
                else len(stk_data.loc[start_date:period_startdate])

            result_loc['Max_SumRatio'] = round(
                (stk_data.loc[period_startdate:, 'close'].max() / stk_data.loc[period_startdate, 'close'] - 1) * 100, 3)
        else:
            result_loc['Turn_Diff'] = None
            result_loc['Max_SumRatio'] = None
    return result_loc


def period_ratio_S(result_loc, stk_data, Section_StartDate, start_date, end_date):
    stk_data = stk_data.set_index('trade_date', drop=False)
    if len(stk_data) > 100 and len(stk_data.loc[:start_date]) > 0:
        periodchang, trendstat = period_stat(
            stk_close=stk_data['close'], stk_open=stk_data['open'])
        section_rise, section_drop, day_list = section_stat(stk_data=stk_data)
        if len(section_rise) == 0 or len(section_rise.query('start_date<@start_date')) == 0 \
                or len(trendstat) == 0 or len(periodchang) == 0 or Section_StartDate is None:
            return result_loc
        section_d = section_drop.query('start_date<@Section_StartDate')
        if len(section_d) > 0 and len(section_rise) > 0:
            # snum = -2
            # while abs(snum) < len(section_d) \
            #         and min(stk_data.loc[section_d['end_date'].iloc[snum], 'close'],
            #                 stk_data.loc[section_d['end_date'].iloc[snum - 1], 'close']) < \
            #         stk_data.loc[section_d['end_date'].iloc[snum + 1], 'close'] \
            #         and len(section_rise[section_rise['start_date'] <
            #                                     section_d['start_date'].iloc[snum + 1]]) > 0 \
            #         and section_d['sumratio'].iloc[snum] >= -15:
            #     snum -= 1
            # snum = snum + 1 if abs(snum) > len(section_d) else snum
            # section_startdate = stk_data.loc[section_d['start_date'].iloc[snum]:, 'close'].idxmin()
            section_startdate = stk_data.loc[section_d['start_date'].iloc[-1]
                                             :, 'close'].idxmin()
        # elif len(section_rise) > 0:
        #     section_startdate = stk_data.loc[section_rise['start_date'].iloc[-1]:, 'close'].idxmin()
        else:
            # section_startdate = stk_data['close'].idxmin()
            section_startdate = Section_StartDate

        result_loc['Turn_Date'] = section_startdate
        result_loc['Turn_Diff'] = len(stk_data.loc[section_startdate:start_date]) \
            if section_startdate < start_date \
            else len(stk_data.loc[start_date:section_startdate])
        result_loc['IdxMax_Date'] = stk_data.loc[section_startdate:,
                                    'close'].idxmax()
        result_loc['Max_SumRatio'] = round(
            (stk_data.loc[section_startdate:, 'close'].max() / stk_data.loc[section_startdate, 'close'] - 1) * 100, 3)
        result_loc['AvgRatio'] = round(
            (result_loc['Max_SumRatio'] /
             (len(stk_data.loc[section_startdate:stk_data.loc[section_startdate:, 'close'].idxmax()]) - 1) - 1) * 100,
            3) \
            if len(stk_data.loc[section_startdate:stk_data.loc[section_startdate:, 'close'].idxmax()]) > 1 \
            else 0
    return result_loc


def cal_movingavg_stat(stk_data, num=5, mode='Rise'):
    """计算股票价格序列低于前N天收盘价格的天数，及占比"""
    stk_data = stk_data.copy()
    stk_data['movavg'] = stk_data['close'].rolling(
        window=num, closed='left').mean()
    stk_data['ratio'] = stk_data['close'] / stk_data['close'].shift(1) - 1
    if mode == 'Rise':
        stk_data['judge'] = stk_data.apply(
            lambda fn: 1 if fn['close'] < fn['movavg'] else 0, axis=1)
        judge = stk_data['judge'].values
        sum, maxnum = 0, 0
        date = stk_data['trade_date'].iloc[0]
        for index in stk_data.index:
            num = stk_data.loc[index, 'judge']
            sum = sum * num + num
            if sum > maxnum:
                maxnum = sum
                date = stk_data.loc[index, 'trade_date']
        # 输出结果：收盘价低于前num天最低收盘价的天数占统计天数比例，收盘价低于前num天最低收盘价的最大连续天数,
        # 收盘价低于前num天最低收盘价的天数, 最大连续下行跌幅
        return round(judge.sum() / len(stk_data), 2), \
            maxnum, \
            judge.sum(), \
            round((stk_data.loc[date, 'close'] /
                   stk_data.loc[:date, 'close'].max() - 1) * 100, 2)
    else:
        stk_data['judge'] = stk_data.apply(
            lambda fn: 1 if fn['close'] > fn['movavg'] else 0, axis=1)
        judge = stk_data['judge'].values
        sum, maxnum = 0, 0
        date = stk_data['trade_date'].iloc[0]
        for index in stk_data.index:
            num = stk_data.loc[index, 'judge']
            sum = sum * num + num
            if sum > maxnum:
                maxnum = sum
                date = stk_data.loc[index, 'trade_date']
        return round(judge.sum() / len(stk_data), 2), \
            maxnum, \
            judge.sum(), \
            round((stk_data.loc[date, 'close'] /
                   stk_data.loc[:date, 'close'].min() - 1) * 100, 2)


def cal_sectionpeak(index_data=None, end_date=None):
    """计算指数的Section_StartDate和Section_PeakDate"""
    if index_data is None and end_date is not None:
        index_data = get_index_data(stk_code='000001.SH', end_date=end_date).set_index(
            'trade_date', drop=False)
    periodchang, _ = period_stat(
        stk_close=index_data['close'], stk_open=index_data['open'])
    period_startdate = periodchang.query('sum_ratio>0')['start_date'].iloc[-2] \
        if len(periodchang.query('sum_ratio>0')) > 1 \
        else index_data.loc[periodchang['start_date'].iloc[0]:, 'close'].idxmin()
    index_data = index_data.rename(columns={'amount': 'turnover'})
    section_rise, section_d, day_list = section_stat(stk_data=index_data,
                                                     start_date=period_startdate,
                                                     end_date=index_data.index[-1])
    if len(section_d) > 0:
        snum = -2
        while abs(snum) <= len(section_d) \
                and section_d['lastdays'].iloc[snum + 1] < 4 \
                and index_data.loc[section_d['end_date'].iloc[snum], 'close'] < index_data.loc[
            section_d['end_date'].iloc[snum + 1], 'close']:
            snum -= 1
        Section_StartDate = index_data.loc[section_d['start_date'].iloc[snum + 1]
                                           :, 'close'].idxmin()
    else:
        Section_StartDate = index_data.loc[section_rise['start_date'].iloc[-1]
                                           :, 'close'].idxmin()

    section_rise_pre = section_rise.query('end_date<@Section_StartDate')
    if len(section_rise_pre) > 0:
        lnum = -2
        while len(section_rise_pre) > 1 and abs(lnum) <= len(section_rise_pre) \
                and index_data.loc[section_rise_pre['end_date'].iloc[lnum], 'close'
        ] > index_data.loc[section_rise_pre['end_date'].iloc[lnum + 1], 'close']:
            lnum -= 1
        Section_PeakDate = section_rise_pre['end_date'].iloc[lnum + 1] \
            if len(section_rise_pre) > 1 else section_rise_pre['end_date'].iloc[-1]
    else:
        lnum = -2
        while len(section_d) > 1 and abs(lnum) + 1 < len(section_d) \
                and index_data.loc[section_d['start_date'].iloc[lnum], 'close'
        ] > index_data.loc[section_d['start_date'].iloc[lnum + 1], 'close']:
            lnum -= 1
        Section_PeakDate = section_d['start_date'].iloc[lnum + 1] \
            if len(section_d) > 1 else section_d['start_date'].iloc[-1]

    sectionpeak_min = index_data.loc[Section_PeakDate:, 'close'].idxmin()
    if len(section_d.query('start_date>@sectionpeak_min')) > 0 \
            and section_d.query('start_date>@sectionpeak_min')['lastdays'].max() < 5:
        Section_StartDate = sectionpeak_min
    return Section_StartDate, \
        Section_PeakDate, \
        len(index_data.loc[Section_PeakDate:Section_StartDate]), \
        section_d.query('start_date<@Section_StartDate')['lastdays'].iloc[-1]


def cal_recent_stat(stk_data, num=5):
    """计算近num天日均涨幅、最大涨幅和日均开收幅度，据此判定是否缓步运行状态"""
    stk_ratio = (stk_data['close'] / stk_data['close'].shift(1) - 1) * 100
    mean_ratio = round(
        abs(stk_ratio.iloc[-min(num, len(stk_ratio)):]).mean(), 2)
    max_ratio = round(stk_ratio.iloc[-min(num, len(stk_ratio)):].max(), 2)
    close_open_ratio = round(np.mean(
        abs(stk_data['close'].iloc[-min(num, len(stk_data)):] /
            stk_data['open'].iloc[-min(num, len(stk_data)):] - 1)) * 100, 2)
    sec_ratio = round(
        (stk_data['close'].iloc[-1] / stk_data['close'].iloc[-min(num, len(stk_data))] - 1) * 100, 2)
    return mean_ratio, max_ratio, close_open_ratio, sec_ratio


def cal_maxcontisum(stk_data, day_list, mode='涨'):
    """计算连续上涨（下跌）的最大涨幅（跌幅）及对应日均涨跌幅和持续天数，上涨（下跌）回补天数"""
    day_list = day_list.reset_index(drop=False)
    day_statis = day_list.query('day_state==@mode').groupby(
        (day_list.day_state.shift() != day_list.day_state).cumsum()).trade_date.agg(['count', 'min', 'max'])
    if len(day_statis) == 0:
        return 0, 0, 0, 0
    day_statis['return'] = (stk_data.loc[day_statis['max'].values, 'close'].values /
                            stk_data.loc[day_statis['min'].values, 'pre_close'].values - 1) * 100
    try:
        day_statis['lastdays'] = day_statis.apply(
            lambda fn: len(stk_data.loc[fn['min']:fn['max']]), axis=1)
        day_statis['avg_return'] = day_statis['return'] / \
                                   day_statis['lastdays']
    except ValueError:
        pdb.set_trace()

    if mode == "涨":
        max_lastdays = len(stk_data.loc[day_statis.loc[day_statis['return'].idxmax(), 'min']:
                                        day_statis.loc[day_statis['return'].idxmax(), 'max']])
        rise_startdate = day_statis.loc[day_statis['return'].idxmax(), 'min']
        rise_enddate = day_statis.loc[day_statis['return'].idxmax(), 'max']
        mid_close = (stk_data.loc[rise_startdate, 'close'] +
                     stk_data.loc[rise_enddate, 'close']) / 2
        if sum(stk_data.loc[rise_enddate:, 'close'] < mid_close) > 0:
            recover_date = stk_data.loc[
                           rise_enddate:][stk_data.loc[rise_enddate:, 'close'] < mid_close].index[0]
            recover_lastdays = len(stk_data.loc[rise_enddate:recover_date]) - 1
        else:
            recover_lastdays = -1
        return round(day_statis['return'].max(), 2), \
            round(day_statis.loc[day_statis['return'].idxmax(), 'avg_return'], 2), \
            max_lastdays, \
            recover_lastdays
    else:
        max_lastdays = len(stk_data.loc[day_statis.loc[day_statis['return'].idxmin(), 'min']:
                                        day_statis.loc[day_statis['return'].idxmin(), 'max']])
        drop_startdate = day_statis.loc[day_statis['return'].idxmin(), 'min']
        drop_enddate = day_statis.loc[day_statis['return'].idxmin(), 'max']
        mid_close = (stk_data.loc[drop_startdate, 'close'] +
                     stk_data.loc[drop_enddate, 'close']) / 2
        if sum(stk_data.loc[drop_enddate:, 'close'] > mid_close) > 0:
            recover_date = stk_data.loc[
                           drop_enddate:][stk_data.loc[drop_enddate:, 'close'] > mid_close].index[0]
            recover_lastdays = len(stk_data.loc[drop_enddate:recover_date]) - 1
        else:
            recover_lastdays = -1
        return round(day_statis['return'].min(), 2), \
            round(day_statis.loc[day_statis['return'].idxmin(), 'avg_return'], 2), \
            max_lastdays, \
            recover_lastdays


def cal_movavg_bias(stk_code=None, start_date=None, end_date=None, stk_data=None, printstyle='off'):
    """计算股票价格相对于移动平均线的偏离程度
    
    功能:
    - 计算股票高低价格相对于移动平均价格的偏离百分比
    - 返回偏离度的均值和标准差,用于衡量股票价格的波动性
    
    参数:
    stk_code: 股票代码,默认为None
    start_date: 开始日期,默认为None 
    end_date: 结束日期,默认为None
    stk_data: 股票数据,默认为None
    printstyle: 是否打印结果,默认'off'
    
    返回:
    mean: 偏离度均值
    std: 偏离度标准差"""
    if stk_data is None:
        stk_data = get_stock_data(
            stk_code=stk_code, start_date=start_date, end_date=end_date).set_index('trade_date')
    if len(stk_data) > 3:
        stk_data = stk_data.copy()
        stk_data['mean'] = (stk_data['close'] + stk_data['open']) / 2
        # stk_data['mov_avg'] = stk_data['mean'].rolling(window=2).mean()
        stk_data['mov_avg'] = stk_data['mean'].rolling(window=3).mean()
        stk_data['bias_up'] = abs(
            stk_data['high'] / stk_data['mov_avg'] - 1) * 100
        stk_data['bias_down'] = abs(
            stk_data['low'] / stk_data['mov_avg'] - 1) * 100
        mean = round((stk_data['bias_up'].mean() +
                      stk_data['bias_down'].mean()) / 2, 3)
        std = round((stk_data['bias_up'].std() +
                     stk_data['bias_down'].std()) / 2, 3)
    else:
        mean, std = None, None
    if printstyle == 'on':
        print('code:', stk_code, '\nmean:', mean, '\nstd:', std)
    return mean, std


def input_to_str(input_date=None, type='str'):
    """将输入日期转换为字符串"""
    if input_date != '':
        input_date = input_date.replace("'", "")
        input_date = input_date.replace("[", "")
        input_date = input_date.replace("]", "")
        input_date = input_date.replace(" ", "")
        if type.lower() == 'list':
            input_date = input_date.split(',')
    return input_date


def cal_indexdrop_recov(stk_data, index_dropdate=None, index_peakdate=None):
    """计算股票在指数下跌日期后的回升情况"""
    if index_dropdate is None:
        index_data = get_index_data(stk_code='000906.SH', start_date=stk_data.index[0], end_date=stk_data.index[-1])
        index_data = index_data.sort_values(
            'trade_date').set_index('trade_date', drop=False)
        index_data['ratio'] = round(
            (index_data['close'] / index_data['close'].shift(1) - 1) * 100, 3)
        if index_peakdate is not None:
            index_dropdate = index_data.loc[index_peakdate:, 'ratio'].idxmin()
        else:
            index_dropdate = index_data['ratio'].idxmin()
    stk_data['ratio'] = (stk_data['close'] /
                         stk_data['close'].shift(1) - 1) * 100
    MaxDrop_Price = max(stk_data.loc[index_dropdate:, 'open'].iloc[0],
                        stk_data.loc[index_dropdate:, 'close'].iloc[0]) \
                    if len(stk_data.loc[index_dropdate:]) > 0 else None
    postmaxdrop_stkdata = stk_data.loc[index_dropdate:].iloc[1:].copy() \
        if len(stk_data.loc[index_dropdate:].iloc[1:]) > 0 else None

    IndexMaxDrop_RecoverDays, IndexMaxDrop_BreakDays = None, None
    if postmaxdrop_stkdata is not None and MaxDrop_Price is not None:
        postmaxdrop_stkdata['Max_CO'] = postmaxdrop_stkdata.apply(
            lambda fn: max(fn['close'], fn['open']), axis=1)
        postbottomdate = postmaxdrop_stkdata['close'].idxmin()
        if len(postmaxdrop_stkdata.loc[postbottomdate:].query('Max_CO>@MaxDrop_Price')) == 0:
            postbottomdate = postmaxdrop_stkdata.index[0]
        if len(postmaxdrop_stkdata.loc[postbottomdate:].query('Max_CO>@MaxDrop_Price')) > 0:
            BreakMaxDrop_Date = postmaxdrop_stkdata.loc[
                                postbottomdate:].query('Max_CO>@MaxDrop_Price').index[0]
            IndexMaxDrop_RecoverDays = len(stk_data.loc[index_dropdate:BreakMaxDrop_Date]) - 1
            IndexMaxDrop_BreakDays = len(stk_data.loc[BreakMaxDrop_Date:])
    return IndexMaxDrop_RecoverDays, IndexMaxDrop_BreakDays


def convert_dates(date_input):
    """
    智能地将字符串、列表、元组或 numpy 数组的日期字符串转换为日期对象，支持多种格式
    :param date_input: 字符串、列表、元组或 numpy 数组形式的日期输入
    :return: 日期对象、列表或 numpy 数组，无法解析的日期转换为 None
    """

    def parse_date(date_str):
        try:
            # 尝试解析日期字符串
            return parser.parse(date_str)
        except (ValueError, TypeError):
            # 无法解析的日期字符串转换为 None
            print(f"无法解析日期: {date_str}")
            return None

    # 判断输入类型
    if isinstance(date_input, str):
        # 如果是字符串，直接解析为日期对象
        return parse_date(date_input)
    elif isinstance(date_input, (list, tuple)):
        # 如果是列表或元组，遍历解析每个元素
        return [parse_date(date_str) for date_str in date_input]
    elif isinstance(date_input, np.ndarray):
        # 如果是 numpy 数组，使用 np.vectorize 进行向量化处理
        parse_date_vectorized = np.vectorize(parse_date)
        return parse_date_vectorized(date_input)
    else:
        raise TypeError("输入类型必须是字符串、列表、元组或 numpy 数组")


def get_totalmv(end_date=None, stk_code=None, industry=None, limit_num=20):
    """获取指定日期品种的市值数据"""
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    stock_data = pd.DataFrame()
    if stk_code is not None:
        if isinstance(stk_code, str):
            stk_code = [stk_code]
        ts_codes = ','.join(["'%s'" % item for item in stk_code])
        sql = f"""select 
                            ts_code, trade_date, close, open, high, low, pre_close, vol, turnover_rate as turnover, adj_factor,
                            total_share, total_mv
                         from stocksfit.stock_data
                         where trade_date = '{end_date}' and ts_code in ({ts_codes})
                      """
    else:
        sql = f"""select 
                    ts_code, trade_date, close, open, high, low, pre_close, vol, turnover_rate as turnover, adj_factor,
                    total_share, total_mv
                 from stocksfit.stock_data
                 where trade_date = '{end_date}'
              """
    for _ in range(3):
        try:
            stock_data = pd.read_sql_query(sql=sql, con=engine)
        except:
            time.sleep(3)
        else:
            break
    if len(stock_data) == 0:
        print('无股票数据')
        return
    stock_info = get_stock_info()
    stock_data = pd.merge(stock_data, stock_info[['ts_code', 'name', 'industry']], on='ts_code', how='left')
    name = stock_data['name']
    indus = stock_data['industry']
    stock_data = stock_data.drop(columns=['name', 'industry'])
    stock_data.insert(loc=1, column='name', value=name)
    stock_data.insert(loc=2, column='industry', value=indus)
    if industry is not None:
        if isinstance(industry, str):
            industry = [industry]
        stock_data = stock_data[stock_data['industry'].isin(industry)]
    stock_data = stock_data.sort_values(by=['industry', 'total_mv'], ascending=False)
    if limit_num > 0:
        stock_data = stock_data.groupby('industry').head(limit_num)
    return stock_data


def get_real_quote(stk_list=None, source='sina'):
    """获取实时行情数据和按行业统计结果"""
    if isinstance(stk_list, list):
        stk_list = ','.join(stk_list)
    elif isinstance(stk_list, np.ndarray):
        stk_list = ','.join(stk_list.tolist())
    realtime_quote = ts.realtime_quote(ts_code=stk_list, src=source)
    realtime_quote['Ratio'] = round((realtime_quote['PRICE'] / realtime_quote['PRE_CLOSE'] - 1) * 100, 3)
    realtime_quote = realtime_quote.rename(columns={'TS_CODE': 'ts_code'})
    stk_info = get_stock_info()
    result = pd.merge(stk_info[['ts_code', 'name', 'industry']], realtime_quote[['ts_code', 'Ratio']],
                      on='ts_code', how='inner')
    result = result.sort_values(by=['industry', 'Ratio'], ascending=[True, False])
    result_grouped = result[['industry', 'Ratio']].groupby('industry')['Ratio'].mean()
    # result_grouped['Ratio'] = round(result_grouped['Ratio'], 3)
    result_count = result[['industry', 'ts_code']].groupby('industry')['ts_code'].count()
    result_grouped = pd.concat([result_grouped, result_count], axis=1)
    result_grouped = result_grouped.rename(columns={'ts_code': 'Count'})
    result_grouped['Ratio'] = round(result_grouped['Ratio'], 3)
    result_grouped = result_grouped.sort_values(by='Ratio', ascending=False)
    # result_grouped = result_grouped.sort_values(by='Ratio', ascending=False)
    return result_grouped, result

if __name__ == '__main__':
    section_rise, section_drop, day_list = section_stat_v2(stk_code='300718.SZ', start_date='2023-10-23', end_date='2024-12-30')