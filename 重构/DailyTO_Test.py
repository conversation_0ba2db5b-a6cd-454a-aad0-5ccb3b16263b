import pandas as pd
import datetime
import time
import random
import matplotlib.pyplot as plt
import tushare as ts
import os
import sys

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from function_ai.Func_Base import get_stock_info, get_trade_date, get_stock_data
ts.set_token('6878fba4d2c23849a422fbd99b3942c37fecc0f06cb6ec22ca7877ae')

def cal_turnover_efficiency(daily_data, total_share=None, period_minutes=30, style='Stock'):
    """计算换手效率指标"""
    # 计算每个周期的价格变动效率
    daily_data['trade_date'] = pd.to_datetime(daily_data['trade_date'])
    daily_data = daily_data.sort_values(['trade_date','trade_time'])
    
    # 按日期和时间周期分组
    daily_data['period'] = daily_data.groupby('trade_date').cumcount() // period_minutes
    period_data = daily_data.groupby(['trade_date','period']).agg({
        'close':['first','last'],
        'vol':'sum',
        'amount':'sum'
    }).reset_index()
    
    # 计算价格变动效率
    period_data['price_change'] = abs(period_data['close']['last'] - period_data['close']['first'])/period_data['close']['first']
    
    # 根据style计算turnover
    if style.lower() == 'stock':
        period_data['turnover'] = period_data['amount']['sum'] / (total_share * period_data['close']['first'])
    else:
        period_data['turnover'] = period_data['amount']['sum']/100000000
        
    period_data['efficiency'] = round(period_data['price_change'] * 10000/ period_data['turnover'],3)
    period_data = period_data.reset_index()
    
    # 计算每日效率指标
    daily_efficiency = period_data.groupby('trade_date')['efficiency'].agg([
        ('mean','mean'),
        ('std','std')
    ]).reset_index()
    
    daily_efficiency.columns = ['trade_date','avg_efficiency','std_efficiency']
    
    # 计算日内最大连续上涨/下跌区间
    daily_data['price_change'] = daily_data['close'].pct_change()
    
    # 根据style计算turnover
    if style.lower() == 'stock':
        daily_data['turnover'] = daily_data['amount'] / (total_share * daily_data['close'])
    else:
        daily_data['turnover'] = daily_data['amount'] / (
            daily_data.groupby('trade_date')['amount'].transform('sum')/100000000)
    
    up_down_stats = []
    for date, group in daily_data.groupby('trade_date'):
        # 初始化变量
        max_up_return = 0
        max_up_turnover = 0
        max_down_return = 0 
        max_down_turnover = 0
        
        # 计算连续上涨/下跌
        curr_up_return = 0
        curr_up_turnover = 0
        curr_down_return = 0
        curr_down_turnover = 0
        
        for i in range(1, len(group)):
            change = group.iloc[i]['price_change']
            turnover = group.iloc[i]['turnover']
            
            if change > 0:
                curr_up_return += change
                curr_up_turnover += turnover
                curr_down_return = 0
                curr_down_turnover = 0
            elif change < 0:
                curr_down_return += abs(change)
                curr_down_turnover += turnover
                curr_up_return = 0
                curr_up_turnover = 0
                
            max_up_return = max(max_up_return, curr_up_return)
            max_up_turnover = max(max_up_turnover, curr_up_turnover)
            max_down_return = max(max_down_return, curr_down_return)
            max_down_turnover = max(max_down_turnover, curr_down_turnover)
        
        up_efficiency = round(max_up_return * 1000 / max_up_turnover, 3) if max_up_turnover > 0 else 0
        down_efficiency = round(max_down_return * 1000 / max_down_turnover, 3) if max_down_turnover > 0 else 0
        
        up_down_stats.append({
            'trade_date': date,
            'up_efficiency': round(up_efficiency, 4),
            'down_efficiency': round(down_efficiency, 4)
        })
    
    up_down_df = pd.DataFrame(up_down_stats)
    daily_efficiency = pd.merge(daily_efficiency, up_down_df, on='trade_date')
    
    # 计算效率统计指标
    daily_efficiency['efficiency_ma5'] = round(daily_efficiency['avg_efficiency'].rolling(5).mean(), 4)
    daily_efficiency['up_efficiency_ma5'] = round(daily_efficiency['up_efficiency'].rolling(5).mean(), 4)
    daily_efficiency['down_efficiency_ma5'] = round(daily_efficiency['down_efficiency'].rolling(5).mean(), 4)
    daily_efficiency['up2down_efficiency_ma5'] = daily_efficiency['up_efficiency'] - daily_efficiency['down_efficiency']
    
    return daily_efficiency

def get_min_indicators(stk_code=None, start_date=None, end_date=None, trade_df=None, 
                      source='api', style='Stock', period_minutes=30,
                      calc_turnover=True, calc_gap=True,
                      draw_turnover=False, draw_gap=False):
    """获取股票分钟数据并计算相关指标"""
    if start_date is None:
        start_date = end_date
    if trade_df is None:
        trade_df = get_trade_date()
    start_date = trade_df[trade_df<start_date][-3]
    
    if stk_code is None:
        stk_info = get_stock_info()
        stk_codes = stk_info['ts_code'].tolist()
    elif isinstance(stk_code, str):
        stk_codes = [stk_code]
    else:
        stk_codes = stk_code
        
    # 获取股本数据
    stock_data = get_stock_data(stk_code=stk_codes, start_date=start_date, end_date=end_date) if calc_turnover and style.lower() == 'stock' else None
    if calc_turnover and style.lower() == 'stock' and len(stock_data) == 0:
        print('获取股本数据失败')
        return None, None
    total_shares = stock_data.groupby('ts_code').last()['total_share'] if calc_turnover and style.lower() == 'stock' else None
    
    result_data = pd.DataFrame()
    
    if source.lower() == 'api':
        pro_api = ts.pro_api()
        start_time = datetime.datetime.combine(
            pd.to_datetime(start_date), datetime.time(9, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')
        end_time = datetime.datetime.combine(
            pd.to_datetime(end_date), datetime.time(17, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')
            
        data_length = len(trade_df[(trade_df >= start_date) & (trade_df <= end_date)]) * 241
        limit = 7000
        max_offset = 99000
        
        for ts_code in stk_codes:
            if calc_turnover and style.lower() == 'stock' and ts_code not in total_shares.index:
                print(f'{ts_code}无股本数据,跳过')
                continue
                
            daily_data = pd.DataFrame()
            offset = 0

            if data_length > max_offset:
                date_list = trade_df[(trade_df >= start_date) & (trade_df <= end_date)]
                segment_days = int(max_offset / 241)
                date_segments = [date_list[i:i+segment_days] for i in range(0, len(date_list), segment_days)]
                
                for dates in date_segments:
                    seg_start = datetime.datetime.combine(
                        pd.to_datetime(dates[0]), datetime.time(9, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')
                    seg_end = datetime.datetime.combine(
                        pd.to_datetime(dates[-1]), datetime.time(17, 0, 0)).strftime('%Y-%m-%d %H:%M:%S')
                    
                    while True:
                        min_data = pd.DataFrame()
                        for _ in range(3):
                            try:
                                min_data = ts.pro_bar(**{"ts_code": ts_code,
                                                     "freq": "1min", 
                                                     "start_date": seg_start,
                                                     "end_date": seg_end,
                                                     "limit": limit,
                                                     "offset": offset})
                                time.sleep(random.uniform(1, 3))
                            except Exception as e:
                                print(f"Error occurred: {e}")
                                time.sleep(2)
                            else:
                                break

                        if len(min_data) > 0:
                            daily_data = pd.concat([daily_data, min_data], ignore_index=True)
                            offset += limit
                            if offset >= max_offset:
                                offset = 0
                                break
                        else:
                            offset = 0
                            break
            else:
                while True:
                    min_data = pd.DataFrame()
                    for _ in range(3):
                        try:
                            min_data = ts.pro_bar(**{"ts_code": ts_code,
                                                 "freq": "1min",
                                                 "start_date": start_time,
                                                 "end_date": end_time,
                                                 "limit": limit,
                                                 "offset": offset})
                            time.sleep(random.uniform(1, 3))
                        except Exception as e:
                            print(f"Error occurred: {e}")
                            time.sleep(2)
                        else:
                            break
                            
                    if len(min_data) > 0:
                        daily_data = pd.concat([daily_data, min_data], ignore_index=True)
                        offset += limit
                    else:
                        break
                        
            if len(daily_data) > 0:
                # 计算换手效率
                if calc_turnover:
                    turnover_df = cal_turnover_efficiency(daily_data, total_shares[ts_code] if style.lower() == 'stock' else None, period_minutes, style=style)
                    turnover_df['ts_code'] = ts_code
                    # 确保trade_date列为字符串格式
                    turnover_df['trade_date'] = turnover_df['trade_date'].astype(str)
                        
                # 计算gap指标
                if calc_gap:
                    if style.lower() == 'stock':
                        gap_df = cal_stk_gapvalue(daily_data, ts_code)
                    elif style.lower() == 'index':
                        gap_df = cal_index_gapvalue(daily_data, ts_code)
                    # 确保trade_date列为字符串格式
                    gap_df['trade_date'] = gap_df['trade_date'].astype(str)
                    gap_df['ts_code'] = ts_code
                    gap_df['vgv_rollavg'] = round(gap_df['valley_gap'].rolling(window=3).mean(), 4)
                    gap_df['pgv_rollavg'] = round(gap_df['peak_gap'].rolling(window=3).mean(), 4)
                
                # 合并结果
                if calc_turnover and calc_gap:
                    result_df = pd.merge(turnover_df, gap_df, on=['trade_date', 'ts_code'], how='outer')
                elif calc_turnover:
                    result_df = turnover_df
                else:
                    result_df = gap_df
                    
                result_data = pd.concat([result_data, result_df], ignore_index=True)
                
            elif len(daily_data) == 0:
                print('api获取数据为空')
                return None, None
                
    elif source.lower() == 'sql':
        from sqlalchemy import create_engine
        import config.config_local as config_local
        conf_a = config_local.configModel()
        engine_local = create_engine(
            'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
                conf_a.DC_DB_PORT) + '/stocksfit')
        
        chunk_size = 10000
        
        for ts_code in stk_codes:
            if calc_turnover and style.lower() == 'stock' and ts_code not in total_shares.index:
                print(f'{ts_code}无股本数据,跳过')
                continue
                
            daily_data = pd.DataFrame()
            offset = 0
            
            while True:
                sql = f"""select * from stocksfit.stock_freqdata 
                         where trade_date between '{start_date}' and '{end_date}'
                         and ts_code = '{ts_code}'
                         limit {chunk_size} offset {offset}"""
                         
                for _ in range(3):
                    try:
                        chunk_data = pd.read_sql_query(sql=sql, con=engine_local)
                    except Exception as e:
                        print(f"Error occurred: {e}")
                        time.sleep(2)
                    else:
                        break
                
                if len(chunk_data) > 0:
                    daily_data = pd.concat([daily_data, chunk_data], ignore_index=True)
                    offset += chunk_size
                else:
                    break
                    
            if len(daily_data) > 0:
                # 计算换手效率
                if calc_turnover:
                    turnover_df = cal_turnover_efficiency(daily_data, total_shares[ts_code] if style.lower() == 'stock' else None, period_minutes, style=style)
                    turnover_df['ts_code'] = ts_code
                    # 确保trade_date列为字符串格式
                    turnover_df['trade_date'] = turnover_df['trade_date'].astype(str)
                        
                # 计算gap指标
                if calc_gap:
                    if style.lower() == 'stock':
                        gap_df = cal_stk_gapvalue(daily_data, ts_code)
                    elif style.lower() == 'index':
                        gap_df = cal_index_gapvalue(daily_data, ts_code)
                    # 确保trade_date列为字符串格式
                    gap_df['trade_date'] = gap_df['trade_date'].astype(str)
                    gap_df['ts_code'] = ts_code
                    gap_df['vgv_rollavg'] = round(gap_df['valley_gap'].rolling(window=3).mean(), 4)
                    gap_df['pgv_rollavg'] = round(gap_df['peak_gap'].rolling(window=3).mean(), 4)
    
                # 合并结果
                if calc_turnover and calc_gap:
                    result_df = pd.merge(turnover_df, gap_df, on=['trade_date', 'ts_code'], how='outer')
                elif calc_turnover:
                    result_df = turnover_df
                else:
                    result_df = gap_df
                    
                result_data = pd.concat([result_data, result_df], ignore_index=True)
                
            elif len(daily_data) == 0:
                print('sql获取数据为空')
                return None, None
                
        engine_local.dispose()
    else:
        print('wrong source!')
        return None, None
        
    result_data = result_data.sort_values(by='trade_date', ascending=True)
    
    # 绘图
    if draw_turnover and calc_turnover and stk_code is not None:
        plt.figure(figsize=(12,6))
        plt.plot(result_data['trade_date'], result_data['efficiency_ma5'], 'b-', label='MA5')
        plt.plot(result_data['trade_date'], result_data['up_efficiency_ma5'], 'r-', label='Up_MA5') 
        plt.plot(result_data['trade_date'], result_data['up2down_efficiency_ma5'], 'g-', label='Up2Down_MA5')
        plt.title(f'{stk_code} Turnover Efficiency Trend')
        plt.xlabel('Date')
        plt.ylabel('Efficiency')
        plt.legend()
        plt.gca().xaxis.set_major_locator(plt.MaxNLocator(20))
        plt.xticks(rotation=30)
        plt.tight_layout()
        plt.show()
        
    if draw_gap and calc_gap and stk_code is not None:
        result_data_sorted = result_data.sort_values(by='trade_date', ascending=True
                                        ).set_index('trade_date')
        fig, ax1 = plt.subplots(figsize=(10, 6))
        ax2 = ax1.twinx()

        line1 = ax1.plot(range(0, len(result_data_sorted)), result_data_sorted['pgv_rollavg'].values, 
                        'm-', label='pgv_rollavg')
        ax1.set_ylabel('Rolling Average (pgv)', fontsize=12, color='m')
        ax1.tick_params(axis='y', labelcolor='m')

        line2 = ax2.plot(range(0, len(result_data_sorted)), result_data_sorted['valley_gap'].values, 
                        'b-', label='valley_gap')
        ax2.set_ylabel('Valley Gap', fontsize=12, color='b')
        ax2.tick_params(axis='y', labelcolor='b')

        plt.title(f'{stk_code} - Rolling Average', fontsize=16)
        ax1.set_xlabel('Trade Date', fontsize=12)

        xticks = range(0, len(result_data_sorted), int(len(result_data_sorted) / 20))
        ax1.set_xticks(xticks)
        ax1.set_xticklabels(result_data_sorted.index[xticks].astype(str), rotation=45)

        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper right')

        plt.tight_layout()
        plt.show()
        
    return result_data


def cal_stk_gapvalue(daily_data, ts_code):
    daily_data = daily_data.sort_values(
        by='trade_time', ascending=True)
    daily_data['vol_cumsum'] = daily_data[
        ['ts_code', 'trade_date', 'vol']].groupby(['ts_code', 'trade_date'])['vol'].cumsum()
    daily_data['amount_cumsum'] = daily_data[
        ['ts_code', 'trade_date', 'amount']].groupby(['ts_code', 'trade_date'])['amount'].cumsum()
    # daily_data['weight_avg'] = daily_data['amount_cumsum'] / \
    #                            daily_data['vol_cumsum']
    # daily_data['weight_avg'] = daily_data.apply(
    #     lambda fn: fn['amount_cumsum']*100/fn['vol_cumsum']
    #     if fn['vol_cumsum'] % 100 == 0 and abs(fn['amount_cumsum']/fn['vol_cumsum']/fn['close'] - 1) > 0.5
    #     else fn['amount_cumsum']/fn['vol_cumsum'], axis=1)
    daily_data['weight_avg'] = daily_data.apply(cal_avg_nozero, axis=1)
    daily_data['valley_gap'] = round(daily_data['weight_avg'] - daily_data['low'], 3)
    daily_data['peak_gap'] = round(daily_data['high'] - daily_data['weight_avg'], 3)
    daily_data['peakgap_ratio'] = round(daily_data['peak_gap'] * 100 / daily_data['weight_avg'], 4)
    daily_data['valleygap_ratio'] = round(daily_data['valley_gap'] * 100 / daily_data['weight_avg'], 4)
    peak_df = daily_data[['trade_date', 'peak_gap']].groupby(
        ['trade_date'], as_index=False)['peak_gap'].max()
    valley_df = daily_data[['trade_date', 'valley_gap']].groupby(
        ['trade_date'], as_index=False)['valley_gap'].max()
    peakratio_df = daily_data[['trade_date', 'peakgap_ratio']].groupby(
        ['trade_date'], as_index=False)['peakgap_ratio'].max()
    valleyratio_df = daily_data[['trade_date', 'valleygap_ratio']].groupby(
        ['trade_date'], as_index=False)['valleygap_ratio'].max()
    peak_df['trade_date'] = pd.to_datetime(peak_df['trade_date'])
    peak_df['trade_date'] = peak_df['trade_date'].apply(
        lambda fn: fn.strftime('%Y-%m-%d'))
    temp_df = pd.merge(
        peak_df, valley_df[['valley_gap']], left_index=True, right_index=True, how='left')
    temp_df = pd.merge(temp_df, peakratio_df[['peakgap_ratio']],
                       left_index=True, right_index=True, how='left')
    temp_df = pd.merge(temp_df, valleyratio_df[['valleygap_ratio']],
                       left_index=True, right_index=True, how='left')
    temp_df['ts_code'] = ts_code

    def cal_daily_cor_change(group):
        daily_open = group['open'].iloc[0]
        daily_close = group['close'].iloc[-1]
        change = round((daily_close - daily_open) / daily_open * 100, 3)
        return change
    cor_df = daily_data.groupby('trade_date', as_index=False).apply(cal_daily_cor_change).reset_index()
    cor_df.columns = ['Index', 'trade_date', 'cor_ratio']
    cor_df['trade_date'] = pd.to_datetime(cor_df['trade_date'])
    cor_df['trade_date'] = cor_df['trade_date'].apply(lambda fn: fn.strftime('%Y-%m-%d'))
    daily_close = daily_data[['trade_date', 'close']].groupby('trade_date', as_index=False)['close'].last()
    daily_close['daily_ratio'] = round(daily_close['close'].pct_change() * 100, 3)
    daily_close['trade_date'] = pd.to_datetime(daily_close['trade_date'])
    daily_close['trade_date'] = daily_close['trade_date'].apply(lambda fn: fn.strftime('%Y-%m-%d'))
    temp_df = pd.merge(temp_df, cor_df[['trade_date', 'cor_ratio']], on='trade_date', how='left')
    temp_df = pd.merge(temp_df, daily_close[['trade_date', 'daily_ratio']], on='trade_date', how='left')
    return temp_df

def cal_index_gapvalue(daily_data, ts_code):
    daily_data = daily_data.sort_values(
        by='trade_time', ascending=True)
    daily_data['amount_cumsum'] = daily_data[
        ['trade_date', 'amount']].groupby(['trade_date'])['amount'].cumsum()
    daily_data['amount_weighted_close'] = (daily_data['close'] + daily_data['open']
                                           ) * daily_data['amount'] / 2
    daily_data['weight_avg'] = daily_data[['trade_date', 'amount_weighted_close']
                               ].groupby(['trade_date'])['amount_weighted_close'].cumsum() / \
                               daily_data['amount_cumsum']
    daily_data['valley_gap'] = daily_data.apply(lambda fn: round(fn['weight_avg'] - fn['low'], 4)
    if fn['low'] > 0 else round(fn['weight_avg'] - fn['close'], 4), axis=1)
    daily_data['peak_gap'] = daily_data.apply(lambda fn: round(fn['high'] - fn['weight_avg'], 4)
    if fn['high'] > 0 else round(fn['close'] - fn['weight_avg'], 4), axis=1)
    daily_data['peakgap_ratio'] = round(daily_data['peak_gap'] * 100 / daily_data['weight_avg'], 4)
    daily_data['valleygap_ratio'] = round(daily_data['valley_gap'] * 100 / daily_data['weight_avg'], 4)
    peak_df = daily_data[['trade_date', 'peak_gap']].groupby(
        ['trade_date'], as_index=False)['peak_gap'].max()
    valley_df = daily_data[['trade_date', 'valley_gap']].groupby(
        ['trade_date'], as_index=False)['valley_gap'].max()
    peakratio_df = daily_data[['trade_date', 'peakgap_ratio']].groupby(
        ['trade_date'], as_index=False)['peakgap_ratio'].max()
    valleyratio_df = daily_data[['trade_date', 'valleygap_ratio']].groupby(
        ['trade_date'], as_index=False)['valleygap_ratio'].max()
    peak_df['trade_date'] = pd.to_datetime(peak_df['trade_date'])
    peak_df['trade_date'] = peak_df['trade_date'].apply(
        lambda fn: fn.strftime('%Y-%m-%d'))
    temp_df = pd.merge(
        peak_df, valley_df[['valley_gap']], left_index=True, right_index=True, how='left')
    temp_df = pd.merge(temp_df, peakratio_df[['peakgap_ratio']],
                       left_index=True, right_index=True, how='left')
    temp_df = pd.merge(temp_df, valleyratio_df[['valleygap_ratio']],
                       left_index=True, right_index=True, how='left')
    temp_df['ts_code'] = ts_code
    return temp_df

def cal_avg_nozero(dailydata, mode='History'):
    if mode == 'History':
        """计算weight_avg,排除分母为0情况"""
        if dailydata['vol_cumsum'] == 0:
            return dailydata['close']
        elif abs(dailydata['amount_cumsum'] / dailydata['vol_cumsum'] / dailydata['close'] - 1) > 0.5:
            return round(dailydata['amount_cumsum'] * 100 / dailydata['vol_cumsum'], 3)
        else:
            return round(dailydata['amount_cumsum'] / dailydata['vol_cumsum'], 3)
    else:
        if dailydata['VOLUME'] == 0:
            return dailydata['PRICE']
        elif abs(dailydata['AMOUNT'] / dailydata['VOLUME'] / dailydata['PRICE'] - 1) > 0.5:
            return round(dailydata['AMOUNT'] / dailydata['VOLUME'] / 100, 3)
        else:
            return round(dailydata['AMOUNT'] / dailydata['VOLUME'], 3)

if __name__ == '__main__':
    minit_data = get_min_indicators(stk_code='605158.SH', start_date='2024-03-27', end_date='2024-12-18', calc_turnover=True, calc_gap=True, draw_turnover=True)