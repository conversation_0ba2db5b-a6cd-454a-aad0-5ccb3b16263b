import os
import sys
import numpy as np
import pandas as pd
import scipy.stats as stats

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)
    
from function_ai.Func_Base import get_stock_data, get_index_data, get_stock_info
from function_ai.Trend_Func import count_und_num, peak_valley_turn, coef_regress, trend_judge


def calculate_section_metrics(price_data, section_dates):
    """计算区间内的各项指标
    
    Args:
        price_data (DataFrame): 价格数据
        section_dates (dict): 包含start_date和end_date的区间信息
        
    Returns:
        dict: 包含该区间的各项指标
    """
    start_date = section_dates['start_date']
    end_date = section_dates['end_date']
    section_data = price_data.loc[start_date:end_date].copy()
    
    # 确保至少有2个交易日的数据
    if len(section_data) < 2:
        return None
        
    metrics = {
        'start_date': start_date,
        'end_date': end_date,
        'lastdays': len(section_data),
        'sumratio': round((section_data['close'].iloc[-1] / section_data['close'].iloc[0] - 1) * 100, 2),
        'cls_diff': round(abs(section_data['close'].max() - section_data['close'].min()), 2),
        'extre_ratio': round(section_data['day_ratio'].min(), 2),
        'avg_turnover': round(section_data['turnover'].mean(), 2),
        'max_turnover': round(section_data['turnover'].max(), 2),
        'clsopen_ratio': round(abs(section_data['close'] / section_data['open'] - 1).mean() * 100, 2)
    }
    
    # 计算平均涨幅
    metrics['avgratio'] = round(metrics['sumratio'] / metrics['lastdays'], 2)
    
    # 计算换手率相关指标
    rise_data = section_data.query('day_ratio > 0')
    drop_data = section_data.query('day_ratio < 0')
    
    metrics['turnover2rise'] = round(
        rise_data['turnover'].sum() / rise_data['day_ratio'].sum() 
        if len(rise_data) > 0 and rise_data['day_ratio'].sum() != 0 else 0, 3)
        
    metrics['turnover2drop'] = round(
        abs(drop_data['turnover'].sum() / drop_data['day_ratio'].sum())
        if len(drop_data) > 0 and drop_data['day_ratio'].sum() != 0 else 0, 3)
    
    # 计算逆势程度
    section_data['stk_rollmax'] = section_data['close'].rolling(window=3, closed='left').max()
    metrics['adverse2trend_sum'] = round(
        len(section_data.query('close > stk_rollmax')) / len(section_data)
        if len(section_data) > 0 else 0, 3)
    
    # 计算波动天数
    section_data['max_ratio'] = abs(section_data['close'] / section_data['close'].shift(1) - 1) * 100
    metrics['und2_contidays'] = int(count_und_num(section_data['max_ratio'], limit=2))
    
    return metrics

def new_section_stat(stk_data=None, stk_code='', start_date=None, end_date=None, index_data=None,
                    index_mode=None):
    """使用波峰波谷法重构的区间统计函数
    
    Returns:
        tuple: (上涨区间DataFrame, 下跌区间DataFrame, 原始价格数据)
    """
    # 获取数据
    if stk_code:
        price_data = get_stock_data(stk_code=stk_code, start_date=start_date, end_date=end_date)
        price_data = price_data.set_index('trade_date')
    else:
        price_data = stk_data.copy()
        if start_date and end_date:
            price_data = price_data.loc[start_date:end_date]
    
    # 计算日涨跌幅
    price_data['day_ratio'] = (price_data['close'] / price_data['close'].shift(1) - 1) * 100
    
    # 获取趋势区间
    trend_sections = find_peaks_valleys(price_data)
    
    # 分别计算上涨和下跌区间的指标
    up_sections = []
    down_sections = []
    
    for _, section in trend_sections.iterrows():
        metrics = calculate_section_metrics(price_data, section)
        if metrics:
            # 获取区间数据并计算稳定性指标
            section_data = price_data.loc[section['start_date']:section['end_date']].copy()
            stability_results = evaluate_section_stability(section_data)
            # 将稳定性指标添加到metrics中
            metrics.update(stability_results)
            
            if section['trend_type'] == 'up':
                up_sections.append(metrics)
            else:
                down_sections.append(metrics)
    
    # 转换为DataFrame
    up_df = pd.DataFrame(up_sections) if up_sections else pd.DataFrame()
    down_df = pd.DataFrame(down_sections) if down_sections else pd.DataFrame()
    
    # 添加指数相关指标
    if index_mode is not None and index_data is not None:
        for df in [up_df, down_df]:
            if not df.empty:
                df['index_sum'] = df.apply(
                    lambda x: (index_data.loc[x['end_date'], 'close'] /
                             index_data.loc[x['start_date'], 'pre_close'] - 1) * 100, axis=1)
                df['index_diff'] = df['sumratio'] - df['index_sum']
    
    return up_df, down_df, price_data


def find_peaks_valleys(df):
    """
    寻找股票价格的波峰和波谷，并构建趋势区段，确保涵盖所有日期区间
    
    参数:
    df: DataFrame, 包含股票价格数据，index为日期，必须包含'close'列
    
    返回:
    DataFrame: 包含波段起止日期和持续天数的DataFrame
    """
    import pandas as pd
    import numpy as np
    
    # 创建结果存储列表
    results = []
    dates = df.index.tolist()
    
    # 找出波峰和波谷
    peaks = []
    valleys = []
    
    for i in range(len(df)):
        # 确定前后查找范围
        start_idx = max(0, i - 5)
        end_idx = min(len(df), i + 6)
        
        window_prices = df['close'].iloc[start_idx:end_idx]
        current_price = df['close'].iloc[i]
        
        # 判断是否为波谷（局部最低点）
        if current_price == window_prices.min() and (i == 0 or current_price != df['close'].iloc[i-1]):
            valleys.append(dates[i])
            
        # 判断是否为波峰（局部最高点）
        if current_price == window_prices.max() and (i == 0 or current_price != df['close'].iloc[i-1]):
            peaks.append(dates[i])
    
    # 将波峰和波谷合并到一个DataFrame中，并标注类型
    peaks_df = pd.DataFrame({'date': peaks, 'type': 'peak'})
    valleys_df = pd.DataFrame({'date': valleys, 'type': 'valley'})
    combined_df = pd.concat([peaks_df, valleys_df]).sort_values(by='date').reset_index(drop=True)
    
    # 确保涵盖所有日期区间
    if not combined_df.empty:
        first_date = df.index.min()
        last_date = df.index.max()
        
        if combined_df.iloc[0]['date'] != first_date:
            first_price = df.loc[first_date, 'close']
            nearest_price = df.loc[combined_df.iloc[0]['date'], 'close']
            first_type = 'valley' if first_price < nearest_price else 'peak'
            combined_df = pd.concat([pd.DataFrame({'date': [first_date], 'type': [first_type]}), combined_df]).reset_index(drop=True)
        
        if combined_df.iloc[-1]['date'] != last_date:
            last_price = df.loc[last_date, 'close']
            nearest_price = df.loc[combined_df.iloc[-1]['date'], 'close']
            last_type = 'peak' if last_price > nearest_price else 'valley'
            combined_df = pd.concat([combined_df, pd.DataFrame({'date': [last_date], 'type': [last_type]})]).reset_index(drop=True)
    
    # 处理相邻的波峰和波谷
    i = 0
    while i < len(combined_df) - 1:
        current_point = combined_df.iloc[i]
        next_point = combined_df.iloc[i + 1]
        
        if current_point['type'] == next_point['type']:
            # 获取两点之间的数据
            mask = (df.index >= current_point['date']) & (df.index <= next_point['date'])
            between_data = df[mask]
            
            if current_point['type'] == 'peak':
                # 在两个波峰之间找波谷
                valley_date = between_data['close'].idxmin()
                new_point = pd.DataFrame({'date': [valley_date], 'type': ['valley']})
            else:
                # 在两个波谷之间找波峰
                peak_date = between_data['close'].idxmax()
                new_point = pd.DataFrame({'date': [peak_date], 'type': ['peak']})
            
            # 插入新点
            combined_df = pd.concat([
                combined_df.iloc[:i+1], 
                new_point, 
                combined_df.iloc[i+1:]
            ]).reset_index(drop=True)
        
        i += 1
    
    # 重新构建趋势区段
    results = []
    for i in range(len(combined_df) - 1):
        current_point = combined_df.iloc[i]
        next_point = combined_df.iloc[i + 1]
        
        if current_point['type'] == 'valley' and next_point['type'] == 'peak':
            start_date = current_point['date']
            end_date = next_point['date']
            days_between = len(df.loc[start_date:end_date])
            price_change = (df.loc[end_date, 'close'] - df.loc[start_date, 'close']) / df.loc[start_date, 'close']
            
            results.append({
                'start_date': start_date,
                'end_date': end_date,
                'days': days_between,
                'price_change': price_change,
                'trend_type': 'up'
            })
        elif current_point['type'] == 'peak' and next_point['type'] == 'valley':
            start_date = current_point['date']
            end_date = next_point['date']
            days_between = len(df.loc[start_date:end_date])
            price_change = (df.loc[end_date, 'close'] - df.loc[start_date, 'close']) / df.loc[start_date, 'close']
            
            results.append({
                'start_date': start_date,
                'end_date': end_date,
                'days': days_between,
                'price_change': price_change,
                'trend_type': 'down'
            })
    
    return pd.DataFrame(results)


def check_trend_consistency(stock_data, threshold=0.6):
    """
    检查趋势的连续性
    
    参数:
        stock_data (DataFrame): 股票数据
        threshold (float): 趋势一致性阈值
        
    返回:
        bool: 是否符合连续趋势特征
    """
    # 1. 计算日间变动
    stock_data['daily_change'] = (stock_data['close'] - stock_data['open']) / stock_data['open']
    
    # 2. 计算与趋势方向一致的天数比例
    if stock_data['close'].iloc[-1] > stock_data['close'].iloc[0]:  # 上升趋势
        trend_consistency = (stock_data['daily_change'] > 0).mean()
    else:  # 下降趋势
        trend_consistency = (stock_data['daily_change'] < 0).mean()
        
    # 3. 计算K线实体占比
    stock_data['body_ratio'] = abs(stock_data['close'] - stock_data['open']) / (stock_data['high'] - stock_data['low'])
    avg_body_ratio = stock_data['body_ratio'].mean()
    
    return trend_consistency >= threshold and avg_body_ratio > 0.3


def check_price_channel(stock_data, min_fit_ratio=0.8):
    """
    检查价格是否在稳定的趋势通道中运行
    """
    if len(stock_data) < 5:
        return False
    
    def fit_trend_line(prices, days, outlier_mask=None):
        """计算趋势线，可选择性排除异常点"""
        if outlier_mask is None:
            return np.polyfit(days, prices, 1)
        return np.polyfit(days[~outlier_mask], prices[~outlier_mask], 1)
    
    def calculate_channel_metrics(slope, intercept, days, prices):
        """计算通道相关指标"""
        trend_line = slope * days + intercept
        deviations = (prices - trend_line) / trend_line
        return trend_line, deviations
    
    # 初始数据准备
    days = np.arange(len(stock_data))
    prices = stock_data['close'].values
    
    # 迭代优化趋势线（最多3次迭代）
    best_slope = None
    best_intercept = None
    best_fit_ratio = 0
    
    for iteration in range(3):
        # 1. 计算当前趋势线
        if iteration == 0:
            slope, intercept = fit_trend_line(prices, days)
        else:
            # 使用非异常点重新拟合趋势线
            slope, intercept = fit_trend_line(prices, days, outlier_mask)
        
        # 2. 计算偏离度
        trend_line, deviations = calculate_channel_metrics(slope, intercept, days, prices)
        
        # 3. 识别异常点（偏离度超过阈值的点）
        threshold = np.std(deviations) * 2  # 使用2倍标准差作为阈值
        outlier_mask = abs(deviations) > threshold
        
        # 4. 计算拟合比例
        fit_ratio = 1 - np.mean(outlier_mask)
        
        # 5. 更新最优结果
        if fit_ratio > best_fit_ratio:
            best_fit_ratio = fit_ratio
            best_slope = slope
            best_intercept = intercept
            
        # 如果已经达到要求，提前结束
        if fit_ratio >= min_fit_ratio:
            break
    
    # 使用最优趋势线计算最终指标
    if best_slope is None or best_fit_ratio < min_fit_ratio:
        return False
        
    final_trend_line = best_slope * days + best_intercept
    stock_data['trend_line'] = final_trend_line
    
    # 计算通道特征
    stock_data['deviation'] = (stock_data['close'] - stock_data['trend_line']) / stock_data['trend_line']
    stock_data['channel_width'] = (stock_data['high'] - stock_data['low']) / stock_data['trend_line']
    
    # 计算趋势强度和稳定性指标
    trend_strength = abs(best_slope) / best_intercept * len(stock_data)
    avg_channel_width = stock_data['channel_width'].mean()
    channel_width_stability = stock_data['channel_width'].std() / avg_channel_width
    
    # 最终判断标准
    is_stable_trend = (
        best_fit_ratio >= min_fit_ratio and     # 足够多的点在通道内
        avg_channel_width <= 0.08 and            # 通道宽度合理
        channel_width_stability <= 0.5 and       # 通道宽度稳定
        trend_strength > 0.08                   # 有足够的趋势强度
    )
    
    # 增加趋势方向判断
    trend_direction = 'up' if best_slope > 0 else 'down'
    
    # 根据趋势方向调整判断标准
    if trend_direction == 'up':
        channel_top = final_trend_line + 2 * np.std(deviations) * final_trend_line
        channel_bottom = final_trend_line - np.std(deviations) * final_trend_line
    else:
        channel_top = final_trend_line + np.std(deviations) * final_trend_line  
        channel_bottom = final_trend_line - 2 * np.std(deviations) * final_trend_line
        
    # 计算突破上下轨的比例
    break_up_ratio = (stock_data['high'] > channel_top).mean()
    break_down_ratio = (stock_data['low'] < channel_bottom).mean()
    
    return is_stable_trend

    
def evaluate_section_stability(stock_data):
    """
    评估股价区段的稳定性
    
    参数:
        stock_data (DataFrame): 区段内的股票数据
        
    返回:
        dict: 包含趋势一致性和通道稳定性的评估结果
    """
    # 获取趋势一致性和通道稳定性
    trend_consistent = check_trend_consistency(stock_data)
    channel_stable = check_price_channel(stock_data)
    
    return {
        'trend_consistency': trend_consistent,
        'channel_stability': channel_stable,
        'section_stability': trend_consistent and channel_stable
    }

def generate_periods(up_df, down_df, price_data, min_days=5):
    """根据上涨和下跌section生成更长周期的period序列，并合并输出
    
    Args:
        up_df (DataFrame): 上涨区间DataFrame，包含start_date和end_date列
        down_df (DataFrame): 下跌区间DataFrame，包含start_date和end_date列
        price_data (DataFrame): 原始价格数据，包含close列
        min_days (int): 最小周期天数，默认为5天
        
    Returns:
        DataFrame: 合并后的period DataFrame，按时间排序
    """
    def merge_sections(df, is_uptrend=True):
        if df.empty:
            return pd.DataFrame()
            
        # 按start_date排序
        df = df.sort_values('start_date').reset_index(drop=True)
        
        periods = []
        current_period = {
            'start_date': df.iloc[0]['start_date'],
            'end_date': df.iloc[0]['end_date']
        }
        
        for i in range(1, len(df)):
            current_section = df.iloc[i]
            
            # 获取价格数据
            curr_start_price = price_data.loc[current_section['start_date'], 'close']
            curr_end_price = price_data.loc[current_section['end_date'], 'close']
            period_start_price = price_data.loc[current_period['start_date'], 'close']
            period_end_price = price_data.loc[current_period['end_date'], 'close']
            
            # 根据趋势方向判断是否可以合并
            can_merge = (curr_start_price > period_start_price and curr_end_price >= period_end_price) if is_uptrend \
                else (curr_start_price < period_start_price and curr_end_price <= period_end_price)
            
            if can_merge:
                # 更新当前period的结束日期
                current_period['end_date'] = current_section['end_date']
            else:
                # 保存当前period并开始新的period
                periods.append(current_period)
                current_period = {
                    'start_date': current_section['start_date'],
                    'end_date': current_section['end_date']
                }
        
        # 添加最后一个period
        periods.append(current_period)
        
        # 转换为DataFrame并计算附加指标
        periods_df = pd.DataFrame(periods)
        if not periods_df.empty:
            periods_df['days'] = periods_df.apply(
                lambda x: len(price_data.loc[x['start_date']:x['end_date']]), axis=1)
            periods_df['price_change'] = periods_df.apply(
                lambda x: (price_data.loc[x['end_date'], 'close'] / 
                          price_data.loc[x['start_date'], 'close'] - 1) * 100, axis=1)
            periods_df['trend'] = 'up' if is_uptrend else 'down'
        
        return periods_df
    
    # 分别处理上涨和下跌区间
    up_periods = merge_sections(up_df, is_uptrend=True)
    down_periods = merge_sections(down_df, is_uptrend=False)
    
    # 再次调用merge_sections，对up_periods和down_periods进行二次整合
    final_up_periods = merge_sections(up_periods, is_uptrend=True)
    final_down_periods = merge_sections(down_periods, is_uptrend=False)
    
    # 合并优化periods
    optimized_periods = []
    
    # 选择开始日期最早的period作为起始period
    if not up_periods.empty and not down_periods.empty:
        if up_periods.iloc[0]['start_date'] <= down_periods.iloc[0]['start_date']:
            current_period = up_periods.iloc[0].to_dict()
            up_periods = up_periods.iloc[1:].reset_index(drop=True)
        else:
            current_period = down_periods.iloc[0].to_dict()
            down_periods = down_periods.iloc[1:].reset_index(drop=True)
    elif not up_periods.empty:
        current_period = up_periods.iloc[0].to_dict()
        up_periods = up_periods.iloc[1:].reset_index(drop=True)
    elif not down_periods.empty:
        current_period = down_periods.iloc[0].to_dict()
        down_periods = down_periods.iloc[1:].reset_index(drop=True)
    else:
        current_period = None
    
    # 交替查询上下行period，确保前一period结束日期为下一period开始日期
    while current_period is not None:
        optimized_periods.append(current_period)
        if current_period['trend'] == 'up':
            next_periods = down_periods
        else:
            next_periods = up_periods
        
        next_period = next_periods[next_periods['start_date'] == current_period['end_date']]
        
        if not next_period.empty:
            current_period = next_period.iloc[0].to_dict()
            if current_period['trend'] == 'up':
                up_periods = up_periods[up_periods['start_date'] != current_period['start_date']].reset_index(drop=True)
            else:
                down_periods = down_periods[down_periods['start_date'] != current_period['start_date']].reset_index(drop=True)
        else:
            current_period = None
    
    combined_periods = pd.DataFrame(optimized_periods)
    
    return combined_periods


def period_stat(stk_code='', mode='ADJ', stk_close=None, stk_open=None, turnover=None, end_date=None, style='Stock'):
    """函数 period_stat 识别股票波峰波谷区间的运行状态，并展示
       输入参数stk_name为股票简称 """
    # from trend_func import peak_valley_turn as pvt
    """ 根据输入股票简称，获取股票代码及股票开盘和收盘数据 """
    if stk_code != '' and style == 'Stock':
        # stk_list = pd.read_csv('dataset/stockList.csv')
        stk_list = get_stock_info()
        stk_code = stk_list[stk_list['ts_code'] == stk_code]['ts_code'].tolist()[
            0]
        attemps = 3
        while len(stk_code) == 0 and attemps > 0:
            stk_code = input('未查询到股票代码，请重新输入：')
            stk_code = stk_list[stk_list['ts_code'].values == stk_code]['ts_code'].tolist()[
                0]
            attemps -= 1
        if attemps == 0:
            raise ValueError('未查询到股票代码，请重新运行程序！')
        stk_data = get_stock_data(stk_code=stk_code, end_date=end_date).set_index(
            'trade_date', drop=False)
        stk_close = stk_data['close']
        stk_open = stk_data['open']
        turnover = stk_data['turnover']
        """ 构建波峰波谷序列 """
    elif stk_code != '' and style == 'Index':
        index_data = get_index_data(stk_code=stk_code)
        if len(index_data) > 0:
            index_data = index_data.set_index('trade_date', drop=False)
            stk_close = index_data['close']
            stk_open = index_data['open']
        else:
            print('未查询到指数行情数据！')
            return
    if end_date is not None:
        stk_close = stk_close[:end_date]
        stk_open = stk_open[:end_date]
    stk_ratio = (stk_close / stk_close.shift(1) - 1) * 100
    tradedate = stk_close.index
    crthr_turn = peak_valley_turn(stk_close, tradedate.values, mode=style)
    if len(crthr_turn) < 2:
        return [], []
    crthr_turn = crthr_turn.set_index(['date'])
    tradedate = stk_close.index
    crthrperiod = crthr_turn[['valley', 'peak']].max(axis=1)
    periodchang = pd.DataFrame({'start_date': crthrperiod.index[:-1], 'end_date': crthrperiod.index[1:]},
                               index=range(0, len(crthrperiod) - 1))
    periodchang['sum_ratio'] = (
            100 * crthrperiod.diff()[1:] / crthrperiod.shift(1)[1:]).values

    avg_ratios = []
    last_days = []
    close_open_ratio = []
    std_bias = []
    r_squared = []
    slope = []
    intercept = []
    si_pro = []
    avg_turnover = []
    max_turnover = []
    rise_avgturnover = []
    drop_avgturnover = []
    for pnum in periodchang.index:
        pnum_startdate = periodchang.loc[pnum, 'start_date']
        pnum_enddate = periodchang.loc[pnum, 'end_date']
        last_day = len(tradedate[(tradedate >= pnum_startdate) &
                                 (tradedate <= pnum_enddate)]) - 1
        avg_ratios.append(periodchang.loc[pnum, 'sum_ratio'] / last_day)
        if turnover is not None:
            avg_a = turnover.loc[pnum_startdate:pnum_enddate].mean()
            max_a = turnover.loc[pnum_startdate:pnum_enddate].max()
            avg_turnover.append(round(avg_a, 2))
            max_turnover.append(round(max_a, 2))
            stk_ratio_temp = stk_ratio.loc[pnum_startdate:pnum_enddate]
            rise_index = stk_ratio_temp[stk_ratio_temp > 0].index
            drop_index = stk_ratio_temp[stk_ratio_temp < 0].index
            rise_avgturnover.append(round(turnover.loc[rise_index].sum() / stk_ratio_temp.loc[rise_index].sum()
                                          if stk_ratio_temp.loc[rise_index].sum() != 0 else 0, 3))
            drop_avgturnover.append(round(abs(turnover.loc[drop_index].sum() / stk_ratio_temp.loc[drop_index].sum())
                                          if stk_ratio_temp.loc[drop_index].sum() != 0 else 0, 3))
        last_days.append(last_day)
        period_close = stk_close[pnum_startdate: pnum_enddate]
        period_open = stk_open[pnum_startdate: pnum_enddate]
        close_open_ratio.append(
            np.mean(abs(period_close / period_open - 1)) * 100)

        # 调用子函数完成线性拟合测试
        coef_data = coef_regress(period_close, mode='err')
        r_squared.append(coef_data[0])
        slope.append(coef_data[1])
        intercept.append(coef_data[2])
        si_pro.append(coef_data[3])
        std_bias.append(coef_data[4])
    periodchang['avg_ratio'] = avg_ratios
    periodchang['last_days'] = last_days
    periodchang['close_open_ratio'] = close_open_ratio
    periodchang['std_bias'] = abs(periodchang['sum_ratio'] / std_bias)
    periodchang['r_squared'] = r_squared
    periodchang['slope'] = slope
    periodchang['intercept'] = intercept
    periodchang['si_pro'] = si_pro
    if turnover is not None:
        periodchang['avg_turnover'] = avg_turnover
        periodchang['max_turnover'] = max_turnover
        periodchang['turnover2rise'] = rise_avgturnover
        periodchang['turnover2drop'] = drop_avgturnover
    if mode != 'ADJ':
        trend_stat = trend_judge(stk_close, crthr_turn)
        return periodchang, trend_stat
    else:
        periodchang_ADJ = periodchang[:1].copy()
        pnum = 1
        while pnum <= len(periodchang) - 1:
            # pnum_startdate = periodchang.at[pnum, 'start_date']
            pnum_enddate = periodchang.at[pnum, 'end_date']
            pnum_sum_ratio = periodchang.at[pnum, 'sum_ratio']
            pnum_last_days = periodchang.at[pnum, 'last_days']
            pnum_minus_one_end_date = periodchang.at[pnum - 1, 'end_date']
            last_index = periodchang_ADJ.index[-1]
            if (pnum < len(periodchang) - 1
                and pnum_sum_ratio > 0
                and (pnum_last_days <= 3
                     or 10 > pnum_sum_ratio)
                and stk_close[periodchang.at[pnum + 1, 'end_date']] <=
                stk_close[pnum_minus_one_end_date]) \
                    or (pnum < len(periodchang) - 1
                        and pnum_sum_ratio < 0
                        and (pnum_last_days < 5
                             or -10 < pnum_sum_ratio)
                        and stk_close[periodchang.at[pnum + 1, 'end_date']] * 1.03 >
                        stk_close[pnum_minus_one_end_date]):
                pnum_plus_one_end_date = periodchang.at[pnum + 1, 'end_date']
                end_date = pnum_plus_one_end_date
                periodchang_ADJ.at[last_index,
                'end_date'] = end_date
                periodchang_ADJ.at[last_index, 'last_days'] = \
                    periodchang_ADJ.at[last_index, 'last_days'] + \
                    periodchang['last_days'].iloc[pnum + 1] + \
                    periodchang['last_days'].iloc[pnum]
                periodchang_ADJ.at[last_index, 'sum_ratio'] = \
                    (stk_close[pnum_plus_one_end_date] /
                     stk_close[periodchang_ADJ.at[last_index, 'start_date']] - 1) * 100
                periodchang_ADJ.at[last_index, 'avg_ratio'] = \
                    periodchang_ADJ.at[last_index, 'sum_ratio'] / \
                    periodchang_ADJ.at[last_index, 'last_days']
                if turnover is not None:
                    periodchang_ADJ.at[last_index, 'avg_turnover'] = \
                        round(turnover.loc[periodchang_ADJ['start_date'].iloc[-1]:periodchang_ADJ['end_date'].iloc[-1]
                              ].mean(), 2)
                    periodchang_ADJ.at[last_index, 'max_turnover'] = \
                        round(turnover.loc[periodchang_ADJ['start_date'].iloc[-1]:periodchang_ADJ['end_date'].iloc[-1]
                              ].max(), 2)
                    stk_ratio_temp = stk_ratio.loc[
                                     periodchang_ADJ['start_date'].iloc[-1]:periodchang_ADJ['end_date'].iloc[-1]]
                    rise_index = stk_ratio_temp[stk_ratio_temp > 0].index
                    drop_index = stk_ratio_temp[stk_ratio_temp < 0].index
                    periodchang_ADJ.loc[last_index, 'turnover2rise'] = round(
                        turnover.loc[rise_index].sum() / stk_ratio_temp.loc[rise_index].sum(), 3)
                    periodchang_ADJ.loc[last_index, 'turnover2drop'] = round(
                        abs(turnover.loc[drop_index].sum() / stk_ratio_temp.loc[drop_index].sum()), 3)
                period_close = stk_close[periodchang_ADJ.at[last_index, 'start_date']:
                                         pnum_plus_one_end_date]
                period_open = stk_open[periodchang_ADJ.at[last_index, 'start_date']:
                                       pnum_plus_one_end_date]
                periodchang_ADJ.at[last_index, 'close_open_ratio'] = np.mean(
                    abs(period_close / period_open - 1)) * 100

                coef_data = coef_regress(period_close, mode='err')
                periodchang_ADJ.at[last_index,
                'r_squared'] = coef_data[0]
                periodchang_ADJ.at[last_index,
                'slope'] = coef_data[1]
                periodchang_ADJ.at[last_index,
                'intercept'] = coef_data[2]
                periodchang_ADJ.at[last_index,
                'si_pro'] = coef_data[3]
                periodchang_ADJ.at[last_index, 'std_bias'] = abs(
                    periodchang_ADJ.at[last_index, 'sum_ratio'].copy() / coef_data[4]) \
                    if coef_data[4] != 0 else 0
                pnum += 2
            elif pnum == len(periodchang) - 1 \
                    and pnum_last_days < 5 \
                    and ((periodchang.at[pnum, 'avg_ratio'] >= 0
                          and stk_close[pnum_enddate] <=
                          (stk_close[pnum_minus_one_end_date] +
                           stk_close[periodchang.at[pnum - 1, 'start_date']]) / 2)
                         or (periodchang.at[pnum, 'avg_ratio'] < 0
                             and stk_close[pnum_enddate] >
                             (stk_close[pnum_minus_one_end_date] +
                              stk_close[periodchang.at[pnum - 1, 'start_date']]) / 2)):
                end_date = pnum_enddate
                periodchang_ADJ.at[last_index, 'end_date'] = end_date
                periodchang_ADJ.at[last_index, 'last_days'] = \
                    periodchang_ADJ.at[last_index,
                    'last_days'] + pnum_last_days
                periodchang_ADJ.at[last_index, 'sum_ratio'] = \
                    (stk_close[pnum_enddate] /
                     stk_close[periodchang_ADJ.at[last_index, 'start_date']] - 1) * 100
                periodchang_ADJ.at[last_index, 'avg_ratio'] = \
                    periodchang_ADJ.at[last_index, 'sum_ratio'] / \
                    periodchang_ADJ.at[last_index, 'last_days']
                period_close = stk_close[periodchang_ADJ.at[last_index, 'start_date']:
                                         pnum_enddate]
                period_open = stk_open[periodchang_ADJ.at[last_index, 'start_date']:
                                       pnum_enddate]
                periodchang_ADJ.at[last_index, 'close_open_ratio'] = np.mean(
                    abs(period_close / period_open - 1)) * 100
                if turnover is not None:
                    periodchang_ADJ.at[last_index, 'avg_turnover'] = \
                        round(turnover.loc[periodchang_ADJ['start_date'].iloc[-1]:pnum_enddate
                              ].mean(), 2)
                    periodchang_ADJ.at[last_index, 'max_turnover'] = \
                        round(turnover.loc[periodchang_ADJ['start_date'].iloc[-1]:pnum_enddate
                              ].max(), 2)
                    stk_ratio_temp = stk_ratio.loc[
                                     periodchang_ADJ['start_date'].iloc[-1]:pnum_enddate]
                    rise_index = stk_ratio_temp[stk_ratio_temp > 0].index
                    drop_index = stk_ratio_temp[stk_ratio_temp < 0].index
                    periodchang_ADJ.loc[last_index, 'turnover2rise'] = round(
                        turnover.loc[rise_index].sum() / stk_ratio_temp.loc[rise_index].sum(), 3)
                    periodchang_ADJ.loc[last_index, 'turnover2drop'] = round(
                        abs(turnover.loc[drop_index].sum() / stk_ratio_temp.loc[drop_index].sum()), 3)

                coef_data = coef_regress(period_close, mode='err')
                periodchang_ADJ.at[last_index, 'r_squared'] = coef_data[0]
                periodchang_ADJ.at[last_index, 'slope'] = coef_data[1]
                periodchang_ADJ.at[last_index, 'intercept'] = coef_data[2]
                periodchang_ADJ.at[last_index, 'si_pro'] = coef_data[3]
                periodchang_ADJ.at[last_index, 'std_bias'] = abs(
                    periodchang_ADJ.at[last_index, 'sum_ratio'].copy() / coef_data[4]) \
                    if coef_data[4] != 0 else 0
                pnum += 1
            elif periodchang_ADJ.at[last_index, 'end_date'] != pnum_enddate:
                periodchang_ADJ.loc[len(periodchang_ADJ)
                ] = periodchang.loc[pnum]
                pnum += 1
            else:
                pnum += 1
        if periodchang_ADJ['end_date'].iloc[-1] != periodchang['end_date'].iloc[-1]:
            periodchang_ADJ.loc[len(periodchang_ADJ)] = periodchang.iloc[-1]
        if len(periodchang_ADJ) >= 2 \
                and (periodchang_ADJ['sum_ratio'].iloc[-1] * periodchang_ADJ['sum_ratio'].iloc[-2] > 0
                     or periodchang_ADJ['sum_ratio'].iloc[-1] == 0):
            periodchang_ADJ.loc[periodchang_ADJ.index[-2],
            'end_date'] = periodchang_ADJ['end_date'].iloc[-1]
            periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'last_days'] = \
                periodchang_ADJ['last_days'].iloc[-1] + \
                periodchang_ADJ['last_days'].iloc[-2]
            periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'sum_ratio'] = (stk_close[
                                                                               periodchang_ADJ['end_date'].iloc[-2]] /
                                                                           stk_close[periodchang_ADJ['start_date'].iloc[
                                                                               -2]] - 1) * 100
            periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'avg_ratio'] = \
                periodchang_ADJ['sum_ratio'].iloc[-2] / \
                periodchang_ADJ['last_days'].iloc[-2]
            if turnover is not None:
                periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'avg_turnover'] = \
                    round(turnover.loc[periodchang_ADJ['start_date'].iloc[-2]:periodchang_ADJ['end_date'].iloc[-2]
                          ].mean(), 2)
                periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'max_turnover'] = \
                    round(turnover.loc[periodchang_ADJ['start_date'].iloc[-2]:periodchang_ADJ['end_date'].iloc[-2]
                          ].max(), 2)
                stk_ratio_temp = stk_ratio.loc[
                                 periodchang_ADJ['start_date'].iloc[-2]:periodchang_ADJ['end_date'].iloc[-2]]
                rise_index = stk_ratio_temp[stk_ratio_temp > 0].index
                drop_index = stk_ratio_temp[stk_ratio_temp < 0].index
                periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'turnover2rise'] = round(
                    turnover.loc[rise_index].sum() / stk_ratio_temp.loc[rise_index].sum(), 3)
                periodchang_ADJ.loc[periodchang_ADJ.index[-2], 'turnover2drop'] = round(
                    abs(turnover.loc[drop_index].sum() / stk_ratio_temp.loc[drop_index].sum()), 3)

            periodchang_ADJ = periodchang_ADJ.drop(
                len(periodchang_ADJ) - 1, axis=0)
        crthr = pd.DataFrame(periodchang_ADJ['end_date'])
        crthr['valley'] = periodchang_ADJ.apply(
            lambda fn: stk_close.loc[fn['end_date']] if fn['sum_ratio'] < 0 else np.nan, axis=1)
        crthr['peak'] = periodchang_ADJ.apply(
            lambda fn: stk_close.loc[fn['end_date']] if fn['sum_ratio'] > 0 else np.nan, axis=1)
        crthr_turn_adj = pd.DataFrame(
            np.insert(crthr.values, 0,
                      values=[periodchang_ADJ['start_date'].iloc[0],
                              stk_close.loc[periodchang_ADJ['start_date'].iloc[0]], np.nan], axis=0)) \
            if periodchang['sum_ratio'].iloc[0] > 0 \
            else pd.DataFrame(
            np.insert(crthr.values, 0, values=[periodchang_ADJ['start_date'].iloc[0],
                                               np.nan, stk_close.loc[periodchang_ADJ['start_date'].iloc[0]]], axis=0))
        crthr_turn_adj.columns = ['date', 'valley', 'peak']
        crthr_turn_adj['valley'] = crthr_turn_adj['valley'].astype('float64')
        crthr_turn_adj['peak'] = crthr_turn_adj['peak'].astype('float64')
        crthr_turn_adj = crthr_turn_adj.set_index(['date'])
        trend_stat = trend_judge(stk_close, crthr_turn_adj)
        return periodchang_ADJ, trend_stat


def calculate_period_metrics(period_data):
    """计算单个period的所有技术指标
    
    Args:
        period_data (DataFrame): 单个period的价格数据
        
    Returns:
        dict: 包含该period所有技术指标的字典
    """
    metrics = {}
    
    # 基础价格指标
    start_close = period_data['close'].iloc[0]
    end_close = period_data['close'].iloc[-1]
    metrics['sum_ratio'] = (end_close / start_close - 1) * 100
    metrics['last_days'] = len(period_data)
    metrics['avg_ratio'] = metrics['sum_ratio'] / metrics['last_days']
    
    # 开盘收盘比率
    metrics['close_open_ratio'] = np.mean(abs(period_data['close'] / period_data['open'] - 1)) * 100
    
    # 换手率指标
    metrics['avg_turnover'] = round(period_data['turnover'].mean(), 2)
    metrics['max_turnover'] = round(period_data['turnover'].max(), 2)
    
    # 上涨/下跌换手率
    day_ratios = (period_data['close'] / period_data['close'].shift(1) - 1) * 100
    rise_mask = day_ratios > 0
    drop_mask = day_ratios < 0
    
    rise_sum = day_ratios[rise_mask].sum()
    drop_sum = day_ratios[drop_mask].sum()
    
    metrics['turnover2rise'] = round(
        period_data.loc[rise_mask, 'turnover'].sum() / rise_sum 
        if rise_sum != 0 else 0, 3)
        
    metrics['turnover2drop'] = round(
        abs(period_data.loc[drop_mask, 'turnover'].sum() / drop_sum)
        if drop_sum != 0 else 0, 3)
        
    # 线性回归指标
    x = np.arange(len(period_data))
    y = period_data['close'].values
    slope, intercept, r_value, _, _ = stats.linregress(x, y)
    
    metrics['r_squared'] = r_value ** 2
    metrics['slope'] = slope
    metrics['intercept'] = intercept
    metrics['si_pro'] = slope / intercept if intercept != 0 else 0
    
    # 标准差偏离度
    std_dev = np.std(period_data['close'])
    metrics['std_bias'] = abs(metrics['sum_ratio'] / std_dev) if std_dev != 0 else 0
    
    return metrics

def process_period_results(periods_df, price_data, index_data=None):
    """处理并整合period分析结果
    
    Args:
        periods_df (DataFrame): generate_periods生成的原始period DataFrame
        price_data (DataFrame): 原始价格数据
        index_data (DataFrame, optional): 指数数据
        
    Returns:
        DataFrame: 添加了所有技术指标的完整period分析结果
    """
    if periods_df.empty:
        return pd.DataFrame()
        
    # 计算每个period的指标
    for idx in periods_df.index:
        period_data = price_data.loc[periods_df.loc[idx, 'start_date']:periods_df.loc[idx, 'end_date']]
        metrics = calculate_period_metrics(period_data)
        
        # 更新DataFrame中的指标
        for key, value in metrics.items():
            periods_df.loc[idx, key] = value
    
    # 添加指数相关指标（如果提供了指数数据）
    if index_data is not None:
        periods_df['index_sum'] = periods_df.apply(
            lambda x: (index_data.loc[x['end_date'], 'close'] /
                     index_data.loc[x['start_date'], 'close'] - 1) * 100, axis=1)
        periods_df['index_diff'] = periods_df['sum_ratio'] - periods_df['index_sum']
    
    return periods_df


if __name__ == '__main__':
    rise_df, down_df, price_data = new_section_stat(stk_code='300026.SZ', start_date='2023-03-18', end_date='2025-01-08')
    period_df = generate_periods(rise_df, down_df, price_data)
    period_df = process_period_results(period_df, price_data, index_data=None)
    print(period_df)
