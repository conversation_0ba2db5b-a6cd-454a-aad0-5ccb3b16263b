{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["已连接到 base (Python 3.11.5)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import struct"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 读取日线数据\n", "dataSet = []\n", "file_name = 'min_data/sh000001.day'\n", "with open(file_name, 'rb') as f:\n", "    buffer = f.read()\n", "    size = len(buffer)\n", "    rowSize = 32\n", "    code = os.path.basename(file_name).replace('.day', '')\n", "    for i in range(0, size, rowSize):\n", "        row = list(struct.unpack('IIIIIfII', buffer[i:i+rowSize]))\n", "        row[1] = row[1] / 100\n", "        row[2] = row[2] / 100\n", "        row[3] = row[3] / 100\n", "        row[4] = row[4] / 100\n", "        row.pop()\n", "        row.insert(0, code)\n", "        dataSet.append(row)\n", "data = pd.DataFrame(dataSet, columns=['code', 'trade_date', 'open', 'high', 'low', 'close', 'volume', 'amount'])\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "数据基本信息:\n", "记录数量: 240\n", "\n", "数据范围:\n", "日期范围: 2025-01-02 00:00:00 至 2025-01-02 00:00:00\n", "\n", "价格范围:\n", "开盘价: 3.02 - 3.12\n", "最高价: 3.03 - 3.13\n", "最低价: 3.02 - 3.12\n", "收盘价: 3.02 - 3.12\n", "\n", "示例数据:\n", "       code       date   time  open  high   low  close    volume     amount\n", "0  sh600744 2025-01-02  09:31  3.12  3.13  3.10   3.11  551200.0  1717866.0\n", "1  sh600744 2025-01-02  09:32  3.11  3.11  3.09   3.09  321500.0   996521.0\n", "2  sh600744 2025-01-02  09:33  3.09  3.10  3.09   3.09  278700.0   861410.0\n", "3  sh600744 2025-01-02  09:34  3.09  3.10  3.08   3.09  120500.0   372085.0\n", "4  sh600744 2025-01-02  09:35  3.09  3.09  3.07   3.07  282700.0   870829.0\n"]}], "source": ["# 读取分钟数据\n", "def read_minute_data(file_path):\n", "    \"\"\"\n", "    读取通达信分钟行情数据文件(.lc1)并转换为DataFrame\n", "    格式说明：\n", "    - 00~01字节：日期，year = date/2048 + 2004\n", "    - 02~03字节：分钟数，从0点开始计算\n", "    - 04~07字节：开盘价，浮点数\n", "    - 08~11字节：最高价，浮点数\n", "    - 12~15字节：最低价，浮点数\n", "    - 16~19字节：收盘价，浮点数\n", "    - 20~23字节：成交额，浮点数\n", "    - 24~27字节：成交量，长整型\n", "    - 28~31字节：保留，长整型\n", "    \"\"\"\n", "    dataSet = []\n", "    code = os.path.basename(file_path).replace('.lc1', '')\n", "    \n", "    with open(file_path, 'rb') as f:\n", "        buffer = f.read()\n", "        row_size = 32\n", "        \n", "        for i in range(0, len(buffer), row_size):\n", "            record = buffer[i:i+row_size]\n", "            \n", "            try:\n", "                # 解析日期和时间（前4个字节）\n", "                date_raw, minutes = struct.unpack('<HH', record[0:4])  # 使用<表示低字节在前\n", "                \n", "                # 解析日期\n", "                year = date_raw // 2048 + 2004  # 基准年为2004\n", "                month = (date_raw % 2048) // 100\n", "                day = date_raw % 2048 % 100\n", "                \n", "                # 解析时间\n", "                hour = minutes // 60\n", "                minute = minutes % 60\n", "                \n", "                # 解析价格和成交数据 - 使用小端格式\n", "                open_price, high_price, low_price, close_price = struct.unpack('<ffff', record[4:20])\n", "                amount = struct.unpack('<f', record[20:24])[0]  # 成交额\n", "                volume = struct.unpack('<i', record[24:28])[0]  # 成交量\n", "                \n", "                # 格式化日期时间\n", "                date_str = f\"{year:04d}-{month:02d}-{day:02d}\"\n", "                time_str = f\"{hour:02d}:{minute:02d}\"\n", "                \n", "                # 处理异常数据\n", "                if open_price > 9999999 or high_price > 9999999 or low_price > 9999999 or close_price > 9999999:\n", "                    print(f\"跳过异常价格数据: {date_str} {time_str}\")\n", "                    continue\n", "                \n", "                if volume < 0 or amount < 0:\n", "                    print(f\"跳过异常成交数据: {date_str} {time_str}\")\n", "                    continue\n", "                \n", "                new_row = [\n", "                    code,                   # 代码\n", "                    date_str,               # 日期\n", "                    time_str,               # 时间\n", "                    round(open_price, 2),   # 开盘价\n", "                    round(high_price, 2),   # 最高价\n", "                    round(low_price, 2),    # 最低价\n", "                    round(close_price, 2),  # 收盘价\n", "                    volume,                 # 成交量\n", "                    round(amount, 2)        # 成交额\n", "                ]\n", "                dataSet.append(new_row)\n", "                \n", "            except Exception as e:\n", "                print(f\"处理记录错误: {e}\")\n", "                continue\n", "    \n", "    # 创建DataFrame并设置数据类型\n", "    df = pd.DataFrame(dataSet, columns=['code', 'date', 'time', 'open', \n", "                                      'high', 'low', 'close', \n", "                                      'volume', 'amount'])\n", "    \n", "    # 转换数据类型\n", "    df['date'] = pd.to_datetime(df['date'])\n", "    df[['open', 'high', 'low', 'close']] = df[['open', 'high', 'low', 'close']].astype(float)\n", "    df[['volume', 'amount']] = df[['volume', 'amount']].astype(float)\n", "    \n", "    # 按日期时间排序\n", "    df = df.sort_values(['date', 'time'])\n", "    \n", "    return df\n", "\n", "def display_data_info(df):\n", "    \"\"\"\n", "    显示数据基本信息\n", "    \"\"\"\n", "    print(\"\\n数据基本信息:\")\n", "    print(f\"记录数量: {len(df)}\")\n", "    print(\"\\n数据范围:\")\n", "    print(f\"日期范围: {df['date'].min()} 至 {df['date'].max()}\")\n", "    print(\"\\n价格范围:\")\n", "    print(f\"开盘价: {df['open'].min():.2f} - {df['open'].max():.2f}\")\n", "    print(f\"最高价: {df['high'].min():.2f} - {df['high'].max():.2f}\")\n", "    print(f\"最低价: {df['low'].min():.2f} - {df['low'].max():.2f}\")\n", "    print(f\"收盘价: {df['close'].min():.2f} - {df['close'].max():.2f}\")\n", "    print(\"\\n示例数据:\")\n", "    print(df.head())\n", "\n", "# 测试代码\n", "if __name__ == \"__main__\":\n", "    file_name = 'min_data/sh600744.lc1'\n", "    df = read_minute_data(file_name)\n", "    display_data_info(df)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}