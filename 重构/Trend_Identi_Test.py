import numpy as np
import pandas as pd
import itertools

import os
import sys

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)
    
from function_ai.Func_Base import get_stock_data


def identify_clear_trend(stock_data, window=20):
    """
    识别明确趋势的函数
    
    参数:
        stock_data (DataFrame): 包含OHLC数据的DataFrame
        window (int): 计算周期，默认20天
        
    返回:
        dict: 包含趋势特征的字典
    """
    # 1. 计算趋势斜率
    stock_data['ma20'] = stock_data['close'].rolling(window=window).mean()
    stock_data['slope'] = (stock_data['ma20'] - stock_data['ma20'].shift(window)) / window
    
    # 2. 计算价格波动范围
    stock_data['price_range'] = (stock_data['high'] - stock_data['low']) / stock_data['low'] * 100
    
    # 3. 计算趋势内的回撤幅度
    stock_data['drawdown'] = stock_data['close'].rolling(window=window).max() - stock_data['close']
    stock_data['drawdown_ratio'] = stock_data['drawdown'] / stock_data['close'].rolling(window=window).max() * 100
    
    return {
        'trend_slope': stock_data['slope'].iloc[-1],
        'avg_range': stock_data['price_range'].rolling(window=window).mean().iloc[-1],
        'max_drawdown': stock_data['drawdown_ratio'].max(),
        'is_clear_trend': (
            abs(stock_data['slope'].iloc[-1]) > 0.01 and  # 斜率要足够大
            stock_data['price_range'].rolling(window=window).mean().iloc[-1] < 3 and  # 日内波动要小
            stock_data['drawdown_ratio'].max() < 10  # 回撤要控制在合理范围
        )
    }
    
    



def check_price_channel(stock_data, window=20):
    """
    检查价格是否在稳定通道中运行
    
    参数:
        stock_data (DataFrame): 股票数据
        window (int): 通道宽度计算周期
        
    返回:
        bool: 是否在稳定通道中运行
    """
    # 1. 计算布林带
    stock_data['ma'] = stock_data['close'].rolling(window=window).mean()
    stock_data['std'] = stock_data['close'].rolling(window=window).std()
    stock_data['upper'] = stock_data['ma'] + 2 * stock_data['std']
    stock_data['lower'] = stock_data['ma'] - 2 * stock_data['std']
    
    # 2. 计算通道宽度
    stock_data['channel_width'] = (stock_data['upper'] - stock_data['lower']) / stock_data['ma']
    
    # 3. 检查价格是否保持在通道内
    in_channel = (
        (stock_data['close'] > stock_data['lower']) & 
        (stock_data['close'] < stock_data['upper'])
    ).mean()
    
    # 4. 检查通道宽度的稳定性
    channel_stability = stock_data['channel_width'].std() / stock_data['channel_width'].mean()
    
    return in_channel > 0.8 and channel_stability < 0.2


def is_clear_trend_stock(ts_code, end_date, days=60):
    """
    综合判断股票是否处于明确趋势中
    """
    # 获取股票数据
    stock_data = get_stock_data(
        stk_code=ts_code,
        end_date=end_date,
        days=days
    )
    
    # 进行多维度判断
    trend_features = identify_clear_trend(stock_data)
    is_consistent = check_trend_consistency(stock_data)
    in_stable_channel = check_price_channel(stock_data)
    
    # 综合判断结果
    return {
        'is_clear_trend': all([
            trend_features['is_clear_trend'],
            is_consistent,
            in_stable_channel
        ]),
        'trend_features': trend_features,
        'trend_consistency': is_consistent,
        'channel_stability': in_stable_channel
    }




def identify_short_trend(stock_data, window=10):
    """
    识别短期趋势特征
    
    参数:
        stock_data (DataFrame): 包含OHLC和成交量数据
        window (int): 观察窗口，默认10天
        
    返回:
        dict: 趋势特征指标
    """
    # 1. 计算日内特征
    stock_data['body'] = stock_data['close'] - stock_data['open']  
    stock_data['body_ratio'] = abs(stock_data['body']) / (stock_data['high'] - stock_data['low'])
    stock_data['upper_shadow'] = stock_data['high'] - stock_data[['open','close']].max(axis=1)
    stock_data['lower_shadow'] = stock_data[['open','close']].min(axis=1) - stock_data['low']
    
    # 2. 计算方向一致性
    stock_data['direction'] = np.where(stock_data['body'] > 0, 1, -1)
    direction_consistency = abs(stock_data['direction'].rolling(window).sum()) / window
    
    # 3. 计算量能变化
    stock_data['vol_ma'] = stock_data['vol'].rolling(window=5).mean()
    stock_data['vol_ratio'] = stock_data['vol'] / stock_data['vol_ma']
    
    # 4. 计算短期趋势强度
    stock_data['price_ratio'] = stock_data['close'].pct_change()
    trend_strength = abs(stock_data['close'].iloc[-1] / stock_data['close'].iloc[-window] - 1)
    
    recent_data = stock_data.iloc[-window:]
    
    return {
        'trend_strength': trend_strength,
        'direction_consistency': direction_consistency.iloc[-1],
        'avg_body_ratio': recent_data['body_ratio'].mean(),
        'shadow_ratio': (recent_data['upper_shadow'] + recent_data['lower_shadow']).mean() / 
                        recent_data['close'].mean(),
        'vol_trend': recent_data['vol_ratio'].mean(),
        'consecutive_days': max(
            len(list(g)) for k, g in itertools.groupby(recent_data['direction'])
        )
    }


def check_short_trend_quality(stock_data, window=10, min_strength=0.05):
    """
    判断短期趋势的质量
    
    参数:
        stock_data (DataFrame): 股票数据
        window (int): 观察窗口
        min_strength (float): 最小趋势强度
        
    返回:
        bool, dict: 是否符合趋势特征及详细指标
    """
    features = identify_short_trend(stock_data, window)
    
    # 定义判断标准
    is_valid_trend = (
        features['trend_strength'] >= min_strength and  # 趋势强度
        features['direction_consistency'] >= 0.7 and    # 方向一致性
        features['avg_body_ratio'] >= 0.6 and          # K线实体占比
        features['shadow_ratio'] <= 0.3 and            # 影线占比
        features['consecutive_days'] >= 3               # 最少连续天数
    )
    
    return is_valid_trend, features


def predict_trend_continuation(stock_data, window=10):
    """
    预判趋势延续可能性
    
    参数:
        stock_data (DataFrame): 股票数据
        window (int): 观察窗口
        
    返回:
        dict: 趋势延续性指标
    """
    recent_data = stock_data.iloc[-window:]
    
    # 1. 计算量价背离
    price_trend = recent_data['close'].iloc[-1] / recent_data['close'].iloc[0] - 1
    vol_trend = recent_data['vol'].iloc[-1] / recent_data['vol'].iloc[0] - 1
    vol_price_divergence = abs(price_trend - vol_trend)
    
    # 2. 计算动能指标
    recent_data['momentum'] = recent_data['close'].diff()
    momentum_strength = recent_data['momentum'].sum() / len(recent_data)
    
    # 3. 计算波动收敛/发散
    recent_data['tr'] = np.maximum(
        recent_data['high'] - recent_data['low'],
        abs(recent_data['high'] - recent_data['close'].shift(1))
    )
    volatility_trend = recent_data['tr'].iloc[-3:].mean() / recent_data['tr'].iloc[:3].mean()
    
    return {
        'vol_price_divergence': vol_price_divergence,
        'momentum_strength': momentum_strength,
        'volatility_trend': volatility_trend,
        'trend_health': (
            vol_price_divergence < 0.3 and  # 量价配合
            momentum_strength > 0 and        # 保持动能
            volatility_trend < 1.2           # 波动不发散
        )
    }
    
    
def analyze_short_trend(ts_code, end_date, window=10):
    """
    分析短期趋势特征
    """
    # 获取数据
    stock_data = get_stock_data(
        stk_code=ts_code,
        end_date=end_date,
        days=window+5  # 多获取几天用于计算指标
    )
    
    # 判断趋势特征
    is_valid, trend_features = check_short_trend_quality(stock_data, window)
    
    # 如果符合趋势特征，进一步判断延续性
    if is_valid:
        continuation_features = predict_trend_continuation(stock_data, window)
        return {
            'is_valid_trend': True,
            'trend_features': trend_features,
            'continuation_features': continuation_features,
            'trend_likely_continue': continuation_features['trend_health']
        }
    
    return {
        'is_valid_trend': False,
        'trend_features': trend_features
    }

########################

def check_trend_consistency(stock_data, threshold=0.6):
    """
    检查趋势的连续性
    
    参数:
        stock_data (DataFrame): 股票数据
        threshold (float): 趋势一致性阈值
        
    返回:
        bool: 是否符合连续趋势特征
    """
    # 1. 计算日间变动
    stock_data['daily_change'] = (stock_data['close'] - stock_data['open']) / stock_data['open']
    
    # 2. 计算与趋势方向一致的天数比例
    trend_direction = 'up' if stock_data['close'].iloc[-1] > stock_data['close'].iloc[0] else 'down'
    if trend_direction == 'up':
        trend_consistency = (stock_data['daily_change'] > 0).mean()
    else:
        trend_consistency = (stock_data['daily_change'] < 0).mean()
        
    # 3. 计算K线实体占比
    stock_data['body_ratio'] = abs(stock_data['close'] - stock_data['open']) / (stock_data['high'] - stock_data['low'])
    avg_body_ratio = stock_data['body_ratio'].mean()
    
    # 4. 打印指标结果
    print(f'趋势方向: {trend_direction}')
    print(f'趋势一致性: {trend_consistency:.2%}')
    print(f'K线实体占比: {avg_body_ratio:.2%}')
    print(f'趋势一致性阈值: {threshold}')
    
    # 5. 返回判定结果
    return trend_consistency > threshold and avg_body_ratio > 0.5


def check_price_channel(stock_data, min_fit_ratio=0.8):
    """
    检查价格是否在稳定的趋势通道中运行
    """
    if len(stock_data) < 5:
        return False
    
    def fit_trend_line(prices, days, outlier_mask=None):
        """计算趋势线，可选择性排除异常点"""
        if outlier_mask is None:
            return np.polyfit(days, prices, 1)
        return np.polyfit(days[~outlier_mask], prices[~outlier_mask], 1)
    
    def calculate_channel_metrics(slope, intercept, days, prices):
        """计算通道相关指标"""
        trend_line = slope * days + intercept
        deviations = (prices - trend_line) / trend_line
        return trend_line, deviations
    
    # 初始数据准备
    days = np.arange(len(stock_data))
    prices = stock_data['close'].values
    
    # 迭代优化趋势线（最多3次迭代）
    best_slope = None
    best_intercept = None
    best_fit_ratio = 0
    
    for iteration in range(3):
        # 1. 计算当前趋势线
        if iteration == 0:
            slope, intercept = fit_trend_line(prices, days)
        else:
            # 使用非异常点重新拟合趋势线
            slope, intercept = fit_trend_line(prices, days, outlier_mask)
        
        # 2. 计算偏离度
        trend_line, deviations = calculate_channel_metrics(slope, intercept, days, prices)
        
        # 3. 识别异常点（偏离度超过阈值的点）
        threshold = np.std(deviations) * 2  # 使用2倍标准差作为阈值
        outlier_mask = abs(deviations) > threshold
        
        # 4. 计算拟合比例
        fit_ratio = 1 - np.mean(outlier_mask)
        
        # 5. 更新最优结果
        if fit_ratio > best_fit_ratio:
            best_fit_ratio = fit_ratio
            best_slope = slope
            best_intercept = intercept
            
        # 如果已经达到要求，提前结束
        if fit_ratio >= min_fit_ratio:
            break
    
    # 使用最优趋势线计算最终指标
    if best_slope is None or best_fit_ratio < min_fit_ratio:
        return False
        
    final_trend_line = best_slope * days + best_intercept
    stock_data['trend_line'] = final_trend_line
    
    # 计算通道特征
    stock_data['deviation'] = (stock_data['close'] - stock_data['trend_line']) / stock_data['trend_line']
    stock_data['channel_width'] = (stock_data['high'] - stock_data['low']) / stock_data['trend_line']
    
    # 计算趋势强度和稳定性指标
    trend_strength = abs(best_slope) / best_intercept * len(stock_data)
    avg_channel_width = stock_data['channel_width'].mean()
    channel_width_stability = stock_data['channel_width'].std() / avg_channel_width
    
    # 最终判断标准
    is_stable_trend = (
        best_fit_ratio >= min_fit_ratio and     # 足够多的点在通道内
        avg_channel_width < 0.04 and            # 通道宽度合理
        channel_width_stability < 0.3 and       # 通道宽度稳定
        trend_strength > 0.01                   # 有足够的趋势强度
    )
    
    # 增加趋势方向判断
    trend_direction = 'up' if best_slope > 0 else 'down'
    
    # 根据趋势方向调整判断标准
    if trend_direction == 'up':
        channel_top = final_trend_line + 2 * np.std(deviations) * final_trend_line
        channel_bottom = final_trend_line - np.std(deviations) * final_trend_line
    else:
        channel_top = final_trend_line + np.std(deviations) * final_trend_line  
        channel_bottom = final_trend_line - 2 * np.std(deviations) * final_trend_line
        
    # 计算突破上下轨的比例
    break_up_ratio = (stock_data['high'] > channel_top).mean()
    break_down_ratio = (stock_data['low'] < channel_bottom).mean()
    
    # 打印计算指标结果
    print("\n通道分析结果:")
    print(f"拟合比例: {best_fit_ratio:.3f}")
    print(f"通道宽度: {avg_channel_width:.3f}")
    print(f"通道稳定性: {channel_width_stability:.3f}")
    print(f"趋势强度: {trend_strength:.3f}")
    print(f"趋势方向: {trend_direction}")
    print(f"向上突破比例: {break_up_ratio:.3f}")
    print(f"向下突破比例: {break_down_ratio:.3f}")
    print(f"是否稳定趋势: {is_stable_trend}")
    
    return {
        'is_stable': is_stable_trend,
        'trend_direction': trend_direction,
        'break_up_ratio': break_up_ratio,
        'break_down_ratio': break_down_ratio,
        'channel_width': avg_channel_width,
        'trend_strength': trend_strength
    }

    
def evaluate_section_stability(stock_data):
    """
    评估股价区段的稳定性
    
    参数:
        stock_data (DataFrame): 区段内的股票数据
        
    返回:
        dict: 包含趋势一致性和通道稳定性的评估结果
    """
    # 获取趋势一致性和通道稳定性
    trend_consistent = check_trend_consistency(stock_data)
    channel_stable = check_price_channel(stock_data)
    
    return {
        'trend_consistency': trend_consistent,
        'channel_stability': channel_stable,
        'section_stability': trend_consistent and channel_stable
    }

def find_peaks_valleys(df):
    """
    寻找股票价格的波峰和波谷，并构建趋势区段，确保涵盖所有日期区间
    
    参数:
    df: DataFrame, 包含股票价格数据，index为日期，必须包含'close'列
    
    返回:
    DataFrame: 包含波段起止日期和持续天数的DataFrame
    """
    import pandas as pd
    import numpy as np
    
    # 创建结果存储列表
    results = []
    dates = df.index.tolist()
    
    # 找出波峰和波谷
    peaks = []
    valleys = []
    
    for i in range(len(df)):
        # 确定前后查找范围
        start_idx = max(0, i - 5)
        end_idx = min(len(df), i + 6)
        
        window_prices = df['close'].iloc[start_idx:end_idx]
        current_price = df['close'].iloc[i]
        
        # 判断是否为波谷（局部最低点）
        if current_price == window_prices.min() and (i == 0 or current_price != df['close'].iloc[i-1]):
            valleys.append(dates[i])
            
        # 判断是否为波峰（局部最高点）
        if current_price == window_prices.max() and (i == 0 or current_price != df['close'].iloc[i-1]):
            peaks.append(dates[i])
    
    # 将波峰和波谷合并到一个DataFrame中，并标注类型
    peaks_df = pd.DataFrame({'date': peaks, 'type': 'peak'})
    valleys_df = pd.DataFrame({'date': valleys, 'type': 'valley'})
    combined_df = pd.concat([peaks_df, valleys_df]).sort_values(by='date').reset_index(drop=True)
    
    # 确保涵盖所有日期区间
    if not combined_df.empty:
        first_date = df.index.min()
        last_date = df.index.max()
        
        if combined_df.iloc[0]['date'] != first_date:
            combined_df = pd.concat([pd.DataFrame({'date': [first_date], 'type': ['valley']}), combined_df]).reset_index(drop=True)
        
        if combined_df.iloc[-1]['date'] != last_date:
            combined_df = pd.concat([combined_df, pd.DataFrame({'date': [last_date], 'type': ['peak']})]).reset_index(drop=True)
    
    # 处理相邻的波峰和波谷
    i = 0
    while i < len(combined_df) - 1:
        current_point = combined_df.iloc[i]
        next_point = combined_df.iloc[i + 1]
        
        if current_point['type'] == next_point['type']:
            # 获取两点之间的数据
            mask = (df.index >= current_point['date']) & (df.index <= next_point['date'])
            between_data = df[mask]
            
            if current_point['type'] == 'peak':
                # 在两个波峰之间找波谷
                valley_date = between_data['close'].idxmin()
                new_point = pd.DataFrame({'date': [valley_date], 'type': ['valley']})
            else:
                # 在两个波谷之间找波峰
                peak_date = between_data['close'].idxmax()
                new_point = pd.DataFrame({'date': [peak_date], 'type': ['peak']})
            
            # 插入新点
            combined_df = pd.concat([
                combined_df.iloc[:i+1], 
                new_point, 
                combined_df.iloc[i+1:]
            ]).reset_index(drop=True)
        
        i += 1
    
    # 重新构建趋势区段
    results = []
    for i in range(len(combined_df) - 1):
        current_point = combined_df.iloc[i]
        next_point = combined_df.iloc[i + 1]
        
        if current_point['type'] == 'valley' and next_point['type'] == 'peak':
            start_date = current_point['date']
            end_date = next_point['date']
            days_between = len(df.loc[start_date:end_date])
            price_change = (df.loc[end_date, 'close'] - df.loc[start_date, 'close']) / df.loc[start_date, 'close']
            
            results.append({
                'start_date': start_date,
                'end_date': end_date,
                'days': days_between,
                'price_change': price_change,
                'trend_type': 'up'
            })
        elif current_point['type'] == 'peak' and next_point['type'] == 'valley':
            start_date = current_point['date']
            end_date = next_point['date']
            days_between = len(df.loc[start_date:end_date])
            price_change = (df.loc[end_date, 'close'] - df.loc[start_date, 'close']) / df.loc[start_date, 'close']
            
            results.append({
                'start_date': start_date,
                'end_date': end_date,
                'days': days_between,
                'price_change': price_change,
                'trend_type': 'down'
            })
    
    return pd.DataFrame(results)



if __name__ == '__main__':
    stk_data = get_stock_data(stk_code='000573.SZ', start_date='2024-05-20', end_date='2025-01-08')
    stk_data = stk_data.set_index('trade_date')
    trends, combined_df = find_peaks_valleys(stk_data.copy())
    print(trends)
    print(combined_df)