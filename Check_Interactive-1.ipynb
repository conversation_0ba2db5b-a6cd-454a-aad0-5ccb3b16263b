{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["已连接到 base (Python 3.11.5)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["starttime:  2024-12-25 23:56:33.126752\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["000910.SZ\n", "> \u001b[0;32m/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkQuota_Func_V7.py\u001b[0m(2826)\u001b[0;36mcal_metrics_3\u001b[0;34m()\u001b[0m\n", "\u001b[0;32m   2824 \u001b[0;31m    \u001b[0;32mif\u001b[0m \u001b[0mmode\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;34m'ADJ'\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0m\u001b[0;32m   2825 \u001b[0;31m        \u001b[0mpdb\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset_trace\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0m\u001b[0;32m-> 2826 \u001b[0;31m    \u001b[0;32mreturn\u001b[0m \u001b[0mResult_Loc\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0m\u001b[0;32m   2827 \u001b[0;31m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0m\u001b[0;32m   2828 \u001b[0;31m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [03:05<00:00, 185.47s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["计算Gap指标：\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [00:00<00:00, 1859.18it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["endtime:  2024-12-26 00:02:28.346766\n", "耗时：  0:05:55.220014\n", "finish date:  2024-11-27\n"]}], "source": ["from function_ai.StkQuota_Func_V7 import stksfit_result_3\n", "result_filtered, _ = stksfit_result_3(end_date='2024-11-27', mode='ADJ', stk_code='000910.SZ')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["cond1 = (result_filtered.eval(\"PostNowSec_Recover_TopOpen_Days<=5 & \"\n", "                                     \"PostNowSec_Recover_TopOpen_Date==Cal_Date\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["cond2 = result_filtered.eval(\"PostNowSec_LastDays<=5 & \"\n", "                                     \"PreNowSec_LastDays>3\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["cond3 = ((result_filtered.eval(\"Section_StartDate<Now_SecDate & \"\n", "                                     \"PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays>0\")) |\n", "                 (result_filtered.eval(\"Section_StartDate==Now_SecDate & \"\n", "                                     \"Peak2Sec_PGV_MinRollAvg2Sec_LastDays>0 & \"\n", "                                     \"Peak2Sec_PGV_MinRollAvg2Sec_LastDays<10\")))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["cond4 = result_filtered.eval(\"PreNowPeak2NowSec_RatioMovAvg_NowSecRank<8\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["cond5 = result_filtered.eval(\"(PostSecStart_Over5MovAvg_Prop>0.5 | Section_StartDate==Now_SecDate) & \"\n", "                                   \"Peak2Sec_Und5MovAvg_Prop>0.6\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PostPeak_Recent_Neg4_RecovDate</th>\n", "      <th>PostNowSec_Recover_TopOpen_Date</th>\n", "      <th>PostNowSec_Recover_TopOpen_Days</th>\n", "      <th>PreNowPeak2NowSec_RatioMovAvg_NowSecRank</th>\n", "      <th>Now_SecDate</th>\n", "      <th>PreNowPeak2NowSec_MinRatioMovAvg_Date</th>\n", "      <th>Section_StartDate</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2632</th>\n", "      <td>2024-11-27</td>\n", "      <td>-</td>\n", "      <td>-1</td>\n", "      <td>1.0</td>\n", "      <td>2024-11-22</td>\n", "      <td>2024-11-22</td>\n", "      <td>2024-08-28</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PostPeak_Recent_Neg4_RecovDate PostNowSec_Recover_TopOpen_Date  \\\n", "2632                     2024-11-27                               -   \n", "\n", "     PostNowSec_Recover_TopOpen_Days PreNowPeak2NowSec_RatioMovAvg_NowSecRank  \\\n", "2632                              -1                                      1.0   \n", "\n", "     Now_SecDate PreNowPeak2NowSec_MinRatioMovAvg_Date Section_StartDate  \n", "2632  2024-11-22                            2024-11-22        2024-08-28  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["result_filtered[['PostPeak_Recent_Neg4_RecovDate', 'PostNowSec_Recover_TopOpen_Date', 'PostNowSec_Recover_TopOpen_Days', 'PreNowPeak2NowSec_RatioMovAvg_NowSecRank', 'Now_SecDate', 'PreNowPeak2NowSec_MinRatioMovAvg_Date', 'Section_StartDate']]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PreNowSec_LastDays</th>\n", "      <th>PostNowSec_LastDays</th>\n", "      <th>PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays</th>\n", "      <th>Peak2Sec_PGV_MinRollAvg2Sec_LastDays</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2632</th>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PreNowSec_LastDays PostNowSec_LastDays  \\\n", "2632                 11                   3   \n", "\n", "     PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays  \\\n", "2632                                             2   \n", "\n", "     Peak2Sec_PGV_MinRollAvg2Sec_LastDays  \n", "2632                                    6  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["result_filtered[['PreNowSec_LastDays', 'PostNowSec_LastDays', 'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays', 'Peak2Sec_PGV_MinRollAvg2Sec_LastDays']]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PostSecStart_PGV_RollAvg_HighQuntl</th>\n", "      <th>Recent3Day_PGV_MaxRollAvg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2632</th>\n", "      <td>0.103</td>\n", "      <td>0.109</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PostSecStart_PGV_RollAvg_HighQuntl Recent3Day_PGV_MaxRollAvg\n", "2632                              0.103                     0.109"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["result_filtered[['PostSecStart_PGV_RollAvg_HighQuntl', 'Recent3Day_PGV_MaxRollAvg']]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/bk/krf0m4hj3_s859d6vdqxpq240000gn/T/ipykernel_37813/4087212277.py:10: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '0.534' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  stk_data.loc[date, 'trend_wls_slope'] = trend_wls_slope\n", "/var/folders/bk/krf0m4hj3_s859d6vdqxpq240000gn/T/ipykernel_37813/4087212277.py:11: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '0.856' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  stk_data.loc[date, 'trend_wls_r2'] = trend_wls_r2\n", "/var/folders/bk/krf0m4hj3_s859d6vdqxpq240000gn/T/ipykernel_37813/4087212277.py:12: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '26.69' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  stk_data.loc[date, 'trend_wls_dev_ratio'] = trend_wls_dev_ratio\n"]}, {"data": {"text/plain": ["(23, '2024-10-16', 0.993, 5.399)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from function_ai.Func_Base import get_stock_data\n", "from function_ai.StkQuota_Func_V7 import cal_trend_wls\n", "stk_data = get_stock_data(stk_code='003041.SZ', start_date='2024-09-13', end_date='2024-11-15')\n", "stk_data = stk_data.set_index('trade_date')\n", "stk_data['trend_wls_slope'] = 0\n", "stk_data['trend_wls_r2'] = 0\n", "stk_data['trend_wls_dev_ratio'] = 0\n", "for date in stk_data.index:\n", "    trend_wls_slope, trend_wls_r2, trend_wls_dev_ratio = cal_trend_wls(stk_data.loc[date:])\n", "    stk_data.loc[date, 'trend_wls_slope'] = trend_wls_slope\n", "    stk_data.loc[date, 'trend_wls_r2'] = trend_wls_r2\n", "    stk_data.loc[date, 'trend_wls_dev_ratio'] = trend_wls_dev_ratio\n", "r2_length = len(stk_data.loc[stk_data['trend_wls_r2'].idxmax():])\n", "r2_length, stk_data['trend_wls_r2'].idxmax(), stk_data['trend_wls_r2'].max(), stk_data.loc[stk_data['trend_wls_r2'].idxmax(),'trend_wls_dev_ratio']"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 计算section状态\n", "from function_ai.Func_Base import section_stat\n", "section_rise, section_drop, day_list = section_stat(stk_code='002691.SZ', start_date='2024-03-18', end_date='2025-01-10')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:232: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:260: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, temp_df], ignore_index=True)\n"]}], "source": ["from function_ai.StkPick_ModelFunc import cal_stock_industry_strength\n", "relative_return, cum_relative_returns = cal_stock_industry_strength(stk_code='603667.SH', start_date='2024-09-18', end_date='2025-01-14')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2024-12-31'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["cum_relative_returns.loc['2024-12-12':].idxmin()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='trade_date'>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cum_relative_returns.plot()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(-1.2827777777777778, 0.9895790350783846)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from function_ai.Func_Base import get_stock_data\n", "stk_data = get_stock_data(stk_code='603928.SH', start_date='2024-12-10', end_date='2025-01-06')\n", "stk_data = stk_data.set_index('trade_date')\n", "stk_data['daily_ratio'] = round((stk_data['close']/stk_data['close'].shift(1) - 1)*100, 2)\n", "ratio_mean = stk_data['daily_ratio'].mean()\n", "ratio_std = stk_data.query('daily_ratio>0')['daily_ratio'].std()\n", "ratio_mean,ratio_std"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:235: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:263: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, temp_df], ignore_index=True)\n"]}], "source": ["from function_ai.StkPick_Func_V7 import induspick_turn_state\n", "indus_count, plat_indus_count = induspick_turn_state(end_date='2025-01-06', section_startdate='2025-01-06')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import get_data_from_pullstart\n", "result = get_data_from_pullstart(end_date='2025-02-28', \n", "                                 class_type='TurnBreak', industry_list=['机械设备'])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:359: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  \n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:459: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  \n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:530: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["行业: 建筑材料, 回撤转折日期: 2024-10-09, 转折日期: 2024-10-16\n", "未存储筛选股票数据\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:1682: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  result_df_turn['Check_Date'] = end_date\n"]}], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "# trade_date = get_trade_date(start_date='2025-02-05', end_date='2025-03-04')\n", "# trade_date = trade_date[::-1]\n", "trade_date = ['2024-10-21']\n", "for date in trade_date:\n", "    # industry_list = ['房地产', '纺织服饰', '建筑装饰', '建筑材料', '轻工制造', '家用电器', '交通运输', '公用事业', \n", "                    #  '煤炭', '农林牧渔']\n", "    industry_list=['建筑材料']\n", "    end_date, index_turndate, index_peakdate = date, ['2024-10-08', '2024-09-18'], '2024-08-28'\n", "    result_df, result_df_turn, pull_start_list = track_pullstart_stocks(end_date=end_date, index_turndate=index_turndate, \n", "                                                                        index_peakdate=index_peakdate, industry_list=industry_list, \n", "                                                                        limit_num=80, store_mode=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_ModelFunc import cal_target_pgv\n", "in_target_pgv_sigle, out_target_pgv_single = cal_target_pgv(stk_list=['300086.SZ','600581.SH'], check_date='2025-06-11')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_ModelFunc import cal_target_pgv\n", "in_target_pgv, out_target_pgv = cal_target_pgv(stk_list=['002805.SZ', '603685.SH'], check_date='2025-06-19')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from function_ai.Func_Base import cal_period_ratio\n", "period_return = cal_period_ratio(start_date='2025-01-06', end_date='2025-03-10', mode='Max')\n", "industry_list = ['非银金融']\n", "period_return_indus = period_return.query('industry in @industry_list')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import get_result_3\n", "result_store = get_result_3(end_date='2025-03-25')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["indus_list = ['机械设备', '基础化工', '煤炭', '电力设备', '有色金属']\n", "result_store_now_firstbreach = result_store.query('industry in @indus_list & NowSec_PRA_FirstBreach_Date==\"2025-03-24\"'\n", "                                                  )[['industry', 'ts_code', 'name', 'PreNow_PeakDate', 'NowSec_PRA_FirstBreach_Date', 'PreNowPeak_PRV_Top3Mean', 'PostNowSec_PGV_MaxRollAvg']]\n", "result_store_sec_firstbreach = result_store.query('industry in @indus_list & SecStart_PRA_FirstBreach_Date==\"2025-03-24\"'\n", "                                                  )[['industry', 'ts_code', 'name', 'Section_PeakDate', 'SecStart_PRA_FirstBreach_Date', 'SectionPeak_PRV_Top3Mean', 'PostSecStart_PGV_MaxRollAvg']]\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2025-06-13T01:34:19.358970Z", "start_time": "2025-06-13T01:32:44.784208Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["检测到变动起始日期: 2025-04-10\n", "\n", "前期 (2025-04-02 至 2025-04-09) peak_gap平均数值: 0.24\n", "后期 (2025-04-10 至 2025-06-13) peak_gap平均数值: 0.10\n", "peak_gap绝对变动幅度: -0.14\n", "peak_gap相对变动幅度: -57.95\n"]}], "source": ["# 调用ruptures检测变动点\n", "\n", "import ruptures as rpt\n", "import numpy as np\n", "from function_ai.Func_Base import get_stock_data\n", "from function_ai.DailyGap_Func import get_min_indicators\n", "\n", "code = '600640.SH'\n", "start_date, end_date = '2025-04-08', '2025-06-13'\n", "# stk_indicator = 'turnover'\n", "stk_indicator = 'peak_gap'\n", "\n", "min_period_for_avg = 4 # 用于计算平均值的最小天数\n", "\n", "if  stk_indicator == 'turnover':\n", "    stock_data = get_stock_data(stk_code=code, start_date=start_date, end_date=end_date)\n", "    stock_data = stock_data.set_index('trade_date')\n", "    data = stock_data[stk_indicator].dropna().values.reshape(-1, 1)\n", "    dates_index = stock_data.index\n", "else:\n", "    gap_data = get_min_indicators(stk_code=code, start_date=start_date, end_date=end_date, draw_turnover=False, draw_gap=False)\n", "    gap_data = gap_data.set_index('trade_date')\n", "    data = gap_data[stk_indicator].dropna().values.reshape(-1, 1)\n", "    dates_index = gap_data.index\n", "\n", "\n", "detector = rpt.Dynp(model=\"l2\", min_size=min_period_for_avg).fit(data)\n", "    \n", "result_indices = detector.predict(n_bkps=1)\n", "\n", "# data_variance = np.var(data)\n", "# if np.isclose(data_variance, 0):  # 如果方差接近零\n", "#     data_variance = 1e-10  # 设置一个很小的默认值\n", "\n", "# penalty_value = np.log(len(data)) * data_variance * 0.1  # 这是一个尝试性的启发式值，可能需要调整\n", "# if show_info:\n", "#     print(f\"\\n使用的 penalty 参数: {penalty_value:.2f}\")\n", "\n", "# result_indices = detector.predict(pen=penalty_value)\n", "\n", "if result_indices and result_indices[0] < len(data):\n", "    change_point_array_index = result_indices[0]\n", "\n", "    # 将 numpy 数组索引映射回 DataFrame 的日期索引\n", "    # change_point_array_index 是新段开始的索引，所以对应的日期就是 T 日\n", "    t_date = dates_index[change_point_array_index]\n", "    print(f\"检测到变动起始日期: {t_date}\")\n", "    \n", "    if change_point_array_index >= min_period_for_avg and \\\n", "            (len(data) - change_point_array_index) >= min_period_for_avg:\n", "    \n", "        # 使用检测到的分界点来划分前期和后期数据\n", "        pre_change_data = data[:change_point_array_index].flatten()  # .flatten() 转换为1D\n", "        post_change_data = data[change_point_array_index:].flatten()\n", "\n", "        post_change_days = len(post_change_data)\n", "\n", "        mean_before = np.mean(pre_change_data)\n", "        mean_after = np.mean(post_change_data)\n", "\n", "        absolute_change = mean_after - mean_before\n", "        relative_change_percent = (absolute_change / mean_before) * 100 if mean_before != 0 else np.nan\n", "        \n", "        print(\n", "            f\"\\n前期 ({dates_index[0]} 至 {dates_index[change_point_array_index - 1]}) \"\n", "            f\"{stk_indicator}平均数值: {mean_before:.2f}\")\n", "        print(\n", "            f\"后期 ({dates_index[change_point_array_index]} 至 {dates_index[-1]}) \"\n", "            f\"{stk_indicator}平均数值: {mean_after:.2f}\")\n", "        print(f\"{stk_indicator}绝对变动幅度: {absolute_change:.2f}\")\n", "        if not np.isnan(relative_change_percent):\n", "            print(f\"{stk_indicator}相对变动幅度: {relative_change_percent:.2f}\")\n", "        else:\n", "            print(\"前期平均数值为0，无法计算相对变动幅度。\")\n", "\n", "else:\n", "    print('未检测到变动起始日期')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["gapvalue_column_names = ['PostSecStart_MaxPeakGap', 'PostSecStart_MaxPGV',\n", "                             'PostSecStart_MaxValleyGap', 'PostSecStart_MaxValleyGapValue',\n", "                             'PostSecStart_MedianPeakGap', 'PostSecStart_MedianPeakGapValue',\n", "                             'PostSecStart_MedianValleyGap', 'PostSecStart_MedianValleyGapValue',\n", "                             'PostSecStart_MinPeakGap', 'PostSecStart_MinValleyGap',\n", "                             'PostSecPeak_MaxValleyGapValue', 'PostSecPeak_MaxPeakGapValue',\n", "                             'PostSecPeak_MinValleyGapValue', 'PostSecPeak_MinPeakGapValue',\n", "                             'PostSecPeak_MedianValleyGapValue', 'PostSecPeak_MedianPeakGapValue',\n", "                             'PostNowSec_MaxPeakGap', 'PostNowSec_MaxPeakGapValue',\n", "                             'PostNowSec_MaxValleyGap', 'PostNowSec_MaxValleyGapValue',\n", "                             'PostNowSec_MedianPeakGap',\n", "                             'PostNowSec_MedianPeakGapValue', 'PostNowSec_MedianValleyGapValue',\n", "                             'PostNowSec_MinPeakGap',\n", "                             'PostSecBottom_MaxPeakGap', 'PostSecBottom_MaxValleyGap',\n", "                             'Peak2Sec_MaxPeakGap', 'Peak2Sec_MaxValleyGap',\n", "                             'Peak2Sec_MaxPeakGapValue', 'Peak2Sec_MaxValleyGapValue',\n", "                             'Now_PeakGap', 'Now_PeakGapValue',\n", "                             'Now_ValleyGap', 'Now_ValleyGapValue',\n", "                             'Recent3Day_MaxPeakGapValue', 'Recent3Day_MaxValleyGapValue',\n", "                             'Recent3Day_MinPeakGapValue', 'Recent3Day_MinValleyGapValue',\n", "                             'Peak3Day_MaxPeakGapValue',\n", "                             'PostSecStart_PeakGap_HighQuntl', 'PostSecStart_PeakGapValue_HighQuntl',\n", "                             'PostSecStart_PeakGap_LowQuntl', 'PostSecStart_PeakGapValue_LowQuntl',\n", "                             'PostSecStart_ValleyGap_HighQuntl', 'PostSecStart_ValleyGapValue_HighQuntl',\n", "                             'PostSecStart_ValleyGap_LowQuntl', 'PostSecStart_ValleyGapValue_LowQuntl',\n", "                             'PostTurn_MedianPeakGapValue', 'PostTurn_PeakGapValue_HighQuntl',\n", "                             'PostTurn_MaxPeakGapValue', 'PostTurn_MedianValleyGapValue',\n", "                             'PostTurn_ValleyGapValue_HighQuntl', 'PostTurn_MaxValleyGapValue',\n", "                             'Peak2Sec_PeakGap_HighQuntl', 'Peak2Sec_PeakGap_LowQuntl',\n", "                             'Peak2Sec_ValleyGap_HighQuntl', 'Peak2Sec_MedianValleyGapValue',\n", "                             'Peak2Sec_PeakGapValue_HighQuntl', 'Peak2Sec_ValleyGapValue_HighQuntl',\n", "                             'PreNowSec_MinPeakGap', 'PreNowSec_MinPeakGap_Date',\n", "                             'PreNowSec_MinValleyGap', 'PreNowSec_MaxValleyGap', 'PreNowSec_MaxValleyGapValue',\n", "                             'PreNowSec_MedianPeakGap', 'PreNowSec_MedianValleyGap',\n", "                             'NowSec_Recent3DValley_Over_HighQuntl_Num',\n", "                             'NowSec_Recent3D_MaxValleyGapValue',\n", "                             'PreTurnPeak_Sec_MaxPeakGapValue', 'PreSecPeak_Sec_MaxPeakGapValue',\n", "                             #    'PostSecStart_MaxPeakGap2Yesd_Ratio',\n", "                             #    'PostSecStart_PeakGapNow2Med_Ratio',\n", "                             #    'PostSecStart_UndMed_SecondDate',\n", "                             #    'PostSecStart_PeakGapValue_TrackSignal', 'PostSecPeak_PeakGapUndHighQuntl_Signal',\n", "                             #    'PostSecPeak_Over_Btw_Num', 'PostPreNowSec_Over_Btw_Num',\n", "                             #    'PostSecPeak_Over_HighQuntl_Num', 'PostPreNowSec_Over_HighQuntl_Num',\n", "                             #    'PostNowSec_Over_HighQuntl_Num', 'PostNowSec_Over_Btw_Num',\n", "                             #    'Recent3DValley_Over_HighQuntl_Num', 'Recent3DPeak_Over_HighQuntl_Num',\n", "                             #    'Recent5DValley_Over_Median_Num', 'Recent5DPeak_Over_Median_Num',\n", "                             #    'PostSecPeak_Rank3_ValleyGapValue',\n", "                             #    'PostSecPeak_DownConsecutive_AvgLastDays', 'PostSecMaxRollAvg_DownConsecutive_AvgLastDays',\n", "                             #    'PostNowSec_UpConsecutive_AvgLastDays', 'PostSecStart_UpConsecutive_AvgLastDays',\n", "                             #    'PostPreSecPeak_DownConsecutive_AvgLastDays',\n", "                             'Now_PostSecPeak_VGV_Desc_Rank', 'Now_PostSecPeak_VGV_MaxCoverDays',\n", "                             'Now_PostSecStart_VGV_MaxCoverDays',\n", "                             'Now_PostNowSec_PGV_Desc_Rank', 'Now_PostNowSec_PGV_MaxCoverDays',\n", "                             'PostSecPeak_MaxVGV2Now_LastDays',\n", "                             'PostSecPeak_VGV_MinRollAvg2MaxRatio', 'PostSecPeak_VGV_NowRollAvg2MaxRatio',\n", "                             'PostSecPeak_VGV_MinRollAvg2Now_LastDays', 'Now_PostSecPeak_VGV_RollAvg_Asc_Rank',\n", "                             'Now_PostSecPeak_VGV_RollAvg_MinCoverDays',\n", "                             'PostSecStart_PGV_MaxRollAvg2MinRatio', 'PostSecStart_PGV_MaxRollAvg2MeanRatio',\n", "                             'PostSecStart_PGV_MaxRollAvg2Now_LastDays',\n", "                             'PostSecStart_PGV_NowRollAvg2MinRatio', 'Now_PostSecStart_PGV_RollAvg_Desc_Rank',\n", "                             'Now_PostSecStart_PGV_RollAvg_Asc_Rank',\n", "                             'Now_PGV_RollAvg_CoverDays', 'PostSecStart_PGV_MinRollAvg2Now_LastDays',\n", "                             'PostSecPeak_PGV_MinRollAvg2MaxRatio', 'PostSecPeak_PGV_NowRollAvg2MaxRatio',\n", "                             'PostSecPeak_PGV_MinRollAvg2Now_LastDays', 'Now_PostSecPeak_PGV_RollAvg_Asc_Rank',\n", "                             'Now_PostSecPeak_PGV_RollAvg_MinCoverDays',\n", "                             'PostSecStart_VGV_MaxRollAvg2MinRatio',\n", "                             'PostSecStart_VGV_NowRollAvg2MinRatio', 'Now_PostSecStart_VGV_RollAvg_Desc_Rank',\n", "                             'Now_VGV_RollAvg_CoverDays',\n", "                             'PostSecPeak_PGV_MinRollAvg', 'PostSecPeak_PGV_MaxRollAvg',\n", "                             'Recent2Day_MeanPeakGapValue', 'Recent2Day_MeanValleyGapValue',\n", "                             'Recent9Day_MaxValleyGapValue', 'Recent9Day_MaxPeakGapValue',\n", "                             'PostSecStart_PGV_MaxRollAvg', 'PostSecStart_PGV_MinRollAvg',\n", "                             'PostSecStart_VGV_MaxRollAvg',\n", "                             'PostSec_Peak_PGV_RollAvg', 'PostSec_Peak_PGV_RollAvg_Desc_Rank',\n", "                             'PostPreNowBottom_PGV_MinRollAvg', 'Now_PostPreNowBottom_PGV_RollAvg_Asc_Rank',\n", "                             'Now_PGV_RollAvg', 'Now_VGV_RollAvg', 'PostSecPeak_PGV_MinRollAvg_Date',\n", "                             'PGV_Post_MinRollAvg_MaxVGV_CoverDays', 'PostNowSec_PGV_MinRollAvg',\n", "                             'PostNowSec_PGV_MaxRollAvg', 'PostNowSec_PGV_MaxRollAvg_Date',\n", "                             'Recent3Day_PGV_MinRollAvg', 'Recent3Day_PGV_MaxRollAvg',\n", "                             'Recent3Day_PGV_MeanRollAvg',\n", "                             'PostSecMaxRollAvg_PGV_MinRollAvg', 'PostSecMaxRollAvg_PGV_MinRollAvg_Date',\n", "                             'PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio',\n", "                             'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays',\n", "                             'PostSecMaxRollAvg_PGV_PostMin_MaxRollAvg2Max_Ratio',\n", "                             'PreSecMaxRollAvg_PGV_MinRollAvg', 'Post2Pre_PGV_MinRollAvg_Ratio',\n", "                             'PostSecMaxRollAvg_PGV_RollAvg_Now2Min_Ratio', 'PreSecMaxRollAvg_PGV_MaxRollAvg2MinRatio',\n", "                             'PreSecMaxRollAvg_PGV_MeanRollAvg', 'PostSecPeak_PGV_MaxRollAvg_Quarter',\n", "                             'PostNowSec_PGV_MeanRollAvg', 'PostSecStart_PGV_MeanRollAvg',\n", "                             'PostSecStart_PGV_MaxRollAvg_Date', 'PostSecStart_PeakDate_Diff',\n", "                             'AftPreTurnPeak_PGV_MinRollAvg', 'AftPreTurnPeak_PGV_MinRollAvg2MaxRatio',\n", "                             'AftPreTurnPeak_PGV_MinRollAvg2Now_LastDays', 'PreNowPeak_PGV_MeanRollAvg',\n", "                             'PostSecMaxRollAvg_MinPGV', 'PostSecMaxRollAvg_MinPGV2Now_LastDays',\n", "                             'PostSecMaxRollAvg_MinPGV2MaxRatio', 'Pre3Date_PeakGapValue', 'PostSecStart_MaxPGV_Date',\n", "                             'PostMinRollAvg_RiseTrend_Prop', 'PostSecStart_RiseTrend_Prop', 'MinRollAvg_NowSec_Diff',\n", "                             'PostSecPeak_DropTrend_Prop', 'PostPreNowPeak_DropTrend_Prop', 'MinRollAvg_Truncated_Diff',\n", "                             'Now_PGVRollAvg_DownCount', 'Now_PGVRollAvg_UpCount',\n", "                             'BfMinRollAvg_PGVRollAvg_DownCount', 'PostSec_MaxRollAvg_PeakDate_Diff',\n", "                             'PostSecPeak_PGV_MeanRollAvg_TruncatedValue',\n", "                             'SecConcave_PGV_MinRollAvg', 'SecConcave_PGV_MinRollAvg2MeanRatio',\n", "                             'SecConcave_PGV_MinRollAvg_Date', 'SecConcave_PGV_MinRollAvg2Now_LastDays',\n", "                             'Peak2Sec_PGV_MinRollAvg', 'Peak2Sec_PGV_MinRollAvg_Date',\n", "                             'Peak2Sec_PGV_MinRollAvg2MeanRatio',\n", "                             'PostSecPeak_DownConsecutive_Num', 'PostSecMaxRollAvg_DownConsecutive_Num',\n", "                             'PostNowSec_UpConsecutive_Num', 'PostSecStart_UpConsecutive_Num',\n", "                             'PreNowSec_PGV_MeanRollAvg', 'PostNowSec_MaxPeakGapValue_Date',\n", "                             'DownConsecutive2Now_LastDays', 'UpConsecutive2Now_LastDays',\n", "                             'PostNow2PostSec_PGV_MeanRollAvg_Ratio', 'PreNow2PostSec_PGV_MeanRollAvg_Ratio',\n", "                             'DownConsecutive_PGVRollAvg_DiffRatio', 'UpConsecutive_PGVRollAvg_DiffRatio',\n", "                             'DownConsecutive_SumRatio', 'UpConsecutive_SumRatio',\n", "                             'DownConsecutive_Start_PGVRollAvg', 'UpConsecutive_Start_PGVRollAvg',\n", "                             'Break_DownSecutiveStart_First2Now_LastDays', 'Break_UpSecutiveStart_First2Now_LastDays',\n", "                             'DownConsecutive_Last2Break_LastDays', 'UpConsecutive_Last2Break_LastDays',\n", "                             'PostNow2PreNow_PGV_MeanRollAvg_Ratio',\n", "                             'PostSecMaxRollAvg_PGV_MaxRollAvg2Min_SumRatio',\n", "                             'PostSecPeak_DownConsecutive_MaxLastDays', 'PostSecMaxRollAvg_DownConsecutive_MaxLastDays',\n", "                             'PostNowSec_UpConsecutive_MaxLastDays', 'PostSecStart_UpConsecutive_MaxLastDays',\n", "                             'PostPreSecPeak_DownConsecutive_Num',\n", "                             'PostPreSecPeak_DownConsecutive_MaxLastDays',\n", "                             'PostPreNowBottom_UpConsecutive_Num', 'PostPreNowBottom_UpConsecutive_MaxLastDays',\n", "                             'PostPreNowBottom_DownConsecutive_Num', 'PostPreNowBottom_DownConsecutive_MaxLastDays',\n", "                             'PreNowPeak_PGV_MinRollAvg2Now_LastDays', 'PreNowPeak_PGV_MinRollAvg2MaxRatio',\n", "                             'PostSecMaxRollAvg_PGV_MinRollAvg2Now_SumRatio', 'PostSecPeak_PGV_MinRollAvg2Now_SumRatio',\n", "                             'Peak2Sec_PGV_MinRollAvg2Sec_LastDays', 'Peak2Sec_PGV_MinRollAvg2Sec_SumRatio',\n", "                             'PreNowPeak_PGV_MaxRollAvg', 'PreNowSec_PGV_MaxRollAvg',\n", "                             'Peak2Sec_PGV_MaxRollAvg', 'Peak2Sec_PGV_MeanRollAvg',\n", "                             'Peak2Sec_PGV_RollAvg_LowQuntl', 'PGVRollAvg_Now2PreSecLowQuntl_Ratio',\n", "                             'PostSecStart_PGV_RollAvg_LowQuntl',\n", "                             'PostSecStart_PGV_RollAvg_HighQuntl', 'PostSec2PreSec_PGV_MaxRollAvg_Ratio',\n", "                             'PGVRollAvg_Now2PostSecLowQuntl_Ratio', 'PGVRollAvg_Now2PostSecHighQuntl_Ratio',\n", "                             'PostSecMaxRollAvg_PGV_MeanRollAvg',\n", "                             'Peak2Sec_PGV_RollAvg_VolRange', 'PostSecStart_PGV_RollAvg_VolRange',\n", "                             'PostSecMaxRollAvg_PGV_RollAvg_VolRange',\n", "                             'PostSec_RiseSec10Days_PGV_MaxRollAvg', 'PreNowSec_DropSec10Days_PGV_MinRollAvg',\n", "                             'Is_Bottom_Reversal', 'Is_Expanding', 'Is_More_Volatile',\n", "                             'PostSecPeak_MaxTO_Eff', 'PostSecPeak_MaxTO_Eff_Date',\n", "                             'PostSecPeak_MaxTO_Eff2Now_LastDays',\n", "                             'PostSecPeak_TO_Eff_Max2Min_Ratio',\n", "                             'Now_TO_Eff',\n", "                             'PostPreNowPeak_MaxTO_Eff', 'PostPreNowPeak_MaxTO_Eff_Date',\n", "                             'PostPreNowPeak_MaxTO_Eff2Now_LastDays',\n", "                             'PostPreNowPeak_MaxTO_Eff_CoverDays', 'PostPreNowPeak_TO_Eff_Max2Min_Ratio',\n", "                             'PostSec_MinTO_Eff', 'PostSec_MinTO_Eff_Date', 'PostSec_MinTO_Eff2Now_LastDays',\n", "                             'PostSec_MinTO_Eff_CoverDays', 'PostSec_TO_Eff_Min2Max_Ratio',\n", "                             'PostPreNowPeak_U2D_MaxTO_Eff', 'PostPreNowPeak_U2D_MaxTO_Eff_Date',\n", "                             'PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays', 'PostPreNowPeak_U2D_MaxTO_Eff_CoverDays',\n", "                             'Eff_Recent2Previous_MinChange',\n", "                             'Is_LowBound_Oscillation',\n", "                             'Recent_EffPeak_ChangeRate', 'Recent_EffPeak_ChangeRate_Percentile',\n", "                             'PostPreNowPeak_MaxTO_Eff_Band', 'PostSec_MinTO_Eff_Band',\n", "                             'PostPreNowPeak_U2D_MaxTO_Eff_Band',\n", "                             'Latest_TO_Eff_Band',\n", "                             'Eff_Recent2Previous_Change',\n", "                             'Eff_Avg_Peak_Period', 'Eff_Avg_Valley_Period',\n", "                             'Eff_Peak_Intensity', 'Eff_Valley_Intensity',\n", "                             'Days_From_Last_Peak', 'Days_From_Last_Valley',\n", "                             'PostSecMaxRollAvg_PGV_MinRollAvg_Band', 'PostSecMaxRollAvg_PGV_MinRollAvg_CoverDays',\n", "                             'PostSecStart_PGV_MaxRollAvg_Band', 'PostSecStart_PGV_MaxRollAvg_CoverDays',\n", "                             'PostNowSec_PGV_MaxRollAvg_Band', 'PostNowSec_PGV_MaxRollAvg_CoverDays',\n", "                             'Now_PGV_RollAvg_Rank',\n", "                             'PGV_RollAvg_Recent2Previous_Change',\n", "                             'PGV_RollAvg_NowSec2Previous_Change',\n", "                             'PGV_RollAvg_VolatilityRatio',\n", "                             'Efficiency_VolatilityRatio', 'U2D_Efficiency_VolatilityRatio',\n", "                             'PostPreNowPeak_U2D_MinTO_Eff', 'PostPreNowPeak_U2D_MinTO_Eff_Date',\n", "                             'PostPreNowPeak_U2D_MinTO_Eff2Now_LastDays',\n", "                             'PostPreNowPeak_U2D_MinTO_Eff_CoverDays',\n", "                             'Eff_Latest2Pre3Mean', 'UpEff_Latest2Pre3Mean',\n", "                             'PostPreNow_MaxUpEff_Date', 'PostPreNow_MaxUpEff2Now_LastDays',\n", "                             'Latest_Eff_Peak_Date', 'Latest_Eff_Peak2Now_Days',\n", "                             'Latest_UpEff_Peak_Date', 'Latest_UpEff_Peak2Now_Days',\n", "                             'PostPreNow_UpEff_MaxTO_Eff_Band', 'PostNowSec_PGVRollAvg_CoverDrop_Diff',\n", "                             'PostNowSec_PGVRollAvg_CoverDrop_DiffRatio',\n", "                             'PreTurnPeak_PRV_Top3Mean', 'SectionPeak_PRV_Top3Mean',\n", "                             'SectionStart_PRV_Top3Mean', 'NowSec_PRV_Top3Mean',\n", "                             'PreNowSec_PRV_Top3Mean', 'PreNowPeak_PRV_Top3Mean',\n", "                             'Turn_PRV_Top3Mean', 'PostTurnPeak_PRV_Top3Mean', 'PostSecPeak_PRV_Top3Mean',\n", "                             'PostSecPeak_PRV_Top3Mean_CoverDays',\n", "                             'PostSecPeak_PRA2Close_CoverDays_Diff',\n", "                             'Recent3Day_PRA_SectionStart_Rank', 'Recent3Day_PRA_NowSec_Rank',\n", "                             'NowSec_PGV_MaxRollAvg_UpCoverDays', 'NowSec_PGV_MinRollAvg_DownCoverDays',\n", "                             'NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio', \n", "                             'NowSec_PGV_DownCoverPeriod_Min2Max_DiffRatio',\n", "                             'NowSec_PRA2Close_CoverDays_Diff',\n", "                             'SecStart_PGV_MaxRollAvg_UpCoverDays', 'SecStart_PGV_MinRollAvg_DownCoverDays',\n", "                             'SecStart_PGV_UpCoverPeriod_Max2Min_DiffRatio',\n", "                             'SecStart_PGV_DownCoverPeriod_Min2Max_DiffRatio',\n", "                             'SecStart_PRA2Close_CoverDays_Diff',\n", "                             'NowSec_MaxPRA_Percentile_PostTurn',\n", "                             'NowSec_MinPRA_Percentile_PostTurn',\n", "                             'NowSec_MaxPRA_Percentile_PostSectionPeak',\n", "                             'NowSec_MinPRA_Percentile_PostSectionPeak',\n", "                             'PostSecStart_MaxPRA_Percentile_PostSectionPeak',\n", "                             'PostSecStart_MaxPRA_Percentile_PostTurn',\n", "                             'SecPeak2NowSec_PRA_UpBand',\n", "                             'SecPeak2NowSec_PRA_LowBand',\n", "                             'Turn2NowSec_PRA_UpBand',\n", "                             'Turn2NowSec_PRA_LowBand',\n", "                             'Latest_EffPeak2NowSec_Diff',\n", "                            #  'PostPreNowPeak_PRA_MaxRate',\n", "                            #  'PostPreNowPeak_PRA_MaxRate_Date',\n", "                            #  'PostPreNowPeak_PRA_MaxRate_Date2NowSec_Diff',\n", "                            #  'PostPreNowPeak_MaxRate_PRA_Percentile',\n", "                             'Now_PRA_Percentile_PostSectionPeak',\n", "                             'PostNowSec_PRA_MaxRate',\n", "                             'PostNowSec_PRA_MaxRate_Date',\n", "                             'PostNowSec_MaxRate2Now_LastDays',\n", "                             'PostNowSec_NowSec2MaxRate_LastDays',\n", "                             'PostSecStart_PRA_MaxRate',\n", "                             'PostSecStart_PRA_MaxRate_Date',\n", "                             'Now_PRA_Rate',\n", "                             'PostNowSec_PRA_MaxRate_BreachCount',\n", "                             'PostSecStart_PRA_MaxRate_BreachCount',\n", "                             'PostNowSec_MaxRate_PRA_Percentile',\n", "                             'PostNowSec_MaxRate_Post2Pre_DiffRatio',\n", "                             'SecStart_PRA_FirstBreach_Date',\n", "                             'SecStart_FirstBreach2Now_LastDays',\n", "                             'SecStart_PRA_LastBreach_Date',\n", "                             'SecStart_LastBreach2Now_LastDays',\n", "                             'SecStart_PRA_Breach_Count',\n", "                             'SecStart_PRA_LastBreach_ContinuousDays',\n", "                             'NowSec_PRA_FirstBreach_Date',\n", "                             'NowSec_FirstBreach2Now_LastDays',\n", "                             'NowSec_PRA_LastBreach_Date',\n", "                             'NowSec_LastBreach2Now_LastDays',\n", "                             'NowSec_PRA_Breach_Count',\n", "                             'NowSec_PRA_LastBreach_ContinuousDays',\n", "                             'PostSecStart_MaxPGVDate2Now_LastDays',\n", "                             'PostSecStart_MaxVGV',\n", "                             'PostSecStart_MaxVGV_Date',\n", "                             'PostSecStart_MaxVGVDate2Now_LastDays',\n", "                             'PostSecStart_PGV_Max2Mean_Ratio',\n", "                             'PostSecStart_VGV_Max2Mean_Ratio',\n", "                             'PostSecStart_PGV_Now2Mean_Ratio',\n", "                             'PostNowSec_PGV_Now2Mean_Ratio',\n", "                             'PostNowSec_PGV_Max2Mean_Ratio',\n", "                             'PGV_Now2Pre3Days_Ratio',\n", "                             'PostSec_PGV_TurnP_Date',\n", "                             'PostSec_PGV_TurnP_AbsChange',\n", "                             'PostSec_PGV_TurnP_RelaChange',\n", "                             'PostSec_PGV_PostTurnP_LastDays',\n", "                             'Peak2Sec_PGV_TurnP_Date',\n", "                             'Peak2Sec_PGV_TurnP_AbsChange',\n", "                             'Peak2Sec_PGV_TurnP_RelaChange',\n", "                             'Peak2Sec_PGV_PostTurnP_LastDays',\n", "                             'Cal_Date']"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["gapvalue_column_names_adj = ['ts_code'] + gapvalue_column_names"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import get_result_3\n", "result_store = get_result_3(start_date='2024-05-20', end_date='2025-06-20')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['2025-05-22'], dtype=object)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result_store['Cal_Date'].unique()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from function_ai.Func_Base import get_stock_info\n", "from function_ai.StkQuota_Func_V7 import set_resultindexs\n", "import pandas as pd\n", "Result_Common = get_stock_info()\n", "Result_Common = Result_Common[['ts_code', 'name', 'industry', 'area']]\n", "common_column_names, gapvalue_column_names = set_resultindexs()\n", "# column_names = common_column_names + gapvalue_column_names\n", "Result_Common = pd.concat([Result_Common, pd.DataFrame(columns=common_column_names)], sort=False, axis=1)\n", "Result_GapValue = pd.concat([Result_Common[['ts_code']], pd.DataFrame(columns=gapvalue_column_names)], sort=False, axis=1)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from function_ai.Func_Base import get_stock_data\n", "stk_data = get_stock_data(stk_code='002691.SZ', start_date='2024-11-19', end_date='2025-04-28')\n", "stk_data = stk_data.set_index('trade_date')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["[20.766, -14.872, '2025-01-16', '2025-04-08']"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_data['mean_price'] = round((stk_data['close'] + stk_data['open'])/2, 3)\n", "stk_data['mean_close_rollavg'] = round(stk_data['close'].rolling(window=3, closed='left').mean(), 3)\n", "stk_data['over_movavg'] = stk_data.apply(lambda x: 1 if x['mean_price']>x['mean_close_rollavg'] else 0, axis=1)\n", "stk_data['mean2movavg_rate'] = round((stk_data['mean_price'] / stk_data['mean_close_rollavg'] - 1) * 100, 3)\n", "[stk_data.loc['2025-01-06':'2025-02-13', 'mean2movavg_rate'].max(), stk_data.loc['2025-02-13':'2025-04-09', 'mean2movavg_rate'].min(), \n", "stk_data.loc['2025-01-06':'2025-02-13', 'mean2movavg_rate'].idxmax(), stk_data.loc['2025-02-13':'2025-04-09', 'mean2movavg_rate'].idxmin()]\n", "\n", " "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 2}